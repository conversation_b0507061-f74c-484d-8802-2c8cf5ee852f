/*
Copyright (c) 2003-2021, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/

html {
  /*background-color: #C7CBD0;*/
}

body {
  /* Font */
  /* Emoji fonts are added to visualise them nicely in Internet Explorer. */
  font-family: sans-serif, <PERSON><PERSON>, <PERSON>erdana, "Trebuchet MS", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 12px;

  /* Text color */
  color: #333;

  /* Remove the background color to make it transparent. */
  background-color: #fff;

  /*margin: 20px;*/
}

.cke_editable {
  font-size: 13px;
  line-height: 1.6;

  /* Fix for missing scrollbars with RTL texts. (#10488) */
  word-wrap: break-word;
}

blockquote {
  font-style: italic;
  font-family: Georgia, Times, "Times New Roman", serif;
  padding: 2px 0;
  border-style: solid;
  border-color: #ccc;
  border-width: 0;
}

.cke_contents_ltr blockquote {
  padding-left: 20px;
  padding-right: 8px;
  border-left-width: 5px;
}

.cke_contents_rtl blockquote {
  padding-left: 8px;
  padding-right: 20px;
  border-right-width: 5px;
}

a {
  color: #0782C1;
}

ol, ul, dl {
  /* IE7: reset rtl list margin. (#7334) */
  *margin-right: 0px;
  /* Preserved spaces for list items with text direction different than the list. (#6249,#8049)*/
  padding: 0 40px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
  line-height: 1.2;
}

hr {
  border: 0px;
  border-top: 1px solid #ccc;
}

img.right {
  border: 1px solid #ccc;
  float: right;
  margin-left: 15px;
  padding: 5px;
}

img.left {
  border: 1px solid #ccc;
  float: left;
  margin-right: 15px;
  padding: 5px;
}

pre {
  white-space: pre-wrap; /* CSS 2.1 */
  word-wrap: break-word; /* IE7 */
  -moz-tab-size: 4;
  tab-size: 4;
}

.marker {
  background-color: Yellow;
}

span[lang] {
  font-style: italic;
}

figure {
  text-align: center;
  outline: solid 1px #ccc;
  background: rgba(0, 0, 0, 0.05);
  padding: 10px;
  margin: 10px 20px;
  display: inline-block;
}

figure > figcaption {
  text-align: center;
  display: block; /* For IE8 */
}

a > img {
  padding: 1px;
  margin: 1px;
  border: none;
  outline: 1px solid #0782C1;
}

/* Widget Styles */
.code-featured {
  border: 5px solid red;
}

.math-featured {
  padding: 20px;
  box-shadow: 0 0 2px rgba(200, 0, 0, 1);
  background-color: rgba(255, 0, 0, 0.05);
  margin: 10px;
}

.image-clean {
  border: 0;
  background: none;
  padding: 0;
}

.image-clean > figcaption {
  font-size: .9em;
  text-align: right;
}

.image-grayscale {
  background-color: white;
  color: #666;
}

.image-grayscale img, img.image-grayscale {
  filter: grayscale(100%);
}

.embed-240p {
  max-width: 426px;
  max-height: 240px;
  margin: 0 auto;
}

.embed-360p {
  max-width: 640px;
  max-height: 360px;
  margin: 0 auto;
}

.embed-480p {
  max-width: 854px;
  max-height: 480px;
  margin: 0 auto;
}

.embed-720p {
  max-width: 1280px;
  max-height: 720px;
  margin: 0 auto;
}

.embed-1080p {
  max-width: 1920px;
  max-height: 1080px;
  margin: 0 auto;
}

.document-editor {
  /*width: 15.8cm;*/
  max-width: 17.8cm;
  min-height: 21cm;
  padding: 20px 30px 30px;
  border: 1px hsl(0, 0%, 82.7%) solid;
  border-radius: 5px;
  background: white;
  box-shadow: 0 0 5px hsla(0, 0%, 0%, .1);
  margin: 0px auto;
  /*box-sizing: border-box;*/
}

.contrast-ck {
  /*width: 100% !important;*/
  margin: 0 !important;
  /*padding: 10px !important;*/
}

/* 滚动条部分 */
::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

::-webkit-scrollbar:horizontal {
  height: 10px;
}

.cke_editable {
  cursor: text
}

.cke_editable img, .cke_editable input, .cke_editable textarea {
  cursor: default
}

[data-cke-editorplaceholder]::before {
  position: absolute;
  opacity: .8;
  color: #aaa;
  content: attr(data-cke-editorplaceholder);
}

.cke_wysiwyg_div[data-cke-editorplaceholder]::before {
  margin-top: 1em;
}

.cke_editable form {
  border: 1px dotted #FF0000;
  padding: 2px;
}

img.cke_hidden {
  background-image: url(http://localhost/ckeditor/plugins/forms/images/hiddenfield.gif?t=LAHF);
  background-position: center center;
  background-repeat: no-repeat;
  border: 1px solid #a9a9a9;
  width: 16px !important;
  height: 16px !important;
}

img.cke_iframe {
  background-image: url(http://localhost/ckeditor/plugins/iframe/images/placeholder.png?t=LAHF);
  background-position: center center;
  background-repeat: no-repeat;
  border: 1px solid #a9a9a9;
  width: 80px;
  height: 80px;
}

.cke_contents_ltr a.cke_anchor, .cke_contents_ltr a.cke_anchor_empty, .cke_editable.cke_contents_ltr a[name], .cke_editable.cke_contents_ltr a[data-cke-saved-name] {
  background: url(http://localhost/ckeditor/plugins/link/images/anchor.png?t=LAHF) no-repeat left center;
  border: 1px dotted #00f;
  background-size: 16px;
  padding-left: 18px;
  cursor: auto;
}

.cke_contents_ltr img.cke_anchor {
  background: url(http://localhost/ckeditor/plugins/link/images/anchor.png?t=LAHF) no-repeat left center;
  border: 1px dotted #00f;
  background-size: 16px;
  width: 16px;
  min-height: 15px;
  height: 1.15em;
  vertical-align: text-bottom;
}

.cke_contents_rtl a.cke_anchor, .cke_contents_rtl a.cke_anchor_empty, .cke_editable.cke_contents_rtl a[name], .cke_editable.cke_contents_rtl a[data-cke-saved-name] {
  background: url(http://localhost/ckeditor/plugins/link/images/anchor.png?t=LAHF) no-repeat right center;
  border: 1px dotted #00f;
  background-size: 16px;
  padding-right: 18px;
  cursor: auto;
}

.cke_contents_rtl img.cke_anchor {
  background: url(http://localhost/ckeditor/plugins/link/images/anchor.png?t=LAHF) no-repeat right center;
  border: 1px dotted #00f;
  background-size: 16px;
  width: 16px;
  min-height: 15px;
  height: 1.15em;
  vertical-align: text-bottom;
}

div.cke_pagebreak {
  background: url(http://localhost/ckeditor/plugins/pagebreak/images/pagebreak.gif?t=LAHF) no-repeat center center !important;
  clear: both !important;
  width: 100% !important;
  border-top: #999 1px dotted !important;
  border-bottom: #999 1px dotted !important;
  padding: 0 !important;
  height: 7px !important;
  cursor: default !important;
}

.cke_show_blocks h6:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks h5:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks h4:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks h3:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks h2:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks h1:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks blockquote:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks address:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks pre:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks div:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks p:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-repeat: no-repeat;
  border: 1px dotted gray;
  padding-top: 8px
}

.cke_show_blocks h6:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h6.png?t=LAHF)
}

.cke_show_blocks h5:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h5.png?t=LAHF)
}

.cke_show_blocks h4:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h4.png?t=LAHF)
}

.cke_show_blocks h3:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h3.png?t=LAHF)
}

.cke_show_blocks h2:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h2.png?t=LAHF)
}

.cke_show_blocks h1:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_h1.png?t=LAHF)
}

.cke_show_blocks blockquote:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_blockquote.png?t=LAHF)
}

.cke_show_blocks address:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_address.png?t=LAHF)
}

.cke_show_blocks pre:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_pre.png?t=LAHF)
}

.cke_show_blocks div:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_div.png?t=LAHF)
}

.cke_show_blocks p:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-image: url(http://localhost/ckeditor/plugins/showblocks/images/block_p.png?t=LAHF)
}

.cke_show_blocks.cke_contents_ltr h6:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr h5:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr h4:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr h3:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr h2:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr h1:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr blockquote:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr address:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr pre:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr div:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_ltr p:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-position: top left;
  padding-left: 8px
}

.cke_show_blocks.cke_contents_rtl h6:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl h5:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl h4:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl h3:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl h2:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl h1:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl blockquote:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl address:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl pre:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl div:not([contenteditable=false]):not(.cke_show_blocks_off), .cke_show_blocks.cke_contents_rtl p:not([contenteditable=false]):not(.cke_show_blocks_off) {
  background-position: top right;
  padding-right: 8px
}

.cke_show_borders table.cke_show_border, .cke_show_borders table.cke_show_border > tr > td, .cke_show_borders table.cke_show_border > tr > th, .cke_show_borders table.cke_show_border > tbody > tr > td, .cke_show_borders table.cke_show_border > tbody > tr > th, .cke_show_borders table.cke_show_border > thead > tr > td, .cke_show_borders table.cke_show_border > thead > tr > th, .cke_show_borders table.cke_show_border > tfoot > tr > td, .cke_show_borders table.cke_show_border > tfoot > tr > th {
  border: #d3d3d3 1px dotted
}

.cke_widget_wrapper {
  position: relative;
  outline: none
}

.cke_widget_inline {
  display: inline-block
}

.cke_widget_wrapper:hover > .cke_widget_element {
  outline: 2px solid #ffd25c;
  cursor: default
}

.cke_widget_wrapper:hover .cke_widget_editable {
  outline: 2px solid #ffd25c
}

.cke_widget_wrapper.cke_widget_focused > .cke_widget_element, .cke_widget_wrapper .cke_widget_editable.cke_widget_editable_focused {
  outline: 2px solid #47a4f5
}

.cke_widget_editable {
  cursor: text
}

.cke_widget_drag_handler_container {
  position: absolute;
  width: 15px;
  height: 0;
  display: block;
  opacity: 0.75;
  transition: height 0s 0.2s;
  line-height: 0
}

.cke_widget_wrapper:hover > .cke_widget_drag_handler_container {
  height: 15px;
  transition: none
}

.cke_widget_drag_handler_container:hover {
  opacity: 1
}

.cke_editable[contenteditable="false"] .cke_widget_drag_handler_container {
  display: none;
}

img.cke_widget_drag_handler {
  cursor: move;
  width: 15px;
  height: 15px;
  display: inline-block
}

.cke_widget_mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block
}

.cke_widget_partial_mask {
  position: absolute;
  display: block
}

.cke_editable.cke_widget_dragging, .cke_editable.cke_widget_dragging * {
  cursor: move !important
}

.cke_upload_uploading img {
  opacity: 0.3
}

table{
  border-collapse: collapse;
  max-width: 100%;
}

td,th{
  border: #d3d3d3 1px solid;
}

img{
  max-width: 100%;
}
