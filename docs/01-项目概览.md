# 易建采电子采购平台 V2 - 项目概览

## 项目简介

易建采电子采购平台第二版（epcos-v2）是一个基于Vue.js 2.x开发的现代化电子采购管理系统。该平台为政府机构、企事业单位提供完整的电子采购解决方案，支持多角色协同工作，包括采购方、供应商、专家等。

## 技术栈

### 前端核心技术
- **Vue.js 2.6.12** - 渐进式JavaScript框架
- **Vue Router 3.4.9** - 官方路由管理器
- **Vuex 3.6.0** - 状态管理模式
- **Element UI 2.15.14** - 基于Vue的桌面端组件库
- **UMY UI 1.1.7** - 自定义组件库

### 构建工具
- **Vue CLI 4.4.6** - Vue.js开发的标准工具
- **Webpack** - 模块打包器
- **Babel** - JavaScript编译器
- **Sass** - CSS预处理器

### 主要依赖库
- **Axios 0.28.1** - HTTP客户端
- **ECharts 5.4.2** - 数据可视化图表库
- **Moment.js 2.29.1** - 日期处理库
- **Lodash 4.17.21** - JavaScript实用工具库
- **Quill 1.3.7** - 富文本编辑器
- **XLSX 0.18.5** - Excel文件处理
- **CryptoJS 4.1.1** - 加密库
- **JSEncrypt 3.0.0** - RSA加密
- **SockJS Client 1.5.1** - WebSocket客户端
- **StompJS 2.3.3** - STOMP协议客户端

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git钩子管理
- **Lint-staged** - 暂存文件检查

## 项目特性

### 多角色支持
- **采购方（Purchaser）** - 发起采购项目、管理供应商、审核流程
- **供应商（Supplier）** - 参与投标、商品管理、项目响应
- **专家（Expert）** - 评审项目、技术评估
- **系统管理员** - 系统配置、用户管理、权限控制

### 核心功能模块
1. **项目管理** - 采购项目全生命周期管理
2. **供应商管理** - 供应商注册、审核、评价
3. **专家管理** - 专家库维护、抽取、评审
4. **公告管理** - 采购公告、中标公告发布
5. **合同管理** - 合同签订、履约管理
6. **支付管理** - 在线支付、订单管理
7. **审批流程** - 可配置的审批工作流
8. **数据统计** - 多维度数据分析和报表

### 技术特色
- **多布局支持** - 针对不同角色提供专门的布局
- **响应式设计** - 适配不同屏幕尺寸
- **组件化开发** - 高度可复用的组件库
- **权限控制** - 细粒度的权限管理
- **国际化支持** - 多语言切换
- **主题定制** - 可配置的主题样式
- **实时通信** - WebSocket实时消息推送

## 项目结构概览

```
epcos-v2/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── api/               # API接口定义
│   ├── assets/            # 静态资源
│   ├── components/        # 公共组件
│   ├── directive/         # 自定义指令
│   ├── layout/            # 布局组件
│   ├── homeLayout/        # 首页布局
│   ├── expertLayout/      # 专家布局
│   ├── processLayout/     # 流程布局
│   ├── supplierLayout/    # 供应商布局
│   ├── plugins/           # 插件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
├── build/                 # 构建脚本
├── dist/                  # 构建输出
└── docs/                  # 项目文档
```

## 环境配置

### 开发环境要求
- Node.js >= 8.9
- NPM >= 3.0.0
- 现代浏览器（支持ES6+）

### 多环境支持
项目支持多个部署环境，通过不同的构建命令进行区分：
- `dev` - 开发环境
- `stage` - 测试环境
- `prod` - 生产环境
- 多个客户定制环境（jxzl、syth、whpr、bjxk、xyzy、fjnx、whws等）

## 快速开始

```bash
# 克隆项目
<NAME_EMAIL>:web/epcos-v2.git

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build:prod
```

## 版本信息
- **当前版本**: 3.4.0
- **许可证**: MIT
- **仓库地址**: **********************:web/epcos-v2.git
