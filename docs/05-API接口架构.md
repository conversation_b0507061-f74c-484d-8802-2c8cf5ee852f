# API接口架构详解

## 接口组织结构

易建采平台的API接口按业务模块进行组织，采用RESTful风格设计，统一使用Axios进行HTTP请求封装。

## 1. 接口分层架构

### 请求封装层
**文件**: `src/utils/request.js`
**功能**: 
- HTTP请求统一封装
- 请求/响应拦截器
- 错误处理机制
- Token自动添加
- 请求重试机制

```javascript
// 请求拦截器
service.interceptors.request.use(config => {
  // 添加Token
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
})

// 响应拦截器
service.interceptors.response.use(res => {
  // 统一错误处理
  const code = res.data.code || 200
  if (code !== 200) {
    return Promise.reject(new Error(res.data.msg))
  }
  return res.data
})
```

### API接口层
**目录**: `src/api/`
**功能**: 
- 业务接口定义
- 参数封装
- 返回数据处理
- 接口文档注释

## 2. 核心业务接口模块

### 2.1 认证授权模块 (`src/api/login.js`)

#### 主要接口
- `login(data)` - 用户登录
- `register(data)` - 用户注册
- `refreshToken()` - 刷新Token
- `getInfo()` - 获取用户信息
- `logout()` - 用户登出
- `getCodeImg()` - 获取验证码
- `getSmsCode(data)` - 获取短信验证码
- `modifyPassword(data)` - 修改密码

#### 接口特点
- 支持多种登录方式
- RSA加密传输
- JWT Token认证
- 验证码防护

### 2.2 采购方业务接口 (`src/api/purchaser/`)

#### 项目管理接口
**文件**: `projectList.js`, `projectInformation.js`, `projectArchive.js`
- 项目创建、编辑、删除
- 项目状态管理
- 项目文件管理
- 项目归档处理

#### 供应商管理接口
**文件**: `supplierAudit.js`, `registrationSupplier.js`
- 供应商注册审核
- 供应商资质管理
- 供应商评价管理
- 供应商黑名单管理

#### 专家管理接口
**文件**: `expertExtract.js`, `reviewExpert.js`
- 专家库管理
- 专家随机抽取
- 专家评审管理
- 专家履职评价

#### 开标评标接口
**文件**: `bidOpen.js`, `startReview.js`, `reviewReport.js`
- 在线开标管理
- 评标流程控制
- 评审报告生成
- 结果公示管理

#### 公告管理接口
**文件**: `bulletin.js`
- 公告发布管理
- 公告审核流程
- 公告模板管理
- 公告统计分析

### 2.3 供应商业务接口 (`src/api/supplier/`)

#### 供应商信息接口
**文件**: `supplierInfo.js`
- 企业基本信息管理
- 资质证书管理
- 财务状况管理
- 联系人信息管理

#### 商品管理接口
**文件**: `goodsManage.js`
- 商品信息录入
- 商品分类管理
- 价格管理
- 库存管理

#### 项目参与接口
**文件**: `supplierProject.js`, `supplierBidOpen.js`
- 项目报名参与
- 投标文件上传
- 开标过程参与
- 中标结果查询

#### 流程管理接口
**文件**: `supplierProcess.js`
- 业务流程跟踪
- 任务处理
- 状态更新
- 历史记录

### 2.4 专家业务接口 (`src/api/expert/`)

#### 评审管理接口
**文件**: `reviewManage.js`
- 评审任务接收
- 在线评审操作
- 评审意见提交
- 评审报告生成

### 2.5 系统管理接口 (`src/api/system/`)

#### 用户管理接口
**文件**: `user.js`, `role.js`, `dept.js`, `post.js`
- 用户账号管理
- 角色权限管理
- 部门组织管理
- 岗位职务管理

#### 系统配置接口
**文件**: `config.js`, `dict/`, `menu.js`
- 系统参数配置
- 数据字典管理
- 菜单权限管理
- 功能模块配置

#### 模板管理接口
**文件**: `approvalTemplate.js`, `fileTemplate.js`
- 审批模板管理
- 文件模板管理
- 模板版本控制
- 模板应用统计

#### 日志监控接口
**文件**: `logininfor.js`, `operlog.js`
- 登录日志记录
- 操作日志记录
- 系统异常日志
- 安全审计日志

### 2.6 监控管理接口 (`src/api/monitor/`)

#### 在线用户接口
**文件**: `online.js`
- 在线用户监控
- 会话管理
- 强制下线
- 并发控制

#### 定时任务接口
**文件**: `job.js`, `jobLog.js`
- 定时任务管理
- 任务执行监控
- 任务日志查看
- 任务调度配置

## 3. 通用功能接口

### 3.1 文件管理接口 (`src/api/file.js`)
- 文件上传处理
- 文件下载服务
- 文件预览功能
- 文件删除管理

### 3.2 短信服务接口 (`src/api/sms.js`)
- 短信发送服务
- 验证码生成
- 短信模板管理
- 发送状态查询

### 3.3 菜单管理接口 (`src/api/menu.js`)
- 动态菜单加载
- 权限菜单过滤
- 菜单结构构建
- 菜单缓存管理

### 3.4 工作台接口 (`src/api/workplace.js`)
- 工作台数据统计
- 待办任务查询
- 消息通知管理
- 快捷操作入口

## 4. 特殊业务接口

### 4.1 AI审核接口 (`src/api/aiReview.js`)
- AI智能审核
- 文档内容分析
- 风险评估
- 审核建议生成

### 4.2 答疑管理接口 (`src/api/answerQuestions.js`)
- 问题提交
- 答疑回复
- 问题分类
- 答疑统计

### 4.3 合同管理接口 (`src/api/contractManage.js`)
- 合同生成
- 电子签署
- 履约管理
- 合同归档

### 4.4 印章管理接口 (`src/api/sealManage.js`)
- 电子印章管理
- 印章权限控制
- 印章使用记录
- 印章安全验证

### 4.5 支付订单接口 (`src/api/payOrder.js`)
- 订单生成
- 支付处理
- 订单状态跟踪
- 退款处理

### 4.6 委托业务接口
**文件**: `entrustBulletin.js`, `entrustProject.js`
- 委托项目管理
- 委托公告发布
- 委托流程跟踪
- 委托结果确认

## 5. 接口调用规范

### 5.1 请求格式
```javascript
// GET请求
export function getList(query) {
  return request({
    url: '/api/list',
    method: 'get',
    params: query
  })
}

// POST请求
export function addData(data) {
  return request({
    url: '/api/add',
    method: 'post',
    data: data
  })
}
```

### 5.2 响应格式
```javascript
{
  "code": 200,          // 状态码
  "msg": "操作成功",     // 消息
  "data": {},           // 数据
  "total": 100          // 总数（分页时）
}
```

### 5.3 错误处理
- 统一错误码定义
- 错误信息国际化
- 错误日志记录
- 用户友好提示

### 5.4 安全机制
- Token认证
- 请求签名
- 参数加密
- 防重放攻击

## 6. 接口优化策略

### 6.1 性能优化
- 请求缓存机制
- 接口防抖处理
- 分页加载
- 懒加载策略

### 6.2 可靠性保障
- 请求重试机制
- 超时处理
- 降级策略
- 熔断保护

### 6.3 监控告警
- 接口调用统计
- 响应时间监控
- 错误率告警
- 性能瓶颈分析
