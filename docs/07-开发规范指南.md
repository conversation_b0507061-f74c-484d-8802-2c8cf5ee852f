# 开发规范指南

## 1. 项目结构规范

### 1.1 目录命名规范
- **文件夹命名**: 使用小写字母，多个单词用连字符分隔
  ```
  ✅ user-manage
  ✅ project-list
  ❌ UserManage
  ❌ projectList
  ```

- **组件文件命名**: 使用PascalCase命名
  ```
  ✅ UserProfile.vue
  ✅ ProjectList.vue
  ❌ userProfile.vue
  ❌ project-list.vue
  ```

- **工具文件命名**: 使用camelCase命名
  ```
  ✅ userUtils.js
  ✅ dateHelper.js
  ❌ user-utils.js
  ❌ DateHelper.js
  ```

### 1.2 文件组织规范
```
src/
├── api/                    # API接口，按业务模块分组
│   ├── user/              # 用户相关接口
│   ├── project/           # 项目相关接口
│   └── common.js          # 通用接口
├── components/            # 公共组件
│   ├── BaseButton/        # 基础按钮组件
│   │   ├── index.vue     # 组件主文件
│   │   ├── index.js      # 组件导出文件
│   │   └── README.md     # 组件说明文档
├── views/                 # 页面组件
│   ├── user/             # 用户管理页面
│   │   ├── index.vue     # 列表页面
│   │   ├── detail.vue    # 详情页面
│   │   └── components/   # 页面专用组件
└── utils/                # 工具函数
    ├── request.js        # HTTP请求封装
    ├── auth.js           # 认证相关工具
    └── validate.js       # 验证工具
```

## 2. 代码编写规范

### 2.1 Vue组件规范

#### 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 1. 导入依赖
import { mapGetters } from 'vuex'
import UserApi from '@/api/user'

// 2. 组件定义
export default {
  name: 'UserProfile',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['user'])
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

#### 组件命名规范
```javascript
// ✅ 正确的组件命名
export default {
  name: 'UserProfile',        // PascalCase
  components: {
    UserCard,                 // PascalCase
    ProjectList              // PascalCase
  }
}

// ❌ 错误的组件命名
export default {
  name: 'userProfile',        // 应该使用PascalCase
  components: {
    'user-card': UserCard,    // 应该使用PascalCase
    projectlist: ProjectList  // 应该使用PascalCase
  }
}
```

#### Props定义规范
```javascript
// ✅ 详细的props定义
props: {
  userId: {
    type: [String, Number],
    required: true,
    validator(value) {
      return value > 0
    }
  },
  userInfo: {
    type: Object,
    default: () => ({})
  },
  showActions: {
    type: Boolean,
    default: true
  }
}

// ❌ 简单的props定义（不推荐）
props: ['userId', 'userInfo', 'showActions']
```

### 2.2 JavaScript编写规范

#### 变量命名规范
```javascript
// ✅ 正确的变量命名
const userName = 'admin'              // camelCase
const USER_ROLES = ['admin', 'user']  // 常量使用UPPER_CASE
const isLoggedIn = true               // 布尔值使用is/has/can等前缀

// ❌ 错误的变量命名
const user_name = 'admin'             // 应该使用camelCase
const userRoles = ['admin', 'user']   // 常量应该使用UPPER_CASE
const loggedIn = true                 // 布尔值应该有明确前缀
```

#### 函数定义规范
```javascript
// ✅ 正确的函数定义
/**
 * 获取用户信息
 * @param {string|number} userId - 用户ID
 * @returns {Promise<Object>} 用户信息对象
 */
async function getUserInfo(userId) {
  try {
    const response = await UserApi.getInfo(userId)
    return response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

// 箭头函数用于简单操作
const formatDate = (date) => moment(date).format('YYYY-MM-DD')
```

#### 异步处理规范
```javascript
// ✅ 使用async/await
async handleSubmit() {
  this.loading = true
  try {
    await this.saveUserInfo()
    this.$message.success('保存成功')
  } catch (error) {
    this.$message.error('保存失败')
  } finally {
    this.loading = false
  }
}

// ❌ 避免使用Promise链（除非必要）
handleSubmit() {
  this.loading = true
  this.saveUserInfo()
    .then(() => {
      this.$message.success('保存成功')
    })
    .catch(() => {
      this.$message.error('保存失败')
    })
    .finally(() => {
      this.loading = false
    })
}
```

### 2.3 CSS/SCSS规范

#### 类名命名规范
```scss
// ✅ 使用BEM命名规范
.user-profile {
  &__header {
    padding: 20px;
  }
  
  &__content {
    margin-top: 10px;
  }
  
  &__button {
    &--primary {
      background-color: #409eff;
    }
    
    &--disabled {
      opacity: 0.5;
    }
  }
}

// ❌ 避免深层嵌套
.user-profile {
  .header {
    .title {
      .text {
        color: #333;  // 嵌套过深
      }
    }
  }
}
```

#### 样式组织规范
```scss
// 1. 变量定义
$primary-color: #409eff;
$border-radius: 4px;

// 2. 混入定义
@mixin button-style($bg-color) {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 8px 16px;
}

// 3. 组件样式
.user-profile {
  // 布局属性
  display: flex;
  flex-direction: column;
  
  // 尺寸属性
  width: 100%;
  height: auto;
  
  // 外观属性
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: $border-radius;
}
```

## 3. API接口规范

### 3.1 接口文件组织
```javascript
// src/api/user.js
import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserInfo(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(userId, data) {
  return request({
    url: `/system/user/${userId}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  })
}
```

### 3.2 接口调用规范
```javascript
// ✅ 在methods中调用API
methods: {
  async loadUserList() {
    this.loading = true
    try {
      const response = await getUserList(this.queryParams)
      this.userList = response.rows
      this.total = response.total
    } catch (error) {
      this.$message.error('加载用户列表失败')
    } finally {
      this.loading = false
    }
  }
}

// ❌ 避免在computed中调用API
computed: {
  userList() {
    // 不要在computed中调用API
    getUserList().then(response => {
      return response.data
    })
  }
}
```

## 4. 状态管理规范

### 4.1 Vuex模块规范
```javascript
// store/modules/user.js
const state = {
  userInfo: {},
  userList: [],
  loading: false
}

const mutations = {
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_USER_LIST: (state, userList) => {
    state.userList = userList
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading
  }
}

const actions = {
  // 获取用户信息
  async getUserInfo({ commit }, userId) {
    commit('SET_LOADING', true)
    try {
      const response = await getUserInfo(userId)
      commit('SET_USER_INFO', response.data)
      return response.data
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  userName: state => state.userInfo.name || '',
  isAdmin: state => state.userInfo.roles?.includes('admin') || false
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### 4.2 组件中使用Vuex
```javascript
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    ...mapState('user', ['userList', 'loading']),
    ...mapGetters('user', ['userName', 'isAdmin'])
  },
  methods: {
    ...mapActions('user', ['getUserInfo']),
    
    async handleGetUser(userId) {
      await this.getUserInfo(userId)
    }
  }
}
```

## 5. 错误处理规范

### 5.1 统一错误处理
```javascript
// utils/request.js
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(new Error(res.msg || 'Error'))
    }
    return res
  },
  error => {
    console.log('err' + error)
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)
```

### 5.2 组件错误处理
```javascript
// ✅ 正确的错误处理
async handleSubmit() {
  try {
    this.loading = true
    await this.saveData()
    this.$message.success('保存成功')
    this.resetForm()
  } catch (error) {
    // 记录错误日志
    console.error('保存失败:', error)
    // 显示用户友好的错误信息
    this.$message.error(error.message || '保存失败，请重试')
  } finally {
    this.loading = false
  }
}
```

## 6. 性能优化规范

### 6.1 组件优化
```javascript
// ✅ 使用函数式组件
export default {
  functional: true,
  props: ['item'],
  render(h, { props }) {
    return h('div', props.item.name)
  }
}

// ✅ 使用keep-alive缓存组件
<keep-alive>
  <router-view />
</keep-alive>

// ✅ 使用v-show代替v-if（频繁切换时）
<div v-show="isVisible">内容</div>
```

### 6.2 列表优化
```javascript
// ✅ 使用key优化列表渲染
<div v-for="item in list" :key="item.id">
  {{ item.name }}
</div>

// ❌ 避免使用index作为key
<div v-for="(item, index) in list" :key="index">
  {{ item.name }}
</div>
```

## 7. 测试规范

### 7.1 单元测试
```javascript
// tests/unit/components/UserProfile.spec.js
import { shallowMount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile.vue', () => {
  it('renders user name when passed', () => {
    const userName = 'John Doe'
    const wrapper = shallowMount(UserProfile, {
      propsData: { userName }
    })
    expect(wrapper.text()).toMatch(userName)
  })
})
```

## 8. 代码审查规范

### 8.1 审查清单
- [ ] 代码符合命名规范
- [ ] 组件结构清晰合理
- [ ] 错误处理完善
- [ ] 性能考虑充分
- [ ] 注释文档完整
- [ ] 测试覆盖充分
- [ ] 安全性考虑

### 8.2 提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(user): 添加用户头像上传功能
fix(api): 修复用户列表分页问题
docs(readme): 更新安装说明
style(css): 调整按钮样式
refactor(utils): 重构日期工具函数
test(user): 添加用户组件单元测试
```
