# 目录结构详解

## 根目录结构

```
epcos-v2/
├── babel.config.js         # Babel配置文件
├── bin/                    # 构建脚本目录
│   ├── build.bat          # Windows构建脚本
│   ├── package.bat        # Windows打包脚本
│   └── run-web.bat        # Windows运行脚本
├── build/                  # 构建相关文件
│   └── index.js           # 构建入口文件
├── dist/                   # 构建输出目录
├── jsconfig.json          # JavaScript项目配置
├── node_modules/          # 依赖包目录
├── package.json           # 项目配置和依赖
├── public/                # 静态资源目录
├── src/                   # 源代码目录
├── vue.config.js          # Vue CLI配置文件
├── README.md              # 项目说明文档
└── CHANGELOG.md           # 更新日志
```

## src目录详细结构

### 1. API接口层 (`src/api/`)

```
api/
├── aiReview.js            # AI审核相关接口
├── answerQuestions.js     # 答疑相关接口
├── approval.js            # 审批相关接口
├── commentSupplier.js     # 供应商评价接口
├── contractManage.js      # 合同管理接口
├── entrustBulletin.js     # 委托公告接口
├── entrustProject.js      # 委托项目接口
├── file.js                # 文件操作接口
├── home.js                # 首页相关接口
├── license.js             # 许可证相关接口
├── login.js               # 登录认证接口
├── menu.js                # 菜单相关接口
├── payOrder.js            # 支付订单接口
├── question.js            # 问题相关接口
├── sealManage.js          # 印章管理接口
├── sms.js                 # 短信相关接口
├── workplace.js           # 工作台接口
├── expert/                # 专家相关接口
│   └── reviewManage.js    # 评审管理
├── monitor/               # 监控相关接口
│   ├── job.js            # 定时任务
│   ├── jobLog.js         # 任务日志
│   └── online.js         # 在线用户
├── purchaser/             # 采购方接口
│   ├── bargaining.js      # 议价管理
│   ├── bidOpen.js         # 开标管理
│   ├── bulletin.js        # 公告管理
│   ├── expertExtract.js   # 专家抽取
│   ├── goodsAduit.js      # 商品审核
│   ├── meetingProject.js  # 会议项目
│   ├── meetingRoom.js     # 会议室管理
│   ├── monitorBidPerson.js # 投标人监控
│   ├── projectApproval.js # 项目审批
│   ├── projectArchive.js  # 项目归档
│   ├── projectFiles.js    # 项目文件
│   ├── projectInformation.js # 项目信息
│   ├── projectList.js     # 项目列表
│   ├── purchaseFile.js    # 采购文件
│   ├── purchaseFileNoClient.js # 无客户端采购文件
│   ├── registrationSupplier.js # 供应商注册
│   ├── researchReport.js  # 调研报告
│   ├── result.js          # 结果管理
│   ├── reviewExpert.js    # 评审专家
│   ├── reviewReport.js    # 评审报告
│   ├── shoppingMall.js    # 商城管理
│   ├── startReview.js     # 开始评审
│   └── supplierAudit.js   # 供应商审核
├── supplier/              # 供应商接口
│   ├── goodsManage.js     # 商品管理
│   ├── inviteBulletin.js  # 邀请公告
│   ├── responseFileNoClient.js # 无客户端响应文件
│   ├── supplierBidOpen.js # 供应商开标
│   ├── supplierInfo.js    # 供应商信息
│   ├── supplierProcess.js # 供应商流程
│   └── supplierProject.js # 供应商项目
└── system/                # 系统管理接口
    ├── approvalTemplate.js # 审批模板
    ├── bulletinSet.js     # 公告设置
    ├── config.js          # 系统配置
    ├── dept.js            # 部门管理
    ├── fileTemplate.js    # 文件模板
    ├── function.js        # 功能管理
    ├── logininfor.js      # 登录日志
    ├── menu.js            # 菜单管理
    ├── notice.js          # 通知公告
    ├── operlog.js         # 操作日志
    ├── organize.js        # 组织管理
    ├── post.js            # 岗位管理
    ├── processNode.js     # 流程节点
    ├── purchaseDict.js    # 采购字典
    ├── purchaseMethod.js  # 采购方式
    ├── purchaseTime.js    # 采购时间
    ├── quoteField.js      # 报价字段
    ├── role.js            # 角色管理
    ├── user.js            # 用户管理
    └── dict/              # 字典管理
        ├── data.js        # 字典数据
        └── type.js        # 字典类型
```

### 2. 静态资源 (`src/assets/`)

```
assets/
├── 401_images/            # 401错误页面图片
├── 404_images/            # 404错误页面图片
├── data/                  # 静态数据文件
├── icons/                 # SVG图标库
├── images/                # 通用图片资源
├── invoice/               # 发票相关图片
├── logo/                  # 各客户Logo
└── styles/                # 样式文件
    ├── element-variables.scss # Element UI主题变量
    ├── index.scss         # 全局样式入口
    └── ruoyi.scss         # 若依框架样式
```

### 3. 公共组件 (`src/components/`)

```
components/
├── Breadcrumb/            # 面包屑导航
├── ChatMessage/           # 聊天消息组件
├── CountDown/             # 倒计时组件
├── Crontab/               # Cron表达式组件
├── DictData/              # 字典数据组件
├── DictTag/               # 字典标签组件
├── Editor/                # 富文本编辑器
├── FileListView/          # 文件列表视图
├── FileUpload/            # 文件上传组件
├── FileUploadSelect/      # 文件上传选择
├── FileUploadSingle/      # 单文件上传
├── Hamburger/             # 汉堡菜单
├── HeaderSearch/          # 头部搜索
├── IconSelect/            # 图标选择器
├── ImagePreview/          # 图片预览
├── ImageUpload/           # 图片上传
├── ImageUploadSelect/     # 图片上传选择
├── ImgView/               # 图片查看器
├── ItemCard/              # 项目卡片
├── MsgNotify/             # 消息通知
├── NavTab/                # 导航标签
├── OverflowTooltip/       # 溢出提示
├── Pagination/            # 分页组件
├── PanThumb/              # 缩略图
├── ParentView/            # 父级视图
├── PdfView/               # PDF查看器
├── PdfViewDialog/         # PDF查看对话框
├── PwdGroup/              # 密码组件
├── RabbitMq/              # 消息队列组件
├── RightPanel/            # 右侧面板
├── RightToolbar/          # 右侧工具栏
├── RuoYi/                 # 若依相关组件
├── Screenfull/            # 全屏组件
├── SealDialog/            # 印章对话框
├── SealTip/               # 印章提示
├── SelectHeads/           # 选择负责人
├── SendSMS/               # 发送短信
├── SignatureSMS/          # 签名短信
├── SizeSelect/            # 尺寸选择
├── StepTab/               # 步骤标签
├── SvgIcon/               # SVG图标
├── TemplateSelect/        # 模板选择
├── ThemePicker/           # 主题选择器
├── Tinymce/               # TinyMCE编辑器
├── TipDialog/             # 提示对话框
├── TipProgress/           # 提示进度
├── TitleBar/              # 标题栏
├── TopNav/                # 顶部导航
├── UploadExcel/           # Excel上传
├── VideoPlayer/           # 视频播放器
├── VideoView/             # 视频查看器
├── canvasSign/            # 画布签名
└── iFrame/                # 内嵌框架
```

### 4. 自定义指令 (`src/directive/`)

```
directive/
├── dialog/                # 对话框指令
├── el-drag/               # 拖拽指令
├── function/              # 功能指令
├── module/                # 模块指令
├── permission/            # 权限指令
└── index.js               # 指令入口文件
```

### 5. 布局组件 (`src/layout/`)

```
layout/                    # 主布局
├── components/            # 布局组件
│   ├── AppMain.vue       # 主内容区
│   ├── InnerLink/        # 内部链接
│   ├── Navbar.vue        # 导航栏
│   ├── Settings/         # 设置面板
│   ├── Sidebar/          # 侧边栏
│   ├── TagsView/         # 标签视图
│   └── index.js          # 组件导出
├── index.vue             # 布局入口
└── mixin/                # 混入
    └── ResizeHandler.js  # 响应式处理

homeLayout/               # 首页布局
├── components/           # 首页组件
├── index.vue            # 首页布局入口
└── mixin/               # 首页混入

expertLayout/            # 专家布局
├── components/          # 专家组件
└── index.vue           # 专家布局入口

processLayout/           # 流程布局
├── components/          # 流程组件
└── index.vue           # 流程布局入口

supplierLayout/          # 供应商布局
├── components/          # 供应商组件
└── index.vue           # 供应商布局入口
```

### 6. 插件系统 (`src/plugins/`)

```
plugins/
├── auth.js              # 权限认证插件
├── cache.js             # 缓存管理插件
├── download.js          # 下载功能插件
├── modal.js             # 模态框插件
├── resumable.js         # 断点续传插件
├── tab.js               # 标签页插件
└── index.js             # 插件入口文件
```

### 7. 路由配置 (`src/router/`)

```
router/
└── index.js             # 路由配置文件
```

### 8. 状态管理 (`src/store/`)

```
store/
├── getters.js           # 全局getters
├── index.js             # store入口文件
└── modules/             # 模块化store
    ├── app.js           # 应用状态
    ├── dict.js          # 字典状态
    ├── expertProcess.js # 专家流程状态
    ├── permission.js    # 权限状态
    ├── process.js       # 流程状态
    ├── purchaseDict.js  # 采购字典状态
    ├── settings.js      # 设置状态
    ├── tagsView.js      # 标签视图状态
    ├── user.js          # 用户状态
    └── websocket.js     # WebSocket状态
```

### 9. 工具函数 (`src/utils/`)

```
utils/
├── auth.js              # 认证工具
├── bignumber.js         # 大数处理
├── components.js        # 组件工具
├── crypto-en.js         # 加密工具
├── errorCode.js         # 错误码定义
├── export2Excel.js     # Excel导出
├── filters.js           # 过滤器
├── index.js             # 通用工具函数
├── jsencrypt.js         # RSA加密
├── permission.js        # 权限工具
├── reg.js               # 正则表达式
├── request.js           # HTTP请求封装
├── ruoyi.js             # 若依工具函数
├── scroll-to.js         # 滚动工具
├── validate.js          # 验证工具
├── dict/                # 字典工具
│   ├── Dict.js          # 字典类
│   ├── DictConverter.js # 字典转换器
│   ├── DictData.js      # 字典数据
│   ├── DictMeta.js      # 字典元数据
│   ├── DictOptions.js   # 字典选项
│   └── index.js         # 字典工具入口
└── generator/           # 代码生成器
    ├── config.js        # 生成器配置
    ├── css.js           # CSS生成
    ├── drawingDefault.js # 默认绘制
    ├── html.js          # HTML生成
    ├── icon.json        # 图标配置
    ├── js.js            # JavaScript生成
    └── render.js        # 渲染器
```

### 10. 页面视图 (`src/views/`)

```
views/
├── dashboard/           # 仪表板
├── error/               # 错误页面
├── expert/              # 专家相关页面
├── monitor/             # 监控页面
├── purchaser/           # 采购方页面
├── supplier/            # 供应商页面
├── system/              # 系统管理页面
├── tool/                # 工具页面
├── wwwViews/            # 官网页面
├── index.vue            # 首页
├── login.vue            # 登录页
├── loginByPhone.vue     # 手机登录页
├── modifyPassword.vue   # 修改密码页
├── modifyPwd.vue        # 修改密码页
├── redirect.vue         # 重定向页
├── register.vue         # 注册页
├── sysLicTip.vue        # 系统许可提示
└── sysLicUp.vue         # 系统许可上传
```

## 公共资源目录 (`public/`)

```
public/
├── index.html           # HTML模板
├── robots.txt           # 爬虫协议
├── ckeditor/            # CKEditor编辑器
├── pdfjs-legacy/        # PDF.js库
├── tinymce/             # TinyMCE编辑器
├── html/                # 静态HTML文件
└── *.ico                # 各客户favicon图标
```

## 构建输出目录 (`dist/`)

```
dist/
├── index.html           # 构建后的HTML
├── static/              # 静态资源
├── ckeditor/            # CKEditor资源
├── pdfjs-legacy/        # PDF.js资源
├── tinymce/             # TinyMCE资源
└── *.ico                # 图标文件
```
