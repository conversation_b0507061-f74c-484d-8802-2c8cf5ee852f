# 核心业务模块详解

## 模块概览

易建采电子采购平台包含以下核心业务模块：

1. **用户认证与权限管理**
2. **采购方管理模块**
3. **供应商管理模块**
4. **专家管理模块**
5. **项目管理模块**
6. **公告管理模块**
7. **审批流程模块**
8. **合同管理模块**
9. **支付管理模块**
10. **系统管理模块**

## 1. 用户认证与权限管理

### 功能特性
- **多种登录方式**: 用户名密码登录、手机号验证码登录
- **安全认证**: RSA加密、JWT Token、验证码验证
- **权限控制**: 基于角色的权限管理（RBAC）
- **会话管理**: Token刷新、自动登出、在线用户监控

### 相关文件
```
src/api/login.js           # 登录认证API
src/views/login.vue        # 登录页面
src/views/loginByPhone.vue # 手机登录页面
src/views/register.vue     # 注册页面
src/utils/auth.js          # 认证工具
src/plugins/auth.js        # 权限插件
src/directive/permission/  # 权限指令
```

### 核心功能
- 用户登录/注册
- 密码找回/修改
- 权限验证
- 角色管理
- 菜单权限控制

## 2. 采购方管理模块

### 功能特性
- **项目管理**: 采购项目创建、编辑、审批、发布
- **供应商管理**: 供应商注册审核、资质管理、黑名单管理
- **专家管理**: 专家库维护、专家抽取、评审管理
- **开标管理**: 在线开标、唱标、评标
- **结果管理**: 中标结果确定、公示、异议处理

### 相关文件
```
src/views/purchaser/       # 采购方页面目录
├── applyProject/          # 申请项目
├── approvalCenter/        # 审批中心
├── assignProject/         # 分配项目
├── entrustBulletin/       # 委托公告
├── entrustProject/        # 委托项目
├── expertExtract/         # 专家抽取
├── expertManage/          # 专家管理
├── goodsAudit/            # 商品审核
├── meetingProject/        # 会议项目
├── meetingRoom/           # 会议室
├── monitorInfo/           # 监控信息
├── organizeUser/          # 组织用户
├── process/               # 流程管理
├── projectArchive/        # 项目归档
├── projectInformation/    # 项目信息
├── projectList/           # 项目列表
├── shoppingCart/          # 购物车
├── shoppingMall/          # 商城
└── supplierAudit/         # 供应商审核

src/api/purchaser/         # 采购方API目录
├── bargaining.js          # 议价管理
├── bidOpen.js             # 开标管理
├── bulletin.js            # 公告管理
├── expertExtract.js       # 专家抽取
├── goodsAduit.js          # 商品审核
├── meetingProject.js      # 会议项目
├── meetingRoom.js         # 会议室管理
├── monitorBidPerson.js    # 投标人监控
├── projectApproval.js     # 项目审批
├── projectArchive.js      # 项目归档
├── projectFiles.js        # 项目文件
├── projectInformation.js  # 项目信息
├── projectList.js         # 项目列表
├── purchaseFile.js        # 采购文件
├── registrationSupplier.js # 供应商注册
├── researchReport.js      # 调研报告
├── result.js              # 结果管理
├── reviewExpert.js        # 评审专家
├── reviewReport.js        # 评审报告
├── shoppingMall.js        # 商城管理
├── startReview.js         # 开始评审
└── supplierAudit.js       # 供应商审核
```

### 核心功能
- 采购需求管理
- 采购计划制定
- 供应商资质审核
- 专家库管理
- 开标评标流程
- 采购结果管理

## 3. 供应商管理模块

### 功能特性
- **企业信息管理**: 基本信息、资质证书、财务状况
- **商品管理**: 商品录入、价格管理、库存管理
- **项目参与**: 项目报名、文件下载、投标文件上传
- **合同履约**: 合同签订、履约管理、验收确认
- **评价管理**: 信用评价、履约评价、服务评价

### 相关文件
```
src/views/supplier/        # 供应商页面目录
├── goodsManage/           # 商品管理
├── inviteBulletin/        # 邀请公告
├── process/               # 流程管理
├── supplierInfo/          # 供应商信息
├── supplierProject/       # 供应商项目
└── supplierSeal/          # 供应商印章

src/api/supplier/          # 供应商API目录
├── goodsManage.js         # 商品管理
├── inviteBulletin.js      # 邀请公告
├── responseFileNoClient.js # 无客户端响应文件
├── supplierBidOpen.js     # 供应商开标
├── supplierInfo.js        # 供应商信息
├── supplierProcess.js     # 供应商流程
└── supplierProject.js     # 供应商项目

src/supplierLayout/        # 供应商专用布局
```

### 核心功能
- 供应商注册认证
- 企业资质管理
- 商品信息维护
- 项目投标管理
- 合同履约跟踪

## 4. 专家管理模块

### 功能特性
- **专家库管理**: 专家信息录入、分类管理、资质审核
- **专家抽取**: 随机抽取、回避管理、抽取记录
- **评审管理**: 在线评审、评分管理、评审报告
- **专家评价**: 专家履职评价、信用管理

### 相关文件
```
src/views/expert/          # 专家页面目录
├── process/               # 专家流程
└── reviewManage/          # 评审管理

src/api/expert/            # 专家API目录
└── reviewManage.js        # 评审管理

src/expertLayout/          # 专家专用布局
```

### 核心功能
- 专家信息管理
- 专家分类维护
- 随机抽取算法
- 在线评审系统
- 专家履职监督

## 5. 项目管理模块

### 功能特性
- **项目全生命周期管理**: 立项、审批、实施、验收、归档
- **文件管理**: 采购文件、投标文件、评审文件
- **时间节点控制**: 关键时间节点提醒、自动流转
- **状态跟踪**: 项目状态实时更新、进度监控

### 相关文件
```
src/views/purchaser/projectList/      # 项目列表
src/views/purchaser/projectInformation/ # 项目信息
src/views/purchaser/projectArchive/   # 项目归档
src/api/purchaser/projectList.js      # 项目列表API
src/api/purchaser/projectInformation.js # 项目信息API
src/api/purchaser/projectArchive.js   # 项目归档API
```

### 核心功能
- 项目立项申请
- 项目审批流程
- 项目实施监控
- 项目验收管理
- 项目归档存储

## 6. 公告管理模块

### 功能特性
- **公告发布**: 采购公告、更正公告、中标公告
- **公告审核**: 多级审核、内容校验
- **公告展示**: 官网展示、分类浏览、搜索功能
- **公告管理**: 公告修改、撤回、归档

### 相关文件
```
src/views/purchaser/entrustBulletin/  # 委托公告
src/views/wwwViews/homeBulletin/      # 首页公告
src/views/wwwViews/bulletinDetail/    # 公告详情
src/api/entrustBulletin.js            # 委托公告API
src/api/purchaser/bulletin.js         # 公告管理API
```

### 核心功能
- 公告模板管理
- 公告内容编辑
- 公告审核发布
- 公告展示浏览
- 公告统计分析

## 7. 审批流程模块

### 功能特性
- **流程配置**: 可视化流程设计、节点配置
- **审批管理**: 多级审批、并行审批、条件审批
- **流程监控**: 审批进度跟踪、超时提醒
- **流程优化**: 流程分析、效率统计

### 相关文件
```
src/views/purchaser/approvalCenter/   # 审批中心
src/views/purchaser/process/          # 流程管理
src/api/approval.js                   # 审批API
src/store/modules/process.js          # 流程状态管理
src/processLayout/                    # 流程专用布局
```

### 核心功能
- 流程模板设计
- 审批节点配置
- 审批任务分配
- 审批进度监控
- 审批历史查询

## 8. 合同管理模块

### 功能特性
- **合同生成**: 自动生成合同、合同模板管理
- **电子签章**: 在线签署、印章管理、签名验证
- **履约管理**: 履约进度跟踪、变更管理
- **合同归档**: 合同存储、检索、统计

### 相关文件
```
src/api/contractManage.js             # 合同管理API
src/api/sealManage.js                 # 印章管理API
src/components/SealDialog/            # 印章对话框
src/components/SealTip/               # 印章提示
src/components/canvasSign/            # 画布签名
```

### 核心功能
- 合同模板管理
- 合同在线签署
- 印章权限控制
- 履约监督管理
- 合同统计分析

## 9. 支付管理模块

### 功能特性
- **在线支付**: 多种支付方式、支付安全保障
- **订单管理**: 订单生成、状态跟踪、退款处理
- **财务管理**: 资金监管、对账管理、财务报表

### 相关文件
```
src/api/payOrder.js                   # 支付订单API
src/views/system/payOrder/            # 支付订单管理
src/views/purchaser/shoppingCart/     # 购物车
src/views/purchaser/shoppingMall/     # 商城
```

### 核心功能
- 支付接口集成
- 订单状态管理
- 资金安全监管
- 财务数据统计
- 退款流程处理

## 10. 系统管理模块

### 功能特性
- **用户管理**: 用户账号、角色权限、组织架构
- **系统配置**: 参数配置、字典管理、模板管理
- **日志管理**: 操作日志、登录日志、系统监控
- **数据管理**: 数据备份、数据清理、数据统计

### 相关文件
```
src/views/system/              # 系统管理页面目录
├── approvalTemplate/          # 审批模板
├── config/                    # 系统配置
├── dept/                      # 部门管理
├── dict/                      # 字典管理
├── fileTemplate/              # 文件模板
├── function/                  # 功能管理
├── license/                   # 许可证管理
├── logininfor/                # 登录日志
├── menu/                      # 菜单管理
├── notice/                    # 通知公告
├── operlog/                   # 操作日志
├── organize/                  # 组织管理
├── post/                      # 岗位管理
├── purchaseDict/              # 采购字典
├── purchaseMethod/            # 采购方式
├── quoteField/                # 报价字段
├── role/                      # 角色管理
└── user/                      # 用户管理

src/api/system/                # 系统管理API目录
```

### 核心功能
- 用户权限管理
- 系统参数配置
- 数据字典维护
- 操作日志审计
- 系统性能监控

## 11. 监控管理模块

### 功能特性
- **在线用户监控**: 实时在线用户、会话管理
- **定时任务管理**: 任务调度、执行监控、日志查看
- **系统性能监控**: 服务器状态、数据库性能

### 相关文件
```
src/views/monitor/             # 监控页面目录
├── job/                       # 定时任务
└── online/                    # 在线用户

src/api/monitor/               # 监控API目录
├── job.js                     # 定时任务
├── jobLog.js                  # 任务日志
└── online.js                  # 在线用户
```

### 核心功能
- 在线用户管理
- 定时任务调度
- 系统性能监控
- 异常告警处理
- 运维数据统计

## 12. 官网展示模块

### 功能特性
- **首页展示**: 平台介绍、功能特色、合作伙伴
- **公告公示**: 采购公告、中标公告、政策法规
- **帮助中心**: 操作指南、常见问题、联系方式

### 相关文件
```
src/views/wwwViews/            # 官网页面目录
├── aboutUs/                   # 关于我们
├── bulletinDetail/            # 公告详情
├── externalBulletin/          # 外部公告
├── home/                      # 首页
├── homeBulletin/              # 首页公告
├── homeEntrustBulletin/       # 首页委托公告
├── homeHelpCenter/            # 帮助中心
├── noticeAnnouncement/        # 通知公告
├── partners/                  # 合作伙伴
├── product/                   # 产品介绍
└── webHome/                   # 网站首页

src/homeLayout/                # 首页专用布局
```

### 核心功能
- 平台宣传展示
- 公告信息发布
- 用户帮助支持
- 政策法规展示
- 联系方式提供
