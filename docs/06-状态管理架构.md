# 状态管理架构详解

## Vuex状态管理概览

易建采平台使用Vuex进行全局状态管理，采用模块化设计，将不同业务领域的状态分离管理，提高代码的可维护性和可扩展性。

## 1. Store架构设计

### 1.1 入口文件 (`src/store/index.js`)
```javascript
import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'

// 自动导入modules目录下的所有模块
const modulesFiles = require.context('./modules', true, /\.js$/)
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules,
  getters
})

export default store
```

### 1.2 全局Getters (`src/store/getters.js`)
```javascript
const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  permission_routes: state => state.permission.routes,
  // ... 其他全局getters
}
export default getters
```

## 2. 核心状态模块

### 2.1 应用状态模块 (`src/store/modules/app.js`)

#### 状态定义
```javascript
const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium'
}
```

#### 主要功能
- **侧边栏状态管理**: 展开/收起状态持久化
- **设备类型检测**: 响应式布局适配
- **组件尺寸控制**: 全局组件大小设置
- **主题设置**: 主题色彩配置

#### 核心Actions
- `toggleSideBar()` - 切换侧边栏状态
- `closeSideBar()` - 关闭侧边栏
- `toggleDevice()` - 切换设备类型
- `setSize()` - 设置组件尺寸

### 2.2 用户状态模块 (`src/store/modules/user.js`)

#### 状态定义
```javascript
const state = {
  token: getToken(),
  name: '',
  avatar: '',
  roles: [],
  permissions: []
}
```

#### 主要功能
- **用户认证**: Token管理和验证
- **用户信息**: 基本信息存储和更新
- **角色权限**: 用户角色和权限管理
- **登录状态**: 登录/登出状态控制

#### 核心Actions
- `login()` - 用户登录
- `getInfo()` - 获取用户信息
- `logout()` - 用户登出
- `resetToken()` - 重置Token

### 2.3 权限状态模块 (`src/store/modules/permission.js`)

#### 状态定义
```javascript
const state = {
  routes: [],
  addRoutes: [],
  defaultRoutes: [],
  topbarRouters: [],
  sidebarRouters: []
}
```

#### 主要功能
- **动态路由**: 根据权限动态生成路由
- **菜单权限**: 侧边栏菜单权限控制
- **页面权限**: 页面访问权限验证
- **按钮权限**: 页面内按钮权限控制

#### 核心Actions
- `generateRoutes()` - 生成动态路由
- `setRoutes()` - 设置路由信息

### 2.4 标签视图模块 (`src/store/modules/tagsView.js`)

#### 状态定义
```javascript
const state = {
  visitedViews: [],
  cachedViews: []
}
```

#### 主要功能
- **访问历史**: 记录用户访问的页面
- **页面缓存**: 控制页面缓存策略
- **标签管理**: 标签页的增删改查
- **导航控制**: 标签页导航功能

#### 核心Actions
- `addView()` - 添加访问视图
- `addVisitedView()` - 添加访问记录
- `addCachedView()` - 添加缓存视图
- `delView()` - 删除视图
- `delAllViews()` - 删除所有视图
- `delOthersViews()` - 删除其他视图

### 2.5 设置状态模块 (`src/store/modules/settings.js`)

#### 状态定义
```javascript
const state = {
  title: '',
  theme: '#409EFF',
  sideTheme: 'theme-dark',
  showSettings: false,
  topNav: false,
  tagsView: true,
  fixedHeader: false,
  sidebarLogo: true,
  dynamicTitle: false
}
```

#### 主要功能
- **界面设置**: 主题色彩、布局配置
- **功能开关**: 各种功能模块的开关控制
- **个性化配置**: 用户个性化设置存储
- **系统标题**: 动态标题管理

## 3. 业务状态模块

### 3.1 字典状态模块 (`src/store/modules/dict.js`)

#### 主要功能
- **字典缓存**: 系统字典数据缓存管理
- **字典更新**: 字典数据的动态更新
- **字典查询**: 快速字典数据查询
- **字典转换**: 字典值与标签的转换

#### 核心Actions
- `loadDict()` - 加载字典数据
- `setDict()` - 设置字典数据
- `removeDict()` - 移除字典数据

### 3.2 采购字典模块 (`src/store/modules/purchaseDict.js`)

#### 主要功能
- **采购专用字典**: 采购业务相关字典管理
- **分类管理**: 采购分类字典
- **状态管理**: 采购状态字典
- **方式管理**: 采购方式字典

### 3.3 流程状态模块 (`src/store/modules/process.js`)

#### 主要功能
- **流程实例**: 当前流程实例状态
- **任务状态**: 流程任务执行状态
- **流程历史**: 流程执行历史记录
- **流程配置**: 流程配置信息管理

### 3.4 专家流程模块 (`src/store/modules/expertProcess.js`)

#### 主要功能
- **专家任务**: 专家评审任务管理
- **评审状态**: 评审进度状态跟踪
- **专家信息**: 当前专家相关信息
- **评审历史**: 专家评审历史记录

### 3.5 WebSocket模块 (`src/store/modules/websocket.js`)

#### 主要功能
- **连接管理**: WebSocket连接状态管理
- **消息处理**: 实时消息接收和处理
- **心跳检测**: 连接心跳检测机制
- **重连机制**: 断线重连处理

## 4. 状态管理最佳实践

### 4.1 模块化设计原则
- **单一职责**: 每个模块只负责特定的业务领域
- **松耦合**: 模块间依赖最小化
- **高内聚**: 相关功能集中在同一模块
- **可扩展**: 便于添加新的业务模块

### 4.2 状态更新规范
```javascript
// 同步更新使用mutations
const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  }
}

// 异步操作使用actions
const actions = {
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.token)
        setToken(data.token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  }
}
```

### 4.3 数据持久化策略
- **Token持久化**: 使用Cookie存储认证Token
- **用户设置**: 使用localStorage存储用户偏好
- **临时数据**: 使用sessionStorage存储会话数据
- **敏感数据**: 不进行本地存储，仅内存保存

### 4.4 性能优化策略
- **按需加载**: 模块懒加载，减少初始包大小
- **数据缓存**: 合理使用缓存，减少重复请求
- **状态清理**: 及时清理不需要的状态数据
- **计算属性**: 使用getters进行数据计算和过滤

## 5. 状态调试和监控

### 5.1 开发工具集成
- **Vue DevTools**: 状态变化可视化调试
- **Time Travel**: 状态变化时间旅行调试
- **State Inspector**: 状态结构检查器

### 5.2 日志记录
```javascript
// 开发环境启用严格模式和日志
const store = new Vuex.Store({
  strict: process.env.NODE_ENV !== 'production',
  plugins: process.env.NODE_ENV !== 'production' ? [createLogger()] : []
})
```

### 5.3 错误处理
- **状态异常捕获**: 捕获状态更新异常
- **回滚机制**: 异常时状态回滚
- **错误上报**: 状态错误自动上报
- **降级处理**: 状态异常时的降级策略

## 6. 状态管理扩展

### 6.1 插件系统
- **持久化插件**: 状态自动持久化
- **同步插件**: 多标签页状态同步
- **缓存插件**: 智能缓存管理
- **日志插件**: 状态变化日志记录

### 6.2 中间件支持
- **权限中间件**: 状态访问权限控制
- **验证中间件**: 状态数据验证
- **转换中间件**: 状态数据格式转换
- **监控中间件**: 状态变化监控统计

### 6.3 模块热替换
- **开发环境**: 支持模块热替换
- **状态保持**: 热替换时保持状态
- **错误恢复**: 热替换错误时自动恢复
