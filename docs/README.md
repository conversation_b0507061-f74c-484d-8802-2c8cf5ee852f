# 易建采电子采购平台 V2 - 项目文档

## 文档概览

本文档库包含了易建采电子采购平台第二版的完整技术文档，涵盖项目架构、开发规范、部署指南等各个方面。

## 文档目录

### 📋 [01-项目概览](./01-项目概览.md)
- 项目简介和技术栈
- 核心功能特性
- 多角色支持说明
- 项目结构概览
- 快速开始指南

### 📁 [02-目录结构详解](./02-目录结构详解.md)
- 根目录结构说明
- src目录详细分析
- API接口层组织
- 组件库结构
- 静态资源管理
- 构建输出目录

### 🏗️ [03-核心业务模块](./03-核心业务模块.md)
- 用户认证与权限管理
- 采购方管理模块
- 供应商管理模块
- 专家管理模块
- 项目管理模块
- 公告管理模块
- 审批流程模块
- 合同管理模块
- 支付管理模块
- 系统管理模块
- 监控管理模块
- 官网展示模块

### 🧩 [04-公共组件库](./04-公共组件库.md)
- 基础UI组件
- 表单组件
- 文件处理组件
- 数据展示组件
- 导航组件
- 反馈组件
- 业务组件
- 工具组件
- 编辑器组件
- 多媒体组件
- 时间组件
- 通信组件

### 🔌 [05-API接口架构](./05-API接口架构.md)
- 接口分层架构
- 核心业务接口模块
- 通用功能接口
- 特殊业务接口
- 接口调用规范
- 安全机制
- 性能优化策略

### 🗃️ [06-状态管理架构](./06-状态管理架构.md)
- Vuex状态管理概览
- Store架构设计
- 核心状态模块
- 业务状态模块
- 状态管理最佳实践
- 状态调试和监控
- 状态管理扩展

### 📝 [07-开发规范指南](./07-开发规范指南.md)
- 项目结构规范
- 代码编写规范
- Vue组件规范
- JavaScript编写规范
- CSS/SCSS规范
- API接口规范
- 状态管理规范
- 错误处理规范
- 性能优化规范
- 测试规范
- 代码审查规范

### 🚀 [08-部署配置指南](./08-部署配置指南.md)
- 环境配置概览
- 构建配置
- 环境变量配置
- 构建脚本
- Docker部署
- CI/CD配置
- 性能优化配置
- 监控和日志
- 安全配置

## 文档使用说明

### 阅读顺序建议

1. **新手入门**: 建议按照文档编号顺序阅读，从项目概览开始
2. **开发人员**: 重点关注03-07章节，了解业务模块和开发规范
3. **运维人员**: 重点关注08章节的部署配置指南
4. **架构师**: 重点关注05-06章节的架构设计

### 文档维护

- 📅 **更新频率**: 随项目版本更新
- 👥 **维护人员**: 项目开发团队
- 📧 **反馈渠道**: 通过项目Issue或邮件反馈

## 项目信息

- **项目名称**: 易建采电子采购平台 V2
- **当前版本**: 3.4.0
- **技术栈**: Vue.js 2.x + Element UI + Vuex + Vue Router
- **仓库地址**: **********************:web/epcos-v2.git

## 快速链接

### 开发相关
- [Vue.js 官方文档](https://cn.vuejs.org/)
- [Element UI 组件库](https://element.eleme.cn/)
- [Vuex 状态管理](https://vuex.vuejs.org/zh/)
- [Vue Router 路由](https://router.vuejs.org/zh/)

### 工具链
- [Vue CLI 脚手架](https://cli.vuejs.org/zh/)
- [ESLint 代码检查](https://eslint.org/)
- [Sass 预处理器](https://sass-lang.com/)
- [Axios HTTP客户端](https://axios-http.com/)

### 第三方库
- [ECharts 图表库](https://echarts.apache.org/zh/index.html)
- [Moment.js 日期处理](https://momentjs.com/)
- [Lodash 工具库](https://lodash.com/)
- [Quill 富文本编辑器](https://quilljs.com/)

## 版本历史

### v3.4.0 (当前版本)
- 完善多客户环境支持
- 优化组件库结构
- 增强安全性配置
- 改进部署流程

### v3.3.x
- 新增AI审核功能
- 优化用户体验
- 修复已知问题

### v3.2.x
- 增加专家管理模块
- 完善审批流程
- 性能优化

### v3.1.x
- 基础功能完善
- 多角色支持
- 初始版本发布

## 贡献指南

### 文档贡献
1. Fork项目仓库
2. 创建文档分支
3. 编写或修改文档
4. 提交Pull Request
5. 等待审核合并

### 文档规范
- 使用Markdown格式
- 保持结构清晰
- 添加适当的代码示例
- 包含必要的图表说明

## 联系方式

- **技术支持**: <EMAIL>
- **项目经理**: <EMAIL>
- **开发团队**: <EMAIL>

---

**注意**: 本文档库包含项目的详细技术信息，请妥善保管，避免泄露给无关人员。
