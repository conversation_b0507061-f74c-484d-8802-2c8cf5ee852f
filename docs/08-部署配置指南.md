# 部署配置指南

## 1. 环境配置概览

易建采平台支持多环境部署，通过不同的环境配置文件和构建命令来适配各种部署场景。

### 1.1 支持的环境类型
- **开发环境** (development) - 本地开发调试
- **测试环境** (staging) - 测试验证环境
- **生产环境** (production) - 正式生产环境
- **客户定制环境** - 针对不同客户的定制化部署

### 1.2 客户定制环境列表
```bash
# 各客户环境构建命令
npm run build:jxzl        # 建信租赁
npm run build:syth        # 三一重工
npm run build:whpr        # 武汉普瑞
npm run build:bjxk        # 北京新科
npm run build:xyzy        # 新余钢铁
npm run build:fjnx        # 福建农信
npm run build:whws        # 武汉物数
npm run build:wzlg        # 梧州两广
```

## 2. 构建配置

### 2.1 Vue CLI配置 (`vue.config.js`)

#### 基础配置
```javascript
module.exports = {
  // 部署路径配置
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  
  // 输出目录
  outputDir: 'dist',
  
  // 静态资源目录
  assetsDir: 'static',
  
  // 生产环境source map
  productionSourceMap: false,
  
  // 开发服务器配置
  devServer: {
    host: '0.0.0.0',
    port: 80,
    open: true,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: `http://smart.epc1688.com/epcos`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    }
  }
}
```

#### Webpack优化配置
```javascript
configureWebpack: {
  name: name,
  resolve: {
    alias: {
      '@': resolve('src')
    }
  },
  plugins: [
    // Gzip压缩
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8
    })
  ]
}
```

### 2.2 环境变量配置

#### 开发环境 (`.env.development`)
```bash
# 开发环境配置
NODE_ENV = development

# 接口地址
VUE_APP_BASE_API = '/dev-api'

# 应用标题
VUE_APP_TITLE = 易建采电子采购平台

# 版本类型
VUE_APP_VERSION_TYPE = 'default'
```

#### 生产环境 (`.env.production`)
```bash
# 生产环境配置
NODE_ENV = production

# 接口地址
VUE_APP_BASE_API = '/prod-api'

# 应用标题
VUE_APP_TITLE = 易建采电子采购平台

# 版本类型
VUE_APP_VERSION_TYPE = 'default'
```

#### 客户定制环境示例 (`.env.jxzl`)
```bash
# 建信租赁环境配置
NODE_ENV = production

# 接口地址
VUE_APP_BASE_API = '/prod-api'

# 应用标题
VUE_APP_TITLE = 建信租赁采购平台

# 版本类型
VUE_APP_VERSION_TYPE = 'jxzl'

# 客户特定配置
VUE_APP_CUSTOMER = 'jxzl'
VUE_APP_THEME_COLOR = '#1890ff'
```

## 3. 构建脚本

### 3.1 Package.json构建命令
```json
{
  "scripts": {
    "dev": "vue-cli-service serve",
    "build:prod": "vue-cli-service build",
    "build:stage": "vue-cli-service build --mode staging",
    "build:jxzl": "vue-cli-service build --mode jxzl",
    "build:jxzlStaging": "vue-cli-service build --mode jxzlStaging",
    "build:syth": "vue-cli-service build --mode syth",
    "build:sythStaging": "vue-cli-service build --mode sythStaging",
    "preview": "node build/index.js --preview",
    "lint": "eslint --ext .js,.vue src"
  }
}
```

### 3.2 Windows批处理脚本

#### 构建脚本 (`bin/build.bat`)
```batch
@echo off
echo 开始构建项目...

REM 安装依赖
call npm install

REM 构建生产版本
call npm run build:prod

echo 构建完成！
pause
```

#### 打包脚本 (`bin/package.bat`)
```batch
@echo off
echo 开始打包项目...

REM 创建打包目录
if not exist "package" mkdir package

REM 复制构建文件
xcopy /E /I /Y dist package\dist\

REM 复制配置文件
copy vue.config.js package\
copy package.json package\

echo 打包完成！
pause
```

#### 运行脚本 (`bin/run-web.bat`)
```batch
@echo off
echo 启动开发服务器...

REM 安装依赖（如果需要）
if not exist "node_modules" (
    echo 安装依赖...
    call npm install
)

REM 启动开发服务器
call npm run dev

pause
```

## 4. Docker部署

### 4.1 Dockerfile
```dockerfile
# 构建阶段
FROM node:14-alpine as build-stage

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:prod

# 生产阶段
FROM nginx:stable-alpine as production-stage

# 复制构建文件
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 4.2 Nginx配置 (`nginx.conf`)
```nginx
user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html index.htm;

        # 处理Vue Router的history模式
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API代理
        location /prod-api/ {
            proxy_pass http://backend-server/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
}
```

### 4.3 Docker Compose配置
```yaml
version: '3.8'

services:
  epcos-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    networks:
      - epcos-network

networks:
  epcos-network:
    driver: bridge
```

## 5. CI/CD配置

### 5.1 GitLab CI配置 (`.gitlab-ci.yml`)
```yaml
stages:
  - build
  - test
  - deploy

variables:
  NODE_VERSION: "14"

# 构建阶段
build:
  stage: build
  image: node:${NODE_VERSION}-alpine
  script:
    - npm ci --cache .npm --prefer-offline
    - npm run build:prod
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  cache:
    paths:
      - .npm/
      - node_modules/

# 测试阶段
test:
  stage: test
  image: node:${NODE_VERSION}-alpine
  script:
    - npm ci --cache .npm --prefer-offline
    - npm run lint
    - npm run test:unit
  cache:
    paths:
      - .npm/
      - node_modules/

# 部署到测试环境
deploy:staging:
  stage: deploy
  script:
    - echo "部署到测试环境"
    - rsync -avz --delete dist/ user@staging-server:/var/www/html/
  only:
    - develop

# 部署到生产环境
deploy:production:
  stage: deploy
  script:
    - echo "部署到生产环境"
    - rsync -avz --delete dist/ user@prod-server:/var/www/html/
  only:
    - master
  when: manual
```

## 6. 性能优化配置

### 6.1 代码分割配置
```javascript
// vue.config.js
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          elementUI: {
            name: 'chunk-elementUI',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true
          }
        }
      }
    }
  }
}
```

### 6.2 预加载配置
```javascript
// vue.config.js
module.exports = {
  chainWebpack: config => {
    // 预加载配置
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // 预获取配置
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/chunk-vendors\..*\.js$/)
      return options
    })
  }
}
```

## 7. 监控和日志

### 7.1 错误监控配置
```javascript
// main.js
import * as Sentry from '@sentry/vue'

if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    Vue,
    dsn: process.env.VUE_APP_SENTRY_DSN,
    environment: process.env.NODE_ENV
  })
}
```

### 7.2 性能监控
```javascript
// utils/performance.js
export function reportPerformance() {
  if ('performance' in window) {
    const timing = performance.timing
    const loadTime = timing.loadEventEnd - timing.navigationStart
    
    // 上报性能数据
    console.log('页面加载时间:', loadTime)
  }
}
```

## 8. 安全配置

### 8.1 CSP配置
```html
<!-- public/index.html -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:;">
```

### 8.2 环境变量安全
```bash
# 敏感信息使用环境变量
VUE_APP_API_KEY=your-api-key
VUE_APP_SENTRY_DSN=your-sentry-dsn

# 不要在代码中硬编码敏感信息
```
