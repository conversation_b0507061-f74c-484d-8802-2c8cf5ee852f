# 公共组件库详解

## 组件分类

易建采平台的公共组件库按功能分为以下几个类别：

1. **基础UI组件** - 基础界面元素
2. **表单组件** - 表单输入和验证
3. **文件处理组件** - 文件上传、预览、下载
4. **数据展示组件** - 数据表格、图表、列表
5. **导航组件** - 菜单、面包屑、标签页
6. **反馈组件** - 消息提示、对话框、进度条
7. **业务组件** - 特定业务场景组件
8. **工具组件** - 辅助功能组件

## 1. 基础UI组件

### Breadcrumb - 面包屑导航
**位置**: `src/components/Breadcrumb/`
**功能**: 显示当前页面在系统中的位置层级
**使用场景**: 页面导航、位置指示

```vue
<breadcrumb />
```

### Hamburger - 汉堡菜单
**位置**: `src/components/Hamburger/`
**功能**: 侧边栏折叠/展开控制按钮
**使用场景**: 响应式布局、空间优化

### SvgIcon - SVG图标
**位置**: `src/components/SvgIcon/`
**功能**: SVG图标组件，支持动态颜色和大小
**使用场景**: 界面图标显示

```vue
<svg-icon icon-class="user" />
```

### ThemePicker - 主题选择器
**位置**: `src/components/ThemePicker/`
**功能**: 动态切换系统主题色
**使用场景**: 个性化设置

### SizeSelect - 尺寸选择
**位置**: `src/components/SizeSelect/`
**功能**: 全局组件尺寸切换
**使用场景**: 界面尺寸调整

## 2. 表单组件

### DictTag - 字典标签
**位置**: `src/components/DictTag/`
**功能**: 根据字典值显示对应的标签
**使用场景**: 状态显示、分类标签

```vue
<dict-tag :options="statusOptions" :value="status" />
```

### DictData - 字典数据
**位置**: `src/components/DictData/`
**功能**: 字典数据管理和转换
**使用场景**: 下拉选项、数据转换

### IconSelect - 图标选择器
**位置**: `src/components/IconSelect/`
**功能**: 图标选择组件，支持搜索和分类
**使用场景**: 菜单图标选择、自定义图标

### TemplateSelect - 模板选择
**位置**: `src/components/TemplateSelect/`
**功能**: 模板选择组件
**使用场景**: 文档模板选择、邮件模板

### SelectHeads - 选择负责人
**位置**: `src/components/SelectHeads/`
**功能**: 人员选择组件
**使用场景**: 任务分配、审批人选择

### PwdGroup - 密码组件
**位置**: `src/components/PwdGroup/`
**功能**: 密码输入和强度验证
**使用场景**: 密码设置、安全验证

## 3. 文件处理组件

### FileUpload - 文件上传
**位置**: `src/components/FileUpload/`
**功能**: 多文件上传，支持拖拽、进度显示
**使用场景**: 文档上传、附件管理

```vue
<file-upload 
  :file-list="fileList" 
  :limit="5"
  @success="handleUploadSuccess" 
/>
```

### FileUploadSingle - 单文件上传
**位置**: `src/components/FileUploadSingle/`
**功能**: 单文件上传组件
**使用场景**: 头像上传、单个文档

### FileUploadSelect - 文件上传选择
**位置**: `src/components/FileUploadSelect/`
**功能**: 文件上传和选择组合组件
**使用场景**: 文件库选择、历史文件复用

### ImageUpload - 图片上传
**位置**: `src/components/ImageUpload/`
**功能**: 图片上传，支持预览、裁剪
**使用场景**: 图片管理、头像设置

### ImageUploadSelect - 图片上传选择
**位置**: `src/components/ImageUploadSelect/`
**功能**: 图片上传和选择组合
**使用场景**: 图片库管理

### FileListView - 文件列表视图
**位置**: `src/components/FileListView/`
**功能**: 文件列表展示和操作
**使用场景**: 文件管理、附件查看

### PdfView - PDF查看器
**位置**: `src/components/PdfView/`
**功能**: PDF文件在线预览
**使用场景**: 文档预览、合同查看

### PdfViewDialog - PDF查看对话框
**位置**: `src/components/PdfViewDialog/`
**功能**: 弹窗式PDF预览
**使用场景**: 快速预览、不离开当前页面

### ImgView - 图片查看器
**位置**: `src/components/ImgView/`
**功能**: 图片预览和缩放
**使用场景**: 图片查看、证书预览

### ImagePreview - 图片预览
**位置**: `src/components/ImagePreview/`
**功能**: 图片预览组件
**使用场景**: 图片展示、相册功能

## 4. 数据展示组件

### Pagination - 分页组件
**位置**: `src/components/Pagination/`
**功能**: 数据分页显示和跳转
**使用场景**: 列表分页、数据浏览

```vue
<pagination 
  :total="total" 
  :page.sync="queryParams.pageNum"
  :limit.sync="queryParams.pageSize"
  @pagination="getList" 
/>
```

### RightToolbar - 右侧工具栏
**位置**: `src/components/RightToolbar/`
**功能**: 表格操作工具栏
**使用场景**: 数据表格、列表操作

### OverflowTooltip - 溢出提示
**位置**: `src/components/OverflowTooltip/`
**功能**: 文本溢出时显示完整内容
**使用场景**: 表格单元格、长文本显示

### ItemCard - 项目卡片
**位置**: `src/components/ItemCard/`
**功能**: 项目信息卡片展示
**使用场景**: 项目列表、卡片布局

## 5. 导航组件

### TopNav - 顶部导航
**位置**: `src/components/TopNav/`
**功能**: 顶部导航栏
**使用场景**: 主导航、用户信息

### HeaderSearch - 头部搜索
**位置**: `src/components/HeaderSearch/`
**功能**: 全局搜索功能
**使用场景**: 快速搜索、菜单查找

### NavTab - 导航标签
**位置**: `src/components/NavTab/`
**功能**: 标签页导航
**使用场景**: 多页面切换、工作区管理

### StepTab - 步骤标签
**位置**: `src/components/StepTab/`
**功能**: 步骤导航组件
**使用场景**: 流程指引、分步操作

## 6. 反馈组件

### MsgNotify - 消息通知
**位置**: `src/components/MsgNotify/`
**功能**: 消息通知组件
**使用场景**: 系统通知、操作反馈

### TipDialog - 提示对话框
**位置**: `src/components/TipDialog/`
**功能**: 自定义提示对话框
**使用场景**: 确认操作、信息提示

### TipProgress - 提示进度
**位置**: `src/components/TipProgress/`
**功能**: 进度提示组件
**使用场景**: 操作进度、加载状态

### CountDown - 倒计时
**位置**: `src/components/CountDown/`
**功能**: 倒计时组件
**使用场景**: 验证码倒计时、活动倒计时

## 7. 业务组件

### SealDialog - 印章对话框
**位置**: `src/components/SealDialog/`
**功能**: 电子印章操作对话框
**使用场景**: 合同签署、文档盖章

### SealTip - 印章提示
**位置**: `src/components/SealTip/`
**功能**: 印章操作提示
**使用场景**: 印章使用指引

### canvasSign - 画布签名
**位置**: `src/components/canvasSign/`
**功能**: 手写签名组件
**使用场景**: 电子签名、手写确认

### SendSMS - 发送短信
**位置**: `src/components/SendSMS/`
**功能**: 短信发送组件
**使用场景**: 验证码发送、通知短信

### SignatureSMS - 签名短信
**位置**: `src/components/SignatureSMS/`
**功能**: 签名短信组件
**使用场景**: 重要通知、身份验证

### ChatMessage - 聊天消息
**位置**: `src/components/ChatMessage/`
**功能**: 聊天消息组件
**使用场景**: 在线沟通、消息展示

## 8. 工具组件

### Screenfull - 全屏组件
**位置**: `src/components/Screenfull/`
**功能**: 全屏切换功能
**使用场景**: 全屏查看、演示模式

### RightPanel - 右侧面板
**位置**: `src/components/RightPanel/`
**功能**: 右侧滑出面板
**使用场景**: 设置面板、详情查看

### PanThumb - 缩略图
**位置**: `src/components/PanThumb/`
**功能**: 图片缩略图组件
**使用场景**: 头像显示、图片预览

### ParentView - 父级视图
**位置**: `src/components/ParentView/`
**功能**: 路由父级视图组件
**使用场景**: 路由嵌套、布局组织

### iFrame - 内嵌框架
**位置**: `src/components/iFrame/`
**功能**: 内嵌页面组件
**使用场景**: 第三方页面集成、外部链接

### TitleBar - 标题栏
**位置**: `src/components/TitleBar/`
**功能**: 页面标题栏组件
**使用场景**: 页面标题、操作按钮

## 9. 编辑器组件

### Editor - 富文本编辑器
**位置**: `src/components/Editor/`
**功能**: 富文本编辑功能
**使用场景**: 内容编辑、公告发布

### Tinymce - TinyMCE编辑器
**位置**: `src/components/Tinymce/`
**功能**: 高级富文本编辑器
**使用场景**: 复杂文档编辑、模板编辑

### UploadExcel - Excel上传
**位置**: `src/components/UploadExcel/`
**功能**: Excel文件上传和解析
**使用场景**: 数据导入、批量操作

## 10. 多媒体组件

### VideoPlayer - 视频播放器
**位置**: `src/components/VideoPlayer/`
**功能**: 视频播放组件
**使用场景**: 培训视频、宣传片播放

### VideoView - 视频查看器
**位置**: `src/components/VideoView/`
**功能**: 视频查看组件
**使用场景**: 视频预览、快速查看

## 11. 时间组件

### Crontab - Cron表达式
**位置**: `src/components/Crontab/`
**功能**: Cron表达式可视化编辑
**使用场景**: 定时任务配置、调度设置

## 12. 通信组件

### RabbitMq - 消息队列
**位置**: `src/components/RabbitMq/`
**功能**: RabbitMQ消息处理
**使用场景**: 实时消息、异步通信
