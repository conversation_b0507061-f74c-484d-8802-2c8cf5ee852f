{"name": "epcos-v2", "version": "3.4.0", "description": "易建采电子采购平台", "author": "epcos-v2", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:jxzlStaging": "vue-cli-service build --mode jxzlStaging", "build:jxzl": "vue-cli-service build --mode jxzl", "build:jxzlIp": "vue-cli-service build --mode jxzlIp", "build:sythStaging": "vue-cli-service build --mode sythStaging", "build:syth": "vue-cli-service build --mode syth", "build:whprStaging": "vue-cli-service build --mode whprStaging", "build:whpr": "vue-cli-service build --mode whpr", "build:bjxkStaging": "vue-cli-service build --mode bjxkStaging", "build:bjxk": "vue-cli-service build --mode bjxk", "build:bjxkIp": "vue-cli-service build --mode bjxkIp", "build:wzlgStaging": "vue-cli-service build --mode wzlgStaging", "build:wzlg": "vue-cli-service build --mode wzlg", "build:xyzyStaging": "vue-cli-service build --mode xyzyStaging", "build:xyzy": "vue-cli-service build --mode xyzy", "build:xyzyIp": "vue-cli-service build --mode xyzyIp", "build:fjnxStaging": "vue-cli-service build --mode fjnxStaging", "build:fjnx": "vue-cli-service build --mode fjnx", "build:whwsStaging": "vue-cli-service build --mode whwsStaging", "build:whws": "vue-cli-service build --mode whws", "build:whwsIp": "vue-cli-service build --mode whwsIp", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "clone **********************:web/epcos-v2.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "animate.css": "^4.1.1", "axios": "0.28.1", "bignumber.js": "^9.0.1", "clipboard": "2.0.8", "core-js": "3.37.1", "crypto-js": "^4.1.1", "echarts": "^5.4.2", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-base64": "^3.7.2", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.21", "moment": "^2.29.1", "nprogress": "0.2.0", "pako": "^1.0.11", "quill": "1.3.7", "screenfull": "5.0.2", "sockjs-client": "^1.5.1", "stompjs": "^2.3.3", "umy-ui": "^1.1.7", "uuid": "^8.3.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-esign": "^1.1.4", "vue-meta": "2.4.0", "vue-particles": "^1.0.9", "vue-router": "3.4.9", "vue-video-player": "^5.0.2", "vuedraggable": "2.24.3", "vuex": "3.6.0", "watermark-dom": "^2.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}