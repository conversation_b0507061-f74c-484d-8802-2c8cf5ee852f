<template>
  <div class="app-container">
    <el-card
      header="系统授权认证"
      shadow="never"
      :body-style="{'padding': '15px 10px 0px 10px'}"
      style="max-width: 600px;margin: 0 auto"
    >
      <div slot="header" class="clearfix">
        <span>系统授权认证</span>
        <span class="fr text-danger">系统到期时间：{{ customerExpireDate }}</span>
      </div>
      <el-form
        ref="form"
        :model="form"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item label="系统验证码" prop="sysPwd">
          <el-input
            v-model.trim="form.sysPwd"
            placeholder="请输入"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="认证文件：" prop="fileList">
          <file-upload-select
            v-model="form.fileList"
            :fileSize="1"
            :limit="1"
            :fileType="['lic']"
            @change="uploadFile"
            @remove="removeFile"
          >
          </file-upload-select>
        </el-form-item>
        <el-form-item label="系统识别码" prop="sysCode">
          <el-input
            v-model.trim="form.sysCode"
            placeholder="请输入"
            disabled
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="showLicTip" label="登录提醒">
          <el-switch
            v-model="form.showLicTip"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </el-form-item>

        <div
          class="text-center mt30 mb30"
          v-if="checkPermi(['system:licUp:up'])"
        >
          <el-button type="primary" @click="submitForm">
            提交
          </el-button>
        </div>
      </el-form>
    </el-card>

  </div>
</template>

<script>

import { upLic } from '@/api/license'
import { mapGetters } from 'vuex'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'LicUp',
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sysPwd: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        fileList: [
          { required: true, trigger: 'change', message: '请上传' }
        ],
        showLicTip: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters([
      'sysCode',
      'customerExpireDate'
    ])
  },
  created() {
    this.reset();
  },
  methods: {
    checkPermi,
    removeFile(data) {
      console.log(data);
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
      this.form.licFile = fileList.length === 0 ? null : fileList[0].file;
    },
    // 表单重置
    reset() {
      this.form = {
        sysPwd: null,
        licFile: null,
        fileList: [],
        showLicTip: false,
        sysCode: this.sysCode
      };
      this.resetForm('form');
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await upLic(this.form);
            this.$message.success('提交成功');
            this.$modal.closeLoading()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }
      });
    }
  }
};
</script>
