<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50" fixed/>
      <el-table-column label="英文名称" prop="value" align="center" width="160" fixed>
        <template v-slot:default="{row}">
          <div class="text-danger">{{ row.value }}</div>
          <div >{{ row.raw.remark ? (row.label + ' - ' + row.raw.remark) : row.label}}</div>
        </template>
      </el-table-column>
      <template v-for="(item,index) in purchase_method">
        <el-table-column :label="item.label" align="center" :key="index">
          <template v-slot:default="{row}">
            <el-button
              v-if="dictFormat(row, item)"
              size="mini"
              type="text"
              @click="handleUpdate(row, item)"
              v-hasPermi="['system:purchaseDict:edit']"
            >
              {{ dictFormat(row, item) }}
            </el-button>
            <el-button
              v-if="!dictFormat(row, item)"
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(row, item)"
              v-hasPermi="['system:purchaseDict:add']"
            >
              添加
            </el-button>
            <el-button
              v-if="dictFormat(row, item)"
              class="btn-text-danger"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row, item)"
              v-hasPermi="['system:purchaseDict:remove']"
            >
            </el-button>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getData"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW500"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购方式" prop="purchaseMethodCode">
          <el-select v-model="form.purchaseMethodCode" placeholder="采购方式" clearable class="block" disabled>
            <el-option
              v-for="dict in purchase_method"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="英文名称" prop="keyName">
          <el-input v-model.trim="form.keyName" maxlength="100" placeholder="请输入英文名称" disabled/>
        </el-form-item>
        <el-form-item label="中文名称" prop="valueName">
          <el-input v-model.trim="form.valueName" maxlength="100" placeholder="请输入中文名称"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="open = false;">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDictionary, addDictionary, updateDictionary, delDictionary, getDictionary } from '@/api/system/purchaseDict';
import { listMethod } from '@/api/system/purchaseMethod';

export default {
  name: 'PurchaseDict',
  dicts: ['sys_purchase_dict'],
  data() {
    return {
      purchase_method: [],
      loading: false,
      tableData: [],
      dictList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseMethodCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        keyName: [
          { required: true, message: '英文名称不能为空', trigger: 'blur' }
        ],
        valueName: [
          { required: true, message: '中文名称不能为空', trigger: 'blur' }
        ]
      },
      allTableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10
    }
  },
  created() {
    this.getList();
  },
  methods: {
    dictFormat(row, item) {
      let obj = this.dictList.find(v => row.value === v.keyName && item.value === v.purchaseMethodCode);
      return obj ? obj.valueName : null
    },
    // 表单重置
    reset() {
      this.form = {
        purchasePersonalizedDictionaryId: null,
        purchaseMethodCode: null,
        keyName: null,
        valueName: null
      };
      this.resetForm('form');
    },
    /** 新增按钮操作 */
    handleAdd(row, item) {
      this.reset();
      this.form.keyName = row.value;
      this.form.purchaseMethodCode = item.value;
      this.open = true;
      this.title = '添加';
    },
    /** 修改按钮操作 */
    handleUpdate(row, item) {
      let obj = this.dictList.find(v => row.value === v.keyName && item.value === v.purchaseMethodCode);
      getDictionary(obj.purchasePersonalizedDictionaryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = '修改';
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, item) {
      let obj = this.dictList.find(v => row.value === v.keyName && item.value === v.purchaseMethodCode);
      this.$modal.confirm('是否确认删除该项？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delDictionary(obj.purchasePersonalizedDictionaryId);
          await this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch((e) => {
        console.log(e)
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (this.form.purchasePersonalizedDictionaryId) {
              await updateDictionary(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addDictionary(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        let res = await Promise.all([this.getDicts('sys_purchase_dict'), listDictionary({ pageNum: 1 }), listMethod()]);
        this.allTableData = this.initDict(res[0].data || []);
        this.dictList = res[1].rows || [];
        let list = res[2].rows || [];
        this.purchase_method = list.map(item => {
          return {
            label: item.purchaseMethodName,
            value: item.purchaseMethodCode,
            raw: item
          }
        });
        this.total = this.allTableData.length;
        this.getData();
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    getData() {
      this.tableData = this.paginatedData();
    },
    paginatedData() {
      const startIndex = (this.pageNum - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.allTableData.slice(startIndex, endIndex);
    }
  }
}
</script>

<style scoped>

</style>
