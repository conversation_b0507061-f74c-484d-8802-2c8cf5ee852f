<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      size="mini"
      type="text"
      icon="el-icon-aim"
      @click="getNodes(1)"
      v-hasPermi="['system:processNode:set']"
    >流程节点设置
    </el-button>

    <!-- 添加或修改对话框 -->
    <el-dialog
      top="5vh"
      title="流程节点设置"
      :visible.sync="open"
      width="90%"
      custom-class="maxW800"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-radio-group v-model="form.nodeRole" @change="changeRadio" class="mb10">
          <el-radio-button :label="1">采购人</el-radio-button>
          <el-radio-button :label="2">供应商</el-radio-button>
          <!--          <el-radio-button :label="3">评审专家</el-radio-button>-->
        </el-radio-group>
        <el-table :data="form.nodes" border v-loading="loading">
          <el-table-column label="功能名称" align="center" prop="purchaseFunctionName"></el-table-column>
          <el-table-column label="节点" align="center" prop="ranking" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item
                :prop="'nodes.'+$index+'.ranking'"
                label-width="0"
                size="mini"
                :rules="rules.ranking"
              >
                <el-select clearable v-model="row.ranking" placeholder="请选择" @change="handleRankingChange($event,$index)">
                  <el-option
                    v-for="item in form.nodes.length"
                    :key="item"
                    :label="item+'级节点'"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="触发点" align="center" prop="triggerPoint" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item
                :prop="'nodes.'+$index+'.triggerPoint'"
                label-width="0"
                size="mini"
                :rules="rules.triggerPoint"
              >
                <el-select clearable v-model="row.triggerPoint" placeholder="请选择">
                  <el-option
                    v-for="dict in process_nodes"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { bindingProcessNode, getProcessNode } from '@/api/system/processNode';

export default {
  name: 'PurchaseTime',
  dicts: ['process_nodes_supplier', 'process_nodes_purchaser'],
  props: {
    purchaseMethodId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ranking: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        triggerPoint: [
          { required: false, message: '请输入', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  computed: {
    process_nodes() {
      let list = []
      if (this.form.nodeRole === 1) {
        list = this.dict.type.process_nodes_purchaser
      } else if (this.form.nodeRole === 2) {
        list = this.dict.type.process_nodes_supplier
      } else {
        list = []
      }
      return list
    }
  },
  methods: {
    changeRadio(val) {
      this.getNodes(val);
    },
    handleRankingChange($event, index) {
      console.log($event, index)
      // this.form.nodes.forEach((item, key) => {
      //   if (key !== index && item.ranking === $event) {
      //     item.ranking = null;
      //   }
      // })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        nodeRole: null,
        purchaseMethodId: null,
        nodes: []
      };
      this.resetForm('form');
    },
    /** 新增按钮操作 */
    getNodes(val) {
      this.reset();
      this.form.nodeRole = val;
      this.loading = true;
      getProcessNode({ purchaseMethodId: this.purchaseMethodId, nodeRole: val }).then(response => {
        let { data } = response;
        this.form.nodes = data || [];
        this.open = true;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.purchaseMethodId = this.purchaseMethodId;
            await bindingProcessNode(this.form);
            this.$modal.msgSuccess('新增成功');
            this.open = false;
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    }
  }
}
</script>

<style scoped>

</style>
