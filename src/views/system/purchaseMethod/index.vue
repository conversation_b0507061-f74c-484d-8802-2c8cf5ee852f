<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="采购方式名称" prop="purchaseMethodName">
        <el-input
          v-model="queryParams.purchaseMethodName"
          placeholder="请输入采购方式名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购方式类型" prop="purchaseMethodType">
        <el-input
          v-model="queryParams.purchaseMethodType"
          placeholder="请输入采购方式类型"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="采购方式状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_purchaseMethod_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:purchaseMethod:add']"
        >新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="methodList" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="采购方式名称" prop="purchaseMethodName" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="采购方式类型" prop="purchaseMethodType" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="状态" align="center" width="100">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.sys_purchaseMethod_disable" :value="row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template v-slot:default="{row}">
          <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.remark || '/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="400">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['system:purchaseMethod:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['system:purchaseMethod:remove']"
          >删除
          </el-button>
          <purchase-time :purchaseMethodCode="row.purchaseMethodCode"/>
          <process-node :purchaseMethodId="row.purchaseMethodId"/>
          <bulletin-set :purchaseMethodCode="row.purchaseMethodCode"/>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购方式配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="125px">
        <el-form-item label="采购方式名称" prop="purchaseMethodName">
          <el-input v-model.trim="form.purchaseMethodName" maxlength="100" placeholder="请输入采购方式名称"/>
        </el-form-item>
        <el-form-item label="采购方式类型" prop="purchaseMethodType">
          <el-input v-model.trim="form.purchaseMethodType" maxlength="100" placeholder="请输入采购方式类型"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_purchaseMethod_disable"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-for="dict in dict.type.sys_function_role">
          <el-form-item :key="dict.value" :label="dict.label + '功能'">
            <function-select
              :ref="'tree'+dict.value"
              :key="'tree'+dict.value"
              :belongRole="dict.value"
              :defaultKeys="defaultFunctionIds"
              :tree="treeOptions"
              v-if="open"/>
          </el-form-item>
        </template>
        <el-form-item label="备注">
          <el-input v-model.trim="form.remark" type="textarea" maxlength="200" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMethod, addMethod, updateMethod, delMethod, getMethod } from '@/api/system/purchaseMethod';
import functionSelect from '@/views/system/purchaseMethod/functionSelect.vue';
import purchaseTime from '@/views/system/purchaseMethod/purchaseTime.vue';
import processNode from '@/views/system/purchaseMethod/processNode.vue';
import bulletinSet from '@/views/system/purchaseMethod/bulletinSet.vue';
import { listFunction } from '@/api/system/function';

export default {
  name: 'PurchaseMethod',
  dicts: ['sys_function_role', 'sys_purchaseMethod_disable'],
  components: {
    functionSelect,
    purchaseTime,
    processNode,
    bulletinSet
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购方式表格数据
      methodList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseMethodName: null,
        purchaseMethodType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseMethodName: [
          { required: true, message: '采购方式名称不能为空', trigger: 'blur' }
        ],
        purchaseMethodType: [
          { required: true, message: '采购方式类型不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      defaultFunctionIds: [],
      treeOptions: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购方式列表 */
    getList() {
      this.loading = true;
      listMethod(this.queryParams).then(response => {
        this.methodList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 所有功能节点数据
    getAllCheckedIds() {
      let functionIds = [];
      let flag = true;
      let msg = '';
      this.dict.type.sys_function_role.forEach(item => {
        let ids = this.$refs['tree' + item.value][0].getAllCheckedKeys();
        functionIds.push(...ids);
        if (ids.length === 0 && item.value !== '3') {
          flag = false;
          msg = item.label + '功能不能为空';
        }
      })
      return { functionIds, flag, msg };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.defaultFunctionIds = [];
      this.form = {
        purchaseMethodId: null,
        purchaseMethodName: null,
        purchaseMethodType: null,
        status: '1',
        purchaseFunctionIds: [],
        remark: ''
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset();
      await this.getFunctionTree();
      this.open = true;
      this.title = '添加采购方式';
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      await this.getFunctionTree();
      getMethod(row.purchaseMethodId).then(response => {
        this.form = response.data;
        this.defaultFunctionIds = response.data.purchaseFunctionIds;
        this.open = true;
        this.title = '修改采购方式';
      });
    },
    /** 查询功能树结构 */
    async getFunctionTree() {
      try {
        let { rows } = await listFunction();
        this.treeOptions = rows || [];
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          let obj = this.getAllCheckedIds();
          if (!obj.flag) {
            this.$modal.msgWarning(obj.msg);
            return
          }
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.purchaseFunctionIds = obj.functionIds;
            if (this.form.purchaseMethodId) {
              await updateMethod(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addMethod(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除采购方式"' + row.purchaseMethodName + '"的数据项？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delMethod(row.purchaseMethodId);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch(() => {
      });
    }
  }
};
</script>
