<template>
  <div>
    <el-checkbox v-model="treeExpand" @change="handleCheckedTreeExpand($event)">展开/折叠</el-checkbox>
    <el-checkbox v-model="treeNodeAll" @change="handleCheckedTreeNodeAll($event)">全选/全不选</el-checkbox>
    <!--    <el-checkbox v-model="treeCheckStrictly" @change="handleCheckedTreeConnect($event)">父子联动</el-checkbox>-->
    <el-tree
      class="tree-border"
      :data="treeOptions"
      show-checkbox
      ref="tree"
      node-key="purchaseFunctionId"
      :check-strictly="true"
      empty-text="暂无数据"
      :props="defaultProps"
    ></el-tree>
  </div>
</template>

<script>

export default {
  name: 'FunctionSelect',
  props: {
    belongRole: {
      type: [String, Number],
      default: '1'
    },
    defaultKeys: {
      type: Array,
      default: () => []
    },
    tree: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeExpand: false,
      treeNodeAll: false,
      treeCheckStrictly: true,
      // 功能列表
      treeOptions: [],
      defaultProps: {
        children: 'children',
        label: 'purchaseFunctionName'
      }
    }
  },
  created() {
    this.getFunctionTree()
  },
  methods: {
    /** 查询功能树结构 */
    getFunctionTree() {
      let list = this.tree.filter(item => item.belongRole === this.belongRole);
      this.treeOptions = this.handleTree(list, 'purchaseFunctionId', 'parentId');
      this.$nextTick(() => {
        this.defaultKeys.forEach((v) => {
          if (list.some(item => item.purchaseFunctionId === v)) {
            this.$nextTick(() => {
              this.$refs.tree.setChecked(v, true, false);
            })
          }
        })
      });
    },
    // 所有功能节点数据
    getAllCheckedKeys() {
      // 目前被选中的功能节点
      let checkedKeys = this.$refs.tree.getCheckedKeys();
      // 半选中的功能节点
      let halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value) {
      let treeList = this.treeOptions;
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.tree.store.nodesMap[treeList[i].purchaseFunctionId].expanded = value;
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value) {
      this.$refs.tree.setCheckedNodes(value ? this.treeOptions : []);
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value) {
      this.treeCheckStrictly = !!value;
    }
  }
}
</script>

<style scoped>

</style>
