<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      size="mini"
      type="text"
      icon="el-icon-date"
      @click="handleAdd()"
      v-hasPermi="['system:purchaseTime:set']"
    >采购时间设置
    </el-button>

    <!-- 添加或修改对话框 -->
    <el-dialog
      title="采购时间设置"
      :visible.sync="open"
      width="90%"
      custom-class="maxW400"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购时间" prop="timeKeyName">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;"></div>
          <el-checkbox-group v-model="form.timeKeyName" @change="handleCheckedCitiesChange">
            <div v-for="dict in dict.type.sys_purchase_time" :key="dict.value">
              <el-checkbox
                :label="dict.value">
                {{ dict.label }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addPurchaseTime, getPurchaseTime } from '@/api/system/purchaseTime';

export default {
  name: 'PurchaseTime',
  dicts: ['sys_purchase_time'],
  props: {
    purchaseMethodCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        timeKeyName: [
          { required: false, message: '请选择', trigger: 'change' }
        ]
      },
      checkAll: false,
      isIndeterminate: true
    }
  },
  methods: {
    handleCheckAllChange(val) {
      this.form.timeKeyName = val ? this.dict.type.sys_purchase_time.map(item => item.value) : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.dict.type.sys_purchase_time.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.dict.type.sys_purchase_time.length;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseMethodCode: null,
        timeKeyName: []
      };
      this.resetForm('form');
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getPurchaseTime(this.purchaseMethodCode).then(response => {
        let { data } = response;
        this.form.timeKeyName = data && data.timeKeyName || [];
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.purchaseMethodCode = this.purchaseMethodCode;
            await addPurchaseTime(this.form);
            this.$modal.msgSuccess('新增成功');
            this.open = false;
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    }
  }
}
</script>

<style scoped>

</style>
