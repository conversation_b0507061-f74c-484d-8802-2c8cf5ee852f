<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      size="mini"
      type="text"
      icon="el-icon-document"
      @click="handleShow()"
      v-hasPermi="['system:bulletin:set']"
    >公告设置
    </el-button>

    <el-dialog
      top="5vh"
      title="公告"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:bulletin:set']"
          >新增
          </el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" border>
        <el-table-column label="序号" align="center" type="index" width="50"/>
        <el-table-column label="公告名称" prop="anName" align="center" :show-overflow-tooltip="true"/>
        <el-table-column label="公告类型" prop="anType" align="center" :show-overflow-tooltip="true"/>
        <el-table-column label="标签" prop="tag" align="center">
          <template v-slot:default="{row}">
            <dict-tag
              :options="dict.type.sys_bulletin_tag"
              :value="row.tag ? row.tag.split(',') : []"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template v-slot:default="{row}">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(row)"
              v-hasPermi="['system:bulletin:set']"
            >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              v-hasPermi="['system:bulletin:set']"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer text-center">
        <el-button @click="isDialog=false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改对话框 -->
    <el-dialog
      title="公告设置"
      :visible.sync="open"
      width="90%"
      custom-class="maxW500"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="公告名称" prop="anName">
          <span slot="label">
            <el-tooltip content="中文显示名称" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            公告名称
          </span>
          <el-input v-model.trim="form.anName" maxlength="100" placeholder="请输入公告名称" v-if="open"/>
        </el-form-item>
        <el-form-item label="公告类型" prop="anType">
          <el-select
            v-model="form.anType"
            placeholder="公告类型"
            clearable
            class="block"
          >
            <el-option
              v-for="dict in dict.type.sys_bulletin_type"
              :key="dict.value"
              :label="dict.value+' - '+dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="tags">
           <span slot="label">
            <el-tooltip content="选择是否在首页、以及发布公告等位置显示" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            标签
          </span>
          <el-select
            v-model="form.tags"
            placeholder="标签"
            clearable
            multiple
            class="block"
          >
            <el-option
              v-for="dict in dict.type.sys_bulletin_tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBulletinSet } from '@/api/system/bulletinSet'
import { addBulletinSet, delBulletinSet, updateBulletinSet } from '@/api/system/bulletinSet'

export default {
  name: 'BulletinSet',
  dicts: ['sys_bulletin_type', 'sys_bulletin_tag'],
  props: {
    purchaseMethodCode: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      isDialog: false,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 1000
      },
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        anName: [
          { required: true, message: '公告名称不能为空', trigger: 'blur' }
        ],
        anType: [
          { required: true, message: '公告类型不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.tag = this.form.tags.join(',')
            if (this.form.announcementId) {
              await updateBulletinSet(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addBulletinSet(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      this.form.tags = this.form.tag ? this.form.tag.split(',') : []
      this.open = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delBulletinSet(row.announcementId);
          await this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch((e) => {
        throw new Error(e);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        announcementId: null,
        purchaseMethodCode: null,
        anName: null,
        anType: null,
        tag: null,
        tags: []
      };
      this.resetForm('form');
      this.form.purchaseMethodCode = this.purchaseMethodCode;
    },
    async handleShow() {
      await this.getList();
      this.isDialog = true;
    },
    async getList() {
      try {
        let { data } = await listBulletinSet({ purchaseMethodCode: this.purchaseMethodCode, ...this.queryParams });
        this.tableData = data.list || [];
        this.total = data.total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
