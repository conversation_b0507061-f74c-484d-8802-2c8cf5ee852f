<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
    >
      <el-form-item
        :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
        prop="buyItemName"
      >
        <el-input
          v-model="queryParams.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderSt">
        <el-select
          v-model="queryParams.orderSt"
          placeholder="订单状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.pay_order_status"
            :key="dict.value"
            :label="dict.label"
            :value="Number(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
    >
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="50"
      />
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
        align="center"
        prop="buyItemName"
      />
      <el-table-column
        label="标段(包)"
        align="center"
        prop="subpackageName"
      />
      <el-table-column
        label="项目编号"
        align="center"
        prop="buyItemCode"
      />
      <el-table-column
        label="商品名称"
        align="center"
        prop="goodsName"
      />
      <el-table-column
        label="订单金额（元）"
        align="center"
        prop="orderAmt"
      >
        <template v-slot:default="{row}">
          {{ row.orderAmt / 100 }}
        </template>
      </el-table-column>
      <el-table-column
        label="商户订单号"
        align="center"
        prop="orderId"
      />
      <el-table-column
        label="支付类型"
        align="center"
        prop="payType"
      />
      <el-table-column
        label="订单状态"
        align="center"
        prop="orderSt"
      >
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.pay_order_status" :value="row.orderSt"/>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        prop="createBy"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createAt"
      >
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import { payOrderList } from '@/api/payOrder'

export default {
  name: 'Index',
  dicts: ['pay_order_status', 'sys_dict_translate'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        buyItemName: null,
        orderSt: null
      },
      total: 0,
      tableData: [],
      loading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await payOrderList(this.queryParams)
        this.tableData = data.records || []
        this.total = data.total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
