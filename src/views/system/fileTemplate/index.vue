<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="模板中文名称" prop="tmpName">
        <el-input
          v-model="queryParams.tmpName"
          placeholder="请输入模板中文名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板英文名称" prop="tmpKey">
        <el-input
          v-model="queryParams.tmpKey"
          placeholder="请输入模板英文名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板分类" prop="tmpType">
        <el-select
          v-model="queryParams.tmpType"
          placeholder="模板分类"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.file_Template_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:fileTemplate:add']"
        >新增
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="模板中文名称" prop="tmpName" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="模板英文名称" prop="tmpKey" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="模板内容" prop="tmpContent" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="模板分类" prop="tmpType" align="center">
        <template v-slot:default="{row}">
          <dict-tag
            :options="dict.type.file_Template_group"
            :value="row.tmpType"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['system:fileTemplate:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['system:fileTemplate:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      top="2vh"
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="模板内容" prop="tmpContent">
              <Tinymce ref="editor" v-model="form.tmpContent" :height="600" :min-height="600" :max-height="700" v-if="open&&initEditor"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label="模板中文名称" prop="tmpName">
              <el-input v-model.trim="form.tmpName" maxlength="100" placeholder="请输入模板中文名称"/>
            </el-form-item>
            <el-form-item label="模板英文名称" prop="tmpKey">
              <span slot="label">
                <el-tooltip content="模板唯一标识，英文关键字，不能重复" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                模板英文名称
              </span>
              <el-input v-model.trim="form.tmpKey" maxlength="32" placeholder="请输入模板英文名称"/>
            </el-form-item>
            <el-form-item label="模板分类" prop="tmpType">
              <el-select
                v-model="form.tmpType"
                placeholder="模板分类"
                clearable
                class="block"
              >
                <el-option
                  v-for="dict in dict.type.file_Template_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-button type="success" @click="handlePreview">预览PDF</el-button>
              <el-button type="primary" @click="submitForm">保存</el-button>
              <el-button @click="cancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <el-dialog
      top="2vh"
      title="模板内容"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <iframe style="height: 650px;" class="html-iframe" :srcdoc="srcdoc"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import { listTemplate, addTemplate, updateTemplate, delTemplate, templatePreview } from '@/api/system/fileTemplate';
import { getPreviewEditorHtml } from '@/utils';
import { verifyEditor } from '@/utils/validate'

export default {
  name: 'FileTemplate',
  dicts: ['file_Template_group'],
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tmpName: undefined,
        tmpKey: undefined,
        tmpType: undefined
      },
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tmpContent: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ],
        tmpName: [
          { required: true, message: '模板中文名称不能为空', trigger: 'blur' }
        ],
        tmpKey: [
          { required: true, message: '模板英文名称不能为空', trigger: 'blur' }
        ],
        tmpType: [
          { required: true, message: '请选择', trigger: ['blur', 'change'] }
        ]
      },
      initEditor: true,
      isDialog: false,
      srcdoc: null
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handlePreview() {
      if (!this.form.tmpContent) {
        this.$modal.msgError('模板内容不能为空')
        return
      }
      this.$modal.loading('数据提交中，请稍候...')
      templatePreview({ filePreview: this.form.tmpContent }).then(res => {
        this.$modal.closeLoading()
        this.$pdfViewDialog({ data: res.data, type: 'blob' })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleLook(row) {
      this.srcdoc = getPreviewEditorHtml(row.tmpContent);
      this.isDialog = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tmpId: null,
        tmpContent: null,
        tmpName: null,
        tmpKey: null,
        tmpType: null
      };
      this.resetForm('form');
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加模版';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      this.open = true;
      this.title = '修改模版';
      this.initEditor = false;
      this.$nextTick(() => {
        this.initEditor = true
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该模版？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delTemplate(row.tmpId);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch((e) => {
        console.log(e)
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (this.form.tmpId) {
              await updateTemplate(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addTemplate(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        this.tableData = response.rows || [];
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style scoped>

</style>
