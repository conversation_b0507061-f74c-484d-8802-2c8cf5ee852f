<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="审批模板名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入审批模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批类型" prop="bindKey">
        <el-select
          v-model="queryParams.bindKey"
          placeholder="审批类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.approval_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批部门" prop="bindDeptId">
        <treeselect
          style="width: 240px"
          v-model="queryParams.bindDeptId"
          :options="deptList"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="审批部门"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:approvalTemplate:add']"
        >新增
        </el-button>
        <el-button
          type="danger"
          plain
          size="mini"
          icon="el-icon-delete"
          :disabled="multiple"
          @click="handleDeleteBatch"
        >删除
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="tableData" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" fixed/>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="审批模板名称" align="center" prop="name"/>
      <el-table-column label="审批类型" prop="bindKeyList" align="center">
        <template v-slot:default="{row}">
          <dict-tag
            :options="dict.type.approval_type"
            :value="row.bindKeyList"
          />
        </template>
      </el-table-column>
      <el-table-column label="审批部门" prop="bindDeptId" align="center">
        <template v-slot:default="{row}">
          {{ getCurrentData(deptList, row.bindDeptId, 'id', 'label') }}
        </template>
      </el-table-column>
      <el-table-column label="炒送人" prop="readUserInfoVoList" align="center">
        <template v-slot:default="{row}">
          {{ row.readUserInfoVoList.map(v => v.nickName).join('、') }}
        </template>
      </el-table-column>
      <el-table-column label="审批人" prop="auditUserInfoVoList" align="center">
        <template v-slot:default="{row}">
          {{ row.auditUserInfoVoList.map(v => v.nickName).join('、') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['system:approvalTemplate:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['system:approvalTemplate:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      top="2vh"
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-form-item label="审批模板名称" prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入审批模板名称"/>
        </el-form-item>
        <el-form-item label="审批类型" prop="bindKeyList">
          <el-select
            class="block"
            v-model="form.bindKeyList"
            placeholder="审批类型"
            clearable
            multiple
          >
            <el-option
              v-for="dict in dict.type.approval_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批部门" prop="bindDeptId">
          <treeselect
            v-model="form.bindDeptId"
            :options="deptList"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="抄送人" prop="readUserInfoVoList">
          <approval-c-c
            :orgCode="orgCode"
            :excludeRoleIds="excludeRoleIds"
            v-model="form.readUserInfoVoList"
          />
        </el-form-item>

        <el-form-item label="审批人" prop="auditUserInfoVoList">
          <approval-by
            :orgCode="orgCode"
            :excludeRoleIds="excludeRoleIds"
            v-model="form.auditUserInfoVoList"
            @change="changeAuditor"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listApprovalTemplate,
  addApprovalTemplate,
  updateApprovalTemplate,
  delApprovalTemplate,
  getApprovalTemplateById, delApprovalTemplateBatch
} from '@/api/system/approvalTemplate';
import approvalBy from '@/views/purchaser/approvalCenter/components/approvalBy.vue'
import approvalCC from '@/views/purchaser/approvalCenter/components/approvalCC.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { treeselect } from '@/api/system/dept'
import { getCurrentData } from '@/utils'

export default {
  name: 'ApprovalTemplate',
  components: { Treeselect, approvalCC, approvalBy },
  dicts: ['approval_type'],
  data() {
    return {
      orgCode: null,
      excludeRoleIds: [],
      loading: false,
      tableData: [],
      deptList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        bindKey: null,
        bindDeptId: null
      },
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        bindKeyList: [
          { required: true, message: '请选择', trigger: ['blur', 'change'] }
        ],
        bindDeptId: [
          { required: false, message: '请选择', trigger: ['blur', 'change'] }
        ],
        readUserInfoVoList: [{ required: false, message: '请选择', trigger: 'change' }],
        auditUserInfoVoList: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      isEdit: false,
      selectList: [],
      multiple: true
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getTreeSelect();
    this.getPurchaserExcludeRoleIds();
    this.getList();
  },
  methods: {
    getCurrentData,
    /** 查询部门下拉树结构 */
    getTreeSelect() {
      treeselect().then(response => {
        this.deptList = response.data
      })
    },
    /** 转换功能数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
    handleSelectionChange(selection) {
      this.selectList = selection.map(item => item.id);
      this.multiple = !selection.length
    },
    async getPurchaserExcludeRoleIds() {
      try {
        let { data } = await this.getConfigKey('purchaser.exclude.roleIds');
        this.excludeRoleIds = data.split(',');
      } catch (e) {
        throw new Error(e);
      }
    },
    changeAuditor() {
      this.$refs['form'].validateField('auditUserInfoVoList');
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sorted: 0,
        bindKeyList: [],
        bindDeptId: null,
        name: null,
        readUserInfoVoList: [],
        auditUserInfoVoList: []
      };
      this.resetForm('form');
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isEdit = false;
      this.title = '添加模版';
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getApprovalTemplateById(row.id);
        this.reset();
        for (let key in this.form) {
          this.form[key] = data[key];
        }
        this.$modal.closeLoading();
        this.open = true;
        this.isEdit = true;
        this.title = '修改模版';
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    },
    handleDeleteBatch() {
      this.$modal.confirm('是否确认删除该模版？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delApprovalTemplateBatch(this.selectList);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch((e) => {
        console.log(e)
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该模版？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delApprovalTemplate(row.id);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch((e) => {
        console.log(e)
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(this.form)
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (this.form.id) {
              await updateApprovalTemplate(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addApprovalTemplate(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listApprovalTemplate(this.queryParams).then(response => {
        let { data } = response;
        this.tableData = data.rows || [];
        this.total = data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<style scoped>

</style>
