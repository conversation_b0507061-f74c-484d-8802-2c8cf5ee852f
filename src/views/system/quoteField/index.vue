<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="dict in quote_field_group_type" :key="dict.value" :label="dict.label" :name="dict.value">
        <quote-field-item
          :groupType="dict.value"
          :key="dict.value"
          v-if="activeName===dict.value"
          :keyGroupList="getKeyGroupList(dict.value)"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import quoteFieldItem from '@/views/system/quoteField/quoteFieldItem.vue';

export default {
  name: 'QuoteField',
  dicts: ['quote_field_key_group', 'goods_key_group'],
  components: {
    quoteFieldItem
  },
  data() {
    return {
      activeName: null,
      quote_field_group_type: []
    }
  },
  created() {
    this.getFunctionRole();
  },
  methods: {
    getKeyGroupList(groupType) {
      switch (groupType) {
        case '0':
          return this.dict.type.quote_field_key_group;
        case '1':
          return this.dict.type.quote_field_key_group;
        case '2':
          return this.dict.type.goods_key_group;
        default :
          return this.dict.type.quote_field_key_group;
      }
    },
    async getFunctionRole() {
      try {
        let { data } = await this.getDicts('quote_field_group_type');
        this.quote_field_group_type = this.initDict(data || []);
        this.activeName = this.quote_field_group_type.length > 0 ? this.quote_field_group_type[0].value : null;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
