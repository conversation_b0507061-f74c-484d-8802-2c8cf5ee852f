<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="字段中文名" prop="entity.keyName">
        <el-input
          v-model="queryParams.entity.keyName"
          placeholder="请输入字段中文名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="entity.keyGroup">
        <el-select v-model="queryParams.entity.keyGroup" placeholder="请选择分类">
          <el-option
            v-for="dict in keyGroupList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否禁用" prop="entity.disabled">
        <el-select v-model="queryParams.entity.disabled" placeholder="请选择是否禁用">
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否显示" prop="entity.displayed">
        <el-select v-model="queryParams.entity.displayed" placeholder="请选择是否显示">
          <el-option label="显示" :value="true"></el-option>
          <el-option label="隐藏" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:quoteField:add']"
        >新增
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableList" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="分类" align="center" width="110">
        <template v-slot:default="{row}">
          <dict-tag :options="keyGroupList" :value="row.keyGroups"/>
        </template>
      </el-table-column>
      <el-table-column label="字段中文名" prop="keyName" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="字段英文名" prop="keyVal" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="排序" align="center" prop="sort" width="80"/>
      <el-table-column label="数据类型" align="center" width="100">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.quote_field_type" :value="row.keyType"/>
        </template>
      </el-table-column>
      <el-table-column label="是否必填" align="center" width="100">
        <template v-slot:default="{row}">
          {{ row.required ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="正则表达式" align="center" prop="regex"/>
      <el-table-column label="是否禁用" align="center" width="100">
        <template v-slot:default="{row}">
          {{ row.disabled ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="是否显示" align="center" width="100">
        <template v-slot:default="{row}">
          {{ row.displayed ? '显示' : '隐藏' }}
        </template>
      </el-table-column>
      <el-table-column label="校验不通过提示语" align="center" prop="remark" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.remark || '/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['system:quoteField:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['system:quoteField:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="145px">
        <el-form-item label="分类" prop="keyGroups">
          <el-select v-model="form.keyGroups" clearable multiple placeholder="请选择" class="block">
            <el-option
              v-for="dict in keyGroupList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段中文名" prop="keyName">
          <span slot="label">
            <el-tooltip content="字段的中文名称" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            字段中文名
          </span>
          <el-input v-model.trim="form.keyName" maxlength="100" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="字段英文名" prop="keyVal">
          <span slot="label">
            <el-tooltip content="字段的英文名称" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            字段英文名
          </span>
          <el-input v-model.trim="form.keyVal" maxlength="100" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="数据类型" prop="keyType">
          <span slot="label">
            <el-tooltip content="选择文件则该字段为文件上传" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            数据类型
          </span>
          <el-radio-group v-model="form.keyType">
            <el-radio
              v-for="dict in dict.type.quote_field_type"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否必填" prop="required">
          <el-radio-group v-model="form.required">
            <el-radio :key="true" :label="true">是</el-radio>
            <el-radio :key="false" :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否禁用" prop="disabled">
          <span slot="label">
            <el-tooltip content="禁用之后，新建的项目中将没有该字段" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            是否禁用
          </span>
          <el-radio-group v-model="form.disabled">
            <el-radio :key="true" :label="true">是</el-radio>
            <el-radio :key="false" :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否显示" prop="displayed">
          <span slot="label">
            <el-tooltip
              content="选择隐藏，则在创建项目、报名单位页面以及供应商预提交时不显示该字段，但客户端以及供应商报价时还是会显示该字段"
              placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            是否显示
          </span>
          <el-radio-group v-model="form.displayed">
            <el-radio :key="true" :label="true">显示</el-radio>
            <el-radio :key="false" :label="false">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="正则表达式" prop="regex">
          <span slot="label">
            <el-tooltip content="只能输入正则表达式，如：/[0-9A-Z].*?/igm" placement="top">
            <i class="el-icon-question"></i>
            </el-tooltip>
            正则表达式
          </span>
          <el-input v-model.trim="form.regex" maxlength="250" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="校验不通过提示语" prop="remark">
          <el-input v-model.trim="form.remark" maxlength="250" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listQuoteField,
  addQuoteField,
  updateQuoteField,
  delQuoteField
} from '@/api/system/quoteField';
import { checkReg } from '@/utils/validate';
import { Base64 } from 'js-base64'

export default {
  name: 'QuoteFieldItem',
  dicts: ['quote_field_type'],
  props: {
    groupType: {
      type: String,
      default: null
    },
    keyGroupList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    let checkRegex = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('输入正则表达式，如：/[0-9A-Z].*?/igm'));
      }
      if (!checkReg(value)) {
        callback(new Error('正则表达式格式不对，如：/[0-9A-Z].*?/igm'));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 采购方式表格数据
      tableList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          groupType: null,
          keyGroup: null,
          keyName: null,
          disabled: null,
          displayed: null
        }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        keyGroups: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        keyName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        keyVal: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        keyType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        required: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        regex: [
          { required: true, validator: checkRegex, trigger: 'blur' }
        ],
        remark: [
          { required: false, message: '不能为空', trigger: 'blur' }
        ],
        disabled: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        displayed: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购方式列表 */
    getList() {
      this.loading = true;
      this.queryParams.entity.groupType = this.groupType;
      listQuoteField(this.queryParams).then(response => {
        this.tableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        groupType: null,
        keyGroups: [],
        keyName: null,
        keyVal: null,
        sort: 0,
        keyType: null,
        required: null,
        regex: null,
        disabled: null,
        displayed: null,
        remark: ''
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加';
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      console.log(this.form)
      console.log(this.keyGroupList)
      this.open = true;
      this.title = '修改';
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.groupType = this.groupType;
            if (this.form.id) {
              await updateQuoteField({
                ...this.form,
                regex: Base64.toBase64(this.form.regex)
              });
              this.$modal.msgSuccess('修改成功');
            } else {
              await addQuoteField({
                ...this.form,
                regex: Base64.toBase64(this.form.regex)
              });
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除"' + row.keyName + '"的数据项？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delQuoteField([row.id]);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch(() => {
      });
    }
  }
};
</script>
