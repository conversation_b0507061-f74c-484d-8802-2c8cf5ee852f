<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="组织名称" prop="orgName">
        <el-input
          v-model="queryParams.orgName"
          placeholder="请输入组织名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="组织状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:organize:add']"
        >新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table :data="tableData" v-loading="loading">
      <el-table-column align="center" label="组织logo" prop="orgLogo">
        <template v-slot:default="{row}">
          <el-image
            style="width: 50px; height: 50px;vertical-align: middle;"
            :src="row.orgLogo"
            :preview-src-list="[row.orgLogo]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="orgName" label="组织名称"></el-table-column>
      <el-table-column prop="orgCode" label="组织编码"></el-table-column>
      <el-table-column prop="orderNum" label="排序"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="组织信息" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleInfo(scope.row)"
            v-hasPermi="['system:organize:edit']"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:organize:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:organize:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改组织对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请输入组织名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织logo" prop="logo">
              <file-upload-single
                :showTip="true"
                :autoUpload="false"
                :file-size="1"
                accept=".png, .jpg, .jpeg"
                uploadName="logo"
                :params="{fileTypeName: 'organize_logo'}"
                @onSuccess="handleUpload"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini">
                  上传
                </el-button>
                <el-button
                  slot="upload-right"
                  type="primary"
                  size="mini"
                  v-if="form.logo"
                  @click="previewFile(form.logo)"
                >预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrganize, delOrganize, addOrganize, updateOrganize } from '@/api/system/organize';
import reg from '@/utils/reg';
import { fileDownByOther, fileUploadByOther } from '@/api/file'

export default {
  name: 'Organize',
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格数据
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        orgName: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orgName: [
          { required: true, message: '组织名称不能为空', trigger: 'blur' },
          { min: 2, max: 25, message: '长度必须介于 2 和 25 之间', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        orderNum: [
          { required: true, message: '显示排序不能为空', trigger: 'blur' }
        ],
        logo: [
          { required: false, message: '请上传', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByOther(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    },
    previewFile(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    async handleUpload(params) {
      console.log('file', params.file);
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleInfo(row) {
      this.$router.push({ name: 'OrganizeInfo', params: { orgCode: row.orgCode, orgName: row.orgName }})
    },
    /** 查询组织列表 */
    getList() {
      this.loading = true;
      listOrganize(this.queryParams).then(response => {
        this.tableData = response.data;
        this.tableData.forEach(item => {
          this.getImgUrl(item.logo).then(res => {
            this.$set(item, 'orgLogo', res);
          })
        })
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        organizeId: undefined,
        orgName: undefined,
        orderNum: undefined,
        logo: undefined,
        status: '0'
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加组织';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      this.open = true;
      this.title = '修改组织';
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.organizeId != undefined) {
            updateOrganize(this.form).then(response => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addOrganize(this.form).then(response => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.orgName + '"的数据项？').then(function () {
        return delOrganize(row.organizeId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    }
  }
};
</script>
