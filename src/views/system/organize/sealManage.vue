<template>
  <div>
    <el-card header="法定代表人印章" shadow="never" :body-style="{'padding': '15px 10px 10px 10px'}" class="card-box">
      <div slot="header" class="clearfix">
        <span>法定代表人印章</span>
        <el-button
          class="fr"
          type="primary"
          size="mini"
          v-if="!legalPersonSeal&&checkPermi(['organize:seal:add'])"
          @click="createSeal(true)">
          创建
        </el-button>
        <el-button
          class="fr"
          type="danger"
          size="mini"
          v-if="legalPersonSeal&&checkPermi(['organize:seal:remove'])"
          @click="removeSeal(true)">
          删除
        </el-button>
      </div>
      <el-image style="width: 150px; height: 150px" :src="legalPersonSeal" fit="fill" v-if="legalPersonSeal"></el-image>
    </el-card>

    <el-card header="企业印章" shadow="never" :body-style="{'padding': '15px 10px 10px 10px'}" class="card-box">
      <div slot="header" class="clearfix">
        <span>企业印章</span>
        <el-button
          class="fr"
          type="primary"
          size="mini"
          v-if="!companySeal&&checkPermi(['organize:seal:add'])"
          @click="createSeal(false)">
          创建
        </el-button>
        <el-button
          class="fr"
          type="danger"
          size="mini"
          v-if="companySeal&&checkPermi(['organize:seal:remove'])"
          @click="removeSeal(false)">
          删除
        </el-button>
      </div>
      <el-image style="width: 150px; height: 150px" :src="companySeal" fit="fill" v-if="companySeal"></el-image>
    </el-card>

    <el-card header="企业印章授权" shadow="never" :body-style="{'padding': '15px 10px 10px 10px'}" class="card-box">
      <div slot="header" class="clearfix">
        <span>企业印章授权</span>
        <el-button
          class="fr mr10"
          type="primary"
          size="mini"
          v-if="companySeal&&checkPermi(['organize:seal:add'])"
          @click="authorizeSeal">
          发起授权
        </el-button>
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="经办人手机号码" align="center" prop="phone"/>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template v-slot:default="{row}">
            <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="授权过期时间" align="center" prop="useExpireTime">
          <template v-slot:default="{row}">
            <span>{{ row.useExpireTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="授权状态" align="center" prop="status">
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.seal_authorize_status" :value="row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="授权链接失效时间" align="center" prop="writeExpireTime">
          <template v-slot:default="{row}">
            <span>{{ row.writeExpireTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="授权链接" align="center" prop="shortUrl">
          <template v-slot:default="{row}">
            <el-link :href="row.shortUrl" target="_blank" type="primary" v-if="row.status===0">去授权</el-link>
            <span v-else-if="row.status===1">/</span>
            <span v-else>请重新发起授权</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" prop="shortUrl">
          <template v-slot:default="{row}">
            <el-button
              type="primary"
              size="mini"
              v-if="checkPermi(['organize:seal:add'])"
              @click="updateStatus(row.authId)">
              更新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

  </div>
</template>

<script>
import {
  querySeal,
  addSeal,
  removeOrgRemove,
  orgGrantAuthorization,
  getSealAuthenticationList,
  getAuthorizationResult
} from '@/api/sealManage';
import { queryPurchaserInfo } from '@/api/system/organize';
import { checkPermi } from '@/utils/permission';
import reg from '@/utils/reg'

export default {
  name: 'SealManage',
  dicts: ['seal_authorize_status'],
  data() {
    return {
      orgCode: null,
      companySeal: null,
      legalPersonSeal: null,
      sealId: null,
      tendererName: null,
      tendererCode: null,
      artificialPerson: null,
      artificialPersonCode: null,
      tableData: []
    }
  },
  created() {
    this.orgCode = this.$route.params.orgCode;
    this.queryPurchaserInfo()
    this.querySeal()
    this.getAuthorizeList();
  },
  methods: {
    checkPermi,
    async updateStatus(authId) {
      this.$modal.loading('数据查询中，请稍候...')
      await getAuthorizationResult({
        authId: authId
      })
      await this.getAuthorizeList();
      this.$modal.closeLoading();
    },
    authorizeSeal() {
      this.$prompt('请输入经办人手机号码', '发起授权', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputPlaceholder: '请输入经办人手机号码',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          return reg.cellphone.test(value)
        },
        inputErrorMessage: '手机号码格式不对'
      }).then(async ({ value }) => {
        this.$modal.loading('数据提交中，请稍候...')
        await orgGrantAuthorization({
          onlySign: this.orgCode,
          phone: value
        })
        this.$modal.msgSuccess('发起成功');
        await this.getAuthorizeList();
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    async getAuthorizeList() {
      try {
        this.$modal.loading('数据查询中，请稍候...')
        let { data } = await getSealAuthenticationList({
          onlySign: this.orgCode,
          type: 2
        })
        this.tableData = data || [];
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
      }
    },
    removeSeal(personal) {
      this.$alert('确定要删除印章？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await removeOrgRemove({
            personal,
            sealId: this.sealId
          })
          await this.querySeal();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('删除成功');
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e)
        }
      }).catch(() => {

      })
    },
    async createSeal(personal) {
      if (personal && !this.artificialPerson) {
        this.$modal.msgError('未查询到法定代表人姓名，请检查企业信息是否完善');
        return
      }
      if (!personal && !this.tendererName) {
        this.$modal.msgError('未查询到企业名称，请检查企业信息是否完善');
        return
      }
      try {
        this.$modal.loading('数据提交中，请稍候...')
        let uniqueId = personal ? this.artificialPersonCode : this.tendererCode;
        await addSeal({
          orgCode: this.orgCode,
          psn: personal,
          name: personal ? this.artificialPerson : this.tendererName,
          uniqueId
        });
        await this.querySeal();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('创建成功');
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    async querySeal() {
      try {
        let { data } = await querySeal({ orgCode: this.orgCode });
        this.sealId = data ? data.sealId : null;
        this.companySeal = data && data.orgSealBlob ? `data:image/png;base64,${data.orgSealBlob}` : null;
        this.legalPersonSeal = data && data.psnSealBlob ? `data:image/png;base64,${data.psnSealBlob}` : null;
      } catch (e) {
        throw new Error(e);
      }
    },
    async queryPurchaserInfo() {
      try {
        let { data } = await queryPurchaserInfo(this.orgCode);
        this.tendererName = data && data.tendererName ? data.tendererName : null;
        this.tendererCode = data && data.tendererCode ? data.tendererCode : null;
        this.artificialPerson = data && data.artificialPerson ? data.artificialPerson : null;
        this.artificialPersonCode = data && data.artificialPersonCode ? data.artificialPersonCode : null;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
