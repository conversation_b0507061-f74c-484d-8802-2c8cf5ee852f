<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="组织信息管理" name="first">
        <organize-detail v-if="activeName==='first'"></organize-detail>
      </el-tab-pane>
      <el-tab-pane label="印章管理" name="second">
        <seal-manage v-if="activeName==='second'"></seal-manage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import organizeDetail from './organizeDetail.vue'
import sealManage from './sealManage'

export default {
  name: 'Index',
  components: {
    organizeDetail,
    sealManage
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick() {

    }
  }
}
</script>

<style scoped>

</style>
