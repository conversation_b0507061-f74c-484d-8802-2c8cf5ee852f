<template>
  <div v-loading="loading">
    <el-form ref="form" :model="form" :rules="rules" label-position="top">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="公司名称" prop="tendererName">
            <el-input v-model.trim="form.tendererName" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="统一社会信用代码" prop="tendererCode">
            <el-input v-model.trim="form.tendererCode" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="采购人代码类型" prop="tendererCodeType">
            <el-input v-model.trim="form.tendererCodeType" placeholder="请输入" maxlength="2" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="法定代表人姓名" prop="artificialPerson">
            <el-input v-model.trim="form.artificialPerson" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="法定代表人身份证号码" prop="artificialPersonCode">
            <el-input :placeholder="form.artificialPersonCode | formatIdCard(5,3,'请输入')" disabled v-if="isDisabled"/>
            <el-input v-model.trim="form.artificialPersonCode" placeholder="请输入" :disabled="isDisabled" v-else/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="机构联系电话" prop="contactNumber">
            <el-input v-model.trim="form.contactNumber" placeholder="请输入" maxlength="12" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="国别/地区" prop="countryRegion">
            <el-input v-model.trim="form.countryRegion" placeholder="请输入" maxlength="3" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="单位性质" prop="unitNature">
            <el-input v-model.trim="form.unitNature" placeholder="请输入" maxlength="25" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="行政区域" prop="regionCode">
            <el-cascader
              :disabled="isDisabled"
              placeholder="请选择"
              class="block"
              v-model="form.regionCode"
              :options="cityList"
              :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="公司详细地址" prop="regionCodeDetail">
            <el-input v-model.trim="form.regionCodeDetail" placeholder="请输入" maxlength="125" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="行业分类" prop="industryCode">
            <el-cascader
              :disabled="isDisabled"
              placeholder="请选择"
              class="block"
              v-model="form.industryCode"
              :options="industryCode"
              :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="资信等级" prop="creditRate">
            <el-input v-model.trim="form.creditRate" placeholder="请输入" maxlength="10" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="开户银行" prop="openingBank">
            <el-input v-model.trim="form.openingBank" placeholder="请输入" maxlength="30" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="基本账户账号" prop="basicAccount">
            <el-input v-model.trim="form.basicAccount" placeholder="请输入" maxlength="30" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="注册资本" prop="regCapital">
            <el-input v-model.trim="form.regCapital" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="注册资本单位" prop="regUnit">
            <el-radio-group v-model="form.regUnit" :disabled="isDisabled">
              <el-radio label="1">元</el-radio>
              <el-radio label="2">万元</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="注册资本币种" prop="regCapCurrency">
            <el-input v-model.trim="form.regCapCurrency" placeholder="请输入" maxlength="3" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="信息申报责任人姓名" prop="infoReporterName">
            <el-input v-model.trim="form.infoReporterName" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="信息申报责任人证件号码" prop="infoReporterCode">
            <el-input :placeholder="form.infoReporterCode | formatIdCard(5,3,'请输入')" disabled v-if="isDisabled"/>
            <el-input v-model.trim="form.infoReporterCode" placeholder="请输入" :disabled="isDisabled" v-else/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="信息申报责任人手机号码" prop="infoReporterContactNumber">
            <el-input v-model.trim="form.infoReporterContactNumber" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input v-model.trim="form.contactAddress" placeholder="请输入" maxlength="125" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="邮政编码" prop="zipCode">
            <el-input v-model.trim="form.zipCode" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model.trim="form.email" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="交易平台标识码" prop="platformCode">
            <el-input v-model.trim="form.platformCode" placeholder="请输入" maxlength="11" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="交易平台名称" prop="platformName">
            <el-input v-model.trim="form.platformName" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="交易平台数据验证责任人姓名" prop="examinerName">
            <el-input v-model.trim="form.examinerName" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="交易平台数据验证责任人证件号码" prop="examinerCode">
            <el-input v-model.trim="form.examinerCode" placeholder="请输入" :disabled="isDisabled"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="营业执照或组织机构代码证件扫描件" prop="businessLicense">
            <file-upload-single
              :fileSize="10"
              :showTip="true"
              :autoUpload="false"
              accept=".png, .jpg, .jpeg, .pdf"
              uploadName="businessLicense"
              :params="{
                    fileTypeName: 'businessLicense'
                  }"
              @onSuccess="handleUploadByOther"
              :disabled="isDisabled"
            >
              <el-button slot="upload-btn" type="primary" size="mini" :disabled="isDisabled">上传</el-button>
              <el-button
                slot="upload-right"
                type="success"
                size="mini"
                v-if="form.businessLicense"
                @click="previewFileByOther(form.businessLicense)"
              >预览
              </el-button>
            </file-upload-single>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
          <el-form-item label="法定代表人身份证扫描件" prop="legalRepresentativeIdentityCertificate">
            <file-upload-single
              :fileSize="10"
              :showTip="true"
              :autoUpload="false"
              accept=".png, .jpg, .jpeg, .pdf"
              uploadName="legalRepresentativeIdentityCertificate"
              :params="{
                    fileTypeName: 'legalRepresentativeIdentityCertificate'
                  }"
              @onSuccess="handleUploadByOther"
              :disabled="isDisabled"
            >
              <el-button slot="upload-btn" type="primary" size="mini" :disabled="isDisabled">上传</el-button>
              <el-button
                slot="upload-right"
                type="success"
                size="mini"
                v-if="form.legalRepresentativeIdentityCertificate"
                @click="previewFileByOther(form.legalRepresentativeIdentityCertificate)"
              >预览
              </el-button>
            </file-upload-single>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="text-center mt30" v-hasPermi="['system:organize:edit']">
        <el-button type="primary" @click="isDisabled=false" v-if="isDisabled">修改</el-button>
        <el-button type="primary" @click="submitForm" v-if="!isDisabled">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import cityList from '@/assets/data/cityList'
import industryCode from '@/assets/data/industryCode'
import { savePurchaserInfo, updatePurchaserInfo, queryPurchaserInfo } from '@/api/system/organize';
import reg from '@/utils/reg'
import { fileUploadByOther } from '@/api/file';

export default {
  name: 'OrganizeDetail',
  dicts: ['supplier_type'],
  data() {
    return {
      cityList,
      industryCode,
      orgCode: null,
      form: {
        id: null,
        tendererName: null,
        tendererCode: null,
        tendererCodeType: null,
        artificialPerson: null,
        artificialPersonCode: null,
        contactNumber: null,
        countryRegion: null,
        unitNature: null,
        regionCode: null,
        regionCodeDetail: null,
        industryCode: null,
        creditRate: null,
        openingBank: null,
        basicAccount: null,
        regCapital: null,
        regUnit: null,
        regCapCurrency: null,
        infoReporterName: null,
        infoReporterCode: null,
        infoReporterContactNumber: null,
        contactAddress: null,
        zipCode: null,
        email: null,
        platformCode: null,
        platformName: null,
        examinerName: null,
        examinerCode: null,
        businessLicense: null,
        legalRepresentativeIdentityCertificate: null
      },
      rules: {
        tendererName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '公司名称长度必须介于 2 和 50 之间', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        tendererCode: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.unifiedCreditCode, message: '格式不对', trigger: 'blur' }
        ],
        artificialPerson: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        artificialPersonCode: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.idNumber, message: '格式不对', trigger: 'blur' }
        ],
        basicAccount: [
          { pattern: reg.bankCardNumber, message: '格式不对', trigger: 'blur' }
        ],
        regCapital: [
          { pattern: reg.money, message: '格式不对（0.00~***********.99）', trigger: 'blur' }
        ],
        infoReporterCode: [
          { required: false, message: '请输入', trigger: 'blur' }
        ],
        infoReporterContactNumber: [
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        zipCode: [
          { pattern: reg.zipCode, message: '格式不对', trigger: 'blur' }
        ],
        email: [
          { pattern: reg.email, message: '格式不对', trigger: 'blur' }
        ],
        examinerCode: [
          { pattern: reg.idNumber, message: '格式不对', trigger: 'blur' }
        ],
        examinerName: [
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        infoReporterName: [
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ]
      },
      isDisabled: false,
      loading: false
    }
  },
  created() {
    this.orgCode = this.$route.params.orgCode;
    this.form.tendererName = this.$route.params.orgName;
    this.getPurchaserInfo()
  },
  methods: {
    async getPurchaserInfo() {
      try {
        this.loading = true;
        let { data } = await queryPurchaserInfo(this.orgCode);
        this.isDisabled = data ? !!data.id : false;
        if (data) {
          for (let key in this.form) {
            this.form[key] = data[key]
          }
        }
        this.loading = false
      } catch (e) {
        this.loading = false
        throw new Error(e);
      }
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    async handleUploadByOther(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...')
            if (!this.form.id) {
              await savePurchaserInfo({ ...this.form, orgCode: this.orgCode })
            } else {
              await updatePurchaserInfo({ ...this.form, orgCode: this.orgCode })
            }
            this.isDisabled = true;
            this.$modal.closeLoading()
            this.$modal.msgSuccess(`${this.form.id ? '修改' : '保存'}成功`)
            await this.getPurchaserInfo()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
