<template>
  <div class="app-container">
    <el-card
      header="系统授权文件生成"
      shadow="never"
      :body-style="{'padding': '15px 10px 0px 10px'}"
      style="max-width: 600px;margin: 0 auto"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item label="系统名称" prop="customer">
          <el-select
            v-model="form.customer"
            placeholder="请选择"
            filterable
            clearable
            class="block"
            @change="customerChange"
          >
            <el-option
              v-for="dict in dict.type.sys_license"
              :key="dict.value"
              :label="dict.label"
              :value="dict.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统识别码" prop="sysCode">
          <el-input
            v-model.trim="form.sysCode"
            placeholder="请输入"
            disabled
          >
          </el-input>
        </el-form-item>
        <el-form-item label="系统地址" prop="customerUrl">
          <el-input
            v-model.trim="form.customerUrl"
            placeholder="请输入"
            disabled
          >
          </el-input>
        </el-form-item>
        <el-form-item label="系统有效期" prop="customerExpireDate">
          <el-date-picker
            class="block"
            style="width: 100%"
            v-model="form.customerExpireDate"
            type="date"
            placeholder="请选择"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>

        <div
          class="text-center mt30 mb30"
          v-if="checkPermi(['system:license:generate'])"
        >
          <el-button type="primary" @click="submitForm">
            文件导出
          </el-button>
        </div>
      </el-form>
    </el-card>

  </div>
</template>

<script>

import { generateLic } from '@/api/license'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'License',
  dicts: ['sys_license'],
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sysCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        customer: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        customerUrl: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        customerExpireDate: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    console.log(this.dict.type.sys_license)
  },
  methods: {
    checkPermi,
    customerChange(val) {
      let obj = this.dict.type.sys_license.find(v => v.label === val);
      this.form.sysCode = obj ? obj.value : null;
      this.form.customerUrl = obj ? obj.raw.remark : null;
    },
    // 表单重置
    reset() {
      this.form = {
        sysCode: null,
        customer: null,
        customerUrl: null,
        customerExpireDate: null
      };
      this.resetForm('form');
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据下载中，请稍候...');
            let res = await generateLic(this.form);
            this.$modal.closeLoading()
            this.$download.downloadSave(res);
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }
      });
    }
  }
};
</script>
