<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="dict in sys_function_role" :key="dict.value" :label="dict.label" :name="dict.value">
        <function-item :belongRole="dict.value" :key="dict.value" v-if="activeName===dict.value"></function-item>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import functionItem from '@/views/system/function/functionItem.vue';

export default {
  name: 'Function',
  components: {
    functionItem
  },
  data() {
    return {
      activeName: null,
      sys_function_role: []
    }
  },
  created() {
    this.getFunctionRole();
  },
  methods: {
    async getFunctionRole() {
      try {
        let { data } = await this.getDicts('sys_function_role');
        this.sys_function_role = this.initDict(data || []);
        this.activeName = this.sys_function_role.length > 0 ? this.sys_function_role[0].value : null;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
