<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="功能名称" prop="purchaseFunctionName">
        <el-input
          v-model="queryParams.purchaseFunctionName"
          placeholder="请输入功能名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="whetherDisabled">
        <el-select v-model="queryParams.whetherDisabled" placeholder="功能状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_function_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:function:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="functionList"
      row-key="purchaseFunctionId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="purchaseFunctionName" label="功能名称" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="sort" label="排序" width="60"></el-table-column>
      <el-table-column prop="purchaseFunctionKey" label="唯一标识" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="whetherDisabled" label="状态" align="center" width="80">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.sys_function_disable" :value="row.whetherDisabled"/>
        </template>
      </el-table-column>
      <el-table-column prop="belongLevel" label="类型" align="center" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.sys_function_type" :value="row.belongLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.remark || '/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['system:function:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(row)"
            v-hasPermi="['system:function:add']"
          >新增
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['system:function:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改功能对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW700"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级模块" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="functionOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级模块"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="类型" prop="belongLevel">
              <el-radio-group v-model="form.belongLevel">
                <el-radio
                  v-for="dict in dict.type.sys_function_type"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="功能名称" prop="purchaseFunctionName">
              <el-input v-model.trim="form.purchaseFunctionName" maxlength="100" placeholder="请输入功能名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="purchaseFunctionKey">
              <el-input v-model.trim="form.purchaseFunctionKey" placeholder="请输入唯一标识" maxlength="100"/>
              <span slot="label">
                <el-tooltip content="功能名称对应的英文名称，值唯一" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                唯一标识
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否流程节点" prop="whetherNode">
              <el-radio-group v-model="form.whetherNode">
                <el-radio
                  v-for="dict in dict.type.sys_function_whether_Node"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="whetherDisabled">
              <span slot="label">
                <el-tooltip content="选择禁用则页面将不再显示" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                功能状态
              </span>
              <el-radio-group v-model="form.whetherDisabled">
                <el-radio
                  v-for="dict in dict.type.sys_function_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model.trim="form.remark" type="textarea" maxlength="200" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFunction, getFunction, delFunction, addFunction, updateFunction } from '@/api/system/function';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
  name: 'FunctionItem',
  dicts: ['sys_function_disable', 'sys_function_type', 'sys_function_whether_Node'],
  components: { Treeselect },
  props: {
    belongRole: {
      type: [String, Number],
      default: '1'
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      functionList: [],
      functionOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        purchaseFunctionName: undefined,
        belongRole: undefined,
        whetherDisabled: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        belongLevel: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        purchaseFunctionName: [
          { required: true, message: '功能名称不能为空', trigger: 'blur' }
        ],
        purchaseFunctionKey: [
          { required: true, message: '唯一标识不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '功能顺序不能为空', trigger: 'blur' }
        ],
        belongRole: [
          { required: true, message: '请选择所属角色', trigger: 'change' }
        ],
        whetherNode: [
          { required: true, message: '请选择是否流程节点', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询功能列表 */
    getList() {
      this.loading = true;
      this.queryParams.belongRole = this.belongRole;
      listFunction(this.queryParams).then(response => {
        this.functionList = this.handleTree(response.rows, 'purchaseFunctionId', 'parentId');
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    /** 转换功能数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.purchaseFunctionId,
        label: node.purchaseFunctionName,
        children: node.children
      };
    },
    /** 查询功能下拉树结构 */
    getTreeSelect() {
      listFunction({ belongRole: this.belongRole }).then(response => {
        this.functionOptions = [];
        const menu = { purchaseFunctionId: 0, purchaseFunctionName: '主类目', children: [] };
        menu.children = this.handleTree(response.rows, 'purchaseFunctionId', 'parentId');
        this.functionOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseFunctionId: undefined,
        parentId: 0,
        belongLevel: '1',
        purchaseFunctionName: undefined,
        purchaseFunctionKey: undefined,
        sort: undefined,
        belongRole: undefined,
        whetherNode: '0',
        whetherDisabled: '1',
        remark: ''
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeSelect();
      if (row != null && row.purchaseFunctionId) {
        this.form.parentId = row.purchaseFunctionId;
      } else {
        this.form.parentId = 0;
      }
      this.open = true;
      this.title = '添加功能';
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeSelect();
      getFunction(row.purchaseFunctionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = '修改功能';
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.belongRole = this.belongRole;
            if (this.form.purchaseFunctionId != undefined) {
              await updateFunction(this.form);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addFunction(this.form);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e;
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.purchaseFunctionName + '"的数据项？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delFunction(row.purchaseFunctionId);
          this.getList();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch((e) => {
        console.log(e)
      });
    }
  }
};
</script>
