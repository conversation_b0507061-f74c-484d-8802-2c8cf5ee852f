<template>
  <div class="register">
    <el-form ref="form" :model="form" :rules="registerRules" class="register-form">
      <h3 class="title">修改密码</h3>
      <el-form-item prop="username">
        <el-input v-model="form.username" type="text" auto-complete="off" placeholder="账号/手机号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="newPassword">
        <el-input
          v-model="form.newPassword"
          type="text"
          auto-complete="off"
          placeholder="新密码"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="text"
          auto-complete="off"
          placeholder="确认新密码"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="mobileCode">
        <el-input
          v-model="form.mobileCode"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
        </el-input>
        <div class="register-code">
          <el-button
            type="primary"
            @click="getCode"
            :disabled="!isVerifyPhone||!isClickCode"
            class="code-btn"
          >{{ this.codeBtnValue }}
          </el-button>
        </div>
      </el-form-item>
      <p class="text-danger fontSize14 mb10" style="line-height: 1.5;">
        温馨提示：只有使用手机号注册的账号才能在此修改密码。非手机号注册的账号，请登录之后点击右上角进入个人中心修改，</p>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleRegister"
        >
          <span v-if="!loading">修 改</span>
          <span v-else>修 改 中...</span>
        </el-button>
        <div style="float: left;">
          <router-link class="link-type" :to="'/home'">返回首页</router-link>
        </div>
        <div style="float: right;">
          <router-link class="link-type" :to="'/login'">去登录</router-link>
        </div>
      </el-form-item>
    </el-form>

    <vue-particles
      class="particles-js"
      color="#409EFF"
      :particleOpacity="0.7"
      :particlesNumber="80"
      shapeType="circle"
      :particleSize="4"
      linesColor="#409EFF"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="150"
      :moveSpeed="3"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
    ></vue-particles>
  </div>
</template>

<script>
import { getSmsCode, modifyPassword } from '@/api/login'
import reg from '@/utils/reg'

export default {
  name: 'Register',
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.form.newPassword !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      form: {
        username: '',
        newPassword: '',
        confirmPassword: '',
        mobileCode: ''
      },
      registerRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的手机号' },
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { pattern: reg.password, message: '8-20位，必须包含数字、字母、特殊字符$@$!%*#?&', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' }
        ],
        mobileCode: [{ required: true, trigger: 'change', message: '请输入验证码' }]
      },
      loading: false,
      codeBtnValue: '获取验证码',
      countdown: 60,
      isClickCode: true,
      timer: null
    }
  },
  computed: {
    isVerifyPhone() {
      return reg.cellphone.test(this.form.username)
    }
  },
  created() {
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  beforeRouteLeave(to, from, next) {
    clearInterval(this.timer);
    this.$destroy();
    next();
  },
  methods: {
    async getCode() {
      try {
        await getSmsCode({
          mobile: this.form.username,
          smsType: 'SMS_FORGET_PASSWORD'
        });
        this.$modal.msgSuccess('验证码发送成功')
        this.countdown = 60;
        this.intervalFun();
        this.timer = setInterval(() => {
          if (this.countdown === 0) {
            this.codeBtnValue = '获取验证码';
            this.isClickCode = true;
            clearInterval(this.timer);
          } else {
            this.intervalFun();
          }
        }, 1000);
      } catch (e) {
        throw new Error(e);
      }
    },
    intervalFun() {
      this.codeBtnValue = '重新发送(' + this.countdown + ')';
      this.isClickCode = false;
      this.countdown--;
    },
    handleRegister() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          modifyPassword(this.form).then(res => {
            const username = this.form.username;
            this.resetForm('form');
            this.$alert('<font color=\'red\'>恭喜你，您的账号 ' + username + ' 修改密码成功！</font>', '系统提示', {
              dangerouslyUseHTMLString: true,
              type: 'success',
              confirmButtonText: '立即登录'
            }).then(() => {
              location.href = '/login';
            }).catch(() => {
            })
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.register {
  width: 100%;
  height: 100%;
  //background-image: url("../assets/images/login-background.jpg");
  background-image: linear-gradient(-180deg, #1a1454 0%, #0e81a5 100%);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.particles-js {
  width: 100%;
  height: calc(100% - 5px);
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #000000;
}

.register-form {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.75);
  width: 100%;
  max-width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.register-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.register-code-img {
  height: 38px;
}
</style>
