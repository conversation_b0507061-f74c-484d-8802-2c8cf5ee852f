<template>
  <div class="pt40 pb40" :style="{'--theme': theme}">
    <div class="container">
      <div class="module-title">
        合作伙伴
      </div>
      <div class="home-info-img">
        <ul class="partners-list">
          <li v-for="item in 14" :key="item">
            <a href="#">
              <img :src="require('@/assets/images/case_'+item+'.png')" alt="">
            </a>
          </li>
          <li class="ellipsis">
            ......
          </li>
        </ul>
      </div>
    </div>
  </div>

</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'bannerHeight',
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" scoped>

.partners-list {
  display: flex;
  flex-wrap: wrap;

  li {
    width: 33.3%;
    margin-bottom: 50px;
    padding: 0 25px;

    a {
      overflow: hidden;
      display: block;
      height: 142px;
      padding: 15px;
      border-bottom: 2px solid  #{'var(--theme)'};
      background: #f0f2f5;
      text-align: center;
      transition: all 0.6s;

      img {
        display: inline-block;
        max-width: 100%;
        vertical-align: middle;
      }

      &:after {
        content: '';
        width: 0px;
        height: 100%;
        display: inline-block;
        vertical-align: middle;
      }

      &:hover {
        transform: scale(1.2, 1.2);
      }
    }

    &.ellipsis {
      font-size: 30px;
      display: flex;
      align-items: center;
    }
  }
}

@media (max-width: 767px) {
  .partners-list {
    li {
      width: 50%;
      margin-bottom: 20px;
      padding: 0 10px;
    }
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .partners-list {
    li {
      width: 33.3%;
      margin-bottom: 30px;
      padding: 0 15px;
    }
  }
}
</style>
