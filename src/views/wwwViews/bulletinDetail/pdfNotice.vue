<template>
  <div class="notice-detail-wrap overflow" :class="classObj">
    <h1 class="notice-title">{{ detail.bulletinName }}</h1>
    <p class="notice-time">
      <span>发布时间：{{ detail.reviewTime | formatTime('YYYY年MM月DD日 HH:mm:ss') }}</span>
    </p>

    <div class="text-group overflow">
      <h2 class="text-title">一、公告信息</h2>
      <div class="text-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')">
            {{ detail.buyItemName }}
          </el-descriptions-item>
          <el-descriptions-item label="公告类型">{{ detail.bulletinTypeName }}</el-descriptions-item>
          <el-descriptions-item label="附件">
            <file-list-view :fileList="detail.attachmentDtoList" :handleDown="downAnnex"/>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="text-group overflow">
      <h2 class="text-title">二、公告内容</h2>

      <div class="text-content">
        <pdf-view :src="pdfData" type="blob"/>
      </div>
    </div>

    <div class="text-group overflow">
      <h2 class="text-title">三、其他公告</h2>

      <div class="text-content">
        <p class="notice-text" v-for="(item,index) in bulletinSimpleVoList" :key="index">
          <el-link @click="toBulletinDetail(item)" type="primary">{{ item.bulletinName }}</el-link>
        </p>
      </div>
    </div>

  </div>
</template>

<script>

import { getHomeBulletinInfo } from '@/api/home';
import { fileDown } from '@/api/file';
import { blobValidate } from '@/utils/ruoyi';
import { mapGetters } from 'vuex';

export default {
  name: 'PdfNotice',
  dicts: ['sys_dict_translate'],
  props: {
    bulletinId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      detail: {},
      pdfData: null,
      bulletinSimpleVoList: []
    }
  },
  computed: {
    ...mapGetters([
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getHomeBulletinInfo()
  },
  methods: {
    toBulletinDetail(item) {
      this.$router.push({
        name: 'BulletinDetail',
        params: { bulletinId: item.bulletinId, bulletinType: item.bulletinType }
      })
    },
    downAnnex(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    async getHomeBulletinInfo() {
      try {
        let { data } = await getHomeBulletinInfo(this.bulletinId)
        this.detail = data || {};
        this.bulletinSimpleVoList = data.bulletinSimpleVoList || [];
        let res = await fileDown(this.detail.bulletinContentKey)
        const isBlob = blobValidate(res.data)
        if (isBlob) {
          this.pdfData = res.data;
        } else {
          await this.$download.printErrMsg(res.data)
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/noticeDetail.scss";
</style>
