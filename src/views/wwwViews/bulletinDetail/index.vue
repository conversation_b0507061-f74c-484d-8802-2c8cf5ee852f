<template>
  <div class="container mt20 mb20 overflow">
    <bidding-notice
      v-if="bulletinType==='bidding_announcement'"
      :bulletinId="bulletinId"
    >
    </bidding-notice>
    <pdf-notice
      v-else
      :bulletinId="bulletinId"
    >
    </pdf-notice>
  </div>
</template>

<script>
import biddingNotice from './biddingNotice';
import pdfNotice from './pdfNotice';

export default {
  name: 'BulletinDetail',
  components: {
    biddingNotice,
    pdfNotice
  },
  data() {
    return {
      bulletinId: null,
      bulletinType: null
    }
  },
  created() {
    this.bulletinType = this.$route.params.bulletinType;
    this.bulletinId = this.$route.params.bulletinId;
  }
}
</script>

<style scoped>

</style>
