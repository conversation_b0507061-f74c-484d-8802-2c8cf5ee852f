<template>
  <div class="notice-detail-wrap overflow" :class="classObj">
    <h1 class="notice-title">{{ detail.bulletinName }}</h1>
    <p class="notice-time">
      <span>发布时间：{{ detail.reviewTime | formatTime('YYYY年MM月DD日 HH:mm:ss') }}</span>
    </p>

    <div class="text-group overflow">
      <h2 class="text-title">一、公告信息</h2>
      <div class="text-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')">
            {{ detail.buyItemName }}
          </el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ detail.innerCode || detail.buyItemCode }}</el-descriptions-item>
          <el-descriptions-item label="公告类型">{{ detail.bulletinTypeName }}</el-descriptions-item>
          <el-descriptions-item label="附件">
            <file-list-view :fileList="detail.attachmentDtoList" :handleDown="downAnnex"/>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="text-group overflow">
      <h2 class="text-title">二、标段列表</h2>
      <div class="text-content">
        <el-table :data="detail.simpleVoList" border>
          <el-table-column label="序号" align="center" type="index" width="50"/>
          <el-table-column label="标段(包)" prop="subpackageName" align="center"/>
          <el-table-column label="报名截止时间" align="center" prop="registrationEndTime">
            <template v-slot:default="{row}">
              <span>{{ row.registrationEndTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="报名倒计时" align="center" prop="registrationEndTime">
            <template v-slot:default="{row}">
              <count-down
                v-if="row.registrationEndTime"
                itemWidth="20px"
                :end-time="row.registrationEndTime"
                :start-time="serverTime">
              </count-down>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template v-slot:default="{row}">
              <router-link to="/login" style="color: #409eff;" v-if="!hasToken">☛☛去登录</router-link>
              <div v-else>
                <span v-if="row.whetherSign==='1'">已报名</span>
                <sign-up
                  v-else
                  :hasToken="!!hasToken"
                  :orgCode="detail.orgCode"
                  :buyItemCode="detail.buyItemCode"
                  :buyItemName="detail.buyItemName"
                  :subpackageCode="row.subpackageCode"
                  :subpackageName="row.subpackageName"
                  :yearMonthSplit="yearMonthSplit"
                  :whetherWrite="whetherWrite"
                  :heads="row.attributeVoList"
                  @success="signSuccess"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="text-group overflow">
      <h2 class="text-title">三、公告内容</h2>

      <div class="text-content">
        <pdf-view :src="pdfData" type="blob"/>
      </div>
    </div>

    <div class="text-group overflow">
      <h2 class="text-title">四、其他公告</h2>

      <div class="text-content">
        <p class="notice-text" v-for="(item,index) in bulletinSimpleVoList" :key="index">
          <el-link @click="toBulletinDetail(item)" type="primary">{{ item.bulletinName }}</el-link>
        </p>
      </div>
    </div>

  </div>
</template>

<script>
import { getHomeBulletinInfo, supplierSignWhether } from '@/api/home';
import { fileDown } from '@/api/file';
import { blobValidate } from '@/utils/ruoyi';
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex';
import signUp from '@/views/wwwViews/bulletinDetail/signUp.vue';

export default {
  name: 'BiddingNotice',
  dicts: ['sys_dict_translate'],
  components: {
    signUp
  },
  props: {
    bulletinId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      detail: {},
      pdfData: null,
      serverTime: null,
      yearMonthSplit: null,
      hasToken: false,
      bulletinSimpleVoList: [],
      whetherWrite: 0
    }
  },
  computed: {
    ...mapGetters([
      'device',
      'userId'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getHomeBulletinInfo();
    this.hasToken = getToken();
  },
  methods: {
    toBulletinDetail(item) {
      this.$router.push({
        name: 'BulletinDetail',
        params: { bulletinId: item.bulletinId, bulletinType: item.bulletinType }
      })
    },
    async signSuccess() {
      await this.getSignWhether();
      await this.$router.push({ path: '/index' })
    },
    downAnnex(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    /** 查询供应商是否报名 */
    async getSignWhether() {
      try {
        let { data } = await supplierSignWhether({
          buyItemCode: this.detail.buyItemCode,
          supplierId: this.userId
        });
        let list = data || [];
        this.detail.simpleVoList.forEach(item => {
          let whetherSign = list.find(v => v.subpackageCode === item.subpackageCode).whetherSign;
          this.$set(item, 'whetherSign', whetherSign);
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 查询公告信息 */
    async getHomeBulletinInfo() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getHomeBulletinInfo(this.bulletinId)
        this.detail = data || {};
        this.serverTime = data.serverTime;
        this.whetherWrite = data.whetherWrite;
        this.bulletinSimpleVoList = data.bulletinSimpleVoList || [];
        this.yearMonthSplit = this.detail.yearMonthSplit;
        let res = await fileDown(this.detail.bulletinContentKey)
        const isBlob = blobValidate(res.data)
        if (isBlob) {
          this.pdfData = res.data;
        } else {
          await this.$download.printErrMsg(res.data)
        }
        if (this.hasToken && this.$auth.hasRole('supplier')) {
          await this.getSignWhether()
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/noticeDetail.scss";
</style>
