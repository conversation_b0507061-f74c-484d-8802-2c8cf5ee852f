<template>
  <div>
    <el-carousel
      :height="bannerHeight"
      class="banner"
      arrow="never"
    >
      <el-carousel-item v-for="(item,k) in bannerList" :key="k">
        <el-image class="img-w img-h" :src="item.imgUrl" fit="fill">
          <div slot="error" class="image-slot fontSize30">
            <i class="el-icon-picture-outline"></i>
          </div>
          <div slot="placeholder" class="image-slot fontSize16">
            加载中<span class="dot">...</span>
          </div>
        </el-image>
      </el-carousel-item>
    </el-carousel>
    <div class="container">
      <el-row :gutter="25" class="mt15 mb15">
        <el-col :xs="24" :sm="16" :md="18" :lg="18" :xl="18">
          <el-card>
            <div slot="header">
              <div style="padding-bottom: 10px">最新公告</div>
            </div>
            <div class="clearfix" :class="classObj" v-loading="loading">
              <el-row class="home-row" v-for="(item,index) in bulletinList" :key="index">
                <el-col :xs="9" :sm="8" :md="7" :lg="7" :xl="7" class="home-row-left">
                  <div class="row-classify">
                    {{ item.bulletinTypeName }}
                  </div>
                  <div class="row-time">
                    <span>发布时间</span>
                  </div>
                  <div class="row-time">
                    <span>{{ item.reviewTime | formatTime('YYYY/MM/DD HH:mm:ss') }}</span>
                  </div>
                </el-col>
                <el-col :xs="15" :sm="16" :md="17" :lg="17" :xl="17" class="home-row-right">
                  <p
                    class="over-ellipsis row-notice-title"
                    :style="{'--theme': theme}"
                    @click="toBulletinDetail(item)">
                    {{ item.bulletinName }}
                  </p>
                  <!--                  <div class="row-notice-info">-->
                  <!--                    <span style="margin-right: 15px;">项目编号：{{ item.innerCode || item.buyItemCode }}</span>-->
                  <!--                  </div>-->
                  <el-button type="text" @click="toBulletinDetail(item)">查看详情</el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="text-center pt10">
                  <el-button type="text" @click="toHomeBulletin">查看更多</el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6" class="hidden-xs-only">
          <el-card :style="{'--theme': theme}">
            <div class="login-box-card">
              <h3 class="login-title">
                <span class="o-line-1"></span>
                <span>登录入口</span>
                <span class="o-line-2"></span>
              </h3>

              <ul class="login-list overflow">
                <li @click="toLogin">
                  <login-bg color1="#2A71E7" color2="#79BBFF" icon="company" title="集团用户登录" v-if="versionType==='wzlg'||versionType==='whws'"/>
                  <login-bg color1="#FF9391" color2="#FFBE88" icon="hospital" title="医院用户登录" alt="" v-else/>
                </li>
                <li @click="toLogin">
                  <login-bg color1="#2A71E7" color2="#79BBFF" icon="supplier" title="供应商登录"/>
                </li>
                <li @click="toLogin" v-if="versionType!=='wzlg'&&versionType!=='whws'">
                  <login-bg color1="#FFA400" color2="#F9CE83" icon="agency" title="代理机构登录"/>
                </li>
              </ul>
            </div>
          </el-card>
        </el-col>
      </el-row>

    </div>
  </div>

</template>

<script>
import { mapGetters } from 'vuex'
import { listHomeBulletin } from '@/api/home';
import { fileDownByPublic, getFileListByPublic } from '@/api/file';
import loginBg from '@/views/wwwViews/home/<USER>'

export default {
  name: 'Index',
  components: {
    loginBg
  },
  data() {
    return {
      bannerList: [],
      bulletinList: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'bannerHeight',
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getBannerList();
    this.getList();
  },
  methods: {
    toLogin() {
      this.$router.push({ path: '/login' })
    },
    toHomeBulletin() {
      this.$router.push({ name: 'HomeBulletin' })
    },
    async getBannerList() {
      try {
        let { data } = await getFileListByPublic({ fileTypeName: 'carousel' });
        this.bannerList = data;
        this.bannerList.forEach(item => {
          this.getImgUrl(item.fileCode).then(res => {
            this.$set(item, 'imgUrl', res);
          })
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByPublic(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    },
    toBulletinDetail(item) {
      this.$router.push({
        name: 'BulletinDetail',
        params: { bulletinId: item.bulletinId, bulletinType: item.bulletinType }
      })
    },
    async getList() {
      try {
        this.loading = true;
        let { rows } = await listHomeBulletin({
          entity: {},
          pageNum: 1,
          pageSize: 10
        })
        this.bulletinList = rows || [];
        this.loading = false;
      } catch (e) {
        this.bulletinList = [];
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hom-row-box {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0 30px;
  margin-bottom: 20px;
  background: #ffffff;
}

.home-row {
  border-bottom: 1px solid $borderColor;
  padding: 20px 0;

  .home-row-left {

    .row-classify {
      text-align: center;
      font-size: 16px;
    }

    .row-time {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      margin-top: 10px;

      span {
        display: inline-block;
        text-align: center;
        flex-grow: 1;
      }

      .row-time-space {
        width: 20px;
        flex-grow: 0;
      }
    }
  }

  .home-row-right {
    padding-left: 25px;
    border-left: 1px solid $borderColor;

    .row-notice-title {
      font-size: 16px;
      cursor: pointer;

      &:hover {
        color: #{'var(--theme)'}
      }
    }

    .row-notice-info {
      font-size: 14px;
      color: #555555;
      margin-top: 15px;
    }
  }
}

.mobile.hom-row-box {
  padding: 0 10px;

  .home-row-left {
    .row-time {
      font-size: 12px !important;

      .row-time-space {
        width: 10px;
      }
    }
  }

  .home-row-right {
    padding-left: 5px;

    .row-notice-title {
      font-size: 14px;
    }

    .row-notice-info {
      font-size: 12px;
    }
  }
}

.login-box-card {

  .login-title {
    color: #{'var(--theme)'};
    font-size: 20px;
    text-align: center;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .o-line-1, .o-line-2 {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #{'var(--theme)'};
    position: relative;

    &:before {
      display: block;
      content: "";
      width: 60px;
      height: 2px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .o-line-1 {
    margin-right: 10px;

    &:before {
      background-image: linear-gradient(to left, #{'var(--theme)'} 0%, #ffffff 100%);
      left: -60px;
    }
  }

  .o-line-2 {
    margin-left: 10px;

    &:before {
      background-image: linear-gradient(to right, #{'var(--theme)'} 0%, #ffffff 100%);
      left: 6px;
    }
  }

  .login-list {
    margin: 15px auto 0;

    li {
      margin-bottom: 20px;
      cursor: pointer;

      img {
        width: 100%;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
