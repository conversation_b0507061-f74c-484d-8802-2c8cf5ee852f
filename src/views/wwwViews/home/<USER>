<template>
  <div class="login-bg" :style="{'--color1': color1, '--color2':color2}">
    <div class="login-icon-box">
      <svg-icon :icon-class="icon" class-name="login-icon"></svg-icon>
    </div>
    <p class="login-text">
      <span>{{ title }}</span>
      <!--<svg-icon icon-class="to-right" class-name="login-icon-left"></svg-icon>-->
    </p>
  </div>

</template>

<script>

export default {
  name: 'LoginBg',
  props: ['icon', 'color1', 'color2', 'title'],
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.login-bg {
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: -webkit-linear-gradient(to right, #{'var(--color1)'}, #{'var(--color2)'});
  background: -moz-linear-gradient(to right, #{'var(--color1)'}, #{'var(--color2)'});
  background: linear-gradient(to right, #{'var(--color1)'}, #{'var(--color2)'});

  .login-icon-box {
    font-size: 40px;
    margin-bottom: 10px;
    background: #ffffff;
    width: 65px;
    height: 65px;
    border-radius: 50%;
    box-shadow: -4px 4px 10px 0px rgba(0, 0, 0, .5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #{'var(--color1)'};
  }

  .login-text {
    font-size: 20px;
    color: #ffffff;
    display: flex;
    align-items: center;
    text-shadow: 0 0 1px #fff, -1px 2px 2px rgba(0, 0, 0, 0.5);
  }

  .login-icon-left {
    font-size: 32px;
  }
}
</style>
