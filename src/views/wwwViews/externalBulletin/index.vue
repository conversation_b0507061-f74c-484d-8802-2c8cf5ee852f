<template>
  <div class="container mt30 clearfix">

    <div class="filter-wrap" :class="classObj">
      <div class="filter-box">
        <div class="filter-title">类型</div>
        <nav-tab
          :tabArray="typeList"
          :option="{
          value: 'value',
          label: 'label',
        }"
          v-model="queryParams.type"
          @tab-click="handleQuery"
        >
        </nav-tab>
      </div>
    </div>

    <ul class="notice-wrap" :class="classObj" v-loading="loading">
      <li v-for="(item,k) in tableData" :key="k">
        <el-row>
          <el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20">
            <h3 class="over-ellipsis pointer">
            <span :style="{'--theme': theme}" class="hover" @click="handleDetail(item)">
              {{ item.title }}
            </span>
            </h3>
            <el-row :gutter="10" class="notice-info">
              <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="11" class="over-ellipsis">
                发布时间：{{ item.webdate | formatTime('YYYY/MM/DD') }}
              </el-col>
            </el-row>
          </el-col>
          <el-col :xs="1" :sm="4" :md="4" :lg="4" :xl="4" class="text-right hidden-xs-only">
            <el-button type="primary" plain style="padding: 15px 40px;" @click="handleDetail(item)">查看详情</el-button>
          </el-col>
        </el-row>
      </li>
    </ul>

    <el-empty description="暂无数据" v-show="total===0"></el-empty>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { searchJX } from '@/api/home';
import { mapGetters } from 'vuex';

export default {
  name: 'ExternalBulletin',
  data() {
    return {
      loading: false,
      typeList: [
        { label: '采购公告', value: 1 },
        { label: '变更公告', value: 2 },
        { label: '答疑澄清', value: 3 },
        { label: '结果公示', value: 4 },
        { label: '单一来源公示', value: 5 },
        { label: '合同公示', value: 6 },
        { label: '采购意向', value: 7 },
        { label: '中小企业执行情况', value: 8 },
        { label: '政府采购合同变更公示', value: 9 }
      ],
      // 总条数
      total: 0,
      // 公告表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.queryParams.type = this.typeList[0].value;
    this.getList();
  },
  methods: {
    handleDetail(row) {
      let url = `https://www.jxsggzy.cn/${row.linkurl}`;
      window.open(url, '_blank');
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询公告列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await searchJX(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$filterItemHeight: 28px;
.filter-wrap {
  border: 1px solid $borderColor;
  background: #fff;
  padding: 0 15px;
  margin-bottom: 20px;
  font-size: 14px;

  .filter-title {
    color: #AAAAAA;
    height: $filterItemHeight;
    line-height: $filterItemHeight;
    margin: 0 20px 0 0;
    flex-shrink: 0;
  }

  &.mobile {
    padding: 0 5px;
  }
}

.filter-box {
  padding: 8px 5px;
  display: flex;
  border-bottom: 1px dashed $borderColor;

  &:last-child {
    border-bottom: 0;
  }
}

.notice-wrap {
  li {
    background-color: #ffffff;
    border-bottom: 1px solid $borderColor;
    padding: 20px 20px;
    font-size: 16px;

    h3 {
      font-size: 16px;
      margin-bottom: 15px;
    }

    .hover {
      &:hover {
        color: #{'var(--theme)'}
      }
    }

    .notice-info {
      color: #555555;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.mobile.notice-wrap {
  li {
    padding: 10px 15px;
  }
}
</style>
