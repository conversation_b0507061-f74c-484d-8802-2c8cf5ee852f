<template>
  <div class="container pt40 pb40">
    <el-tabs :tab-position="device === 'mobile'?'top':'left'" v-model="activeName">
      <el-tab-pane label="产品介绍" name="1">
        <product-introduction/>
      </el-tab-pane>
      <el-tab-pane label="交易系统" name="2">
        <trading-system/>
      </el-tab-pane>
      <el-tab-pane label="服务系统" name="3">
        <service-system/>
      </el-tab-pane>
      <el-tab-pane label="监管系统" name="4">
        <regulatory-system/>
      </el-tab-pane>
      <el-tab-pane label="运营商" name="5">
        <operator/>
      </el-tab-pane>
      <el-tab-pane label="优势特点" name="6">
        <advantage/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import advantage from '@/views/wwwViews/product/advantage.vue'
import productIntroduction from '@/views/wwwViews/product/productIntroduction.vue'
import regulatorySystem from '@/views/wwwViews/product/regulatorySystem.vue'
import operator from '@/views/wwwViews/product/operator.vue'
import serviceSystem from '@/views/wwwViews/product/serviceSystem.vue'
import tradingSystem from '@/views/wwwViews/product/tradingSystem.vue'

export default {
  name: 'Index',
  components: {
    advantage,
    productIntroduction,
    regulatorySystem,
    operator,
    serviceSystem,
    tradingSystem
  },
  data() {
    return {
      activeName: '1'
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    let type = this.$route.query.type;
    this.activeName = type || '1';
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
