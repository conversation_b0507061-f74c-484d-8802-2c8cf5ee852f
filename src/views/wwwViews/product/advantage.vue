<template>
  <div :style="{'--theme': theme}">
    <div class="module-characteristic">
      <div class="container pb40">
        <div class="module-title">
          安全 高效 便捷 合法
        </div>
        <div class="home-info-text">
          <p>平台上各个应用以微服务构建，支持灵活组合无缝链接；用户自主按需选用，构建自己适用的采购电子化系统。平台已通过三星认证，省市各级公共资源交易中心、大型企业和招标代理机构使用“易建采”可以作为公共资源交易平台的第三方交易系统，与当地公共资源交易服务平台对接；交易数据全程留痕不可更改，数据、系统和网络安全全面保证，能抵御专业级黑客的攻击。</p>
        </div>
        <div class="home-info-img">
          <ul class="characteristic-list">
            <li v-for="(item,index) in characteristicList" :key="index" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
              <div class="icon-box">
                <svg-icon :icon-class="item.icon" class-name="characteristic-icon"/>
              </div>
              <h4>{{ item.title }}</h4>
              <p>{{ item.content }}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="module-diversification">
      <div class="container pt40 pb40">
        <div class="module-title">
          多元化采购及其管理
        </div>
        <div class="home-info-text">
          <p>产品库、供应商库、专家库，三库紧关联，方便用户查产品、找企业、寻商机、抽专家、获咨询。</p>
          <p>功能简单实用，操作界面友好，操作便捷；一个界面单次输入，其他页面重复回显，不增加工作量，同时减少差错。</p>
          <p>平台不仅满足国家标准公开和邀请招标采购的规范要求，还支持多种采购方式（政府采购非招标采购方式：询价、单一来源、竞争性谈判、竞争性磋商、框架协议-网上商城、合作创新采购；企业采购非招标采购方式：谈判采购、询比采购、竞价采购、直接采购、框架协议），能灵活适应所有业务场景：既能开展法定招标业务，也支持开展其他业务，便于采购人根据项目性质和要求定制化完成招采工作。</p>
          <p>平台功能支持自定义管理，包括采购预算、项目计划、前期调研等。</p>
        </div>
        <div class="home-info-img text-center">
          <el-image :src="require('@/assets/images/home_8.png')" fit="fill"></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { addAnimateCSS, removeAnimateCSS } from '@/utils'

export default {
  name: 'Index',
  data() {
    return {
      characteristicList: [
        { icon: 'bid', title: '招标及非招标方式采购', content: '公开招标，邀请招标' },
        { icon: 'auxiliary', title: '交易辅助', content: '公告发布工具、在线售卖标书、开标预告、归档管理' },
        { icon: 'operate', title: '交易资源', content: '供应商库、专家库、采购商库' },
        { icon: 'price', title: '交易费用', content: '标书费管理、保证金管理、服务费管理' },
        { icon: 'web', title: '网站门户', content: '导航配置、页面配置、公告发布' },
        { icon: 'statistics', title: '统计分析', content: '招标帐台、费用统计' },
        { icon: 'tool', title: '办公辅助', content: '业务审批、事项审批、文档管理、会议室预定' },
        { icon: 'expert', title: '专家服务', content: '专家录入、专家推荐、专家库管理' },
        { icon: 'set', title: '系统管理', content: '项目配置、流程配置、角色权限、公司配置' },
        { icon: 'person', title: '我的信息', content: '个人信息、CA证书、电子签章' },
        { icon: 'swagger', title: '后台管理', content: '项目根据、进度查看、痕迹打印' },
        { icon: 'technology', title: '平台服务', content: '平台采购、业务咨询、企业入驻' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
  },
  methods: {
    handleMouseEnter($event) {
      // 鼠标移入时的处理逻辑
      addAnimateCSS($event.target.querySelector('.characteristic-icon'), 'rotateIn');
      addAnimateCSS($event.target.querySelector('h4'), 'fadeInLeft');
      addAnimateCSS($event.target.querySelector('p'), 'fadeInRight');
    },
    handleMouseLeave($event) {
      // 鼠标移出时的处理逻辑
      removeAnimateCSS($event.target.querySelector('.characteristic-icon'), 'rotateIn');
      removeAnimateCSS($event.target.querySelector('h4'), 'fadeInLeft');
      removeAnimateCSS($event.target.querySelector('p'), 'fadeInRight');
    }
  }
}
</script>

<style lang="scss" scoped>
.characteristic-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  border-bottom: 1px solid $borderColor;
  border-right: 1px solid $borderColor;

  li {
    width: calc(100% / 4);
    padding: 20px;
    border-top: 1px solid $borderColor;
    border-left: 1px solid $borderColor;
    transition: background-color 0.6s;

    .icon-box {
      text-align: center;
      margin-bottom: 10px;

      .characteristic-icon {
        font-size: 36px;
        color: #{'var(--theme)'};
      }
    }

    h4 {
      font-size: 16px;
      color: #{'var(--theme)'};
      text-align: center;
      margin-bottom: 10px;
    }

    p {
      line-height: 1.5;
      font-size: 14px;
      text-align: center;
    }

    &:hover {
      background: $colorPrimary;

      .characteristic-icon {
        color: #FFFFFF;
      }

      h4 {
        color: #FFFFFF;
      }

      p {
        color: #FFFFFF;
      }
    }
  }
}
</style>
