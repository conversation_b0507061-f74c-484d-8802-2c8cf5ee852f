<template>
  <div :style="{'--theme': theme}">
    <div class="pb40">
      <div class="container">
        <div class="module-title">
          业务框架
        </div>
        <el-row :gutter="30">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="text-center">
            <el-image :src="require('@/assets/images/home_3.png')" fit="fill" style="max-width: 260px;"></el-image>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <div class="home-info-text">
              <p>交易系统：融合了采购人、供应商、评标专家等多种角色，实现了从招、投、开、评、定标的全流程无纸化操作。</p>
              <p>服务系统：为交易系统实现信息发布，数据对接，检测认证以及对评标专家的培训考核并为交易系统和监管系统做政策导向。</p>
              <p>监管系统：为省、市、区监督机构提供各交易系统的招标投标数据，实时监控、预警交易项目。</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="pt40 pb40">
      <div class="container">
        <div class="module-title">
          数据流转结构
        </div>
        <div class="home-info-img text-center">
          <el-image :src="require('@/assets/images/home_42.png')" fit="fill"></el-image>
        </div>
      </div>
    </div>

    <div class="pt40 pb40">
      <div class="container">
        <div class="module-title">
          易建采招标投标系统交易平台三星认证优化服务功能
        </div>
        <div class="home-info-img text-center">
          <el-image :src="require('@/assets/images/home_43.png')" fit="fill" style="width: 80%;"></el-image>
          <el-image :src="require('@/assets/images/home_44.png')" fit="fill" class="mt20"></el-image>
        </div>
      </div>
    </div>

    <div class="pt40 pb40">
      <div class="container">
        <el-row :gutter="30">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="text-center">
            <el-image :src="require('@/assets/images/home_4.png')" fit="fill"></el-image>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="text-center">
            <el-image :src="require('@/assets/images/home_5.png')" fit="fill"></el-image>
          </el-col>
        </el-row>
        <el-row :gutter="30" class="mt40">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="text-center">
            <el-image :src="require('@/assets/images/home_6.png')" fit="fill"></el-image>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="text-center">
            <el-image :src="require('@/assets/images/home_7.png')" fit="fill"></el-image>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>
