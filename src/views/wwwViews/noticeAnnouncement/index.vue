<template>
  <div class="container mt30 clearfix">
    <el-row type="flex" justify="center" class="mb20">
      <el-col :xs="20" :sm="16" :md="14" :lg="12" :xl="12">
        <el-input
          placeholder="标题"
          prefix-icon="el-icon-search"
          v-model="queryParams.noticeTitle"
          @keyup.enter.native="handleQuery">
          <el-button slot="append" type="primary" @click="handleQuery">搜索</el-button>
        </el-input>
      </el-col>
    </el-row>

    <div class="filter-wrap" :class="classObj">
      <div class="filter-box">
        <div class="filter-title">类型</div>
        <nav-tab
          :tabArray="sys_notice_type"
          :option="{
          value: 'value',
          label: 'label',
        }"
          v-model="queryParams.noticeType"
          @tab-click="handleQuery"
        >
        </nav-tab>
      </div>
    </div>

    <ul class="notice-wrap" :class="classObj" v-loading="loading">
      <li v-for="(item,k) in noticeList" :key="k">
        <el-row>
          <el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20">
            <h3 class="over-ellipsis pointer">
            <span :style="{'--theme': theme}" class="hover" @click="handleDetail(item)">
               <el-tag type="info">{{ noticeTypeFormat(item.noticeType) }}</el-tag>
              {{ item.noticeTitle }}
            </span>
            </h3>
            <el-row :gutter="10" class="notice-info">
              <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="11" class="over-ellipsis">
                发布时间：{{ item.createTime | formatTime('YYYY/MM/DD HH:mm:ss') }}
              </el-col>
            </el-row>
          </el-col>
          <el-col :xs="1" :sm="4" :md="4" :lg="4" :xl="4" class="text-right hidden-xs-only">
            <el-button type="primary" plain style="padding: 15px 40px;" @click="handleDetail(item)">查看详情</el-button>
          </el-col>
        </el-row>
      </li>
    </ul>

    <el-empty description="暂无数据" v-show="total===0"></el-empty>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listNotice } from '@/api/system/notice';
import { mapGetters } from 'vuex';

export default {
  name: 'NoticeAnnouncement',
  data() {
    return {
      loading: false,
      sys_notice_type: [],
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        noticeType: null,
        status: '0'
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getDicts('sys_notice_type').then(res => {
      let list = this.initDict(res.data || []);
      this.sys_notice_type = list.filter(item => item.value !== '1');
      let noticeType = this.$route.query.noticeType;
      if (noticeType) {
        this.queryParams.noticeType = noticeType;
      } else {
        this.queryParams.noticeType = this.sys_notice_type.length > 0 ? this.sys_notice_type[0].value : null;
      }
      this.getList();
    })
  },
  methods: {
    noticeTypeFormat(value) {
      return this.selectDictLabel(this.sys_notice_type, value);
    },
    handleDetail(row) {
      this.$router.push({
        name: 'NoticeAnnouncementDetail',
        params: { noticeId: row.noticeId }
      })
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询公告列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await listNotice(this.queryParams);
        this.noticeList = rows;
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$filterItemHeight: 28px;
.filter-wrap {
  border: 1px solid $borderColor;
  background: #fff;
  padding: 0 15px;
  margin-bottom: 20px;
  font-size: 14px;

  .filter-title {
    color: #AAAAAA;
    height: $filterItemHeight;
    line-height: $filterItemHeight;
    margin: 0 20px 0 0;
    flex-shrink: 0;
  }

  &.mobile {
    padding: 0 5px;
  }
}

.filter-box {
  padding: 8px 5px;
  display: flex;
  border-bottom: 1px dashed $borderColor;

  &:last-child {
    border-bottom: 0;
  }
}

.notice-wrap {
  li {
    background-color: #ffffff;
    border-bottom: 1px solid $borderColor;
    padding: 20px 20px;
    font-size: 16px;

    h3 {
      font-size: 16px;
      margin-bottom: 15px;
    }

    .hover {
      &:hover {
        color: #{'var(--theme)'}
      }
    }

    .notice-info {
      color: #555555;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.mobile.notice-wrap {
  li {
    padding: 10px 15px;
  }
}
</style>
