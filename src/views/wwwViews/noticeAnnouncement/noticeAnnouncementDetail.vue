<template>
  <div class="container mt20 mb20">
    <div class="notice-detail-wrap overflow" :class="classObj">
      <h1 class="notice-title">{{ detail.noticeTitle }}</h1>
      <p class="notice-time">发布时间：{{ detail.createTime | formatTime('YYYY年MM月DD日 HH:mm:ss') }}</p>

      <div class="text-group overflow">
        <div class="text-content">
          <div style="min-height: 400px;border: none;">
            <Tinymce v-model="detail.noticeContent" :readonly="true" class="tinymce-view"/>
            <div class="mt10" v-if="detail.noticeFiles&&detail.noticeFiles.length>0">
              <div class="mb5">附件：</div>
              <file-list-view :fileList="detail.noticeFiles" :handleDown="downFile"/>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import { getNotice } from '@/api/system/notice';
import { mapGetters } from 'vuex';

export default {
  name: 'NoticeAnnouncementDetail',
  data() {
    return {
      noticeId: null,
      detail: {}
    }
  },
  created() {
    this.noticeId = this.$route.params.noticeId
    this.getDetail();
  },
  computed: {
    ...mapGetters([
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    downFile(fileKey) {
      this.$download.downloadFileByPublic(fileKey)
    },
    getDetail() {
      getNotice(this.noticeId).then(response => {
        this.detail = response.data;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/noticeDetail.scss";
</style>
