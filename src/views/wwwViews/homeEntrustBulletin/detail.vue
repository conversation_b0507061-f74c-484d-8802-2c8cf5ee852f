<template>
  <div class="container mt20 mb20 overflow">
    <div class="container notice-detail-wrap overflow" :class="classObj">
      <h1 class="notice-title">{{ detail.bulletinName }}</h1>
      <p class="notice-time">发布时间：{{ detail.auditTime | formatTime('YYYY年MM月DD日 HH:mm:ss') }}</p>

      <div class="text-group overflow">
        <div class="text-content">
          <div style="min-height: 400px;border: none;">
            <Tinymce v-model="detail.bulletinText" :readonly="true" class="tinymce-view"/>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import { listEntrustBulletinByHome } from '@/api/entrustBulletin';
import { mapGetters } from 'vuex';

export default {
  name: 'HomeEntrustBulletinDetail',
  data() {
    return {
      id: null,
      detail: {}
    }
  },
  created() {
    this.id = this.$route.params.id
    this.getDetail();
  },
  computed: {
    ...mapGetters([
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    getDetail() {
      listEntrustBulletinByHome({ id: this.id }).then(response => {
        let { data } = response;
        this.detail = data.records[0] || {};
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/noticeDetail.scss";
</style>
