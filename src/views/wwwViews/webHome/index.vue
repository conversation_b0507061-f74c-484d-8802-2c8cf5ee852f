<template>
  <div :style="{'--theme': theme}">
    <el-carousel
      :height="bannerHeight"
      class="banner"
      arrow="never"
    >
      <el-carousel-item v-for="(item,k) in bannerList" :key="k">
        <el-image class="img-w img-h" :src="item.imgUrl" fit="fill">
          <div slot="error" class="image-slot fontSize30">
            <i class="el-icon-picture-outline"></i>
          </div>
          <div slot="placeholder" class="image-slot fontSize16">
            加载中<span class="dot">...</span>
          </div>
        </el-image>
      </el-carousel-item>
    </el-carousel>

    <div class="pt40 pb40">
      <div class="container">
        <div class="module-title">产品优势</div>
        <div>
          <ul class="advantage-list">
            <li class="pointer" @click="toProduct('2')">
              <svg-icon icon-class="transaction" class-name="advantage-icon"/>
              <p class="advantage-title">交易系统</p>
              <p class="advantage-title-en">— Trading system —</p>
            </li>
            <li class="pointer" @click="toProduct('3')">
              <svg-icon icon-class="monitor" class-name="advantage-icon"/>
              <p class="advantage-title">服务系统</p>
              <p class="advantage-title-en">— Service system —</p>
            </li>
            <li class="pointer" @click="toProduct('4')">
              <svg-icon icon-class="operate" class-name="advantage-icon"/>
              <p class="advantage-title">监管系统</p>
              <p class="advantage-title-en">— Supervision system —</p>
            </li>
            <li class="pointer" @click="toProduct('5')">
              <svg-icon icon-class="tree" class-name="advantage-icon"/>
              <p class="advantage-title">运营商</p>
              <p class="advantage-title-en">— Operator —</p>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="module-company pt40 pb40">
      <div class="container">
        <div class="module-title">
          公司简介
        </div>
        <p class="company-profile">
          易建采电子招标投标平台是集PC、APP、微信于一体的B2B、B2M集采与运营分级管理，采购人、供应商信用管理与金融创新相融合的互联网平台。我们着力打破行业传统，“降成本、控风险、提管理”，塑造“高效、公正、阳光”的新型互联网采购生态系统，以货物、工程、服务的公开招标采购管理为基础，政府采购非招标方式（竞争性谈判、竞争性磋商、询价、单一来源、框架协议-电子商城、创新合作采购），以及企业采购非招标方式（竞价采购、谈判采购、询比采购、直接采购、框架协议采购）管理及云服务并存，提供全场景采购供应链整体解决方案……
        </p>
        <div class="text-center">
          <el-button type="primary" round @click="toAboutUs">了解我们更多 >></el-button>
        </div>
      </div>
    </div>

    <div class="pt40 pb40">
      <div class="container">
        <div class="module-title">新闻动态</div>
        <div>
          <el-tabs v-model="queryParams.noticeType" @tab-click="handleQuery">
            <template v-for="(item,index) in sys_notice_type">
              <el-tab-pane :label="item.label" :name="item.value" :key="index">
                <ul class="news-list animate__animated animate__lightSpeedInRight" v-if="item.value===queryParams.noticeType">
                  <li v-for="(v,key) in noticeList" :key="key" class="hover">
                    <div class="news-time">
                      <div class="news-time-1">{{ v.createTime | formatTime('DD') }}</div>
                      <div class="news-time-2">{{ v.createTime | formatTime('YYYY-MM') }}</div>
                    </div>
                    <p class="ellipsis-more-2 news-text">
                      <span class="pointer" @click="toNewsDetail(v)">{{ v.noticeTitle }}</span>
                    </p>
                  </li>
                </ul>
              </el-tab-pane>
            </template>
          </el-tabs>
          <div class="text-center mt30">
            <el-button type="primary" round @click="toNews">查看更多 >></el-button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fileDownByPublic, getFileListByPublic } from '@/api/file';
import { listNotice } from '@/api/system/notice'

export default {
  name: 'Index',
  data() {
    return {
      bannerList: [],
      sys_notice_type: [],
      noticeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        noticeTitle: null,
        noticeType: null,
        status: '0'
      }
    }
  },
  computed: {
    ...mapGetters([
      'bannerHeight',
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getBannerList();
    this.getDicts('sys_notice_type').then(res => {
      let list = this.initDict(res.data || []);
      this.sys_notice_type = list.filter(item => item.value !== '1');
      let noticeType = this.$route.query.noticeType;
      if (noticeType) {
        this.queryParams.noticeType = noticeType;
      } else {
        this.queryParams.noticeType = this.sys_notice_type.length > 0 ? this.sys_notice_type[0].value : null;
      }
      this.getList();
    })
  },
  methods: {
    toProduct(type) {
      this.$router.push({ name: 'Product', query: { type }})
    },
    toAboutUs() {
      this.$router.push({ name: 'AboutUs' })
    },
    toNews() {
      this.$router.push({ name: 'NoticeAnnouncement' })
    },
    toNewsDetail(row) {
      this.$router.push({
        name: 'NoticeAnnouncementDetail',
        params: { noticeId: row.noticeId }
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询公告列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows } = await listNotice(this.queryParams);
        this.noticeList = rows;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    },
    async getBannerList() {
      try {
        let { data } = await getFileListByPublic({ fileTypeName: 'carousel' });
        this.bannerList = data;
        this.bannerList.forEach(item => {
          this.getImgUrl(item.fileCode).then(res => {
            this.$set(item, 'imgUrl', res);
          })
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByPublic(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.advantage-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  li {
    width: calc(100% / 4);
    padding: 30px;
    text-align: center;
    transition: all 0.6s;

    .advantage-icon {
      font-size: 70px;
      color: #{'var(--theme)'};
    }

    .advantage-title {
      line-height: 1.5;
      font-size: 22px;
      margin-top: 20px;
      text-align: center;
    }

    .advantage-title-en {
      color: #848484;
      font-size: 14px;
      margin-top: 2px;
    }

    &:hover {
      transform: scale(1.2, 1.2);
      position: relative;
      z-index: 9;
    }
  }
}

.module-company {
  background-color: #f2f2f2;
  background-image: url("../../../assets/images/home_46.png");
  background-repeat: no-repeat;
  background-size: cover;

  .company-profile {
    text-indent: 2em;
    font-size: 20px;
    line-height: 1.7;
    text-align: left;
    margin-bottom: 50px;
  }
}

.news-list {
  li {
    padding: 10px 20px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    align-items: center;

    .news-time {
      text-align: center;
      margin-right: 30px;

      .news-time-1 {
        font-size: 20px;
        line-height: 1;
        position: relative;

        &:after {
          content: '';
          display: block;
          width: 50%;
          height: 1px;
          background: #333333;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .news-time-2 {
        font-size: 14px;
        margin-top: 5px;
      }
    }

    .news-text {
      flex: 1;
      font-size: 16px;
    }

    &:hover {
      .news-time-1 {
        &:after {
          background: $colorPrimary;
        }
      }
    }
  }
}

@media (max-width: 767px) {
  .advantage-list {

    li {
      width: calc(100% / 2);
    }
  }
}
</style>
