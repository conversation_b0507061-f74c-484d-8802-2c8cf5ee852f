<template>
  <ul class="bulletin-wrap" :class="classObj">
    <li v-for="(item,k) in list" :key="k">
      <el-row>
        <el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20">
          <h3 class="over-ellipsis pointer">
            <div :style="{'--theme': theme}" class="hover" @click="toBulletinDetail(item)">
              <el-tag type="info">{{ item.bulletinTypeName }}</el-tag>
              {{ item.bulletinName }}
            </div>
          </h3>
          <el-row :gutter="10" class="bulletin-info">
            <!--            <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="11" class="over-ellipsis">-->
            <!--              采购人：{{ item.tenderName }}-->
            <!--            </el-col>-->
            <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7" v-if="item.bulletinType==='bidding_announcement'" class="over-ellipsis">
              报名截止时间：{{ item.registrationEndTime | formatTime('YYYY/MM/DD HH:mm:ss') }}
            </el-col>
            <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="6" class="over-ellipsis">
              发布时间：{{ item.reviewTime | formatTime('YYYY/MM/DD HH:mm:ss') }}
            </el-col>
          </el-row>
        </el-col>
        <el-col :xs="1" :sm="4" :md="4" :lg="4" :xl="4" class="text-right hidden-xs-only">
          <el-button type="primary" plain style="padding: 15px 40px;" @click="toBulletinDetail(item)">查看详情</el-button>
        </el-col>
      </el-row>
    </li>
  </ul>
</template>

<script>

import { mapGetters } from 'vuex'
import { formatTime } from '@/utils/filters';

export default {
  name: 'BulletinList',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    formatTime,
    toBulletinDetail(item) {
      this.$router.push({
        name: 'BulletinDetail',
        params: { bulletinId: item.bulletinId, bulletinType: item.bulletinType }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bulletin-wrap {
  li {
    background-color: #ffffff;
    border-bottom: 1px solid $borderColor;
    padding: 20px 20px;
    font-size: 16px;

    h3 {
      font-size: 16px;
      margin-bottom: 15px;
    }

    .hover {
      display: inline-block;

      &:hover {
        color: #{'var(--theme)'}
      }
    }

    .bulletin-info {
      color: #555555;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.mobile.bulletin-wrap {
  li {
    padding: 10px 15px;
  }
}
</style>
