<template>
  <div class="container mt30 clearfix">

    <el-row type="flex" justify="center" class="mb20">
      <el-col :xs="20" :sm="16" :md="14" :lg="12" :xl="12">
        <el-input
          placeholder="公告名称"
          prefix-icon="el-icon-search"
          v-model="queryParams.entity.bulletinName"
          @keyup.enter.native="handleQuery">
          <el-button slot="append" type="primary" @click="handleQuery">搜索</el-button>
        </el-input>
      </el-col>
    </el-row>

    <div class="filter-wrap" :class="classObj">
      <div class="filter-box">
        <div class="filter-title">{{ selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod') }}</div>
        <nav-tab
          :tabArray="purchaseMethodList"
          :option="{
          value: 'purchaseMethodType',
          label: 'purchaseMethodName',
        }"
          v-model="queryParams.entity.purchaseMethodType"
          @tab-click="handlePurchaseMethod"
        >
        </nav-tab>
      </div>
      <div class="filter-box">
        <div class="filter-title">公告类型</div>
        <nav-tab
          :tabArray="bulletinTypeList"
          :option="{
          value: 'anType',
          label: 'anName',
        }"
          v-model="queryParams.entity.bulletinType"
          @tab-click="handleBulletinType"
        >
        </nav-tab>
      </div>
    </div>

    <div style="background: #ffffff">
      <bulletin-list v-loading="loading" :list="tableData"></bulletin-list>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <el-empty description="暂无数据" v-show="total===0"></el-empty>
    </div>

  </div>
</template>

<script>
import bulletinList from './bulletinList.vue'
import { getHomePurchaseConfig, listHomeBulletin } from '@/api/home';
import { mapGetters } from 'vuex';

export default {
  name: 'HomeBulletin',
  dicts: ['sys_dict_translate'],
  components: {
    bulletinList
  },
  data() {
    return {
      purchaseMethodList: [],
      bulletinTypeList: [],
      queryParams: {
        entity: {
          bulletinName: null,
          purchaseMethodType: null,
          bulletinType: null
        },
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getPurchaseMethod()
  },
  methods: {
    async getPurchaseMethod() {
      try {
        let { data } = await getHomePurchaseConfig();
        let list = data || [];
        list.unshift({ purchaseMethodName: '全部', purchaseMethodType: null, bulletinKVList: [] })
        this.purchaseMethodList = list;
        this.queryParams.entity.purchaseMethodType = this.purchaseMethodList[0].purchaseMethodType;
        this.bulletinTypeList = this.getBulletinTypeList(this.queryParams.entity.purchaseMethodType);
        this.queryParams.entity.bulletinType = this.bulletinTypeList.length > 0 ? this.bulletinTypeList[0].anType : null;
        await this.getList();
      } catch (e) {
        throw new Error(e)
      }
    },
    getBulletinTypeList(purchaseMethodType) {
      let obj = this.purchaseMethodList.find(item => item.purchaseMethodType === purchaseMethodType);
      let arr = obj ? obj.bulletinKVList.filter(item => item.tag.includes('home_show')) : [];
      arr.unshift({ anName: '全部', anType: null })
      return arr;
    },
    handlePurchaseMethod(val) {
      this.bulletinTypeList = this.getBulletinTypeList(val);
      this.queryParams.entity.bulletinType = this.bulletinTypeList.length > 0 ? this.bulletinTypeList[0].anType : null;
      this.handleQuery();
    },
    handleBulletinType() {
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await listHomeBulletin(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.tableData = [];
        this.total = 0;
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$filterItemHeight: 28px;
.filter-wrap {
  border: 1px solid $borderColor;
  background: #fff;
  padding: 0 15px;
  margin-bottom: 20px;
  font-size: 14px;

  .filter-title {
    color: #AAAAAA;
    height: $filterItemHeight;
    line-height: $filterItemHeight;
    margin: 0 20px 0 0;
    flex-shrink: 0;
  }

  &.mobile {
    padding: 0 5px;
  }
}

.filter-box {
  padding: 8px 5px;
  display: flex;
  border-bottom: 1px dashed $borderColor;

  &:last-child {
    border-bottom: 0;
  }
}
</style>
