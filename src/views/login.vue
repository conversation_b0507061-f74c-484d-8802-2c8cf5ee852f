<template>
  <div class="login">
    <div class="login-form">
      <el-tabs v-model="loginType" @tab-click="handleClick">
        <el-tab-pane label="密码登录" name="PASSWORD">
          <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
            <!--<h3 class="title">用户登录</h3>-->
            <el-form-item prop="username">
              <el-input
                v-model.trim="loginForm.username"
                type="text"
                auto-complete="off"
                placeholder="账号"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="user"
                  class="el-input__icon input-icon"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="password"
                  class="el-input__icon input-icon"
                />
                <svg-icon
                  slot="suffix"
                  class="el-input__icon input-icon pointer"
                  :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
                  @click="showPwd"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaOnOff">
              <el-input
                v-model.trim="loginForm.code"
                auto-complete="off"
                placeholder="请输入右侧计算结果"
                style="width: 63%"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon
                  slot="prefix"
                  icon-class="validCode"
                  class="el-input__icon input-icon"
                />
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>

            <div>
              <el-checkbox
                v-model="loginForm.rememberMe"
                style="margin:0px 0px 25px 0px;"
              >
                记住密码
              </el-checkbox>
              <div style="float: right;" v-if="versionType==='wzlg'">
                <router-link class="link-type fontSize14" :to="'/modifyPassword'">忘记密码？</router-link>
              </div>
            </div>

            <el-form-item style="width:100%;">
              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                style="width:100%;"
                @click.native.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
              <div style="float: left;">
                <router-link class="link-type" :to="'/home'">返回首页</router-link>
              </div>
              <div style="float: right;" v-if="register">
                <router-link class="link-type" :to="'/register'">立即注册</router-link>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="短信登录" name="SMS" v-if="versionType==='jxzl'">
          <login-by-phone v-if="loginType==='SMS'"/>
        </el-tab-pane>
      </el-tabs>
    </div>

    <vue-particles
      class="particles-js"
      color="#409EFF"
      :particleOpacity="0.7"
      :particlesNumber="80"
      shapeType="circle"
      :particleSize="4"
      linesColor="#409EFF"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="150"
      :moveSpeed="3"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
    ></vue-particles>

    <modify-pwd
      ref="modifyPwd"
      @ok="handleOk"
    />

    <sys-lic-tip
      ref="sysLicTip"
      @ok="handleLicTipOk"
      @close="handleLicTipClose"
    />

    <sys-lic-up
      ref="sysLicUp"
      @ok="handleLicUpOk"
    />

  </div>
</template>

<script>
import { getCodeImg } from '@/api/login';
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/jsencrypt'
import loginByPhone from '@/views/loginByPhone.vue'
import modifyPwd from '@/views/modifyPwd.vue'
import sysLicUp from '@/views/sysLicUp.vue'
import sysLicTip from '@/views/sysLicTip.vue'

export default {
  name: 'Login',
  components: {
    loginByPhone,
    modifyPwd,
    sysLicUp,
    sysLicTip
  },
  data() {
    return {
      codeUrl: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的账号' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' }
        ],
        code: [
          { required: true, trigger: 'change', message: '请输入右侧计算结果' }
        ]
      },
      loading: false,
      // 验证码开关
      captchaOnOff: false,
      // 注册开关
      register: true,
      redirect: undefined,
      passwordType: 'password',
      loginType: 'PASSWORD'
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    handleLicUpOk() {
      this.resetForm('loginForm');
      this.getCode();
      this.getCookie();
    },
    handleLicTipOk() {
      this.$router.push({ path: this.redirect || '/index' })
    },
    handleLicTipClose() {
      this.$router.push({ path: this.redirect || '/index' })
    },
    handleOk() {
      this.resetForm('loginForm');
      this.getCode();
      this.getCookie();
    },
    handleClick() {
      if (this.loginType === 'PASSWORD') {
        this.resetForm('loginForm')
        this.getCode();
        this.getCookie();
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? false : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = 'data:image/gif;base64,' + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get('username');
      const password = Cookies.get('password');
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set('username', this.loginForm.username, { expires: 30 });
            Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove('username');
            Cookies.remove('password');
            Cookies.remove('rememberMe');
          }
          let { username, password, code, uuid } = this.loginForm;
          this.$store.dispatch('Login', { username, password, code, uuid, loginType: this.loginType })
            .then(async (res) => {
              this.loading = false;
              let licInfo = res.licInfo || {};
              if (res.needUpLic) {
                this.$refs.sysLicUp.show({
                  token: res.access_token,
                  sysCode: licInfo.sysCode
                });
                return
              }
              if (res.flag) {
                this.$refs.modifyPwd.show(res.access_token);
                return
              }
              if (res.showLicTip) {
                this.$refs.sysLicTip.show({
                  token: res.access_token,
                  sysCode: licInfo.sysCode,
                  customerExpireDate: licInfo.customerExpireDate
                });
                return
              }
              await this.$router.push({ path: this.redirect || '/index' })
            })
            .catch(() => {
              this.loading = false;
              if (this.captchaOnOff) {
                this.getCode();
              }
            });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  width: 100%;
  height: 100%;
  //background-image: url("../assets/images/login-background.jpg");
  background-image: linear-gradient(-180deg, #1a1454 0%, #0e81a5 100%);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.particles-js {
  width: 100%;
  height: calc(100% - 5px);
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #000000;
}

.login-form {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.75);
  width: 100%;
  max-width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}
</style>
