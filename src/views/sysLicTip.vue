<template>
  <div>
    <el-dialog
      title="系统提示"
      center
      width="90%"
      custom-class="maxW500"
      :visible.sync="isDialog"
      @close="closeTip"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="text-left text-danger mb30">
        <div class="mb10">
          系统将于{{ customerExpireDate }}到期，请及时延长系统使用时间，以免到期关闭系统对项目产生影响!
        </div>
        <div>系统识别码：{{ sysCode }}</div>
      </div>
      <div class="mb10">
        <el-checkbox v-model="noTipMonth">本月不再提示</el-checkbox>
      </div>
      <div>
        <el-checkbox v-model="noTipWeek">本周不再提示</el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="isDialog = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="submit"
          :loading="isLoading"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { closeLicTip } from '@/api/license'

export default {
  data() {
    return {
      isDialog: false,
      isLoading: false,
      token: null,
      customerExpireDate: null,
      sysCode: null,
      noTipMonth: false,
      noTipWeek: false
    };
  },
  methods: {
    closeTip() {
      this.isDialog = false;
      this.$emit('close');
    },
    show(data) {
      let { token, sysCode, customerExpireDate } = data || {};
      this.token = token;
      this.customerExpireDate = customerExpireDate;
      this.sysCode = sysCode;
      this.noTipMonth = false;
      this.noTipWeek = false;
      this.isDialog = true;
    },
    async submit() {
      try {
        this.isLoading = true;
        await closeLicTip({
          noTipMonth: this.noTipMonth,
          noTipWeek: this.noTipWeek,
          token: this.token
        });
        this.$message.success('提交成功');
        this.isLoading = false;
        this.isDialog = false;
        this.$emit('ok');
      } catch (e) {
        this.isLoading = false;
        throw new Error(e)
      }
    }
  }
};
</script>
