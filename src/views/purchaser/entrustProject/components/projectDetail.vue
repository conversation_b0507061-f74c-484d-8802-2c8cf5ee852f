<template>
  <div class="app-container">
    <el-alert
      class="mb10"
      v-if="auditStatus===3"
      title="审核未通过"
      type="error"
      :closable="false"
      :description="'不通过理由：'+reason"
      show-icon
    >
    </el-alert>

    <el-form ref="form" :model="form" :rules="rules" label-width="165px">
      <el-card
        header="项目信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="委托项目名称" prop="projectName">
              <el-input v-model.trim="form.projectName" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="标的类型" prop="industriesType">
              <el-select
                :disabled="isDisabled"
                v-model="form.industriesType"
                placeholder="请选择"
                class="block">
                <el-option
                  v-for="dict in dict.type.project_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="资金来源" prop="fundSource">
              <el-select
                v-model="form.fundSource"
                placeholder="请选择"
                class="block"
                :disabled="isDisabled"
              >
                <el-option
                  v-for="dict in dict.type.project_fund_source"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预算金额" prop="projectAmount">
              <el-input v-model.number="form.projectAmount" placeholder="请输入" :disabled="isDisabled">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="使用科室" prop="useDepartment">
              <el-input v-model.trim="form.useDepartment" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="使用科室代表" prop="representative">
              <el-input v-model.trim="form.representative" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
              prop="procurementMethod"
            >
              <el-select v-model="form.procurementMethod" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.entrust_purchase_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="指定代理机构" prop="agencyId">
              <el-select
                v-model="form.agencyId"
                class="block"
                :disabled="isDisabled || checkRole(['agency'])"
                clearable
                filterable
                placeholder="请选择代理机构"
                @change="changeAgency"
              >
                <el-option
                  v-for="dict in agencyList"
                  :key="dict.userId"
                  :label="dict.nickName"
                  :value="dict.userId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="是否采用评定分离法" prop="evaluationMethod">
              <el-radio-group v-model="form.evaluationMethod" :disabled="isDisabled">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="是否进口" prop="imPort">
              <el-radio-group v-model="form.imPort" :disabled="isDisabled">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card
        header="标段信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <template v-for="(bsItem,key) in form.agentSectionList">
          <el-row :gutter="20" :key="key">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item
                label="标段(包)名称"
                :prop="'agentSectionList.'+key+'.bidSectionName'"
                :rules="rules.bidSectionName"
              >
                <div style="display:flex;">
                  <el-input v-model.trim="bsItem.bidSectionName" placeholder="请输入" maxlength="100" :disabled="isDisabled"/>
                  <el-button
                    class="ml10"
                    size="mini"
                    icon="el-icon-delete"
                    type="danger"
                    plain
                    v-show="form.agentSectionList.length>1"
                    v-if="checkPermi(['entrust:project:add','entrust:project:edit'])&&auditStatus!==2"
                    @click="removeBidSection(form.agentSectionList,key)"
                  >删除
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <div class="text-center mb20" v-if="checkPermi(['entrust:project:add','entrust:project:edit'])&&auditStatus!==2">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="addBidSection(form.agentSectionList)"
            plain
          >
            添加标段(包)
          </el-button>
        </div>
      </el-card>

      <div class="text-center mt20 mb20">
        <el-button type="primary" @click="submitForm" v-if="!isEdit&&checkPermi(['entrust:project:add'])">保存</el-button>
      </div>

      <el-card
        header="文件"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="采购申请批复相关文件" prop="approvalDocumentsFileKey" :required="isEdit">
              <file-upload-single
                :fileSize="200"
                :showTip="true"
                :autoUpload="false"
                uploadName="approvalDocumentsFileKey"
                :params="{
                  fileLabel: '采购申请批复相关文件',
                  fileTypeName: 'entrust_approval_file',
                  oldFileKey: form.approvalDocumentsFileKey
                }"
                @onSuccess="handleUpload"
                :disabled="isDisabled||!isEdit"
              >
                <el-button slot="upload-btn" type="primary" size="mini" :disabled="isDisabled||!isEdit">上传</el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.approvalDocumentsFileKey"
                  @click="downFile(form.approvalDocumentsFileKey)"
                >下载
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="需求文件" prop="requirementsFileKey" :required="isEdit">
              <file-upload-single
                :fileSize="200"
                :showTip="true"
                :autoUpload="false"
                uploadName="requirementsFileKey"
                :params="{
                  fileLabel: '需求文件',
                  fileTypeName: 'entrust_requirements_file',
                  oldFileKey: form.requirementsFileKey
                }"
                @onSuccess="handleUpload"
                :disabled="isDisabled||!isEdit"
              >
                <el-button slot="upload-btn" type="primary" size="mini" :disabled="isDisabled||!isEdit">上传</el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.requirementsFileKey"
                  @click="downFile(form.requirementsFileKey)"
                >下载
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="其它附件" prop="otherFileKey">
              <file-upload-single
                :fileSize="200"
                :showTip="true"
                :autoUpload="false"
                uploadName="otherFileKey"
                :params="{
                  fileLabel: '其它附件',
                  fileTypeName: 'entrust_project_other_annex',
                  oldFileKey: form.otherFileKey
                }"
                @onSuccess="handleUpload"
                :disabled="isDisabled||!isEdit"
              >
                <el-button slot="upload-btn" type="primary" size="mini" :disabled="isDisabled||!isEdit">上传</el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.otherFileKey"
                  @click="downFile(form.otherFileKey)"
                >下载
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div class="text-center mt30 mb30">
        <el-button type="danger" @click="onDelete" v-if="isEdit&&checkPermi(['entrust:project:remove'])">删除</el-button>
        <el-button type="primary" @click="submitForm" v-if="!isEdit&&checkPermi(['entrust:project:add'])" :disabled="!isEdit">
          提交审核
        </el-button>
        <el-button type="primary" @click="submitForm" v-if="isEdit&&checkPermi(['entrust:project:edit'])&&auditStatus!==2">提交审核
        </el-button>
        <el-button
          type="primary"
          @click="handleAudit(2)"
          v-if="isEdit&&checkPermi(['entrust:project:audit'])&&auditStatus===1">
          审核通过
        </el-button>
        <el-button
          type="danger"
          @click="handleAudit(3)"
          v-if="isEdit&&checkPermi(['entrust:project:audit'])&&auditStatus===1">
          审核不通过
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  saveEntrustProject,
  upEntrustProject,
  queryEntrustProjectDetail,
  auditEntrustProject,
  delEntrustProject,
  fileUploadByEntrust,
  fileDeleteByEntrust
} from '@/api/entrustProject';
import reg from '@/utils/reg'
import { checkPermi, checkRole } from '@/utils/permission';
import { modifyFileName } from '@/utils';
import { mapGetters } from 'vuex';
import { listUser } from '@/api/system/user'

export default {
  name: 'ProjectDetail',
  dicts: ['project_fund_source', 'entrust_purchase_method', 'project_category', 'sys_dict_translate'],
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let validateUpload = (rule, value, callback) => {
      if (this.isEdit) {
        if (value) {
          callback()
        } else {
          callback(new Error('请上传'))
        }
      } else {
        callback()
      }
    };
    return {
      form: {},
      rules: {
        projectName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' },
          { max: 100, message: '最长100个字符', trigger: 'blur' }
        ],
        industriesType: [{ required: true, message: '请选择', trigger: 'change' }],
        fundSource: [{ required: true, message: '请选择', trigger: 'change' }],
        projectAmount: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ],
        evaluationMethod: [{ required: false, message: '请选择', trigger: 'change' }],
        imPort: [{ required: false, message: '请选择', trigger: 'change' }],
        procurementMethod: [{ required: true, message: '请选择', trigger: 'change' }],
        useDepartment: [{ required: true, message: '请输入', trigger: 'blur' }],
        representative: [{ required: true, message: '请选择', trigger: 'blur' }],
        agencyId: [{ required: true, message: '请选择', trigger: 'change' }],
        approvalDocumentsFileKey: [{ required: true, validator: validateUpload, trigger: 'change' }],
        requirementsFileKey: [{ required: true, validator: validateUpload, trigger: 'change' }],
        otherFileKey: [{ required: false, message: '请上传', trigger: 'change' }],
        bidSectionName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' },
          { max: 100, message: '最长100个字符', trigger: 'blur' }
        ]
      },
      auditStatus: null,
      yearMonthSplit: null,
      reason: null,
      projectCode: null,
      agencyList: []
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName'
    ]),
    isDisabled() {
      return !(checkPermi(['entrust:project:add', 'entrust:project:edit']) && this.auditStatus !== 2)
    }
  },
  created() {
    this.reset();
    this.getAgencyList();
    if (this.isEdit) {
      this.form.id = this.$route.params.id;
      this.queryDetail();
    }
  },
  methods: {
    checkPermi,
    checkRole,
    async getAgencyList() {
      try {
        let res = await this.getConfigKey('sys.agency.roleId');
        let roleId = Number(res.data);
        let orgCode = this.$cache.local.get('loginOrgCode');
        let { rows } = await listUser({ roleId, orgCode, status: '0' })
        let list = rows || [];
        this.agencyList = list.map(item => {
          return {
            nickName: item.nickName,
            userId: item.userId
          }
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    changeAgency(val) {
      let obj = this.agencyList.find(v => v.userId === val);
      this.form.agencyName = obj ? obj.nickName : null;
    },
    onDelete() {
      let tipHtml = `<p style="color: #ff4949;margin-bottom: 10px;font-size: 18px;">确定要删除该项目？</p>
                     <p style="color: #ff4949;">注意：该项目所有数据都将删除，并且无法恢复，请谨慎操作！！！</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delEntrustProject({ projectCode: this.projectCode });
          this.$message.success('删除成功');
          this.$modal.closeLoading();
          await this.$router.push({ name: 'EntrustProject' });
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    /** 项目审核 */
    handleAudit(status) {
      this.$prompt(status === 2 ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: status === 3,
        inputType: 'textarea',
        inputPlaceholder: status === 3 ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (status === 2) {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 100
        },
        inputErrorMessage: '请输入不通过理由，最多100个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await auditEntrustProject({
            id: this.form.id,
            status: status,
            reason: value
          })
          this.$modal.msgSuccess('审核成功');
          this.$modal.closeLoading();
          await this.$router.push({
            name: 'EntrustProject'
          });
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    removeBidSection(arr, index) {
      arr.splice(index, 1)
    },
    addBidSection(arr) {
      arr.push({ bidSectionName: `${this.prefixInteger(arr.length + 1, 2)}包` })
    },
    downFile(fileKey) {
      this.$download.downloadFileByEntrust(fileKey)
    },
    async handleUpload(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByEntrust({
          buyItemCode: this.form.projectCode,
          yearMonthSplit: this.yearMonthSplit,
          ...params,
          file: modifyFileName(params.file, params.fileLabel)
        });
        if (params.oldFileKey) {
          await fileDeleteByEntrust(params.oldFileKey);
        }
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (this.isEdit) {
              await upEntrustProject(this.form)
              this.$modal.msgSuccess('提交成功');
              await this.$router.push({
                name: 'EntrustProject'
              });
            } else {
              let { data } = await saveEntrustProject(this.form);
              this.$modal.msgSuccess('保存成功');
              await this.$router.push({
                name: 'EditEntrustProject',
                params: {
                  id: data
                }
              });
            }
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    async queryDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryEntrustProjectDetail(this.form.id);
        this.projectCode = data.projectCode;
        this.auditStatus = data.auditStatus;
        this.reason = data.reason;
        this.yearMonthSplit = data.yearMonthSplit;
        for (let key in this.form) {
          if (key !== 'agentSectionList') {
            this.form[key] = data[key];
          } else {
            this.form.agentSectionList = data.agentSectionList.map(item => {
              return {
                bidSectionName: item.bidSectionName,
                bidSectionCode: item.bidSectionCode
              }
            })
          }
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        projectName: '',
        industriesType: null,
        fundSource: null,
        projectAmount: null,
        evaluationMethod: null,
        imPort: null,
        procurementMethod: null,
        useDepartment: '',
        representative: null,
        agencyId: null,
        agencyName: null,
        approvalDocumentsFileKey: null,
        requirementsFileKey: null,
        otherFileKey: null,
        agentSectionList: [
          {
            bidSectionCode: null,
            bidSectionName: '01包'
          }
        ]
      }
      this.resetForm('form')
      if (this.$auth.hasRole('agency')) {
        this.form.agencyId = this.userId;
        this.form.agencyName = this.nickName;
      }
    },
    prefixInteger(num, n) {
      return (Array(n).join('0') + num).slice(-n);
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
