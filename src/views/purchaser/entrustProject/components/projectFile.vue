<template>
  <div v-loading="loading">
    <el-card header="项目文件" shadow="never" :body-style="{'padding': '15px 10px 10px 10px'}" class="card-box">
      <div slot="header" class="clearfix">
        <span>项目文件</span>
        <el-button
          style="float: right;"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(entrust_file_type_project,true,null,  null, null)"
          v-if="!isComplete&&checkPermi(['entrust:process:operate'])"
        >添加文件
        </el-button>
      </div>
      <el-table :data="projectFileList" border>
        <el-table-column label="序号" align="center" type="index" width="55"/>
        <el-table-column prop="fileTypeLabel" align="center" label="文件类型" width="180">
          <template v-slot:default="{row}">
            <dict-tag :options="entrust_file_type_project" :value="row.fileType" v-if="!row.disabled"/>
            <span v-else>{{ row.fileTypeLabel }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fileStoragePath" align="center" label="文件名">
          <template v-slot:default="{row}">
            {{ row.fileStoragePath || '/' }}
          </template>
        </el-table-column>
        <el-table-column prop="createAt" align="center" label="上传时间" width="160">
          <template v-slot:default="{row}">
            <span v-if="row.createAt">{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="240">
          <template v-slot:default="{row}">
            <el-button
              v-if="!row.disabled&&!isComplete&&checkPermi(['entrust:process:operate'])"
              type="text"
              icon="el-icon-upload"
              size="mini"
              @click="handleAdd(entrust_file_type_project, false,null,row.fileType, row.fileCode)"
            >
              上传
            </el-button>
            <el-button type="text" icon="el-icon-view" size="mini" v-if="row.fileCode" @click="previewFile(row.fileCode)">预览</el-button>
            <el-button type="text" icon="el-icon-download" size="mini" v-if="row.fileCode" @click="handleDown(row.fileCode)">下载
            </el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              size="mini"
              class="btn-text-danger"
              @click="handleRemove(row.fileCode)"
              v-if="row.fileCode&&checkPermi(['entrust:process:operate'])&&!row.disabled&&!isComplete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <template v-for="(item,index) in sectionVoList">
      <el-card
        :header="'标段(包)：' + item.bidSectionName"
        shadow="never"
        :body-style="{'padding': '15px 10px 10px 10px'}"
        class="card-box"
        :key="index">
        <div slot="header" class="clearfix">
          <span>{{ '标段(包)：' + item.bidSectionName }}</span>
          <el-button
            style="float: right;"
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd(entrust_file_type_bid, true, item.bidSectionCode,null, null)"
            v-if="!isComplete&&checkPermi(['entrust:process:operate'])"
          >添加文件
          </el-button>
        </div>
        <el-table :data="item.fileList" border>
          <el-table-column label="序号" align="center" type="index" width="55"/>
          <el-table-column prop="fileTypeLabel" align="center" label="文件类型" width="180">
            <template v-slot:default="{row}">
              <dict-tag :options="entrust_file_type_bid" :value="row.fileType"/>
            </template>
          </el-table-column>
          <el-table-column prop="fileStoragePath" align="center" label="文件名">
            <template v-slot:default="{row}">
              {{ row.fileStoragePath || '/' }}
            </template>
          </el-table-column>
          <el-table-column prop="createAt" align="center" label="上传时间" width="160">
            <template v-slot:default="{row}">
              <span v-if="row.createAt">{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
              <span v-else>/</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="240">
            <template v-slot:default="{row}">
              <el-button
                type="text"
                icon="el-icon-upload"
                size="mini"
                v-if="!isComplete&&checkPermi(['entrust:process:operate'])"
                @click="handleAdd(entrust_file_type_bid, false, row.bidSectionCode, row.fileType, row.fileCode)"
              >
                上传
              </el-button>
              <el-button type="text" icon="el-icon-view" size="mini" v-if="row.fileCode" @click="previewFile(row.fileCode)">预览</el-button>
              <el-button type="text" icon="el-icon-download" size="mini" v-if="row.fileCode" @click="handleDown(row.fileCode)">下载
              </el-button>
              <el-button
                type="text"
                icon="el-icon-delete"
                size="mini"
                class="btn-text-danger"
                @click="handleRemove(row.fileCode)"
                v-if="row.fileCode&&checkPermi(['entrust:process:operate'])&&!isComplete"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>

    <div class="mt10 text-center">
      <el-button type="primary" @click="handledComplete" v-if="checkPermi(['entrust:process:operate'])&&!isComplete">
        确认项目完成
      </el-button>
      <el-button type="primary" @click="handlePack">
        打包文件
      </el-button>
      <el-button type="primary" @click="handleDownZip">
        下载打包文件
      </el-button>
    </div>

    <el-dialog
      title="上传文件"
      :visible.sync="isDialog"
      @closed="closedDialog"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-form-item label="文件类型" prop="fileType" v-if="isShowSelect">
          <el-select
            v-model="form.fileType"
            placeholder="请选择"
            class="block"
          >
            <el-option
              v-for="dict in entrust_file_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件" prop="fileData">
          <el-upload
            ref="upload"
            :limit="1"
            :accept="upload.accept"
            action="upload"
            :before-upload="beforeUpload"
            :http-request="uploadSectionFile"
            :on-remove="removeFile"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip" style="line-height: 1.5;">
              <p><span v-if="upload.accept">仅允许导入 {{ upload.accept }} 格式的文件，</span>文件大小不能超过 {{ upload.fileSize }} MB。</p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { isEmpty, modifyFileName } from '@/utils';
import {
  queryEntrustFileList,
  fileUploadByEntrust,
  fileDeleteByEntrust,
  entrustProjectComplete, fileDownByEntrustZip, entrustPackZip
} from '@/api/entrustProject';

export default {
  name: 'ProjectFile',
  props: {
    isComplete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      entrust_file_type_project_detail: [
        { label: '采购申请批复相关文件', value: 'entrust_approval_file', disabled: true },
        { label: '需求文件', value: 'entrust_requirements_file', disabled: true },
        { label: '其它附件', value: 'entrust_project_other_annex', disabled: true }
      ],
      entrust_file_type_project: [],
      entrust_file_type_bid: [],
      entrust_file_type: [],
      projectCode: null,
      yearMonthSplit: null,
      projectFileList: [],
      sectionVoList: [],
      isDialog: false,
      bidSectionCode: null,
      fileData: null,
      upload: {
        accept: '',
        fileSize: 200
      },
      form: {
        fileType: null,
        fileData: null
      },
      rules: {
        fileType: [{ required: true, message: '请选择', trigger: 'change' }],
        fileData: [{ required: true, message: '请选择文件', trigger: 'change' }]
      },
      loading: false,
      isShowSelect: true,
      oldFileKey: null
    }
  },
  created() {
    this.yearMonthSplit = this.$route.params.yearMonthSplit;
    this.projectCode = this.$route.params.projectCode;
    this.getFileList();
  },
  methods: {
    checkPermi,
    fileTypeFormat(fileType) {
      return this.selectDictLabel(this.entrust_file_type, fileType);
    },
    async handlePack() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        await entrustPackZip({
          buyItemCode: this.projectCode,
          yearMonthSplit: this.yearMonthSplit
        });
        this.$modal.msgSuccess('打包成功');
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    async handleDownZip() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await fileDownByEntrustZip(this.projectCode);
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    async handledComplete() {
      this.$confirm('<p>确认项目完成之后，项目信息将无法再进行修改，是否确认提交？</p>', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await entrustProjectComplete({
            projectCode: this.projectCode
          })
          this.$emit('updateDisabled');
          this.$modal.msgSuccess('提交成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e)
        }
      }).catch(() => {
      })
    },
    handleAdd(list, isShowSelect, bidSectionCode, fileType, oldFileKey) {
      this.entrust_file_type = list;
      this.isShowSelect = isShowSelect;
      this.bidSectionCode = bidSectionCode;
      this.form.fileType = fileType;
      this.oldFileKey = oldFileKey;
      this.isDialog = true;
    },
    closedDialog() {
      this.$refs.upload.clearFiles();
      this.bidSectionCode = null;
      this.resetForm('form');
    },
    beforeUpload(file) {
      if (!isEmpty(this.upload.accept)) {
        let rules = this._.map(this.upload.accept.split(','), this._.trim)
        let index = file.name.lastIndexOf('.')
        let format = file.name.substring(index).toLowerCase()
        if (this._.indexOf(rules, format) === -1) {
          this.$modal.msgError(`文件格式不正确, 请上传${rules.join(' ')}格式文件!`)
          return false
        }
      }
      let fileSize = file.size
      if (fileSize <= 0) {
        this.$modal.msgError('上传文件大小不能为空')
        return false
      }
      if (fileSize > Number(this.upload.fileSize) * 1024 * 1024) {
        this.$modal.msgError(`上传文件大小不能超过 ${this.upload.fileSize} MB!`)
        return false
      }
    },
    removeFile() {
      this.form.fileData = null;
      this.$refs['form'].validateField('fileData');
    },
    uploadSectionFile(params) {
      this.form.fileData = params.file;
      this.$refs['form'].validateField('fileData');
    },
    submitFileForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('正在上传文件，请稍候...');
            let fileTypeName = this.fileTypeFormat(this.form.fileType);
            await fileUploadByEntrust({
              buyItemCode: this.projectCode,
              yearMonthSplit: this.yearMonthSplit,
              subpackageCode: this.bidSectionCode,
              fileTypeName: this.form.fileType,
              file: modifyFileName(this.form.fileData, fileTypeName)
            });
            if (this.oldFileKey) {
              await fileDeleteByEntrust(this.oldFileKey);
            }
            this.isDialog = false;
            await this.getFileList();
            this.$modal.msgSuccess('上传成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    previewFile(fileKey) {
      this.$download.previewFileByEntrust(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFileByEntrust(fileKey)
    },
    handleRemove(fileKey) {
      let tipHtml = `<p style="color: #ff4949;margin-bottom: 10px;font-size: 18px;">确定要删除改文件？</p>
                     <p style="color: #ff4949;text-align: center;">注意：文件一旦删除，将无法恢复，请谨慎操作！！！</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await fileDeleteByEntrust(fileKey)
            await this.getFileList();
            this.$modal.msgSuccess('删除成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    async getFileList() {
      try {
        this.loading = true;
        let dictData = await this.getDicts('entrust_file_type_project');
        let dictData1 = await this.getDicts('entrust_file_type_bid');
        this.entrust_file_type_project = this.initDict(dictData.data || []);
        this.entrust_file_type_bid = this.initDict(dictData1.data || []);
        let { data } = await queryEntrustFileList(this.projectCode);
        /** 项目详情文件*/
        let detailFileList = [];
        this.entrust_file_type_project_detail.forEach(item => {
          let projectFileList = data.projectFileList || [];
          let obj = projectFileList.find(v => v.fileTypeName === item.value);
          if (obj) {
            detailFileList.push({
              fileCode: obj.fileCode,
              fileStoragePath: obj.fileStoragePath,
              createAt: obj.createAt,
              fileType: obj.fileTypeName,
              fileTypeLabel: item.label,
              disabled: item.disabled
            })
          }
        })
        /** 项目文件*/
        this.projectFileList = [];
        this.entrust_file_type_project.forEach(item => {
          let projectFileList = data.projectFileList || [];
          let arr = projectFileList.filter(v => v.fileTypeName === item.value);
          if (arr.length > 0) {
            arr.forEach(v => {
              this.projectFileList.push({
                fileCode: v.fileCode,
                fileStoragePath: v.fileStoragePath,
                createAt: v.createAt,
                fileType: v.fileTypeName,
                fileTypeLabel: item.label
              })
            })
          } else {
            this.projectFileList.push({
              fileCode: null,
              fileStoragePath: null,
              createAt: null,
              fileType: item.value,
              fileTypeLabel: item.label
            })
          }
        })
        this.projectFileList = [...detailFileList, ...this.projectFileList];
        /** 标段文件*/
        data.sectionVoList.forEach(bidItem => {
          let fileList = [];
          this.entrust_file_type_bid.forEach(item => {
            let sectionFileList = bidItem.sectionFileList || [];
            let arr = sectionFileList.filter(v => v.fileTypeName === item.value);
            if (arr.length > 0) {
              arr.forEach(v => {
                fileList.push({
                  fileCode: v.fileCode,
                  fileStoragePath: v.fileStoragePath,
                  createAt: v.createAt,
                  fileType: v.fileTypeName,
                  fileTypeLabel: item.label,
                  bidSectionCode: bidItem.bidSectionCode
                })
              })
            } else {
              fileList.push({
                fileCode: null,
                fileStoragePath: null,
                createAt: null,
                fileType: item.value,
                fileTypeLabel: item.label,
                bidSectionCode: bidItem.bidSectionCode
              })
            }
          })
          bidItem.fileList = fileList;
        })
        this.sectionVoList = data.sectionVoList;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss">
.my-label {
  min-width: 230px;
}
</style>
