<template>
  <el-form
    ref="form"
    :model="form"
    :rules="rules"
    size="small"
    label-width="165px"
    :disabled="!checkPermi(['entrust:process:operate']) || isComplete"
  >
    <el-row :gutter="10">
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-form-item label="意向公开时间" prop="intentionTime">
          <el-date-picker
            class="block"
            style="width: 100%"
            v-model="form.intentionTime"
            type="datetime"
            placeholder="请选择"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="10:00:00"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-form-item label="意向公开网址" prop="intentionUrl">
          <el-input v-model.trim="form.intentionUrl" placeholder="请输入" maxlength="100"/>
        </el-form-item>
      </el-col>
    </el-row>
    <template v-for="(item,key) in form.agentSectionList">
      <item-card :key="key" :header="'标段(包)：'+ item.bidSectionName" style="margin-bottom: 0;">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <el-form-item label="公告时间" :prop="'agentSectionList.'+key+'.announcementTime'" :rules="rules.announcementTime">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="item.announcementTime"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <el-form-item label="报名截止时间" :prop="'agentSectionList.'+key+'.registrationDeadline'" :rules="rules.registrationDeadline">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="item.registrationDeadline"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <el-form-item label="开标时间" :prop="'agentSectionList.'+key+'.bidOpeningTime'" :rules="rules.bidOpeningTime">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="item.bidOpeningTime"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <el-form-item label="评标截止时间" :prop="'agentSectionList.'+key+'.deadlineBidEvaluation'" :rules="rules.deadlineBidEvaluation">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="item.deadlineBidEvaluation"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <el-form-item label="结果公示（公告）时间" :prop="'agentSectionList.'+key+'.resultPublicityTime'" :rules="rules.resultPublicityTime">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="item.resultPublicityTime"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </item-card>
    </template>

    <div class="text-center mt10 mb20" v-if="checkPermi(['entrust:process:operate'])&&!isComplete">
      <el-button type="primary" @click="submitForm">
        保存招标准备信息
      </el-button>
    </div>
  </el-form>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { saveEntrustPrepareInfo, queryEntrustProjectDetail } from '@/api/entrustProject';

export default {
  name: 'ProjectPrepare',
  props: {
    isComplete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      id: null,
      projectCode: null,
      form: {
        intentionTime: null,
        intentionUrl: null,
        agentSectionList: []
      },
      rules: {
        intentionTime: [{ required: false, message: '请选择', trigger: 'change' }],
        intentionUrl: [{ required: false, message: '请输入', trigger: 'blur' }],
        announcementTime: [{ required: true, message: '请选择', trigger: 'change' }],
        registrationDeadline: [{ required: true, message: '请选择', trigger: 'change' }],
        bidOpeningTime: [{ required: true, message: '请选择', trigger: 'change' }],
        deadlineBidEvaluation: [{ required: true, message: '请选择', trigger: 'change' }],
        resultPublicityTime: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  created() {
    this.id = this.$route.params.id;
    this.projectCode = this.$route.params.projectCode;
    this.queryDetail();
  },
  methods: {
    checkPermi,
    async queryDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryEntrustProjectDetail(this.id);
        for (let key in this.form) {
          if (key !== 'agentSectionList') {
            this.form[key] = data[key];
          } else {
            this.form.agentSectionList = [];
            data.agentSectionList.forEach(item => {
              this.form.agentSectionList.push({
                bidSectionName: item.bidSectionName,
                bidSectionCode: item.bidSectionCode,
                bidOpeningTime: item.bidOpeningTime,
                deadlineBidEvaluation: item.deadlineBidEvaluation,
                announcementTime: item.announcementTime,
                resultPublicityTime: item.resultPublicityTime,
                registrationDeadline: item.registrationDeadline
              })
            })
          }
        }
        console.log(this.form.agentSectionList)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await saveEntrustPrepareInfo({
              id: this.id,
              projectCode: this.projectCode,
              ...this.form
            })
            await this.queryDetail();
            this.$emit('updateDisabled');
            this.$modal.msgSuccess('保存成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    }
  }
}
</script>

<style scoped>

</style>
