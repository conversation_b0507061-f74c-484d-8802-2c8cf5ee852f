<template>
  <el-form
    ref="form"
    :model="form"
    :rules="rules"
    size="mini"
    :disabled="!checkPermi(['entrust:process:operate']) || isComplete"
    class="mb20"
  >
    <template v-for="(item,key) in form.winBidSectionVoList">
      <item-card :key="key" :header="'标段(包)：'+ item.bidSectionName" style="margin-bottom: 0;">
        <el-table :data="item.supplierList" border>
          <el-table-column label="序号" align="center" type="index" width="55"></el-table-column>
          <el-table-column label="供应商名称" align="center" prop="bidderName" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.bidderName'" :rules="rules.bidderName">
                <el-input v-model.trim="row.bidderName" maxlength="100" placeholder="请输入"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="联系人" align="center" prop="infoReporterName" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.infoReporterName'" :rules="rules.infoReporterName">
                <el-input v-model.trim="row.infoReporterName" maxlength="20" placeholder="请输入"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="联系方式" align="center" prop="infoReporterContactNumber" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item
                :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.infoReporterContactNumber'"
                :rules="rules.infoReporterContactNumber">
                <el-input v-model.trim="row.infoReporterContactNumber" placeholder="请输入"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="候选人排序" align="center" prop="candidateRanking" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.candidateRanking'" :rules="rules.candidateRanking">
                <el-select
                  v-model="row.candidateRanking"
                  placeholder="请选择"
                  clearable
                  class="block">
                  <el-option
                    v-for="val in item.supplierList.length"
                    :key="val"
                    :label="'第'+numToChinese(val)+'候选人'"
                    :value="val">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="是否中标" align="center" prop="winningTheBid" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.winningTheBid'" :rules="rules.winningTheBid">
                <el-radio-group v-model="row.winningTheBid">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="品牌/型号" align="center" prop="brandModel" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.brandModel'" :rules="rules.brandModel">
                <el-input v-model.trim="row.brandModel" maxlength="100" placeholder="请输入"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="投标报价（元）" align="center" prop="bidWinningPrice" class-name="form-cell">
            <template v-slot:default="{row,$index}">
              <el-form-item :prop="'winBidSectionVoList.'+key+'.supplierList.'+$index+'.bidWinningPrice'" :rules="rules.bidWinningPrice">
                <el-input v-model.trim="row.bidWinningPrice" placeholder="请输入"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="90" v-if="checkPermi(['entrust:process:operate'])">
            <template v-slot:default="{$index}">
              <el-button
                :disabled="item.supplierList.length===1"
                size="mini"
                icon="el-icon-delete"
                class="btn-text-danger"
                type="text"
                @click="removeItem(item.supplierList,$index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center pt10" v-if="checkPermi(['entrust:process:operate'])&&!isComplete">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-plus"
            @click="addItem(item.supplierList)"
          >
            添加一行
          </el-button>
        </div>
      </item-card>
    </template>

    <div class="text-center mt10 mb20" v-if="checkPermi(['entrust:process:operate'])&&!isComplete">
      <el-button type="primary" @click="submitForm">
        保存中标结果信息
      </el-button>
    </div>

  </el-form>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { numToChinese } from '@/utils';
import reg from '@/utils/reg';
import { queryEntrustProjectWinPage, saveEntrustWinBid } from '@/api/entrustProject';

export default {
  name: 'ProjectResult',
  props: {
    isComplete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        projectCode: null,
        projectName: null,
        winBidSectionVoList: []
      },
      rules: {
        bidderName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        infoReporterName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        infoReporterContactNumber: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: reg.cellphone, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        candidateRanking: [{ required: true, message: '请选择', trigger: 'change' }],
        winningTheBid: [{ required: true, message: '请选择', trigger: 'change' }],
        bidWinningPrice: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ],
        brandModel: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.form.projectCode = this.$route.params.projectCode;
    this.queryDetail();
  },
  methods: {
    checkPermi,
    numToChinese,
    addItem(arr) {
      arr.push({
        bidderId: null,
        bidderName: null,
        infoReporterName: null,
        infoReporterContactNumber: null,
        candidateRanking: null,
        winningTheBid: null,
        bidWinningPrice: null,
        brandModel: null
      })
    },
    removeItem(arr, index) {
      arr.splice(index, 1);
      let len = arr.length;
      arr.forEach(item => {
        if (item.candidateRanking > len) {
          item.candidateRanking = null;
        }
      })
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            let isEmpty = false;
            this.form.winBidSectionVoList.forEach(item => {
              if (item.supplierList.length === 0) {
                isEmpty = true;
              }
            })
            if (isEmpty) {
              this.$modal.msgError('每个标段必须有一个供应商');
              return
            }
            this.$modal.loading('数据提交中，请稍候...');
            await saveEntrustWinBid(this.form);
            await this.queryDetail();
            this.$emit('updateDisabled');
            this.$modal.msgSuccess('保存成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    async queryDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryEntrustProjectWinPage(this.form.projectCode);
        data.winBidSectionVoList.forEach(item => {
          this.$set(item, 'supplierList', item.supplierList || [{
            bidderId: null,
            bidderName: null,
            infoReporterName: null,
            infoReporterContactNumber: null,
            candidateRanking: null,
            winningTheBid: null,
            bidWinningPrice: null,
            brandModel: null
          }])
        })
        for (let key in this.form) {
          this.form[key] = data[key];
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
