<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="采购项目名称" prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          clearable
          placeholder="采购项目名称"
          style="width: 240px"
          @keyup.enter.native="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item label="使用科室" prop="useDepartment">
        <el-input
          v-model.trim="queryParams.useDepartment"
          clearable
          placeholder="使用科室"
          style="width: 240px"
          @keyup.enter.native="handleQuery">
        </el-input>
      </el-form-item>
      <el-form-item label="代理机构名称" prop="agencyName">
        <el-input
          v-model="queryParams.agencyName"
          placeholder="代理机构名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="审核状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.entrust_project_audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完成状态" prop="projectStatus">
        <el-select
          v-model="queryParams.projectStatus"
          placeholder="完成状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.entrust_project_complete_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="toCreateProject"
          v-hasPermi="['entrust:project:add']"
        >创建
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['entrust:project:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出项目报表
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" v-loading="loading" border>
      <el-table-column label="序号" type="index" align="center" width="55"></el-table-column>
      <el-table-column label="采购项目名称" prop="projectName" align="center" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <div class="pointer" @click="toProjectProgress(row)">
            {{ row.projectName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="代理机构名称" prop="agencyName" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="使用科室" prop="useDepartment" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="预算金额（元）" prop="projectAmount" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目信息" align="center" width="85">
        <template v-slot:default="{row}">
          <el-button type="text" size="mini" @click="toEditProject(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="100">
        <template v-slot:default="{row}">
          <dict-tag v-if="row.auditStatus" :options="dict.type.entrust_project_audit_status" :value="row.auditStatus"/>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="完成状态" align="center" prop="projectStatus" width="100">
        <template v-slot:default="{row}">
          <dict-tag v-if="row.projectStatus" :options="dict.type.entrust_project_complete_status" :value="row.projectStatus"/>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="110">
        <template v-slot:default="{row}">
          <el-button
            type="text"
            size="mini"
            @click="toProjectProgress(row)"
            :disabled="!(checkPermi(['entrust:process:query'])&&row.auditStatus===2)">
            进入项目
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="导出项目报表"
      center
      @closed="closedDialog"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="expertForm" :rules="rules" ref="expertForm" size="small" label-width="150px">
        <el-form-item label="项目完成时间" prop="dateRange">
          <el-date-picker
            v-model="expertForm.dateRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '00:00:00']">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">关闭</el-button>
        <el-button type="primary" @click="submitExport">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { exportExcelOfAgent, queryEntrustProjectList } from '@/api/entrustProject';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'EntrustProject',
  dicts: ['entrust_project_audit_status', 'entrust_project_complete_status'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: '',
        useDepartment: '',
        agencyName: '',
        auditStatus: null,
        projectStatus: null
      },
      total: 0,
      tableData: [],
      loading: false,
      isDialog: false,
      expertForm: {
        dateRange: []
      },
      rules: {
        dateRange: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleExport() {
      this.isDialog = true;
    },
    closedDialog() {
      this.resetForm('expertForm')
    },
    submitExport() {
      this.$refs['expertForm'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('正在下载数据，请稍候...');
            let res = await exportExcelOfAgent({
              beginTime: this.expertForm.dateRange[0],
              endTime: this.expertForm.dateRange[1]
            });
            this.$modal.closeLoading()
            this.$download.downloadSave(res);
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }
      })
    },
    toCreateProject() {
      this.$router.push({ name: 'CreateEntrustProject' });
    },
    toEditProject(row) {
      if (!this.$auth.hasPermi('entrust:project:query')) {
        this.$modal.msgError('没有项目信息查询权限');
        return
      }
      this.$router.push({
        name: 'EditEntrustProject',
        params: {
          id: row.id
        }
      });
    },
    toProjectProgress(row) {
      if (!this.$auth.hasPermi('entrust:process:query')) {
        this.$modal.msgError('没有项目流程查询权限');
        return
      }
      this.$router.push({
        name: 'EntrustProjectProcess',
        params: {
          id: row.id,
          projectCode: row.projectCode,
          yearMonthSplit: row.yearMonthSplit,
          projectName: row.projectName
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await queryEntrustProjectList(this.queryParams)
        this.tableData = data.rows;
        this.total = data.total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
