<template>
  <div class="app-container">
    <h3 :style="{color: theme, fontStyle:'16px'}" class="text-center over-ellipsis">{{ projectName }}</h3>

    <el-tabs v-model="activeName">
      <el-tab-pane label="招标准备" name="first">
        <project-prepare
          :isComplete="isComplete"
          v-if="activeName==='first'"
          key="first"
          @updateDisabled="updateDisabled">
        </project-prepare>
      </el-tab-pane>
      <el-tab-pane label="中标结果" name="second" :disabled="!enterPrepare">
        <project-result
          :isComplete="isComplete"
          v-if="activeName==='second'"
          key="second"
          @updateDisabled="updateDisabled">
        </project-result>
      </el-tab-pane>
      <el-tab-pane label="文件上传" name="three" :disabled="!(enterPrepare && saveResult)">
        <project-file
          :isComplete="isComplete"
          v-if="activeName==='three'"
          key="three"
          @updateDisabled="updateDisabled">
        </project-file>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import projectPrepare from './components/projectPrepare';
import projectResult from './components/projectResult';
import projectFile from './components/projectFile';
import { mapGetters } from 'vuex';
import { queryEntrustProjectIsComplete } from '@/api/entrustProject';

export default {
  name: 'EntrustProjectProcess',
  components: {
    projectPrepare,
    projectResult,
    projectFile
  },
  data() {
    return {
      activeName: 'first',
      projectName: null,
      projectCode: null,
      isComplete: false,
      enterPrepare: false,
      saveResult: false
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  created() {
    this.projectName = this.$route.params.projectName;
    this.projectCode = this.$route.params.projectCode;
    this.queryIsComplete();
  },
  methods: {
    checkPermi,
    updateDisabled() {
      this.queryIsComplete();
    },
    async queryIsComplete() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryEntrustProjectIsComplete({
          projectCode: this.projectCode
        })
        this.isComplete = data.complete;
        this.enterPrepare = data.enterPrepare;
        this.saveResult = data.saveResult;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
