<template>
  <div>
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" v-if="checkPermi(['process:purchaseFile:upload'])">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleRemove()"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5" class="fr" v-if="checkPermi(['process:purchaseFile:upload'])">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >自定义添加
        </el-button>
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleSelect"
        >选择添加
        </el-button>
      </el-col>
    </el-row>
    <el-table :data="tableData" border @selection-change="handleSelectionRemove">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" align="center" type="index" width="50px"/>
      <el-table-column label="属性" align="center" prop="attribute"/>
      <el-table-column label="描述" align="center" prop="description"/>
      <el-table-column label="操作" align="center" v-if="checkPermi(['process:purchaseFile:upload'])">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(row)"
          >修改
          </el-button>
          <el-button
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleRemove(row.id)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="title"
      :visible.sync="isDialog"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-form-item label="属性" prop="attribute">
          <el-input v-model.trim="form.attribute" placeholder="请输入" maxlength="125"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model.trim="form.description" placeholder="请输入" type="textarea" maxlength="250"/>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="选择属性"
      :visible.sync="isAttrDialog"
      top="2vh"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="form" :inline="true" size="small">
        <el-form-item label="类别" prop="groupType">
          <el-select
            v-model="groupType"
            placeholder="类别"
            clearable
            style="width: 240px"
            @change="selectGroupType"
          >
            <el-option
              v-for="dict in allAttribute"
              :key="dict.id"
              :label="dict.groupName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-table :data="attributeList" @selection-change="handleSelectionChange" border max-height="500">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="属性" align="center" prop="attribute"/>
        <el-table-column label="描述" align="center" prop="description"/>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isAttrDialog = false">取消</el-button>
        <el-button type="primary" @click="submitSelectForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { requirementAdd, requirementDel, requirementUpdate } from '@/api/purchaser/purchaseFileNoClient';

export default {
  name: 'PurchaseFileNoClientItem',
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      id: null,
      isDialog: false,
      title: '添加',
      form: {
        attribute: null,
        description: null
      },
      rules: {
        attribute: [{ required: true, message: '请输入', trigger: 'blur' }],
        description: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      allAttribute: [],
      isAttrDialog: false,
      groupType: null,
      attributeList: [],
      // 选中数组
      selectAttr: [],
      removeIds: [],
      // 非多个禁用
      multiple: true
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    handleSelect() {
      this.getConfigKey('sys.purchase.attribute').then(response => {
        this.allAttribute = JSON.parse(response.data);
        this.groupType = null;
        this.attributeList = [];
        this.selectAttr = [];
        this.isAttrDialog = true;
      })
    },
    selectGroupType(val) {
      let obj = this.allAttribute.find(v => v.id === val)
      this.attributeList = obj ? obj.children : [];
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectAttr = selection.map(item => {
        return {
          attribute: item.attribute,
          description: item.description
        }
      })
    },
    async submitSelectForm() {
      try {
        if (this.selectAttr.length === 0) {
          this.$modal.msgWarning('请选择需要添加的属性');
          return
        }
        this.$modal.loading('数据提交中，请稍候...');
        await requirementAdd({
          subpackageCode: this.subpackageCode,
          requestsDetailList: this.selectAttr
        })
        this.$modal.msgSuccess('提交成功');
        this.isAttrDialog = false;
        this.$emit('update');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleAdd() {
      this.resetForm('form');
      this.title = '添加';
      this.id = null;
      this.isDialog = true;
    },
    handleEdit(row) {
      this.resetForm('form');
      this.title = '修改';
      this.id = row.id;
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      this.isDialog = true;
    },
    handleSelectionRemove(selection) {
      this.multiple = !selection.length;
      this.removeIds = selection.map(item => {
        return { id: item.id }
      });
    },
    handleRemove(id) {
      console.log(id)
      let ids = id ? [{ id }] : this.removeIds
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await requirementDel(ids)
          this.$modal.msgSuccess('删除成功');
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (!this.id) {
              await requirementAdd({
                subpackageCode: this.subpackageCode,
                requestsDetailList: [this.form]
              })
            } else {
              await requirementUpdate([{
                id: this.id,
                ...this.form
              }])
            }
            this.isDialog = false;
            this.$modal.msgSuccess('提交成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    getData(data) {
      if (!data) {
        return
      }
      this.tableData = data;
    }
  }
}
</script>

<style scoped>

</style>
