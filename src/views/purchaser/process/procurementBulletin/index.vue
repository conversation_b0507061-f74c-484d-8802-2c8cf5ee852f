<template>
  <div>
    <div v-if="!showEditor">
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['process:bulletin:publish']"
          >新增
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="tableData" border v-loading="loading">
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="公告名称" align="center" prop="bulletinName" :show-overflow-tooltip="true"/>
        <el-table-column label="公告类型" align="center" prop="bulletinTypeName" width="130" :show-overflow-tooltip="true"/>
        <el-table-column label="公告内容" align="center" width="80px">
          <template v-slot:default="{row}">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handlePreview(row.bulletinContentKey)"
            >查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="公告详情" align="center" width="100px">
          <template v-slot:default="{row}">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(row)"
            >查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="附件" align="center" prop="attachmentDtoList">
          <template v-slot:default="{row}">
            <file-list-view
              v-if="row.attachmentDtoList&&row.attachmentDtoList.length>0"
              :fileList="row.attachmentDtoList"
              :handlePreview="handlePreview"
              :handleDown="handleDown"
            />
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createAt" width="160">
          <template v-slot:default="{row}">
            <span>{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批状态" align="center" prop="auditStatus">
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.approval_process_status" :value="row.auditStatus"/>
            <div class="td-tip" v-if="row.auditStatus==='0'">
              理由：{{ row.auditDesc || '/' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="展示状态"
          align="center"
          key="whetherShow"
          width="290"
          v-if="checkFunction(['purchaser_procurement_bulletin_set_show'])"
        >
          <template v-slot:default="{row}">
            <el-radio-group
              v-model="row.whetherShow"
              @change="handleShowChange($event,row)"
              :disabled="row.auditStatus!=='1' || !checkPermi(['process:bulletin:show'])"
            >
              <el-radio
                v-for="dict in dict.type.bulletin_whether_show"
                :label="dict.value"
                :key="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="140">
          <template v-slot:default="{row}">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleUpdate(row)"
              v-if="checkFunction(['purchaser_procurement_bulletin_resubmit'])&&checkPermi(['process:bulletin:resubmit'])"
              :disabled="['1','2'].includes(row.auditStatus)"
            >再次提交
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh-left"
              @click="handleWithdraw(row)"
              v-if="checkFunction(['purchaser_procurement_bulletin_withdraw'])&&checkPermi(['process:bulletin:withdraw'])"
              :disabled="row.auditStatus!=='2'"
            >撤回
            </el-button>
            <!--            <el-button-->
            <!--              size="mini"-->
            <!--              type="text"-->
            <!--              class="btn-text-danger"-->
            <!--              icon="el-icon-delete"-->
            <!--              @click="handleDelete(row)"-->
            <!--              v-if="checkFunction(['purchaser_procurement_bulletin_remove'])&&checkPermi(['process:bulletin:remove'])"-->
            <!--              :disabled="['1','2'].includes(row.auditStatus)"-->
            <!--            >删除-->
            <!--            </el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <bulletin-editor v-if="showEditor" :bulletinId="bulletinId" :editType="editType" @publicSuccess="publicSuccess">
      <el-button type="primary" size="small" @click="handleBack">返回</el-button>
    </bulletin-editor>

  </div>
</template>

<script>
import bulletinEditor from './bulletinEditor/index'
import { rollBackAudit, delBulletin, listProcurementBulletin, showBulletin } from '@/api/purchaser/bulletin'
import { checkPermi, checkFunction } from '@/utils/permission';
import { mapGetters } from 'vuex';

export default {
  name: 'ProcurementBulletin',
  components: {
    bulletinEditor
  },
  dicts: ['approval_process_status', 'bulletin_whether_show'],
  data() {
    return {
      loading: false,
      showEditor: false,
      tableData: [],
      bulletinId: null,
      editType: null // 0:新增 1:再次提交 2查看
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    checkFunction,
    handleUpdate(row) {
      this.bulletinId = row.bulletinId;
      this.editType = 1;
      this.showEditor = true
    },
    handleDetail(row) {
      this.bulletinId = row.bulletinId;
      this.editType = 2;
      this.showEditor = true
    },
    async handleShowChange(whetherShow, row) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await showBulletin({
          bulletinId: row.bulletinId,
          whetherShow: whetherShow
        })
        this.$modal.msgSuccess('操作成功')
        await this.getList()
        this.$modal.closeLoading()
      } catch (e) {
        await this.getList()
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    handleWithdraw(row) {
      this.$modal.confirm('是否确认撤回该公告？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            let { auditCode, auditType } = row.auditProcessDto;
            await rollBackAudit({ auditCode: auditCode, auditType: auditType, status: 3 })
            await this.getList();
            this.$modal.closeLoading()
            this.$modal.msgSuccess('撤回成功');
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }).catch(() => {
        });
    },
    handleDelete(row) {
      this.$modal.confirm('删除后不可恢复，是否确认删除该公告？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await delBulletin(row.bulletinId)
            await this.getList();
            this.$modal.closeLoading()
            this.$modal.msgSuccess('删除成功');
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }).catch(() => {
        });
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handlePreview(fileKey) {
      this.$download.previewFile(fileKey)
    },
    publicSuccess() {
      this.showEditor = false
      this.getList();
    },
    handleAdd() {
      this.bulletinId = null;
      this.editType = 0;
      this.showEditor = true
    },
    handleBack() {
      this.showEditor = false
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listProcurementBulletin(this.buyItemCode);
        this.tableData = data;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
