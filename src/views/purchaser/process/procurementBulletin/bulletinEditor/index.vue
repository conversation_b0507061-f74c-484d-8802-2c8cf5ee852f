<template>
  <div>
    <el-form
      ref="form"
      size="small"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-row :gutter="10">
        <el-col :xs="24" :sm="18" :md="18" :lg="16" :xl="14">
          <el-form-item prop="bulletinContentHtml">
            <template-select
              v-if="initEditor"
              :disabled="editType===2"
              :min-height="600"
              :max-height="1000"
              tmp-type="bidding_announcement"
              :showSealTip="signatureOff==='0'"
              v-model="form.bulletinContentHtml"
              :getTemplateFunc="getTemplateFunc"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="8" :xl="10" style="max-width: 600px;">
          <el-form-item>
            <slot></slot>
          </el-form-item>
          <el-form-item label="公告标题" prop="bulletinName">
            <el-input
              v-model.trim="form.bulletinName"
              maxlength="100"
              placeholder="请输入公告标题"
              :disabled="editType===2"
            />
          </el-form-item>

          <template v-for="(item,index) in form.bulletinTimeDtoList">
            <el-form-item
              :label="item.timeName"
              :prop="'bulletinTimeDtoList.'+index+'.time'"
              :key="'time'+index"
              :rules="rules[item.timeType]"
            >
              <el-date-picker
                :disabled="editType===2"
                class="block"
                style="width: 100%"
                v-model="item.time"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="10:00:00"
              >
              </el-date-picker>
            </el-form-item>
          </template>

          <template
            v-if="checkFunction(['purchaser_additional_conditions'])||checkFunction(['purchaser_invite_supplier'])">
            <template v-for="(item,index) in form.bulletinSupplierDtoList">
              <div :key="'supplier'+index">
                <el-card
                  class="mb15"
                  :header="'标段(包)：'+item.subpackageName"
                  shadow="never"
                >
                  <el-form-item
                    label="响应附加条件"
                    :prop="'bulletinSupplierDtoList.'+index+'.attributeVoList'"
                    :rules="rules.attributeVoList"
                    v-if="checkFunction(['purchaser_additional_conditions'])"
                  >
                    <select-additional-conditions
                      :disabled="editType===2"
                      v-model="form.bulletinSupplierDtoList[index].attributeVoList"
                    />
                  </el-form-item>
                  <el-form-item
                    label="邀请供应商"
                    :prop="'bulletinSupplierDtoList.'+index+'.supplierInfoVoList'"
                    :rules="rules.supplierInfoVoList"
                    v-if="checkFunction(['purchaser_invite_supplier'])"
                  >
                    <select-supplier
                      :disabled="editType===2"
                      :versionType="versionType"
                      :creditRating="dict.type.credit_rating"
                      :goodsCategory="item.goodsCategory"
                      v-model="form.bulletinSupplierDtoList[index].supplierInfoVoList"
                    />
                  </el-form-item>
                </el-card>

              </div>
            </template>
          </template>

          <el-form-item
            label="是否要求供应商填写股东信息"
            prop="whetherWrite"
            v-if="checkFunction(['purchaser_shareholder_info'])"
          >
            <el-radio-group
              v-model="form.whetherWrite"
              :disabled="editType===2"
            >
              <el-radio :label="1">需要</el-radio>
              <el-radio :label="0">不需要</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="附件" prop="attachmentDtoList">
            <file-upload
              :disabled="editType===2"
              :showUploadBtn="editType!==2"
              v-model="form.attachmentDtoList"
              :showTip="true"
              :fileSize="20"
              :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
              :params="{
                fileTypeName: 'announcement_annex',
                buyItemCode: buyItemCode,
                yearMonthSplit: createYearMonth
              }"
              @preview="previewFile"
              @down="downFile"
            >
            </file-upload>
          </el-form-item>

          <el-form-item v-if="editType!==2&&checkPermi(['process:bulletin:publish'])">
            <publish-submit
              v-if="checkFunction(['purchaser_procurement_bulletin_not_approved']) "
              :submit-func="submitForm"
              :verify-func="verifyForm"
              @success="successSubmit"
            />
            <div v-else>
              <submit-approval
                v-if="versionType==='bjxk'"
                :auditTitle="form.bulletinName+'审批'"
                :verify-func="verifyForm"
                :submit-func="submitForm"
                @success="successSubmit"
              />
              <approval-form
                v-else
                bindKey="bulletin"
                :orgCode="orgCode"
                :bindDeptId="projectDeptId"
                :filterStr="filterStr"
                :auditTitle="form.bulletinName+'审批'"
                :verify-func="verifyForm"
                :submit-func="submitForm"
                @success="successSubmit"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import selectSupplier from './selectSupplier.vue'
import selectAdditionalConditions from './selectAdditionalConditions.vue'
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import submitApproval from '@/views/purchaser/components/SubmitApproval/index.vue'
import {
  fillTemplate,
  getBulletinInfo,
  publishProcurementBulletin
} from '@/api/purchaser/bulletin'
import { checkFunction, checkPermi } from '@/utils/permission';
import { getSubpackageList } from '@/api/purchaser/projectList';
import { verifyEditor } from '@/utils/validate'
import { Base64 } from 'js-base64'
import PublishSubmit from '@/views/purchaser/components/PublishSubmit/index.vue'

export default {
  name: 'Index',
  dicts: ['credit_rating'],
  components: {
    PublishSubmit,
    selectSupplier,
    selectAdditionalConditions,
    approvalForm,
    submitApproval
  },
  props: {
    bulletinId: {
      type: Number,
      default: null
    },
    editType: {
      type: Number, // 0:新增 1:再次提交 2查看
      default: null
    }
  },
  data() {
    return {
      initEditor: true,
      form: {},
      rules: {
        bulletinContentHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ],
        bulletinName: [
          { required: true, message: '公告标题不能为空', trigger: 'blur' }
        ],
        registrationEndTime: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        responseFileEndTime: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        meetingTime: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        attributeVoList: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        supplierInfoVoList: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        whetherWrite: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'signatureOff',
      'bulletinList',
      'purchaseMethodCode',
      'getPurchaseDict',
      'orgCode',
      'buyItemCode',
      'buyItemName',
      'createYearMonth',
      'projectDeptId',
      'filterStr'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.reset();
  },
  methods: {
    checkPermi,
    checkFunction,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    getTemplateFunc(data) {
      return fillTemplate({
        buyItemCode: this.buyItemCode,
        purchaseMethodCode: this.purchaseMethodCode,
        tmpKey: data.tmpKey,
        tmpType: data.tmpType
      });
    },
    verifyForm() {
      return new Promise((resolve, reject) => {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            let obj = this.form.bulletinTimeDtoList.find(v => v.timeType === 'meetingTime');
            let meetingTime = obj ? obj.time : null;
            if (checkFunction(['purchaser_meeting_time_verify']) && meetingTime) {
              let star = this.$moment().valueOf();
              let end = this.$moment(meetingTime).valueOf();
              let ms = end - star;
              if (ms < 20 * 24 * 60 * 60 * 1000) {
                this.$modal.msgWarning('开标时间必须大于等于20天');
                resolve(false)
              } else {
                resolve(true)
              }
            } else {
              resolve(true)
            }
          } else {
            resolve(false)
          }
        });
      })
    },
    successSubmit() {
      this.$emit('publicSuccess');
    },
    submitForm(data) {
      let params = this._.cloneDeep(this.form);
      params.bulletinSupplierDtoList.forEach(item => {
        let attributeVoList = item.attributeVoList.map(v => {
          let regex = Base64.toBase64(v.regex);
          return {
            ...v,
            regex
          }
        });
        item.attributeVoList = attributeVoList;
      })
      return publishProcurementBulletin({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        ...params,
        ...data
      });
    },
    async getDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getBulletinInfo(this.bulletinId);
        for (let key in this.form) {
          if (key === 'bulletinSupplierDtoList') {
            this.form.bulletinSupplierDtoList.forEach(item => {
              this.$set(item, 'attributeVoList', data[key].find(v => v.subpackageCode === item.subpackageCode).attributeVoList)
              this.$set(item, 'supplierInfoVoList', data[key].find(v => v.subpackageCode === item.subpackageCode).supplierInfoVoList)
            })
          } else {
            this.form[key] = data[key];
          }
        }
        this.initEditor = false;
        this.$nextTick(() => {
          this.initEditor = true;
        })
        console.log(this.form)
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    // 表单重置
    async reset() {
      let timeList = [];
      if (this.versionType === 'wzlg') {
        timeList = [
          { timeType: 'registrationEndTime', timeName: null, time: null },
          { timeType: 'meetingTime', timeName: null, time: null }
        ];
      } else {
        timeList = [
          { timeType: 'registrationEndTime', timeName: null, time: null },
          { timeType: 'responseFileEndTime', timeName: null, time: null },
          { timeType: 'meetingTime', timeName: null, time: null }
        ];
      }
      timeList.forEach(item => {
        this.$set(item, 'timeName', this.getPurchaseDict(this.purchaseMethodCode, item.timeType))
      })
      this.form = {
        bulletinName: null,
        bulletinType: null,
        bulletinTypeName: null,
        bulletinContentHtml: '',
        attachmentDtoList: [],
        bulletinSupplierDtoList: [],
        bulletinTimeDtoList: timeList,
        whetherWrite: 0
      };
      this.resetForm('form');
      await this.getSubpackage();
      if (!this.bulletinId) {
        this.form.bulletinType = 'bidding_announcement';
        let obj = this.bulletinList.find(item => item.value === this.form.bulletinType);
        this.form.bulletinTypeName = obj ? obj.label : '采购公告';
        this.form.bulletinName = this.buyItemName + this.form.bulletinTypeName;
      } else {
        await this.getDetail()
      }
    },
    async getSubpackage() {
      try {
        let { data } = await getSubpackageList(this.buyItemCode);
        this.form.bulletinSupplierDtoList = data.map(item => {
          return {
            subpackageCode: item.subpackageCode,
            subpackageName: item.subpackageName,
            goodsCategory: item.shopJson,
            attributeVoList: [],
            supplierInfoVoList: []
          }
        })
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
