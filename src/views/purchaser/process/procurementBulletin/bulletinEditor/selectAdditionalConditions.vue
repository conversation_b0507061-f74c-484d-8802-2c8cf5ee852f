<template>
  <el-select
    :disabled="disabled"
    v-model="selectValues"
    multiple
    clearable
    filterable
    placeholder="请选择响应附件条件"
    @change="selectChange"
    class="block"
  >
    <el-option
      v-for="(v,key) in allColumns"
      :key="key"
      :label="v.keyName"
      :value="v.keyVal"
    ></el-option>
  </el-select>
</template>

<script>
import { allQuoteField } from '@/api/system/quoteField'

export default {
  name: 'SelectAdditionalConditions',
  props: {
    value: {},
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectValues: [],
      loading: false,
      allColumns: []
    }
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(value) {
        this.setCurrentValue(value);
      }
    }
  },
  created() {
    this.getAllHeaders();
  },
  methods: {
    setCurrentValue(value) {
      let list = value || [];
      this.selectValues = list.map(v => v.keyVal);
      this.$emit('input', value);
    },
    selectChange(value) {
      let list = this.allColumns.filter(item => value.includes(item.keyVal));
      this.setCurrentValue(list)
      this.$emit('change', list);
    },
    async getAllHeaders() {
      try {
        let { data } = await allQuoteField({ disabled: false, groupType: '1' })
        this.allColumns = data || [];
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
