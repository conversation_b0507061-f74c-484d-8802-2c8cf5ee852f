<template>
  <div>
    <el-row :gutter="10" class="mb10" v-if="!disabled">
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          @click="handleAdd"
        >添加供应商
        </el-button>
      </el-col>
    </el-row>
    <el-table :data="selectSupplierList" v-if="selectSupplierList.length>0" border>
      <el-table-column label="公司名称" align="center" prop="bidderName" :show-overflow-tooltip="true"/>
      <el-table-column label="统一社会信用代码" align="center" prop="licNumber" :show-overflow-tooltip="true"/>
      <el-table-column label="法定代表人姓名" align="center" prop="certificateName" :show-overflow-tooltip="true" width="90"/>
      <el-table-column label="经营负责人手机号码" align="center" prop="contactNumber" :show-overflow-tooltip="true" width="120"/>
      <el-table-column label="操作" align="center" width="60" class-name="small-padding fixed-width" v-if="!disabled">
        <template v-slot:default="{$index}">
          <el-button
            size="mini"
            type="text"
            class="btn-text-danger"
            icon="el-icon-delete"
            @click="handleRemove($index)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="添加供应商"
      :visible.sync="isDialog"
      custom-class="maxW1000"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
        <el-form-item label="供应商名称" prop="bidderName">
          <el-input
            v-model.trim="queryParams.bidderName"
            placeholder="请输入供应商名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="getSupplierList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getSupplierList">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="supplierList" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="公司名称" align="center" prop="bidderName" :show-overflow-tooltip="true"/>
        <el-table-column label="统一社会信用代码" align="center" prop="licNumber" :show-overflow-tooltip="true"/>
        <el-table-column label="法定代表人姓名" align="center" prop="certificateName" :show-overflow-tooltip="true"/>
        <el-table-column label="经营负责人手机号码" align="center" prop="contactNumber" :show-overflow-tooltip="true"/>
        <el-table-column
          label="操作"
          align="center"
          width="80"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="!currentValue.some(item=>item===row.userId)"
              type="text"
              icon="el-icon-edit"
              @click="submitAdd(row)"
            >添加
            </el-button>
            <span v-else>已添加</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getSupplierList"
      />
    </el-dialog>
  </div>
</template>

<script>
import { findByCompanyAndOrg } from '@/api/purchaser/bulletin';
import { mapGetters } from 'vuex'

export default {
  name: 'BulletinFormInvite',
  props: {
    value: {},
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentValue: this.value,
      selectSupplierList: [],
      isDialog: false,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orgAuditStatus: '2',
        orgCode: null,
        bidderName: null
      },
      total: 0,
      supplierList: []
    }
  },
  computed: {
    ...mapGetters([
      'orgCode'
    ])
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    setCurrentValue(value) {
      this.currentValue = value
      this.$emit('input', value)
    },
    submitAdd(row) {
      this.selectSupplierList.push({
        bidderName: row.bidderName,
        supplierId: row.userId,
        licNumber: row.licNumber,
        certificateName: row.certificateName,
        contactNumber: row.contactNumber
      })
      let list = this.selectSupplierList.map(v => v.supplierId);
      this.setCurrentValue(list)
      this.$emit('select-change', list, this.selectSupplierList)
    },
    handleRemove(index) {
      this.selectSupplierList.splice(index, 1)
      let list = this.selectSupplierList.map(v => v.supplierId);
      this.setCurrentValue(list)
      this.$emit('select-change', list, this.selectSupplierList);
    },
    handleAdd() {
      this.queryParams.orgCode = this.orgCode;
      this.queryParams.bidderName = null;
      this.queryParams.pageNum = 1;
      this.getSupplierList()
      this.isDialog = true
    },
    async getSupplierList() {
      this.loading = true
      try {
        let { rows, total } = await findByCompanyAndOrg(this.queryParams)
        this.loading = false
        this.supplierList = rows || []
        this.total = total
      } catch (e) {
        this.loading = false
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
