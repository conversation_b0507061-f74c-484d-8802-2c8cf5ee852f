<template>
  <el-select
    :disabled="disabled"
    v-model="selectValues"
    multiple
    clearable
    filterable
    placeholder="请选择供应商"
    @change="selectChange"
    class="block"
  >
    <el-option
      v-for="(v,key) in supplierList"
      :key="key"
      :label="versionType==='wzlg'?v.supplierCompanyName+' - '+v.gradeLabel+' - 邀约未报名'+v.inviteNum+'次':v.supplierCompanyName"
      :value="v.supplierId"
    ></el-option>
  </el-select>
</template>

<script>
import { findByCompanyAndOrg } from '@/api/purchaser/bulletin';
import { mapGetters } from 'vuex'

export default {
  name: 'SelectSupplier',
  props: {
    value: {},
    disabled: {
      type: Boolean,
      default: false
    },
    goodsCategory: {
      type: String,
      default: null
    },
    creditRating: {
      type: Array,
      default: () => []
    },
    versionType: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      selectValues: [],
      loading: false,
      supplierList: []
    }
  },
  computed: {
    ...mapGetters([
      'orgCode',
      'bidType'
    ])
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(value) {
        this.setCurrentValue(value);
      }
    },
    goodsCategory: {
      deep: true,
      immediate: true,
      handler() {
        this.getAllSupplier();
      }
    }
  },
  created() {
    // this.getAllSupplier();
  },
  methods: {
    gradeFormat(grade) {
      return this.selectDictLabel(this.creditRating, grade);
    },
    setCurrentValue(value) {
      let list = value || [];
      this.selectValues = list.map(v => v.supplierId);
      this.$emit('input', value);
    },
    selectChange(value) {
      let list = this.supplierList.filter(v => value.includes(v.supplierId));
      this.setCurrentValue(list)
      this.$emit('change', list);
    },
    async getAllSupplier() {
      try {
        let { rows } = await findByCompanyAndOrg({
          pageNum: 1,
          pageSize: 10000,
          orgCode: this.orgCode,
          goodsCategory: this.goodsCategory,
          bidTypes: [this.bidType]
        })
        let list = rows || []
        this.supplierList = list.map(v => {
          return {
            supplierCompanyName: v.bidderName,
            supplierId: v.userId,
            inviteNum: v.inviteNum,
            gradeLabel: this.gradeFormat(v.supplierScore)
          }
        });
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
