<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column label="评委姓名" align="center" prop="judgeName"/>
      <el-table-column label="类别" align="center" prop="specialtyCategory"/>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
        align="center"
        prop="dept"
        v-if="expertDeptShow!=='0'"
      />
      <el-table-column label="联系方式" align="center" prop="expertCellphone"/>
      <el-table-column label="票数" align="center" prop="numberVotes"/>
      <el-table-column label="投票人" align="center" prop="judgeNames">
        <template v-slot:default="{row}">
          <span v-if="row.judgeNames&&row.judgeNames.length>0">{{ row.judgeNames.join('、') }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="是否投票" align="center" prop="voting">
        <template v-slot:default="{row}">
          <span v-if="row.voting === 1">已投票</span>
          <span v-else class="text-danger">未投票</span>
        </template>
      </el-table-column>
      <el-table-column label="是否组长" align="center">
        <template v-slot:default="{row}">
          <el-radio
            v-model="groupId"
            :label="row.judgeId"
            :disabled="!(confirmReview!=='1'&&confirmCounterSign==='1'&&checkPermi(['process:review:start']))"
          >
            组长
          </el-radio>
        </template>
      </el-table-column>
      <el-table-column label="使用科室代表" align="center" v-if="checkFunction(['purchaser_dept_representative'])">
        <template v-slot:default="{row}">
          <el-radio
            v-model="delegateId"
            :label="row.judgeId"
            :disabled="confirmCounterSign==='1' || !checkPermi(['process:review:start'])"
          >
            代表
          </el-radio>
        </template>
      </el-table-column>
    </el-table>

    <div class="text-center mt15">
      <el-button
        type="primary"
        @click="submitCounterSign"
        v-if="confirmCounterSign!=='1'&&checkPermi(['process:review:start'])">
        评委会签
      </el-button>
      <el-button
        type="primary"
        plain
        :disabled="confirmCounterSign!=='1'"
        @click="handleReVote"
        v-if="confirmReview!=='1'&&checkPermi(['process:review:reVote'])&&checkFunction(['purchaser_review_reVote'])">
        重新投票
      </el-button>
      <el-button
        type="primary"
        @click="handleReview"
        :disabled="confirmCounterSign!=='1'"
        v-if="confirmReview!=='1'&&checkPermi(['process:review:start'])"
      >
        确认评审
      </el-button>
      <el-button
        v-if="confirmReview==='1'&&checkPermi(['process:review:again'])&&checkFunction(['purchaser_review_again'])"
        type="danger"
        @click="againReview"
      >
        重新评审
      </el-button>
    </div>

    <el-dialog
      title="提示"
      :visible.sync="visible"
      custom-class="maxW600"
      width="90%"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="text-warning text-center"><i class="el-icon-warning fontSize60"></i></div>
      <h3 class="text-center mt10 mb20 text-danger">请选择是否线上评审，确认后将直接进入评审阶段</h3>
      <el-form ref="form" size="small" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="是否线上评审" prop="isReview">
          <el-radio-group v-model="form.isReview">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReview">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  againVote,
  againReview,
  submitConfirmReview,
  submitConfirmCounterSign
} from '@/api/purchaser/startReview';
import { checkPermi, checkFunction } from '@/utils/permission';

export default {
  name: 'StartReview',
  dicts: ['sys_dict_translate'],
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    },
    expertDeptShow: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableData: [],
      delegateId: null,
      groupId: null,
      confirmCounterSign: '0', // 是否确认会签 0-未确认，1-已确认
      confirmReview: '0', // 是否确认评审 0-未确认，1-已确认
      visible: false,
      form: { isReview: null },
      rules: {
        isReview: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    checkFunction,
    againReview() {
      let tipHtml = `<p style="color: #ff4949;margin-bottom: 10px;font-size: 18px;">确定要重新评审？</p>
                     <p style="color: #ff4949;text-align: left;">注意：该操作将删除本标段所有评委的评审数据，并且无法恢复，请谨慎操作！！！</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await againReview(this.subpackageCode);
            this.$modal.msgSuccess('提交成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    handleReVote() {
      this.$modal.confirm(`确定要重新投票？`)
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await againVote(this.subpackageCode)
            this.$modal.msgSuccess('提交成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    handleReview() {
      if (this.tableData.some(item => item.voting !== 1)) {
        this.$tipDialog({
          tipText: '还有评委未投票',
          tipTextColor: '#ff4949',
          type: 'warning'
        })
        return
      }
      if (!this.groupId) {
        this.$tipDialog({
          tipText: '请设置组长',
          tipTextColor: '#ff4949',
          type: 'warning'
        })
        return;
      }
      this.visible = true;
      this.form.isReview = null;
    },
    submitReview() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await submitConfirmReview({
              isReview: this.form.isReview,
              groupId: this.groupId,
              subpackageCode: this.subpackageCode,
              subpackageName: this.subpackageName
            })
            this.visible = false;
            this.$modal.msgSuccess('提交成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      });
    },
    submitCounterSign() {
      if (this.checkFunction(['purchaser_dept_representative']) && !this.delegateId) {
        this.$tipDialog({
          tipText: '请设置使用科室代表',
          tipTextColor: '#ff4949',
          type: 'warning'
        })
        return;
      }
      this.$confirm(`<p style="color: #ff4949">确认后不能再进行评委信息修改！</p>`, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await submitConfirmCounterSign({
            deptJudgeId: this.delegateId,
            subpackageCode: this.subpackageCode,
            subpackageName: this.subpackageName
          })
          this.$modal.msgSuccess('提交成功');
          this.$emit('update');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    getData(data) {
      if (!data) {
        return
      }
      this.confirmReview = data.confirmReview;
      this.confirmCounterSign = data.confirmCounterSign;
      this.tableData = data.judgeVoList || [];
      let obj = this.tableData.find(v => v.deptRepresent === '1');
      this.delegateId = obj ? obj.judgeId : null;
      let obj1 = this.tableData.find(v => v.whetherGroup === '1');
      this.groupId = obj1 ? obj1.judgeId : null;
    }
  }
}
</script>

<style scoped>

</style>
