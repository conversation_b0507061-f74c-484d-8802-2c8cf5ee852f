<template>
  <div v-loading="loading">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" class="fr">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新信息
        </el-button>
      </el-col>
    </el-row>

    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <start-review-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          :expertDeptShow="expertDeptShow"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <start-review-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          :expertDeptShow="expertDeptShow"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { confirmReviewPage } from '@/api/purchaser/startReview';
import { mapGetters } from 'vuex';
import startReviewItem from '@/views/purchaser/process/startReview/startReviewItem.vue';

export default {
  name: 'StartReview',
  components: { startReviewItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null,
      expertDeptShow: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getConfigKey('expert.dept.show').then(response => {
      this.expertDeptShow = response.data
    })
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await confirmReviewPage(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
