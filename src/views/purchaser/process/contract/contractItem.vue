<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="供应商" align="center" prop="supplierContractVo.supplierCompanyName"/>
      <el-table-column label="草拟合同" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.supplierContractVo.contractUrl)"
          >查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.supplierContractVo.contractUrl)"
          >下载
          </el-button>
          <el-button
            v-if="checkPermi(['process:contract:edit'])"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(row.supplierContractVo)"
            :disabled="[1,2].includes(row.auditProcessDto.status)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="盖章合同" align="center">
        <template v-slot:default="{row}">
          <div v-if="row.supplierContractVo.attachment">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(row.supplierContractVo.attachment)"
            >查看
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDown(row.supplierContractVo.attachment)"
            >下载
            </el-button>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center">
        <template v-slot:default="{row}">
          <div v-if="!isEmpty(row.auditProcessDto.status)">
            <dict-tag :options="dict.type.approval_process_status" :value="row.auditProcessDto.status"/>
            <div class="td-tip" v-if="row.auditProcessDto.status===0">
              审批意见：{{ row.auditProcessDto.remark || '/' }}
            </div>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <approval-form
            size="mini"
            type="text"
            v-if="![1,2].includes(row.auditProcessDto.status)&&checkPermi(['process:contract:audit'])"
            :disabled="!(row.supplierContractVo.auditStatus===3&&row.supplierContractVo.attachment)"
            :orgCode="orgCode"
            :bindDeptId="projectDeptId"
            :auditTitle="buyItemName+'（'+subpackageName+'）合同审批'"
            :filterStr="filterStr"
            bindKey="winning_contract"
            :params="row"
            :verify-func="verifyForm"
            :submit-func="handleInitiate"
            @success="successSubmit"
          />
          <el-button
            v-if="!isEmpty(row.auditProcessDto.status)"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row.auditProcessDto.id)"
          >查看审批
          </el-button>
          <el-button
            v-if="checkPermi(['process:contract:back'])&&[0,1,2].includes(row.auditProcessDto.status)"
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleWithdraw(row.auditProcessDto)"
          >
            撤销审批
          </el-button>
          <el-button
            v-if="checkPermi(['process:contract:back'])"
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="sendBack(row.supplierContractVo)"
          >
            退回
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      top="2vh"
      title="草拟合同"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="合同内容" prop="htmlTemplate">
              <Tinymce ref="editor" v-model="form.htmlTemplate" :height="620" :max-height="620" v-if="open&&initEditor"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label=" ">
              <el-button type="primary" @click="submitForm">提交并发给供应商确认</el-button>
            </el-form-item>
            <el-form-item label=" ">
              <el-button @click="open = false">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { editContractTemplate, backContractTemplate, auditContract } from '@/api/contractManage';
import { mapGetters } from 'vuex'
import { isEmpty } from '@/utils'
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import { checkPermi } from '@/utils/permission'
import { cancelApprovalByCode } from '@/api/approval'
import { verifyEditor } from '@/utils/validate'

export default {
  name: 'ContractItem',
  components: { approvalForm },
  dicts: ['approval_process_status'],
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      open: false, // 表单参数
      form: {},
      // 表单校验
      rules: {
        htmlTemplate: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ]
      },
      initEditor: true
    };
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'orgCode',
      'purchaseMethodCode',
      'getPurchaseDict',
      'projectDeptId',
      'filterStr'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    isEmpty,
    handleDetail(id) {
      this.$router.push({ name: 'AlreadyApprovalDetail', params: { id }})
    },
    handleWithdraw(row) {
      this.$modal.confirm('是否确认撤销该审批？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await cancelApprovalByCode(row.auditCode);
            this.$modal.msgSuccess('撤销成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        });
    },
    verifyForm(row) {
      return new Promise((resolve, reject) => {
        if (row.supplierContractVo.auditStatus === 3 && row.supplierContractVo.attachment && ![1, 2].includes(row.auditProcessDto.status)) {
          resolve(true)
        } else {
          resolve(false)
        }
      })
    },
    successSubmit() {
      this.$emit('update');
    },
    handleInitiate(data) {
      return auditContract({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        subpackageCode: this.subpackageCode,
        contractId: data.supplierContractVo.id,
        attachment: data.supplierContractVo.attachment,
        supplierId: data.supplierContractVo.supplierId,
        supplierCompanyName: data.supplierContractVo.supplierCompanyName,
        auditProcessDto: data.auditProcessDto
      });
    },
    sendBack(data) {
      this.$modal.confirm('退回将清空之前的操作数据，是否确认退回？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await backContractTemplate({
            id: data.id,
            auditStatus: 0
          });
          this.$modal.msgSuccess('操作成功');
          this.$emit('update');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await editContractTemplate(this.form);
            this.$modal.msgSuccess('提交成功');
            this.open = false;
            this.$emit('update');
            this.$modal.closeLoading()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        htmlTemplate: null,
        attachment: null
      };
      this.resetForm('form');
    },
    handleEdit(data) {
      this.open = true;
      this.reset();
      for (let key in this.form) {
        this.form[key] = data[key]
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey);
    },
    getData(data) {
      let list = data || [];
      this.tableData = list.map(item => {
        return {
          auditProcessDto: item.auditProcessDto || {},
          supplierContractVo: item.supplierContractVo || {}
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.description {
  padding: 10px 15px 25px;
}
</style>
