<template>
  <div>
    <el-row :gutter="10" class="mb20">
      <el-col
        :span="1.5"
        v-if="checkFunction(['purchaser_benchmark_score'])"
      >
        <span
          style="display: inline-block;width: 110px;font-weight: bold;color: #606266;"
          class="pr10 text-right fontSize14"
        >
          价格总分
        </span>
        <el-input
          size="small"
          v-model.trim="priceTotalSource"
          placeholder="请输入价格总分"
          style="width: 180px;margin-right: 5px;"
          :disabled="!checkPermi(['process:benchmark:score'])"
        />
        <el-button
          size="small"
          type="primary"
          @click="handleSaveScore"
          v-if="checkPermi(['process:benchmark:score'])"
        >
          保存
        </el-button>
      </el-col>
    </el-row>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="110px"
      style="max-width: 600px;"
    >
      <el-form-item
        :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
        prop="pdfFile"
      >
        <file-upload-single
          :showUploadBtn="checkPermi(['process:purchaseFile:upload'])"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :showTip="true"
          :autoUpload="false"
          :file-size="0"
          accept=".zepc"
          uploadName="pdfFile"
          @onSuccess="handleUpload"
        >
          <el-button
            slot="upload-btn"
            type="primary"
            size="mini"
            :disabled="releaseStatus==='1'||[1,2].includes(processStatus)">
            {{ form.pdfFile ? '重新上传' : '上传' }}
          </el-button>
          <el-button
            slot="upload-right"
            type="primary"
            size="mini"
            v-if="form.pdfFile"
            @click="previewFile(form.pdfFile)"
          >预览
          </el-button>
          <el-button
            v-if="checkFunction(['purchase_file_import'])&&checkPermi(['process:purchaseFile:upload'])"
            :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
            slot="upload-right"
            type="primary"
            size="mini"
            @click="handleClickImport"
          >导入其他标段(包)文件
          </el-button>
          <el-button
            v-if="checkFunction(['purchase_file_apply'])&&checkPermi(['process:purchaseFile:upload'])"
            :disabled="!form.pdfFile"
            slot="upload-right"
            type="primary"
            size="mini"
            @click="handleClickUse"
          >将文件应用到其他标段(包)
          </el-button>
        </file-upload-single>
      </el-form-item>
      <el-form-item
        label=" "
        prop="isAgree"
        v-if="checkPermi(['process:purchaseFile:upload'])"
      >
        <el-checkbox
          v-model="form.isAgree"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
        >
          同意发布，已阅读并确认文件内容
        </el-checkbox>
      </el-form-item>

      <el-form-item label="查看审批" v-if="!isEmpty(processStatus)">
        <el-button
          type="primary"
          size="mini"
          @click="handleDetail"
        >查看
        </el-button>
      </el-form-item>
      <el-form-item label="审批状态" v-if="!isEmpty(processStatus)">
        <dict-tag :options="dict.type.approval_process_status" :value="processStatus"/>
        <div class="td-tip" v-if="processStatus===0">
          审批意见：{{ remark || '/' }}
        </div>
      </el-form-item>

      <el-form-item
        label=" "
        v-if="checkPermi(['process:purchaseFile:upload'])&&![1,2].includes(processStatus)"
      >
        <submit-approval
          v-if="versionType==='bjxk'"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :auditTitle="buyItemName+'（'+subpackageName+'）'+getPurchaseDict(purchaseMethodCode, 'purchaseFile')+'审批'"
          :verify-func="verifyForm"
          :submit-func="submitForm"
          @success="successSubmit"
        />
        <approval-form
          v-else
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :orgCode="orgCode"
          :bindDeptId="projectDeptId"
          :auditTitle="buyItemName+'（'+subpackageName+'）'+getPurchaseDict(purchaseMethodCode, 'purchaseFile')+'审批'"
          :filterStr="filterStr"
          bindKey="claims_file"
          :verify-func="verifyForm"
          :submit-func="submitForm"
          @success="successSubmit"
        />
      </el-form-item>
    </el-form>

    <el-dialog
      title="导入其他标段(包)文件"
      :visible.sync="isDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table :data="subpackageList" border>
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="标段(包)"
          align="center"
          prop="subpackageName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
          align="center"
          width="80"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.pdfFile"
              type="text"
              size="mini"
              @click="previewFile(row.pdfFile)"
            >预览
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          label="文件状态"
          align="center"
          prop="releaseStatus"
        >
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.purchase_file_status" :value="row.releaseStatus"/>
          </template>
        </el-table-column>
        <el-table-column
          label="报价表头是否一致"
          align="center"
          prop="areQuoteHeadEquals"
        >
          <template v-slot:default="{row}">
            {{ row.areQuoteHeadEquals === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.releaseStatus"
              :disabled="row.areQuoteHeadEquals!==1"
              type="text"
              size="mini"
              @click="importFile(row)"
            >导入
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer text-center">
        <el-button size="medium" @click="isDialog=false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="将文件应用到其他标段(包)"
      :visible.sync="isUseDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            :disabled="multiple"
            @click="handleUse()"
          >应用
          </el-button>
        </el-col>
      </el-row>
      <el-table
        :data="subpackageList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="标段(包)"
          align="center"
          prop="subpackageName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
          align="center"
          width="80"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.pdfFile"
              type="text"
              size="mini"
              @click="previewFile(row.pdfFile)"
            >预览
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          label="文件状态"
          align="center"
          prop="releaseStatus"
        >
          <template v-slot:default="{row}">
            <dict-tag
              :options="dict.type.purchase_file_status"
              :value="row.releaseStatus"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="报价表头是否一致"
          align="center"
          prop="areQuoteHeadEquals"
        >
          <template v-slot:default="{row}">
            {{ row.areQuoteHeadEquals === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              :disabled="row.areQuoteHeadEquals!==1"
              type="text"
              size="mini"
              @click="handleUse(row)"
            >应用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer text-center">
        <el-button size="medium" @click="isUseDialog=false">关闭</el-button>
      </div>
    </el-dialog>

    <TipProgress
      ref="progress"
      :percentage="percentage"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  // uploadPurchaseFile,
  purchaseFileComplete,
  releaseAndStampPurchaseFile,
  getPurchaseFileList,
  importOtherFile, applyToOtherPackages
} from '@/api/purchaser/purchaseFile';
import { checkPermi } from '@/utils/permission';
import { checkFunction } from '@/utils/permission';
import { saveSource } from '@/api/purchaser/startReview';
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import { getStore, isEmpty } from '@/utils'
// import { encryptFileToBinary } from '@/utils/jsencrypt'
import submitApproval from '@/views/purchaser/components/SubmitApproval/index.vue'
import Resumable from '@/plugins/resumable';
import { getToken } from '@/utils/auth'
import TipProgress from '@/components/TipProgress/index.vue'

export default {
  name: 'PurchaseFileItem',
  components: {
    submitApproval,
    approvalForm,
    TipProgress
  },
  dicts: ['purchase_file_status', 'approval_process_status'],
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    let validatorAgree = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请勾选'))
        return
      }
      callback();
    };
    return {
      releaseStatus: null, // 0已上传 1已发布
      form: {
        pdfFile: null,
        isAgree: false
      },
      rules: {
        pdfFile: [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        isAgree: [
          { required: true, validator: validatorAgree, trigger: 'change' }
        ]
      },
      isDialog: false,
      subpackageList: [],
      isUseDialog: false,
      selectSubpackage: [],
      subpackageCodeList: [],
      multiple: true,
      priceTotalSource: null,
      approvalId: null,
      processStatus: null, // 0 拒绝 1 通过 2 审批中 3 撤销
      remark: null, // 审批意见
      resumable: null,
      percentage: 100
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'orgCode',
      'purchaseMethodCode',
      'getPurchaseDict',
      'projectDeptId',
      'filterStr'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  mounted() {
    this.initResumable();
  },
  methods: {
    isEmpty,
    checkPermi,
    checkFunction,
    initResumable() {
      this.resumable = new Resumable({
        target: process.env.VUE_APP_BASE_API + '/bidding/purchase/claims/up', // 替换为你的上传地址
        // 可选，额外的查询参数
        query: {
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode
        },
        headers: {
          'Authorization': 'Bearer ' + getToken(),
          'validToken': getStore('validToken')
        },
        withCredentials: true,
        // setChunkTypeFromFile: true,
        fileType: ['zepc'], // 允许上传的文件类型
        chunkSize: 10 * 1024 * 1024, // 分块大小，单位字节，这里设置为1MB
        testChunks: false, // 是否测试分块完整性，默认为false
        throttleProgressCallbacks: 1, // 节流上传进度回调的频率（毫秒）
        simultaneousUploads: 4, // 同时上传的文件数
        fileParameterName: 'file', // 用于文件块的多部分请求参数的名称
        chunkNumberParameterName: 'chunkNumber', // 当前上传 POST 参数中用于文件块的块索引
        totalChunksParameterName: 'totalChunks', // 用于文件块的块总数 POST 参数的名称
        fileNameParameterName: 'filename', // 用于文件块的原始文件名 POST 参数的名称
        currentChunkSizeParameterName: 'currentChunkSize' // 用于文件块的当前块大小 POST 参数的名称
      });
      // 绑定文件选择器，如果你的上传按钮不是input[type=file]则需要调整选择器或手动添加文件。
      // this.resumable.assignBrowse(document.querySelector('input[type=file]'));
      // 文件添加时触发的事件处理函数
      this.resumable.on('fileAdded', this.onFileAdded);
      this.resumable.on('uploadStart', this.onUploadStart);
      this.resumable.on('fileRetry', this.onFileRetry);
      // 文件上传进度时触发的事件处理函数
      this.resumable.on('fileProgress', this.onFileProgress);
      // 文件上传成功时触发的事件处理函数
      this.resumable.on('fileSuccess', this.onFileSuccess);
      // 文件上传失败时触发的事件处理函数
      this.resumable.on('fileError', this.onFileError);
    },
    async handleUpload(data) {
      console.log(data.file)
      // 添加文件到上传队列中
      this.resumable.addFile(data.file);
      // 开始上传文件或队列中的文件
      // console.log(11111111)
      // try {
      //   console.log(data.file)
      //   this.$modal.loading('正在上传文件，请稍候...')
      //   let { file, aesKey } = await encryptFileToBinary(data.file);
      //   await uploadPurchaseFile({
      //     buyItemCode: this.buyItemCode,
      //     subpackageCode: this.subpackageCode,
      //     aesKey: aesKey,
      //     file: data.file
      //   })
      //   this.$modal.msgSuccess('上传成功');
      //   this.$emit('update');
      //   this.$modal.closeLoading();
      // } catch (e) {
      //   this.$modal.closeLoading();
      //   throw e
      // }
    },
    onFileAdded(file, event) {
      console.log('File added:', file, event);
      this.$refs.progress.show();
      this.resumable.upload();
    },
    onUploadStart() {
      // 上传开始前触发，可以用来构造 FormData 和发送数据
      console.log('onUploadStart:', this.resumable.files)
    },
    onFileRetry(file) {
      console.log('File retry:', file);
    },
    onFileProgress(file) {
      console.log('File progress:', file.progress()); // 获取上传进度信息，范围从0到100。
      this.percentage = Math.round(file.progress() * 100)
    },
    async onFileSuccess(file, message) {
      console.log('File uploaded successfully:', file, message);
      this.resumable.cancel();
      try {
        await purchaseFileComplete({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode
        })
        this.$modal.msgSuccess('上传成功');
        this.$emit('update');
        this.$refs.progress.close();
      } catch (e) {
        this.$refs.progress.close();
        throw new Error(e);
      }
    },
    onFileError(file, message) {
      console.error('File upload error:', file, message);
      this.resumable.cancel();
      this.$modal.msgError(message);
      this.$refs.progress.close();
    },
    handleDetail() {
      this.$router.push({
        name: 'AlreadyApprovalDetail',
        params: {
          id: this.approvalId
        }
      })
    },
    async handleSaveScore() {
      try {
        let reg = /^\d\.([1-9]{1,2}|[0-9][1-9])$|^[1-9]\d{0,1}(\.\d{1,2}){0,1}$|^100(\.0{1,2}){0,1}$/
        if (!reg.test(this.priceTotalSource)) {
          this.$modal.msgError('价格总分，必须大于0，小于等于100，并且最多保留两位小数');
          return;
        }
        this.$modal.loading('数据提交中，请稍候...');
        await saveSource({
          priceTotalSource: Number(this.priceTotalSource),
          subpackageCode: this.subpackageCode
        })
        this.$emit('update');
        this.$modal.msgSuccess('提交成功');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleSelectionChange(selection) {
      this.selectSubpackage = selection;
      this.subpackageCodeList = selection.map(item => item.subpackageCode);
      this.multiple = !selection.length;
    },
    handleUse(row) {
      let subpackageCodeList = row ? [row.subpackageCode] : this.subpackageCodeList;
      this.$confirm('确定要将该标段(包)文件应用到其他该标段(包)', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '再考虑',
        confirmButtonText: '确认应用',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await applyToOtherPackages({
            applySubpackageCodeList: subpackageCodeList,
            subpackageCode: this.subpackageCode
          });
          this.$modal.msgSuccess('操作成功');
          this.isUseDialog = false;
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    importFile(row) {
      this.$confirm('确定要导入该标段(包)文件', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '再考虑',
        confirmButtonText: '确认导入',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await importOtherFile({
            importSubpackageCode: row.subpackageCode,
            subpackageCode: this.subpackageCode
          });
          this.$modal.msgSuccess('操作成功');
          this.isDialog = false;
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    async handleClickUse() {
      await this.getFileList();
      this.isUseDialog = true;
    },
    async handleClickImport() {
      await this.getFileList();
      this.isDialog = true;
    },
    async getFileList() {
      try {
        let { rows } = await getPurchaseFileList({
          pageNum: 1,
          entity: {
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode
          }
        })
        this.subpackageList = rows;
      } catch (e) {
        throw new Error(e)
      }
    },
    verifyForm() {
      return new Promise((resolve) => {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            let tipHtml = `<p>1、对本文件内容已确认无异议</p><p>2、本文件审核通过之后将无法撤回</p>`
            this.$confirm(tipHtml, '提示', {
              customClass: 'max-tip',
              dangerouslyUseHTMLString: true,
              cancelButtonText: '再考虑',
              confirmButtonText: '提交审批',
              type: 'warning'
            }).then(() => {
              resolve(true)
            }).catch(() => {
              resolve(false)
            })
          } else {
            resolve(false)
          }
        });
      })
    },
    successSubmit() {
      this.$emit('update');
    },
    submitForm(data) {
      return releaseAndStampPurchaseFile({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        subpackageCode: this.subpackageCode,
        ...this.form,
        ...data
      });
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    getData(val) {
      let data = val.data || {};
      let auditProcessDto = val.auditProcessDto || {};
      this.priceTotalSource = data.priceTotalSource;
      this.releaseStatus = data.releaseStatus;
      this.form.pdfFile = data.pdfFile;
      this.form.isAgree = data.releaseStatus === '1';
      this.processStatus = auditProcessDto.status;
      this.remark = auditProcessDto.remark;
      this.approvalId = auditProcessDto.id;
    }
  }
}
</script>

<style scoped>

</style>
