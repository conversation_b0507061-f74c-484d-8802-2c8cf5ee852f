<template>
  <el-tabs tab-position="left" v-model="activeName">
    <el-tab-pane
      v-for="(item,index) in subpackageList"
      :key="index"
      :label="'标段(包)：'+item.subpackageName"
      :name="item.subpackageCode"
    >
      <question-item
        v-if="activeName===item.subpackageCode"
        :key="item.subpackageCode"
        :subpackageCode="item.subpackageCode"
        :subpackageName="item.subpackageName"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { mapGetters } from 'vuex';
import questionItem from '@/views/purchaser/process/question/questionItem.vue';
import { getSubpackageList } from '@/api/purchaser/projectList';

export default {
  name: 'Question',
  components: { questionItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeName: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      try {
        this.loading = true;
        let { data } = await getSubpackageList(this.buyItemCode);
        this.subpackageList = data;
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e;
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
