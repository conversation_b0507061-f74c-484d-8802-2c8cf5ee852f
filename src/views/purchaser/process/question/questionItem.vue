<template>
  <div>
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      size="small"
      :inline="true"
      label-width="80px"
      v-if="checkPermi(['process:question:operate'])&&checkFunction(['purchaser_question_time'])"
    >
      <el-form-item label="质疑类型" prop="doubtType">
        <el-select v-model="form.doubtType" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.doubt_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="质疑时间" prop="timeList">
        <el-date-picker
          v-model="form.timeList"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleSaveTime">保存</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="timeData"
      class="mb20"
      border
      v-if="checkFunction(['purchaser_question_time'])"
    >
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="质疑类型" align="center">
        <template v-slot:default="{row}">
          {{ doubtTypeFormat(row.doubtType) }}
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center">
        <template v-slot:default="{row}">
          {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center">
        <template v-slot:default="{row}">
          {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="质疑人名称" align="center" prop="doubtUserName"/>
      <el-table-column label="收到质疑时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="质疑回复时间" align="center" prop="updateAt">
        <template v-slot:default="{row}">
          <span v-if="row.updateAt">{{ row.updateAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="质疑类型" align="center" prop="doubtType">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.doubt_type" :value="row.doubtType"/>
        </template>
      </el-table-column>
      <el-table-column label="质疑内容" align="center" width="100">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.doubtFileKey)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="质疑内容附件" align="center" prop="doubtAnnexFileKey" width="110">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            v-if="row.doubtAnnexFileKey"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.doubtAnnexFileKey)"
          >下载
          </el-button>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="质疑回复" align="center" width="100">
        <template v-slot:default="{row}">
          <el-button
            v-if="row.replyFileKey"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.replyFileKey)"
          >查看
          </el-button>
          <div v-else>
            <el-button
              v-if="checkPermi(['process:question:operate'])"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleReply(row)"
            >回复
            </el-button>
            <span v-else>/</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="质疑回复附件" align="center" width="110">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            v-if="row.replyAnnexFileKey"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.replyAnnexFileKey)"
          >下载
          </el-button>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="质疑"
      :visible.sync="isDialog"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-form ref="replyForm" :model="replyForm" :rules="rules" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item prop="fileHtml">
              <Tinymce ref="editor" v-model="replyForm.fileHtml" :height="700" :max-height="700" v-if="isDialog"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6" style="max-width: 400px;">
            <el-form-item>
              <el-button type="success" @click="handlePreview">预览</el-button>
            </el-form-item>
            <el-form-item label="附件" prop="replyAnnexFileKey">
              <file-upload
                :limit="1"
                :params="{
                  fileTypeName: 'question_reply_annex',
                  buyItemCode: buyItemCode,
                  subpackageCode: subpackageCode,
                  yearMonthSplit: createYearMonth,
                }"
                @change="getFileKey"
              >
              </file-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitReplyForm">提交</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestion, putAndReplyQuestion, getQuestionTime, saveQuestionTime } from '@/api/question';
import { mapGetters } from 'vuex';
import { templatePreview } from '@/api/system/fileTemplate';
import { checkFunction, checkPermi } from '@/utils/permission';
import { verifyEditor } from '@/utils/validate'

export default {
  name: 'QuestionItem',
  dicts: ['doubt_type'],
  props: {
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      form: {
        doubtType: null,
        timeList: []
      },
      timeData: [],
      tableData: [],
      isDialog: false,
      replyForm: {
        id: null,
        doubtType: null,
        fileHtml: '',
        replyAnnexFileKey: null
      },
      rules: {
        doubtType: [{ required: true, message: '请选择', trigger: 'change' }],
        timeList: [{ required: true, message: '请选择', trigger: 'change' }],
        fileHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'createYearMonth',
      'buyItemCode',
      'userId'
    ])
  },
  created() {
    this.getTime();
    this.getList();
  },
  methods: {
    checkFunction,
    checkPermi,
    doubtTypeFormat(value) {
      return this.selectDictLabel(this.dict.type.doubt_type, value);
    },
    getFileKey(key) {
      this.replyForm.replyAnnexFileKey = key
    },
    submitReplyForm() {
      this.$refs['replyForm'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await putAndReplyQuestion({
              subpackageCode: this.subpackageCode,
              replyUserId: this.userId,
              ...this.replyForm
            })
            this.isDialog = false;
            this.$modal.msgSuccess('提交成功');
            await this.getList();
            this.$modal.closeLoading()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      });
    },
    handleReply(row) {
      this.replyForm.id = row.id;
      this.replyForm.doubtType = row.doubtType;
      this.replyForm.fileHtml = '';
      this.replyForm.replyAnnexFileKey = null;
      this.isDialog = true;
    },
    handlePreview() {
      if (!this.replyForm.fileHtml) {
        this.$modal.msgError('内容不能为空')
        return
      }
      this.$modal.loading('数据提交中，请稍候...')
      templatePreview({ filePreview: this.replyForm.fileHtml }).then(res => {
        this.$modal.closeLoading()
        this.$pdfViewDialog({ data: res.data, type: 'blob' })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleSaveTime() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await saveQuestionTime({
              buyItemCode: this.buyItemCode,
              subpackageName: this.subpackageName,
              subpackageCode: this.subpackageCode,
              doubtType: this.form.doubtType,
              startTime: this.form.timeList[0],
              endTime: this.form.timeList[1]
            })
            this.resetForm('form');
            this.$modal.msgSuccess('设置成功');
            this.$modal.closeLoading();
            await this.getTime();
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }
      })
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    async getTime() {
      try {
        let { data } = await getQuestionTime(this.subpackageCode);
        this.timeData = data || [];
      } catch (e) {
        throw new Error(e);
      }
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listQuestion({
          doubtUserType: '1',
          subpackageCode: this.subpackageCode
        })
        this.tableData = data;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
