<template>
  <div v-loading="loading">
    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <project-approval-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <project-approval-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import projectApprovalItem from '@/views/purchaser/process/projectApproval/projectApprovalItem.vue'
import { getProjectApprovalList } from '@/api/purchaser/projectApproval'

export default {
  name: 'ProjectApproval',
  components: { projectApprovalItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      try {
        this.loading = true;
        let { data } = await getProjectApprovalList(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
