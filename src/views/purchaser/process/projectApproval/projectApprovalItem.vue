<template>
  <div>
    <el-row :gutter="10" class="mb10" v-if="checkPermi(['process:projectApproval:add'])">
      <el-col :span="1.5">
        <approval-form
          icon="el-icon-plus"
          size="mini"
          plain
          :showAnnex="true"
          :showCustomize="true"
          :orgCode="orgCode"
          :bindDeptId="projectDeptId"
          :params="{buyItemCode,subpackageCode,purchaseMethodCode,yearMonthSplit:createYearMonth}"
          :filterStr="filterStr"
          :verify-func="verifyForm"
          :submit-func="submitForm"
          @success="successSubmit"
        />
      </el-col>
    </el-row>

    <el-table :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="审批发起人" align="center" prop="paramApplyUser"/>
      <el-table-column label="审批类型" align="center" prop="auditType">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.approval_type" :value="row.auditType"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row.auditId)"
          >查看审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { isEmpty } from '@/utils'
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import { checkPermi } from '@/utils/permission'
import { initiateProjectApproval } from '@/api/purchaser/projectApproval'

export default {
  dicts: ['approval_type'],
  name: 'ProjectApprovalItem',
  components: { approvalForm },
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: []
    };
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'createYearMonth',
      'purchaseMethodCode',
      'orgCode',
      'projectDeptId',
      'filterStr'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    isEmpty,
    handleDetail(id) {
      this.$router.push({ name: 'AlreadyApprovalDetail', params: { id }})
    },
    verifyForm() {
      return new Promise((resolve, reject) => {
        resolve(true)
      })
    },
    successSubmit() {
      this.$emit('update');
    },
    submitForm(data) {
      return initiateProjectApproval({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        buyItemName: this.buyItemName,
        subpackageCode: this.subpackageCode,
        subpackageName: this.subpackageName,
        ...data
      });
    },
    getData(data) {
      this.tableData = data || [];
    }
  }
};
</script>
<style lang="scss" scoped>
.description {
  padding: 10px 15px 25px;
}
</style>
