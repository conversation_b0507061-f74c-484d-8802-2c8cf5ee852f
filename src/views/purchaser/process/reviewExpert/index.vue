<template>
  <div v-loading="loading">
    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <review-expert-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          :auditStatus="item.auditProcessDto?item.auditProcessDto.status: null"
          :expertDeptShow="expertDeptShow"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <review-expert-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          :auditStatus="item.auditProcessDto?item.auditProcessDto.status: null"
          :expertDeptShow="expertDeptShow"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { queryJudgeList } from '@/api/purchaser/reviewExpert';
import { mapGetters } from 'vuex';
import reviewExpertItem from '@/views/purchaser/process/reviewExpert/reviewExpertItem.vue';

export default {
  name: 'ReviewExpert',
  components: { reviewExpertItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null,
      expertDeptShow: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getConfigKey('expert.dept.show').then(response => {
      this.expertDeptShow = response.data
    })
    this.getList()
  },
  methods: {
    async getList() {
      try {
        this.loading = true;
        let { data } = await queryJudgeList(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
