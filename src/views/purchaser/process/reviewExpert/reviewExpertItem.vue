<template>
  <div>
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" v-if="checkPermi(['process:expert:add'])&&checkFunction(['purchaser_expert_import'])">
        <el-button
          type="primary"
          size="mini"
          plain
          @click="handleImport">
          导入抽取评委
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="checkPermi(['process:expert:add'])">
        <el-button
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          @click="handleAdd">
          添加评委
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="checkPermi(['process:expert:export'])&&checkFunction(['purchaser_expert_export'])">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出评委抽取表
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" empty-text="请添加评委" border>
      <el-table-column label="评委姓名" align="center" prop="judgeName" :show-overflow-tooltip="true"/>
      <el-table-column label="类别" align="center" prop="specialtyCategory" :show-overflow-tooltip="true"/>
      <el-table-column
        v-if="expertDeptShow!=='0'"
        :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
        align="center"
        prop="dept"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="联系方式"
        align="center"
        prop="expertCellphone"
        :show-overflow-tooltip="true"></el-table-column>
      <el-table-column
        label="账号/工号"
        align="center"
        prop="judgeAccount"
        :show-overflow-tooltip="true"></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="60"
        class-name="small-padding fixed-width"
        v-if="checkPermi(['process:expert:add'])"
      >
        <template v-slot:default="{$index}">
          <el-button
            v-if="!checkFunction(['purchaser_expert_audit'])"
            size="mini"
            type="text"
            class="btn-text-danger"
            icon="el-icon-delete"
            @click="handleRemove($index)"
          >删除
          </el-button>
          <el-button
            v-if="checkFunction(['purchaser_expert_audit'])&&(!auditStatus || auditStatus===0 || auditStatus===3)"
            size="mini"
            type="text"
            class="btn-text-danger"
            icon="el-icon-delete"
            @click="handleRemove($index)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="text-center mt15" v-if="!checkFunction(['purchaser_expert_audit'])">
      <el-button type="primary" @click="submit" v-if="checkPermi(['process:expert:add'])">
        保存
      </el-button>
    </div>
    <div class="text-center mt15" v-if="checkFunction(['purchaser_expert_audit'])&&(!auditStatus || auditStatus===0 || auditStatus===3)">
      <el-button type="primary" @click="submit" v-if="checkPermi(['process:expert:add'])">
        保存
      </el-button>
      <approval-form
        v-if="checkFunction(['purchaser_expert_audit'])&&checkPermi(['process:expert:audit'])"
        bindKey="extract_judge"
        :bindDeptId="projectDeptId"
        :orgCode="orgCode"
        :filterStr="filterStr"
        auditTitle="专家审批"
        :verify-func="verifyForm"
        :submit-func="submitAudit"
        @success="successSubmit"
      />
    </div>
    <div
      class="mt15"
      style="display: flex;justify-content: center;"
      v-if="checkFunction(['purchaser_expert_audit'])&&(auditStatus===1 || auditStatus===2)">
      审批状态：
      <dict-tag :options="dict.type.approval_process_status" :value="auditStatus"/>
    </div>

    <el-dialog
      title="添加评委"
      center
      :visible.sync="isDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px" @submit.native.prevent>
        <el-form-item label="评委姓名">
          <el-input
            v-model.trim="queryParams.nickName"
            placeholder="请输入评委姓名"
            clearable
            style="width: 240px"
            @keyup.enter.native="getExpertList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getExpertList">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="isLoading" :data="expertList" v-if="expertList.length>0">
        <el-table-column label="评委姓名" align="center" prop="nickName" :show-overflow-tooltip="true"/>
        <el-table-column label="类别" align="center" prop="type" :show-overflow-tooltip="true"/>
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="department"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="联系方式" align="center" prop="phonenumber" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="账号/工号" align="center" prop="userName" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="60"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="!tableData.some(item=>item.judgeId===row.userId)"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleConfirm(row)"
            >添加
            </el-button>
            <span v-else>已添加</span>
          </template>
        </el-table-column>
      </el-table>
      <el-empty description="暂无数据" :image-size="100" v-loading="isLoading" v-else></el-empty>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="isDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getJudgeInfoByName,
  saveExtractJudgeInfo,
  queryLogJudge,
  auditJudgeInfo
} from '@/api/purchaser/reviewExpert';
import { mapGetters } from 'vuex';
import { checkPermi, checkFunction } from '@/utils/permission';
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
// import { blobValidate } from '@/utils/ruoyi';

export default {
  name: 'ReviewExpertItem',
  dicts: ['approval_process_status', 'sys_dict_translate'],
  components: { approvalForm },
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    },
    auditStatus: {
      type: [String, Number],
      default: null
    },
    expertDeptShow: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableData: [],
      expertList: [],
      isDialog: false,
      queryParams: {
        nickName: null
      },
      isLoading: false
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'projectDeptId',
      'orgCode',
      'filterStr'
    ])
  },
  watch: {
    itemData(value) {
      this.tableData = this._.cloneDeep(value || []);
    }
  },
  created() {
    this.tableData = this._.cloneDeep(this.itemData || []);
  },
  methods: {
    checkPermi,
    checkFunction,
    async handleExport() {
      this.$modal.msgWarning('功能暂未开发')
    },
    successSubmit() {
      this.$emit('update');
    },
    verifyForm() {
      return new Promise((resolve) => {
        if (this.itemData.length > 0) {
          resolve(true)
        } else {
          this.$message.warning('请先保存专家');
          resolve(false)
        }
      })
    },
    submitAudit(data) {
      let { auditProcessDto } = data;
      return auditJudgeInfo({
        buyItemCode: this.buyItemCode,
        subpackageCode: this.subpackageCode,
        voList: this.itemData,
        auditProcessDto
      });
    },
    async submit() {
      try {
        if (this.tableData.length === 0) {
          this.$modal.msgError('请添加评委');
          return
        }
        this.$modal.loading('数据提交中，请稍候...');
        await saveExtractJudgeInfo({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          voList: this.tableData
        })
        this.$emit('update');
        this.$modal.closeLoading();
        this.$modal.msgSuccess('保存成功');
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleConfirm(item) {
      this.tableData.push({
        judgeName: item.nickName,
        judgeId: item.userId,
        idNumber: item.idNumber,
        expertCellphone: item.phonenumber,
        specialtyCategory: item.type,
        dept: item.department,
        judgeAccount: item.userName
      })
    },
    async getExpertList() {
      try {
        if (!this.queryParams.nickName) {
          this.expertList = [];
          return
        }
        this.isLoading = true;
        let { data } = await getJudgeInfoByName(this.queryParams);
        this.expertList = data;
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        throw new Error(e);
      }
    },
    handleRemove(index) {
      this.tableData.splice(index, 1);
    },
    handleAdd() {
      this.queryParams.nickName = null;
      this.expertList = [];
      this.isDialog = true;
    },
    // 导入抽取评委
    async handleImport() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryLogJudge({
          buyItemName: this.buyItemName,
          buyItemCode: this.buyItemCode,
          subpackageName: this.subpackageName
        });
        this.tableData = data.map(item => {
          return {
            judgeName: item.nickName,
            judgeId: item.userId,
            idNumber: item.idNumber,
            expertCellphone: item.phonenumber,
            specialtyCategory: item.type,
            dept: item.department,
            judgeAccount: item.userName
          }
        });
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
