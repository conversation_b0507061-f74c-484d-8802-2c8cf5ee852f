<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="0px" v-loading="loading">
    <ul class="qa-list">
      <li class="overflow" v-for="(item,index) in form.tableData" :key="index">
        <div class="qa-item">
          <span>【{{ item.askUserName + ' - 问' }}】{{ item.askContent }}</span>
          <span class="qa-time">提交时间：{{ item.createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </div>
        <div class="qa-item" v-if="item.answerContent">
          <span>【答】{{ item.answerContent }}</span>
          <span class="qa-time">回复时间：{{ item.updateAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </div>
        <el-form-item
          label=""
          :prop="'tableData.'+index+'.content'"
          :rules="rules.content"
          style="max-width: 600px;"
          v-if="!item.answerContent&&checkPermi(['process:answerQuestions:operate'])">
          <div class="flex-box">
            <el-input
              type="textarea"
              maxlength="200"
              resize="none"
              :rows="2"
              placeholder="请输入内容"
              v-model="item.content">
            </el-input>
            <el-button class="flex-btn" type="primary" size="small" @click="handleReply(item,'tableData.'+index+'.content')">
              回复
            </el-button>
          </div>
        </el-form-item>
      </li>
    </ul>
    <el-empty description="暂无数据" v-if="form.tableData.length===0"></el-empty>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex';
import { replyAnswerQuestions, listAnswerQuestions } from '@/api/answerQuestions';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'AnswerQuestionsItem',
  props: {
    subpackageCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      activeName: 'first',
      loading: false,
      form: {
        tableData: []
      },
      rules: {
        content: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleReply(row, prop) {
      this.$refs['form'].validateField(prop, async valid => {
        if (!valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await replyAnswerQuestions({
              subpackageCode: this.subpackageCode,
              id: row.id,
              answerUserId: this.userId,
              answerUserName: this.nickName,
              answerUserType: '2',
              answerContent: row.content
            })
            this.$modal.msgSuccess('回复成功');
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listAnswerQuestions({
          subpackageCode: this.subpackageCode,
          askUserType: '1'
        });
        this.form.tableData = data || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;

  .flex-btn {
    align-self: flex-end;
    margin-left: 5px;
  }
}

.qa-list {
  li {
    font-size: 14px;
    line-height: 1.5;
    border-bottom: 1px solid #dfe6ec;
    padding: 10px 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .qa-item {
    margin-bottom: 5px;

    span {
      word-wrap: break-word;
      word-break: break-all;
    }

    .qa-time {
      font-size: 12px;
      color: #808080;
      margin-left: 15px;
    }
  }
}
</style>
