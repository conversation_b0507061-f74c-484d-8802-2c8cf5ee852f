<template>
  <div>
    <el-form ref="form" size="small" :model="form" :rules="rules" label-position="top">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="18" :md="18" :lg="16" :xl="14">
          <el-form-item prop="reviewReportHtml">
            <template-select
              v-if="isShow"
              :min-height="600"
              :max-height="1000"
              tmp-type="review_report"
              v-model="form.reviewReportHtml"
              :getTemplateFunc="getTemplateFunc"
            />
            <pdf-view :src="pdfData" type="blob" v-else/>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="8" :xl="10" style="max-width: 600px;">
          <el-form-item v-if="checkPermi(['process:reviewReport:edit'])">
            <el-button type="primary" @click="submitForm" v-if="isShow">提交</el-button>
            <el-button type="primary" @click="cancelForm" v-if="isShow&&pdfData">取消</el-button>
            <el-button type="primary" @click="isShow = true" v-if="!isShow">编辑</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { checkPermi } from '@/utils/permission'
import { verifyEditor } from '@/utils/validate'
import { fillTemplate } from '@/api/purchaser/bulletin'
import { editReviewReport } from '@/api/purchaser/reviewReport'
import { fileDown } from '@/api/file'
import { blobValidate } from '@/utils/ruoyi'

export default {
  name: 'ReviewReportItem',
  props: {
    itemData: {
      type: String,
      default: null
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: {},
      rules: {
        reviewReportHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ]
      },
      pdfData: null,
      isShow: true
    };
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'createYearMonth',
      'purchaseMethodCode'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    cancelForm() {
      this.form.reviewReportHtml = '';
      this.isShow = false;
    },
    getTemplateFunc(data) {
      return fillTemplate({
        buyItemCode: this.buyItemCode,
        purchaseMethodCode: this.purchaseMethodCode,
        tmpKey: data.tmpKey,
        tmpType: data.tmpType
      });
    },
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await editReviewReport({
              ...this.form,
              subpackageCode: this.subpackageCode
            });
            this.$modal.msgSuccess('提交成功');
            this.open = false;
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }
      });
    },
    async getData(data) {
      if (!data) {
        this.pdfData = null;
        this.isShow = true;
        return
      }
      try {
        let res = await fileDown(data)
        const isBlob = blobValidate(res.data)
        if (isBlob) {
          this.pdfData = res.data;
          this.isShow = false;
        } else {
          this.isShow = true;
          await this.$download.printErrMsg(res.data)
        }
      } catch (e) {
        this.isShow = true;
        throw new Error(e);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.description {
  padding: 10px 15px 25px;
}
</style>
