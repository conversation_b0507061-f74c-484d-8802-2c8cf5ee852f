<template>
  <div v-loading="loading">
    <el-row :gutter="10" class="mb10" v-if="versionType==='whws'">
      <el-col :span="1.5">
        <select-notification-supplier/>
      </el-col>
    </el-row>

    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <registration-supplier-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :isSignReview="isSignReview"
          :commentLevelOff="commentLevelOff"
          :ipShow="ipShow"
          :itemData="item.data"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <registration-supplier-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :isSignReview="isSignReview"
          :commentLevelOff="commentLevelOff"
          :ipShow="ipShow"
          :itemData="item.data"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { supplierSignList } from '@/api/purchaser/registrationSupplier';
import { mapGetters } from 'vuex';
import registrationSupplierItem from '@/views/purchaser/process/registrationSupplier/registrationSupplierItem.vue';
import selectNotificationSupplier from '@/views/purchaser/process/registrationSupplier/selectNotificationSupplier.vue'

export default {
  name: 'RegistrationSupplier',
  components: {
    registrationSupplierItem,
    selectNotificationSupplier
  },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null,
      isSignReview: '0',
      commentLevelOff: '0',
      ipShow: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('supplier_sign_up.review_status.enabled').then(response => {
      this.isSignReview = response.data || '0';
    })
    this.getConfigKey('sys.commentLevel.off').then(response => {
      this.commentLevelOff = response.data || '0'
    })
    this.getConfigKey('sys.ip.show').then(response => {
      this.ipShow = response.data
    })
    this.getList();
  },
  methods: {
    async getList() {
      try {
        this.loading = true;
        let { data } = await supplierSignList(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
