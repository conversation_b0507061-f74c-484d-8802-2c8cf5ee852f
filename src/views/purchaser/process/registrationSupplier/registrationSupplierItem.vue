<template>
  <div>
    <el-row
      :gutter="10"
      class="mb10"
    >
      <el-col
        :span="1.5"
        v-if="checkFunction(['purchaser_registration_export'])&&checkPermi(['process:registration:export'])"
      >
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出报名信息表
        </el-button>
      </el-col>
      <el-col
        :span="1.5"
        v-if="checkFunction(['purchaser_shareholder_info'])"
      >
        <el-button
          type="primary"
          plain
          icon="el-icon-coin"
          size="mini"
          @click="handleContrast"
        >
          报名单位对比
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" empty-text="暂无供应商报名" border>
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="50px"
      />
      <el-table-column
        label="供应商"
        align="center"
        prop="supplierSignUpInfo.bidderName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')"
        align="center"
        prop="supplierSignUpInfo.infoReporterName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
        align="center"
        prop="supplierSignUpInfo.infoReporterContactNumber"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="供应商信息"
        align="center"
        width="100"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="报名信息审核状态"
        align="center"
        v-if="isSignReview==='1'"
      >
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.sign_audit_status" :value="row.reviewStatus"/>
          <div class="td-tip" v-if="row.reviewStatus===2">
            理由：{{ row.reviewReason || '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="报名时间"
        align="center"
        prop="signUpCreateAtTime"
        width="155"
      >
        <template v-slot:default="{row}">
          {{ row.signUpCreateAtTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column
        label="报名IP地址"
        align="center"
        prop="signUpIp"
        v-if="ipShow==='1'"
      >
      </el-table-column>
      <el-table-column
        label="付款凭证"
        align="center"
        v-if="checkFunction(['purchaser_payment_voucher'])"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            v-if="row.paymentVoucher"
            @click="previewFileByAnnex(row.paymentVoucher)"
          >查看
          </el-button>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否允许响应"
        align="center"
        prop="permit"
        width="100px"
        v-if="checkFunction(['purchaser_allow_bidding'])"
      >
        <template v-slot:default="{row}">
          <el-switch
            :disabled="!checkPermi(['process:registration:permit'])"
            v-model="row.permit"
            active-value="1"
            inactive-value="0"
            @change="handlePermitChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="响应文件"
        align="center"
        width="100"
        v-if="checkFunction(['purchaser_purchase_file_no_client'])"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleResponseFile(row.supplierId,row.supplierSignUpInfo.bidderName)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="是否响应"
        align="center"
        v-if="checkFunction(['purchaser_purchase_file'])"
      >
        <template v-slot:default="{row}">
          {{ row.releaseStatus === '1' ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        label="响应时间"
        align="center"
        prop="submissionTime"
        width="155"
        v-if="checkFunction(['purchaser_purchase_file'])"
      >
        <template v-slot:default="{row}">
          {{ row.submissionTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
        </template>
      </el-table-column>
      <el-table-column
        label="响应上传IP地址"
        align="center"
        prop="ip"
        v-if="checkFunction(['purchaser_purchase_file'])&&ipShow==='1'">
      </el-table-column>
      <el-table-column
        label="是否合格供应商"
        prop="qualified"
        align="center"
        v-if="checkFunction(['purchaser_set_supplier_qualified'])"
      >
        <template v-slot:default="{row}">
          <el-switch
            :disabled="!checkPermi(['process:registration:qualified'])"
            v-model="row.qualified"
            active-value="1"
            inactive-value="0"
            @change="handleQualifiedChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="评论"
        align="center"
        width="120"
      >
        <template v-slot:default="{row}">
          <comment-supplier
            :supplierId="row.supplierId"
            :supplierName="row.supplierSignUpInfo.bidderName"
            :buyItemName="buyItemName"
            :buyItemCode="buyItemCode"
            :subpackageName="subpackageName"
            :subpackageCode="subpackageCode"
            :commentLevelOff="commentLevelOff"
          />
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="供应商信息"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="1vh"
      width="90%"
      modal-append-to-body
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-card
        header="主体信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 10px 10px'}"
        class="card-box"
      >
        <el-descriptions
          direction="vertical"
          :column="3"
          border
        >
          <el-descriptions-item label="公司名称">
            {{ infoData.bidderName || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码">
            {{ infoData.licNumber || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="类别">
            <dict-tag :options="dict.type.supplier_bid_type" :value="infoData.bidType"/>
          </el-descriptions-item>
          <el-descriptions-item label="行政区域">
            {{ getParents(cityList, infoData.regionCode, 'code', 'name').join(' / ') || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="详细地址">
            {{ infoData.contactAddress || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="电子邮箱">
            {{ infoData.email || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商类型" v-if="versionType!=='whws'">
            <dict-tag :options="dict.type.supplier_type" :value="infoData.unitNature"/>
          </el-descriptions-item>
          <el-descriptions-item label="开户银行">
            {{ infoData.openingBank || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="基本账户账号">
            {{ infoData.basicAccount || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本">
            {{ infoData.registeredCapital || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="营业期限">
            {{ infoData.operatingPeriod || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{ infoData.dateOfEstablishment || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="登记机关">
            {{ infoData.registrationAndAuthority || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="是否中小微企业">
            {{ infoData.whetherMicroEnterprises === 1 ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="中小微企业" v-if="infoData.whetherMicroEnterprises===1">
            <el-button
              type="text"
              icon="el-icon-view"
              size="mini"
              v-if="infoData.microEnterprises"
              @click="previewFileByOther(infoData.microEnterprises)"
            >预览
            </el-button>
            <span v-else>/</span>
          </el-descriptions-item>
          <el-descriptions-item label="营业执照或组织机构代码证件扫描件">
            <el-button
              type="text"
              icon="el-icon-view"
              size="mini"
              v-if="infoData.businessLicense"
              @click="previewFileByOther(infoData.businessLicense)"
            >
              预览
            </el-button>
            <span v-else>/</span>
          </el-descriptions-item>
          <el-descriptions-item label="附件（安全许可证、企业资质等）">
            <file-list-view
              v-if="infoData.supplierAttachmentList&&infoData.supplierAttachmentList.length>0"
              :fileList="infoData.supplierAttachmentList"
              :handlePreview="previewFileByOther"
              :handleDown="downloadFileByOther"
            />
            <span v-else>/</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card
        header="法定代表人信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 10px 10px'}"
        class="card-box"
      >
        <el-descriptions direction="vertical" :column="3" border>
          <el-descriptions-item label="法定代表人姓名">
            {{ infoData.certificateName || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人证件类型">
            <dict-tag :options="dict.type.id_type" :value="infoData.certificate"/>
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人证件号码">
            {{ infoData.certificateCode | formatIdCard() }}
          </el-descriptions-item>
          <el-descriptions-item label="经营负责人手机号码（法定代表人或实际控制人，不得使用其他联系人号码）">
            {{ infoData.contactNumber || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人身份证扫描件">
            <el-button
              type="text"
              icon="el-icon-view"
              size="mini"
              v-if="infoData.legalRepresentativeIdentityCertificate"
              @click="previewFileByOther(infoData.legalRepresentativeIdentityCertificate)"
            >预览
            </el-button>
            <span v-else>/</span>
          </el-descriptions-item>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')">
            {{ infoData.infoReporterName || '/' }}
          </el-descriptions-item>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporter')">
            <dict-tag :options="dict.type.id_type" :value="infoData.infoReporter"/>
          </el-descriptions-item>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterCode')">
            {{ infoData.infoReporterCode | formatIdCard() }}
          </el-descriptions-item>
          <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')">
            {{ infoData.infoReporterContactNumber || '/' }}
          </el-descriptions-item>
          <el-descriptions-item
            :label="selectDictValue(dict.type.sys_dict_translate,'powerOfAttorney')"
            v-if="versionType!=='xyzy'"
          >
            <el-button
              type="text"
              icon="el-icon-view"
              size="mini"
              v-if="infoData.powerOfAttorney"
              @click="previewFileByAnnex(infoData.powerOfAttorney)"
            >
              预览
            </el-button>
            <span v-else>/</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card
        header="响应附加条件"
        shadow="never"
        :body-style="{'padding': '15px 10px 10px 10px'}"
        class="card-box"
        v-if="heads.length>0">
        <el-descriptions direction="vertical" :column="3" border>
          <template v-for="(item,index) in heads">
            <el-descriptions-item :label="item.keyName" :key="index">
              <span v-if="item.keyType!=='file'">
                {{ additionalInformationMap[item.keyVal] }}
              </span>
              <el-button
                type="text"
                icon="el-icon-view"
                size="mini"
                v-if="item.keyType==='file'&&additionalInformationMap[item.keyVal]"
                @click="previewFileByAnnex(additionalInformationMap[item.keyVal])"
              >
                预览
              </el-button>
            </el-descriptions-item>
          </template>

        </el-descriptions>

      </el-card>

      <div slot="footer" class="text-center">
        <el-button @click="isDialog = false">关闭</el-button>
        <el-button
          type="primary"
          @click="handleSignAudit(1)"
          v-if="reviewStatus===0&&checkPermi(['process:registration:review'])&&isSignReview==='1'"
        >
          审核通过
        </el-button>
        <el-button
          type="danger"
          @click="handleSignAudit(2)"
          v-if="reviewStatus!==2&&checkPermi(['process:registration:review'])&&isSignReview==='1'"
        >
          审核不通过
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="响应附加条件"
      :visible.sync="isOpen"
      custom-class="maxW1200"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="mb10">{{ bidderName }}</p>
      <el-table
        :data="conditionsBodyMaps"
        border
        max-height="580"
      >
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="50"
        >
        </el-table-column>
        <template v-for="(item,k) in conditionsHeads">
          <el-table-column
            :label="item.keyName"
            align="center"
            :key="k"
          >
            <template v-slot:default="{row}">
              <span v-if="item.keyType!=='file'">
                {{ row[item.keyVal] }}
              </span>
              <el-button
                v-else
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="previewFile(row[item.keyVal])"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </el-dialog>

    <el-dialog
      title="响应文件"
      :visible.sync="isFileDialog"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <p class="mb10">{{ bidderName }}</p>
      <el-table :data="fileList" max-height="500" border>
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <el-table-column
          label="属性"
          align="center"
          prop="attribute"
        />
        <el-table-column
          label="描述"
          align="center"
          prop="description"
        />
        <el-table-column
          label="响应文件"
          align="center"
        >
          <template v-slot:default="{row}">
            <div v-if="row.currentBidFileKey">
              <el-button
                type="text"
                icon="el-icon-view"
                size="mini"
                @click="previewFile(row.currentBidFileKey)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                icon="el-icon-download"
                size="mini"
                @click="handleDown(row.currentBidFileKey)"
              >
                下载
              </el-button>
            </div>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          label="驳回次数"
          align="center"
        >
          <template v-slot:default="{row}">
            {{ row.rejectionList.length }}
          </template>
        </el-table-column>
        <el-table-column
          label="驳回记录"
          align="center"
        >
          <template v-slot:default="{row}">
            <el-button
              type="text"
              icon="el-icon-view"
              size="mini"
              @click="handleRecord(row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.status===1&&checkPermi(['process:responseFile:audit'])"
              size="mini"
              type="danger"
              @click="handleRejection(row)"
            >驳回
            </el-button>
            <dict-tag
              v-else
:options="dict.type.response_file_no_client_status"
:value="row.status"
              style="display: inline-block;"
            />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isFileDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="驳回记录"
      :visible.sync="isRecordDialog"
      width="90%"
      top="5vh"
      custom-class="maxW1100"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <p class="mb10">{{ bidderName }}</p>
      <el-table
        :data="rejectionList"
        max-height="500"
        border
      >
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <el-table-column
          label="属性"
          align="center"
          prop="attribute"
        >
          <template>
            {{ attribute }}
          </template>
        </el-table-column>
        <el-table-column
          label="描述"
          align="center"
          prop="description"
        >
          <template>
            {{ description }}
          </template>
        </el-table-column>
        <el-table-column
          label="响应文件"
          align="center"
        >
          <template v-slot:default="{row}">
            <div v-if="row.bidFileKey">
              <el-button
                type="text"
                icon="el-icon-view"
                size="mini"
                @click="previewFile(row.bidFileKey)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                icon="el-icon-download"
                size="mini"
                @click="handleDown(row.bidFileKey)"
              >
                下载
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="驳回理由"
          align="center"
          prop="reason"
        >
        </el-table-column>
        <el-table-column
          label="驳回日期"
          align="center"
        >
          <template v-slot:default="{row}">
            {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作人"
          align="center"
          prop="createName"
        >
        </el-table-column>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isRecordDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="查看对比"
      :visible.sync="isContrastDialog"
      width="90%"
      top="2vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <template v-for="(item,index) in contrastList">
        <el-card
          :header="item.bidderName"
          shadow="never"
          :body-style="{'padding': '15px 10px 10px 10px'}"
          class="card-box"
          :key="index"
        >
          <related-list
            :list="item.relatedList"
            :relatedCompanyList="item.relatedCompanyList"
            :relatedShareholderList="item.relatedShareholderList"
          />
        </el-card>
      </template>
      <div slot="footer" class="text-center">
        <el-button @click="isContrastDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  signPermit,
  signQualify,
  getSupplierCondition,
  exportExcelOfRegisteredBidders,
  supplierSignContrast,
  auditSignInfo
} from '@/api/purchaser/registrationSupplier';
import cityList from '@/assets/data/cityList'
import { getParents } from '@/utils'
import { checkFunction, checkPermi } from '@/utils/permission';
import { requirementListBySupplier, requirementRejection } from '@/api/purchaser/purchaseFileNoClient'
import { mapGetters } from 'vuex'
import relatedList from '@/views/purchaser/process/registrationSupplier/relatedList.vue'
import CommentSupplier from '@/views/purchaser/process/registrationSupplier/commentSupplier.vue'

export default {
  name: 'RegistrationSupplierItem',
  dicts: ['supplier_type', 'response_file_no_client_status', 'sys_dict_translate', 'sign_audit_status', 'supplier_bid_type', 'id_type'],
  components: {
    CommentSupplier,
    relatedList
  },
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    },
    isSignReview: {
      type: String,
      default: '0'
    },
    commentLevelOff: {
      type: String,
      default: '0'
    },
    ipShow: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      cityList,
      tableData: [],
      sectionSignList: [],
      isDialog: false,
      reviewStatus: null, // 报名审核状态，0-未审核，1-审核通过，2-审核不通过
      infoData: {},
      isOpen: false,
      conditionsHeads: [],
      conditionsBodyMaps: [],
      isFileDialog: false,
      bidderName: null,
      supplierId: null,
      fileList: [],
      isRecordDialog: false,
      rejectionList: [],
      attribute: null,
      description: null,
      isContrastDialog: false,
      contrastList: [],
      heads: [],
      additionalInformationMap: {}
    }
  },
  computed: {
    ...mapGetters([
      'buyItemName',
      'buyItemCode'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  watch: {
    itemData(value) {
      this.tableData = value;
    }
  },
  created() {
    this.tableData = this.itemData;
  },
  methods: {
    checkFunction,
    checkPermi,
    getParents,
    handleSignAudit(status) {
      this.$prompt(status === 1 ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: status !== 1,
        inputType: 'textarea',
        inputPlaceholder: status !== 1 ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (status === 1) {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 250
        },
        inputErrorMessage: '请输入不通过理由，最多250个字符'
      }).then(({ value }) => {
        this.$modal.loading('数据提交中，请稍候...')
        auditSignInfo({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId,
          reviewStatus: status,
          reviewReason: value
        }).then(res => {
          this.$modal.msgSuccess('审核成功')
          this.isDialog = false;
          this.$emit('update');
          this.$modal.closeLoading()
        }).catch(() => {
          this.$modal.closeLoading()
        })
      }).catch(() => {
      })
    },
    async handleContrast() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await supplierSignContrast(this.subpackageCode);
        this.contrastList = data || [];
        this.isContrastDialog = true;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    },
    async handleResponseFile(supplierId, bidderName) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await requirementListBySupplier({ subpackageCode: this.subpackageCode, supplierId });
        this.fileList = data.currentTenderFileVoList || [];
        this.bidderName = bidderName;
        this.supplierId = supplierId;
        this.$modal.closeLoading();
        this.isFileDialog = true;
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleRejection(row) {
      this.$prompt('请输入驳回理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入驳回理由',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 200
        },
        inputErrorMessage: '请输入驳回理由，最多200个字符'
      }).then(({ value }) => {
        this.$modal.loading('数据提交中，请稍候...')
        requirementRejection({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId,
          bidFileKey: row.currentBidFileKey,
          requirementId: row.id,
          reason: value
        }).then(res => {
          this.$modal.msgSuccess('操作成功')
          this.handleResponseFile(this.supplierId, this.bidderName);
          this.$modal.closeLoading();
        }).catch(() => {
          this.$modal.closeLoading()
        })
      }).catch(() => {
      })
    },
    handleRecord(row) {
      this.rejectionList = row.rejectionList;
      this.attribute = row.attribute;
      this.description = row.description;
      this.isRecordDialog = true;
    },
    async handleConditions(row) {
      try {
        let { data } = await getSupplierCondition({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          supplierId: row.supplierId
        })
        if (!data) {
          this.$modal.msgWarning('未查询到数据')
          return
        }
        let { supplierAdditionList, additionalInformationMap } = data;
        this.conditionsHeads = supplierAdditionList || [];
        this.conditionsBodyMaps = additionalInformationMap || [];
        this.bidderName = row.supplierSignUpInfo.bidderName;
        this.isOpen = true;
      } catch (e) {
        throw new Error(e);
      }
    },
    async handleExport() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await exportExcelOfRegisteredBidders(this.subpackageCode);
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    handlePermitChange(row) {
      let text = row.permit === '0' ? '不允许' : '允许'
      this.$modal.confirm(`确认要${text}该单位响应吗？`).then(async () => {
        try {
          await signPermit({
            subpackageCode: row.subpackageCode,
            supplierId: row.supplierId,
            permit: row.permit
          })
          this.$modal.msgSuccess('操作成功')
          this.$emit('update');
        } catch (e) {
          row.permit = row.permit === '0' ? '1' : '0'
          throw new Error(e);
        }
      }).catch(() => {
        row.permit = row.permit === '0' ? '1' : '0'
      })
    },
    handleQualifiedChange(row) {
      let text = row.qualified === '0' ? '不合格' : '合格'
      this.$modal.confirm(`确认要设置该单位${text}吗？`).then(async () => {
        try {
          await signQualify({
            subpackageCode: row.subpackageCode,
            supplierId: row.supplierId,
            qualified: row.qualified
          })
          this.$modal.msgSuccess('操作成功')
          this.$emit('update');
        } catch (e) {
          row.qualified = row.qualified === '0' ? '1' : '0'
          throw new Error(e);
        }
      }).catch(() => {
        row.qualified = row.qualified === '0' ? '1' : '0'
      })
    },
    handleLook(row) {
      if (!row.supplierSignUpInfo) {
        this.$message.error('未查询到供应商信息')
        return
      }
      this.infoData = row.supplierSignUpInfo;
      this.reviewStatus = row.reviewStatus;
      this.supplierId = row.supplierId;
      this.heads = row.additionalInformation || [];
      this.additionalInformationMap = row.additionalInformationMap && row.additionalInformationMap.length > 0 ? row.additionalInformationMap[0] : [];
      this.isDialog = true;
    },
    previewFileByAnnex(fileKey) {
      this.$download.previewFileByAnnex(fileKey)
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    }
  }
}
</script>

<style scoped>

</style>
