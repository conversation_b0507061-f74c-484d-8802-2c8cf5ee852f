<template>
  <div>
    <el-button
      size="mini"
      type="text"
      icon="el-icon-plus"
      @click="handleComment()"
      v-hasPermi="['process:comment:add']"
    >评论
    </el-button>
    <el-button
      size="mini"
      type="text"
      icon="el-icon-view"
      @click="handleLook()"
      v-hasPermi="['process:comment:query']"
    >查看
    </el-button>

    <el-dialog
      :title="'【'+supplierName+'】评论详情'"
      :visible.sync="isOpen"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5" v-if="checkPermi(['process:comment:add'])">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleComment()"
          >添加
          </el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" height="400" border v-loading="loading">
        <el-table-column label="序号" align="center" type="index" width="55"/>
        <el-table-column label="评论内容" align="center" prop="commentContent"/>
        <el-table-column label="评论人" align="center" prop="createBy" width="120"/>
        <el-table-column label="评论时间" align="center" width="160">
          <template v-slot:default="{row}">
            {{ row.commentTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template v-slot:default="{row}">
            <el-button
              v-if="checkPermi(['process:comment:remove'])"
              :disabled="row.createId!==userId"
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="btn-text-danger"
              @click="removeComment(row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      :title="'评论【'+supplierName+'】'"
      :visible.sync="isDialog"
      width="90%"
      top="5vh"
      custom-class="maxW600"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评论等级" prop="score" v-if="commentLevelOff==='1'">
          <el-select v-model="form.score" placeholder="请选择评论等级" class="block" clearable>
            <el-option
              v-for="dict in dict.type.comment_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="评论内容" prop="commentContent">
          <el-input
            v-model.trim="form.commentContent"
            type="textarea"
            rows="3"
            resize="none"
            placeholder="请输入评论内容"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="isDialog=false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addSupplierComment,
  deleteSupplierComment,
  getSupplierCommentByProject
} from '@/api/commentSupplier';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'CommentSupplier',
  dicts: ['comment_level'],
  props: {
    supplierId: {
      type: Number,
      default: null
    },
    buyItemName: {
      type: String,
      default: null
    },
    buyItemCode: {
      type: String,
      default: null
    },
    supplierName: {
      type: String,
      default: null
    },
    subpackageCode: {
      type: String,
      default: null
    },
    subpackageName: {
      type: String,
      default: null
    },
    commentLevelOff: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      isOpen: false,
      isDialog: false,
      form: {},
      rules: {
        commentContent: [
          { required: true, message: '请输入评论内容', trigger: 'blur' },
          { max: 500, message: '最多500个字符', trigger: 'blur' }
        ],
        score: [{ required: true, message: '请选择评论等级', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  methods: {
    checkPermi,
    reset() {
      this.form = {
        commentContent: null,
        score: null
      }
      this.resetForm('form')
    },
    async removeComment(row) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await deleteSupplierComment(row.id);
        this.$modal.msgSuccess('删除成功');
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await addSupplierComment({
              subpackageName: this.subpackageName,
              subpackageCode: this.subpackageCode,
              buyItemName: this.buyItemName,
              buyItemCode: this.buyItemCode,
              supplierId: this.supplierId,
              ...this.form
            })
            this.$modal.msgSuccess('提交成功');
            this.isDialog = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    handleComment() {
      this.reset();
      this.isDialog = true;
    },
    async handleLook() {
      try {
        this.isOpen = true;
        this.$modal.loading('数据查询中，请稍候...');
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getSupplierCommentByProject({
          supplierId: this.supplierId,
          subpackageCode: this.subpackageCode
        })
        this.tableData = data;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
