<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      size="mini"
      type="primary"
      plain
      icon="el-icon-chat-line-square"
      @click="handleShow()"
    >通知供应商报名
    </el-button>

    <el-dialog
      top="5vh"
      title="选择供应商"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :modal-append-to-body="false"
    >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="公司名称" prop="bidderName">
          <el-input
            v-model="queryParams.bidderName"
            placeholder="请输入公司名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="bidTypes">
          <el-select
            v-model="queryParams.bidTypes"
            placeholder="类型"
            clearable
            filterable
            multiple
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.supplier_bid_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入库状态" prop="storageType">
          <el-select
            v-model="queryParams.storageType"
            placeholder="入库状态"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.supplier_storage_type"
              :key="dict.value"
              :label="dict.label"
              :value="Number(dict.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-chat-dot-square"
            size="mini"
            :disabled="!selectList.length"
            @click="handleSms"
          >选择供应商发送短信
          </el-button>
          <el-button
            type="primary"
            plain
            icon="el-icon-chat-dot-square"
            size="mini"
            @click="handleSmsAll"
          >根据条件发送
          </el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="公司名称" align="center" prop="bidderName" :show-overflow-tooltip="true"/>
        <el-table-column label="统一社会信用代码" align="center" prop="licNumber" :show-overflow-tooltip="true"/>
        <el-table-column label="类别" align="center" prop="bidType" :show-overflow-tooltip="true">
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.supplier_bid_type" :value="row.bidType"/>
          </template>
        </el-table-column>
        <el-table-column label="入库状态" align="center" prop="storageType" :show-overflow-tooltip="true">
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.supplier_storage_type" :value="row.storageType"/>
          </template>
        </el-table-column>
        <el-table-column
          :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')"
          align="center"
          prop="contactNumber"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
          align="center"
          prop="infoReporterContactNumber"
          :show-overflow-tooltip="true"
        />
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </el-dialog>
  </div>
</template>

<script>
import { findByCompanyAndOrg } from '@/api/purchaser/bulletin'
import { sendSMS, sendToSupplier } from '@/api/sms'
import { mapGetters } from 'vuex'
import { isEmpty } from '@/utils'

export default {
  name: 'SelectNotificationSupplier',
  dicts: ['supplier_bid_type', 'sys_dict_translate', 'supplier_storage_type'],
  props: {
    purchaseMethodCode: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      isDialog: false,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bidderName: null,
        storageType: null,
        bidTypes: []
      },
      selectList: []
    }
  },
  computed: {
    ...mapGetters([
      'orgCode'
    ])
  },
  methods: {
    /** 新增按钮操作 */
    async handleSms() {
      let { data } = await this.getDicts('sys_sms_template');
      let templateList = this.initDict(data || []);
      this.$sendSMS({
        title: '发送短信',
        mobiles: this._.uniq(this.selectList.map(item => item.value)),
        smsRecipient: this.selectList,
        append: '【拒收请回复R】',
        templateList: templateList,
        handleConfirm: (val) => {
          this.$modal.loading('短信发送中，请稍候...')
          sendSMS({ content: val.msg, mobiles: val.mobiles })
            .then(() => {
              this.$modal.msgSuccess('发送成功');
              this.$modal.closeLoading();
            })
            .catch(() => {
              this.$modal.closeLoading();
            })
        }
      })
    },
    async handleSmsAll() {
      if (isEmpty(this.queryParams.storageType) && !(this.queryParams.bidTypes && this.queryParams.bidTypes.length > 0)) {
        this.$message.warning('请选择条件');
        return
      }
      let { data } = await this.getDicts('sys_sms_template');
      let templateList = this.initDict(data || []);
      let bidTypes = [];
      this.queryParams.bidTypes.forEach(item => {
        let label = this.selectDictLabel(this.dict.type.supplier_bid_type, item);
        bidTypes.push(label);
      })
      let text1 = `${bidTypes.length > 0 ? bidTypes.join('、') + '类别' : ''}`;
      let text2 = `${isEmpty(this.queryParams.storageType) ? '' : this.selectDictLabel(this.dict.type.supplier_storage_type, this.queryParams.storageType)}`;
      this.$sendSMS({
        title: `给${text1 + text2}的所有供应商发送短信`,
        append: '【拒收请回复R】',
        templateList: templateList,
        handleConfirm: (val) => {
          this.$modal.loading('短信发送中，请稍候...')
          sendToSupplier({ content: val.msg, bidTypes: this.queryParams.bidTypes, storageType: this.queryParams.storageType })
            .then(() => {
              this.$modal.msgSuccess('发送成功');
              this.$modal.closeLoading();
            })
            .catch(() => {
              this.$modal.closeLoading();
            })
        }
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.selectList = selection.map(item => {
        return {
          userId: item.userId,
          label: item.bidderName,
          value: item.infoReporterContactNumber
        }
      });
    },
    handleShow() {
      this.selectList = [];
      this.resetQuery();
      this.isDialog = true;
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.orgCode = this.orgCode;
      this.queryParams.pageNum = 1
      this.getList()
    },
    async getList() {
      try {
        let { rows, total } = await findByCompanyAndOrg(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
