<template>
  <div>
    <el-table :data="tableData" border :span-method="objectSpanMethod">
      <el-table-column label="序号" align="center" prop="index" width="55"></el-table-column>
      <el-table-column label="关联公司名称" align="center" prop="bidderName"></el-table-column>
      <el-table-column label="统一社会信用代码" align="center" prop="licNumber">
        <template v-slot:default="{row}">
          <span :class="{'text-primary':relatedCompanyList.includes(row.relatedId)}">{{ row.licNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="股东或法人名称" align="center" prop="name"></el-table-column>
      <el-table-column label="股东或法人证件号码" align="center" prop="number">
        <template v-slot:default="{row}">
          <span :class="{'text-danger':relatedShareholderList.includes(row.id)}">{{ row.number }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

export default {
  name: 'RelatedList',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    relatedCompanyList: {
      type: Array,
      default: () => []
    },
    relatedShareholderList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      spanArr: [],
      pos: 0
    }
  },
  watch: {
    list: {
      handler(val) {
        console.log('list', val)
        this.getData(val);
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // this.getData(this.list);
  },
  methods: {
    getData(data) {
      let list = data || [];
      this.tableData = [];
      list.forEach((item, index) => {
        let shareholderList = item.shareholderList || [];
        if (shareholderList.length > 0) {
          shareholderList.forEach(v => {
            this.tableData.push({
              ...item,
              ...v,
              index: index + 1
            })
          })
        } else {
          this.tableData.push({
            ...item,
            index: index + 1
          })
        }
      })
      this.getSpanArr(this.tableData)
      console.log(this.tableData)
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [3, 4]
      if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].relatedId === data[i - 1].relatedId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style lang="scss">

</style>
