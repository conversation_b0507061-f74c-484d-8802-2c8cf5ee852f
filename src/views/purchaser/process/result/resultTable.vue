<template>
  <div>
    <el-table
      :data="tableData"
      border
      :span-method="objectSpanMethod"
    >
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="50"
      />
      <el-table-column
        label="供应商"
        align="center"
        prop="supplierCompanyName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="'设定'+getPurchaseDict(purchaseMethodCode, 'winBidder')"
        align="center"
      >
        <el-table-column
          label="选择"
          align="center"
        >
          <template v-slot:default="{row}">
            <el-select
              clearable
              v-model="row.winBid"
              placeholder="请选择"
              :disabled="!checkPermi(['process:result:save'])"
            >
              <el-option
                :label="getPurchaseDict(purchaseMethodCode, 'winBidder')"
                value="1"
              >
              </el-option>
              <el-option
                :label="'非'+getPurchaseDict(purchaseMethodCode, 'winBidder')"
                value="0"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="确认"
          align="center"
        >
          <template>
            <el-button
              v-if="checkPermi(['process:result:save'])"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleSetWinBidder"
            >
              {{ selected === 1 ? '重新确认' : '确认' }}
            </el-button>
            <span v-else>
              {{ selected === 1 ? '已确认' : '未确认' }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column
        :label="getPurchaseDict(purchaseMethodCode, 'winCandidatePublicity')"
        align="center"
        v-if="checkFunction(['purchaser_candidate_publicity'])"
      >
        <template>
          <div v-if="!candidateInfo.bulletinKey">
            <el-button
              v-if="checkPermi(['process:candidate:publicity'])"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              :disabled="selected!==1"
              @click="handleResultPublicity('win_candidate_publicity')"
            >
              发送
            </el-button>
            <span v-else>/</span>
          </div>
          <div v-else>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(candidateInfo.bulletinKey)"
            >
              查看
            </el-button>
            <el-button
              v-if="checkPermi(['process:candidate:publicity'])&&(candidateInfo.auditStatus==='3'||candidateInfo.auditStatus==='0')"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              @click="handleResultPublicity('win_candidate_publicity')"
            >
              重新发送
            </el-button>
            <el-button
              v-if="checkPermi(['process:candidate:publicity'])"
              size="mini"
              type="text"
              icon="el-icon-refresh-left"
              :disabled="candidateInfo.auditStatus!=='2'"
              @click="handleWithdraw(candidateInfo)"
            >撤回
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="getPurchaseDict(purchaseMethodCode, 'winResultPublicity')"
        align="center"
        v-if="checkFunction(['purchaser_result_publicity'])"
      >
        <template>
          <div v-if="!winInfo.bulletinKey">
            <el-button
              v-if="checkPermi(['process:result:publicity'])"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              :disabled="selected!==1"
              @click="handleResultPublicity('win_result_publicity')"
            >
              发送
            </el-button>
            <span v-else>/</span>
          </div>
          <div v-else>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(winInfo.bulletinKey)"
            >
              查看
            </el-button>
            <el-button
              v-if="checkPermi(['process:result:publicity'])&&(winInfo.auditStatus==='3'||winInfo.auditStatus==='0')"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              @click="handleResultPublicity('win_result_publicity')"
            >
              重新发送
            </el-button>
            <el-button
              v-if="checkPermi(['process:result:publicity'])&&winInfo.auditStatus!=='1'"
              size="mini"
              type="text"
              icon="el-icon-refresh-left"
              :disabled="winInfo.auditStatus!=='2'"
              @click="handleWithdraw(winInfo)"
            >撤回
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="结果通知书"
        align="center"
        v-if="checkFunction(['purchaser_result_notice'])"
      >
        <template v-slot:default="{row}">
          <div v-if="!row.resultNotice">
            <el-button
              v-if="checkPermi(['process:result:notice'])"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              :disabled="selected!==1"
              @click="handleResultNotice(row,false)"
            >
              发送
            </el-button>
            <span v-else>/</span>
          </div>
          <div v-if="row.resultNotice">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(row.resultNotice)"
            >
              查看
            </el-button>
            <el-button
              v-if="checkPermi(['process:result:notice'])"
              size="mini"
              type="text"
              icon="el-icon-edit"
              :disabled="selected!==1"
              @click="handleResultNotice(row,true)"
            >
              重新发送
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="isDialog"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item prop="bulletinContentHtml" label="内容">
              <template-select
                v-if="isDialog"
                :tmp-type="tmpType"
                :showSealTip="signatureOff==='0'"
                v-model="form.bulletinContentHtml"
                :getTemplateFunc="getTemplateFunc"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6" style="max-width: 400px;">
            <el-form-item
              label="公告标题"
              prop="bulletinName"
              v-if="!['win_result_notice', 'not_win_result_notice'].some(item => item === form.bulletinType)"
            >
              <el-input v-model="form.bulletinName" maxlength="100" placeholder="请输入公告标题"/>
            </el-form-item>

            <el-form-item>
              <el-button @click="isDialog = false">取消</el-button>
              <el-button
                type="primary"
                @click="submitFormNotice"
                v-if="['win_result_notice', 'not_win_result_notice'].some(item => item === form.bulletinType)">
                提交
              </el-button>
              <template v-else>
                <publish-submit
                  :submit-func="submitApproval"
                  :verify-func="verifyForm"
                  @success="successSubmit"
                  v-if="form.bulletinType==='win_candidate_publicity'&&checkFunction(['purchaser_candidate_publicity_not_approved'])"
                />
                <publish-submit
                  :submit-func="submitApproval"
                  :verify-func="verifyForm"
                  @success="successSubmit"
                  v-else-if="form.bulletinType==='win_result_publicity'&&checkFunction(['purchaser_result_publicity_not_approved']) "
                />
                <template v-else>
                  <submit-approval
                    v-if="versionType==='bjxk'"
                    :auditTitle="form.bulletinName+'审批'"
                    :verify-func="verifyForm"
                    :submit-func="submitApproval"
                    @success="successSubmit"
                  />
                  <approval-form
                    v-else
                    :orgCode="orgCode"
                    :bindDeptId="projectDeptId"
                    :auditTitle="form.bulletinName+'审批'"
                    :filterStr="filterStr"
                    :params="{buyItemCode,subpackageCode,purchaseMethodCode,yearMonthSplit:createYearMonth}"
                    :showAnnex="versionType==='whws'&&form.bulletinType==='win_result_publicity'"
                    bindKey="bulletin"
                    :verify-func="verifyForm"
                    :submit-func="submitApproval"
                    @success="successSubmit"
                  />
                </template>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { checkFunction, checkPermi } from '@/utils/permission';
import { sendNotice, saveWinResult } from '@/api/purchaser/result';
import { fillTemplate, publishBulletin, rollBackAudit } from '@/api/purchaser/bulletin';
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import submitApproval from '@/views/purchaser/components/SubmitApproval/index.vue'
import PublishSubmit from '@/views/purchaser/components/PublishSubmit/index.vue'

export default {
  name: 'ResultTable',
  components: {
    submitApproval,
    PublishSubmit,
    approvalForm
  },
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tmpType: null,
      tableData: [],
      spanArr: [],
      pos: 0,
      selected: 0, // 是否已确定选择中标人[0-未选择，1-已经选择]"
      candidateInfo: {},
      winInfo: {}, // 中标结果公告 auditStatus 0-审批拒绝, 1-审批通过, 2-审批中, 3-已撤销
      isDialog: false,
      dialogTitle: null,
      form: {},
      rules: {
        bulletinContentHtml: [
          { required: true, message: '公告内容不能为空', trigger: ['change', 'blur'] }
        ],
        bulletinName: [
          { required: true, message: '公告标题不能为空', trigger: 'blur' }
        ],
        bulletinType: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      supplierId: null,
      supplierCompanyName: null,
      isAgain: null
    }
  },
  computed: {
    ...mapGetters([
      'signatureOff',
      'purchaseMethodCode',
      'getPurchaseDict',
      'orgCode',
      'buyItemCode',
      'createYearMonth',
      'bulletinList',
      'buyItemName',
      'projectDeptId',
      'filterStr'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.reset();
    this.getData(this.itemData);
  },
  methods: {
    checkFunction,
    checkPermi,
    handleSetWinBidder() {
      if (this.tableData.some(item => !item.winBid)) {
        this.$modal.msgError('请设置供应商是否为中标人')
        return
      }
      if (!this.tableData.some(item => item.winBid === '1')) {
        this.$modal.msgError('请设置中标人')
        return
      }
      this.$confirm(`<p>请确认是否提交${this.getPurchaseDict(this.purchaseMethodCode, 'winBidder')}？</p>`, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await saveWinResult({
            subpackageCode: this.subpackageCode,
            subpackageName: this.subpackageName,
            winSupplierList: this.tableData.map(item => {
              return {
                supplierCompanyName: item.supplierCompanyName,
                supplierId: item.supplierId,
                winBid: item.winBid
              }
            })
          });
          this.$modal.msgSuccess('提交成功');
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      })
    },
    verifyForm() {
      return new Promise((resolve, reject) => {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        });
      })
    },
    successSubmit() {
      this.isDialog = false;
      this.$emit('update');
    },
    submitApproval(data) {
      console.log(data)
      return publishBulletin({
        orgCode: this.orgCode,
        subpackageCodeList: [this.subpackageCode],
        buyItemCode: this.buyItemCode,
        ...this.form,
        ...data,
        attachmentDtoList: data.attList || []
      })
    },
    submitFormNotice() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await sendNotice({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode,
              subpackageName: this.subpackageName,
              isAgain: this.isAgain,
              supplierId: this.supplierId,
              supplierCompanyName: this.supplierCompanyName,
              noticeTemplate: this.form.bulletinContentHtml,
              bulletinType: this.form.bulletinType
            })
            this.$modal.msgSuccess('提交成功');
            this.isDialog = false;
            this.$modal.closeLoading();
            this.$emit('update');
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      });
    },
    handleWithdraw(row) {
      this.$modal.confirm('是否确认撤回该公告？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            let { auditCode } = row;
            await rollBackAudit({ auditCode: auditCode, auditType: 'bulletin', status: 3 })
            this.$emit('update');
            this.$modal.closeLoading()
            this.$modal.msgSuccess('撤回成功');
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }).catch(() => {
        });
    },
    async handleResultNotice(row, isAgain) {
      this.reset();
      this.form.bulletinType = row.winBid === '1' ? 'win_result_notice' : 'not_win_result_notice';
      let obj = this.bulletinList.find(item => item.value === this.form.bulletinType);
      if (!obj) {
        this.$modal.msgWarning('模板未配置');
        return
      }
      this.dialogTitle = obj.label;
      this.supplierId = row.supplierId;
      this.supplierCompanyName = row.supplierCompanyName;
      this.isAgain = isAgain;
      this.form.bulletinContentHtml = '';
      this.tmpType = this.form.bulletinType;
      this.isDialog = true;
    },
    async handleResultPublicity(val) {
      this.reset();
      let obj = this.bulletinList.find(item => item.value === val);
      if (!obj) {
        this.$modal.msgWarning('模板未配置');
        return
      }
      this.dialogTitle = obj.label;
      this.form.bulletinTypeName = obj.label;
      this.form.bulletinName = this.buyItemName + this.form.bulletinTypeName;
      this.form.bulletinType = val;
      this.form.bulletinContentHtml = '';
      this.tmpType = this.form.bulletinType;
      this.isDialog = true;
    },
    getTemplateFunc(data) {
      return fillTemplate({
        buyItemCode: this.buyItemCode,
        subpackageCode: this.subpackageCode,
        purchaseMethodCode: this.purchaseMethodCode,
        supplierId: ['win_result_notice', 'not_win_result_notice'].includes(this.form.bulletinType) ? this.supplierId : null,
        tmpKey: data.tmpKey,
        tmpType: data.tmpType
      });
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    // 表单重置
    reset() {
      this.form = {
        bulletinName: null,
        bulletinType: null,
        bulletinTypeName: null,
        bulletinContentHtml: ''
      }
      this.resetForm('form')
    },
    getData(data) {
      if (!data) {
        return
      }
      this.selected = data.selected;
      this.candidateInfo = data.candidateInfo || {};
      this.winInfo = data.winInfo || {};
      this.tableData = data.winBidVoList || [];
      this.getSpanArr(this.tableData);
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [3, 4];
      if (this.checkFunction(['purchaser_candidate_publicity']) && this.checkFunction(['purchaser_result_publicity'])) {
        columnList = [3, 4, 5];
      }
      if (!this.checkFunction(['purchaser_candidate_publicity']) && !this.checkFunction(['purchaser_result_publicity'])) {
        columnList = [3];
      }
      if (columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          this.spanArr[this.pos] += 1;
          this.spanArr.push(0);
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
