<template>
  <div>
    <el-table :data="tableData" border :span-method="objectSpanMethod" key="1">
      <el-table-column label="序号" prop="index" width="50" align="center"/>
      <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
      <el-table-column label="票数" align="center" prop="votesNum"/>
      <el-table-column label="投票人" align="center" prop="votersNames"/>
      <el-table-column label="推荐名次" align="center">
        <template v-slot:default="{row}">
          <span v-if="!row.rank">/</span>
          <span v-else>{{ '第' + numToChinese(tableData.find(v => v.supplierId === row.supplierId).rank) + '候选人' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="确认评审结果" align="center">
        <template>
          <el-button
            v-if="!reportFileKey"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm"
          >确认
          </el-button>
          <span v-else>已确认</span>
        </template>
      </el-table-column>
      <el-table-column label="评审报告" align="center">
        <template>
          <div v-if="reportFileKey">
            <el-button
              size="mini"
              type="text"
              @click="previewFile(reportFileKey)"
            >查看
            </el-button>
            <div class="td-tip" v-if="judgeSignInfo.filter(v => v.isSign !== 1).length>0&&signatureOff==='0'">
              未签字评委：{{ judgeSignInfo.filter(v => v.isSign !== 1).map(v => v.judgeName).join('、') }}
            </div>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { numToChinese } from '@/utils';
import { confirmReviewResult } from '@/api/purchaser/result';
import { mapGetters } from 'vuex'
import { checkFunction } from '@/utils/permission'

export default {
  name: 'ReviewTableVote',
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      spanArr: [],
      pos: 0,
      judgeSignInfo: [],
      reportFileKey: null
    }
  },
  computed: {
    ...mapGetters([
      'signatureOff'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkFunction,
    numToChinese,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleConfirm() {
      this.$confirm(`<p>是否确认评委评审结果？</p>`, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await confirmReviewResult(this.subpackageCode);
          this.$modal.msgSuccess('确认成功');
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    getData(data) {
      if (!(data && data.summaryVo)) {
        return
      }
      let summaryVo = data.summaryVo;
      this.tableData = summaryVo.supplierDetailsVoList || [];
      this.getSpanArr(this.tableData);
      let judgesSign = summaryVo.judgesSign || {};
      this.judgeSignInfo = judgesSign.judgeSignInfo || [];
      this.reportFileKey = judgesSign.reportFileKey;
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [5, 6];
      if (columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          this.spanArr[this.pos] += 1;
          this.spanArr.push(0);
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
