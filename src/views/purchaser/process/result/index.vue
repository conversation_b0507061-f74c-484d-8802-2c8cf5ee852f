<template>
  <div v-loading="loading">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" class="fr">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新信息
        </el-button>
      </el-col>
    </el-row>
    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <review-table
          class="mb20"
          v-if="checkFunction(['purchaser_start_review'])&&item.data.reviewMethod!==2"
          :key="'review'+item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :itemData="item.data"
          @update="getList"
        />
        <review-table-vote
          class="mb20"
          v-if="checkFunction(['purchaser_start_review'])&&item.data.reviewMethod===2"
          :key="'review_vote'+item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :itemData="item.data"
          @update="getList"
        />
        <result-table
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <review-table
          class="mb20"
          v-if="activeName===item.subpackageCode&&checkFunction(['purchaser_start_review'])&&item.data.reviewMethod!==2"
          :key="'review'+item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :itemData="item.data"
          @update="getList"
        />
        <review-table-vote
          class="mb20"
          v-if="activeName===item.subpackageCode&&checkFunction(['purchaser_start_review'])&&item.data.reviewMethod===2"
          :key="'review_vote'+item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :itemData="item.data"
          @update="getList"
        />
        <result-table
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item.data"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import reviewTable from './reviewTable.vue';
import resultTable from './resultTable.vue';
import reviewTableVote from './reviewTableVote.vue';
import { checkFunction } from '@/utils/permission';
import { mapGetters } from 'vuex';
import { queryWinResultPage } from '@/api/purchaser/result';

export default {
  name: 'Result',
  components: {
    reviewTable,
    resultTable,
    reviewTableVote
  },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkFunction,
    refresh() {
      this.getList();
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await queryWinResultPage(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
