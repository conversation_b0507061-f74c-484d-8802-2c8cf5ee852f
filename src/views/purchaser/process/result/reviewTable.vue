<template>
  <div>
    <div v-if="incompleteJudges.length>0">
      <p class="mb5 text-danger text-center fontSize18">以下评委未完成评审</p>
      <el-table :data="incompleteJudges" border key="0">
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="评委" align="center" prop="judgeName"/>
        <el-table-column label="未评审供应商" align="center" prop="supplierCompanyName"/>
      </el-table>
    </div>

    <div v-else>
      <el-table :data="tableData" border :span-method="objectSpanMethod" key="1">
        <el-table-column label="序号" prop="index" width="50" align="center"/>
        <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
        <el-table-column label="评审结果" align="center" prop="qualifiedResult" key="qualifiedResult" v-if="isType===0">
          <template v-slot:default="{row}">
            {{ row.qualifiedResult ? '合格' : '不合格' }}
          </template>
        </el-table-column>
        <el-table-column label="评审结果" align="center" prop="totalSource" key="totalSource" v-if="isType===1"/>
        <el-table-column
          label="价格分"
          align="center"
          prop="priceSource"
          v-if="isType===1&&checkFunction(['purchaser_benchmark_score'])"/>
        <el-table-column label="评委评审明细" align="center">
          <el-table-column label="评委" align="center" prop="judgeName"/>
          <el-table-column label="评审结果" align="center" prop="judgeResult"/>
          <el-table-column label="评审详情" align="center">
            <template v-slot:default="{row}">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="推荐名次" align="center">
          <template v-slot:default="{row}">
            <span v-if="!originalList.find(v => v.supplierId === row.supplierId).rank">/</span>
            <span v-else>{{ '第' + numToChinese(originalList.find(v => v.supplierId === row.supplierId).rank) + '候选人' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="确认评审结果" align="center">
          <template>
            <el-button
              v-if="!reportFileKey"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleConfirm"
            >确认
            </el-button>
            <span v-else>已确认</span>
          </template>
        </el-table-column>
        <el-table-column label="评审报告" align="center">
          <template>
            <div v-if="reportFileKey">
              <el-button
                size="mini"
                type="text"
                @click="previewFile(reportFileKey)"
              >查看
              </el-button>
              <div class="td-tip" v-if="judgeSignInfo.filter(v => v.isSign !== 1).length>0&&signatureOff==='0'">
                未签字评委：{{ judgeSignInfo.filter(v => v.isSign !== 1).map(v => v.judgeName).join('、') }}
              </div>
            </div>
            <span v-else>/</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog
      title="评审详情"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="mb5">{{ supplierCompanyName }}</p>
      <qualified-table ref="qualifiedTable" :reviewInfo="reviewInfo" :isType="isType"></qualified-table>
      <p v-if="isType===1" class="mt10 fontSize16 text-right pr20">评审结果：{{ scoreResult }}</p>
    </el-dialog>
  </div>
</template>

<script>
import qualifiedTable from '@/views/expert/process/components/qualifiedTable.vue';
import { numToChinese } from '@/utils';
import { confirmReviewResult } from '@/api/purchaser/result';
import { mapGetters } from 'vuex'
import { checkFunction } from '@/utils/permission'

export default {
  name: 'ReviewTable',
  components: {
    qualifiedTable
  },
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isType: 0, // 1-评分   0-合格
      incompleteJudges: [],
      originalList: [],
      tableData: [],
      spanArr: [],
      pos: 0,
      reviewInfo: [],
      isDialog: false,
      supplierCompanyName: null,
      scoreResult: null,
      judgeSignInfo: [],
      reportFileKey: null
    }
  },
  computed: {
    ...mapGetters([
      'signatureOff'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkFunction,
    numToChinese,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleConfirm() {
      this.$confirm(`<p>是否确认评委评审结果？</p>`, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await confirmReviewResult(this.subpackageCode);
          this.$modal.msgSuccess('确认成功');
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    handleDetail(row) {
      this.reviewInfo = row.judgesVoList || [];
      this.supplierCompanyName = row.supplierCompanyName;
      this.scoreResult = row.scoreResult;
      this.isDialog = true;
    },
    getData(data) {
      if (!(data && data.summaryVo)) {
        return
      }
      let summaryVo = data.summaryVo;
      this.incompleteJudges = summaryVo.incompleteJudges || [];
      this.originalList = summaryVo.supplierDetailsVoList || [];
      this.isType = this.originalList.length > 0 ? this.originalList[0].isType : null;
      this.tableData = [];
      this.originalList.forEach((item, index) => {
        let judgesVoList = item.judgesVoList || [];
        judgesVoList.forEach(v => {
          this.tableData.push({
            ...item,
            index: index + 1,
            judgeId: v.judgeId,
            judgeName: v.judgeName,
            judgeResult: this.isType === 0 ? (v.qualifiedResult ? '合格' : '不合格') : v.scoreResult
          })
        })
      })
      console.log('1111111111', this.tableData)
      this.getSpanArr(this.tableData);
      let judgesSign = summaryVo.judgesSign || {};
      this.judgeSignInfo = judgesSign.judgeSignInfo || [];
      this.reportFileKey = judgesSign.reportFileKey;
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList1 = [7, 8];
      let columnList = [3, 4];
      if (this.isType === 1 && checkFunction(['purchaser_benchmark_score'])) {
        columnList1 = [8, 9];
        columnList = [4, 5];
      }
      if (columnList1.includes(columnIndex)) {
        const _row = rowIndex === 0 ? this.tableData.length : 0;
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      } else if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].supplierId === data[i - 1].supplierId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
