<template>
  <div>
    <el-row :gutter="10" class="mb20">
      <el-col
        :span="1.5"
        v-if="checkFunction(['purchaser_benchmark_score'])"
      >
        <span
          style="display: inline-block;width: 110px;font-weight: bold;color: #606266;"
          class="pr10 text-right fontSize14"
        >
          价格总分
        </span>
        <el-input
          size="small"
          v-model.trim="priceTotalSource"
          placeholder="请输入价格总分"
          style="width: 180px;margin-right: 5px;"
          :disabled="!checkPermi(['process:benchmark:score'])"
        />
        <el-button
          size="small"
          type="primary"
          @click="handleSaveScore"
          v-if="checkPermi(['process:benchmark:score'])"
        >
          保存
        </el-button>
      </el-col>
    </el-row>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item
        :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
        prop="pdfFile"
      >
        <file-upload-single
          :showUploadBtn="checkPermi(['process:purchaseFile:upload'])"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :showTip="true"
          :autoUpload="false"
          :file-size="200"
          accept=".pdf"
          uploadName="pdfFile"
          @onSuccess="handleUpload"
        >
          <el-button
            slot="upload-btn"
            type="primary"
            size="mini"
            :disabled="releaseStatus==='1'||[1,2].includes(processStatus)">
            {{ form.pdfFile ? '重新上传' : '上传' }}
          </el-button>
          <el-button
            slot="upload-right"
            type="primary"
            size="mini"
            v-if="form.pdfFile"
            @click="previewFile(form.pdfFile)"
          >预览
          </el-button>
          <el-button
            v-if="checkFunction(['purchase_file_import'])&&checkPermi(['process:purchaseFile:upload'])"
            :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
            slot="upload-right"
            type="primary"
            size="mini"
            @click="handleClickImport"
          >导入其他标段(包)文件
          </el-button>
          <el-button
            v-if="checkFunction(['purchase_file_apply'])&&checkPermi(['process:purchaseFile:upload'])"
            :disabled="!form.pdfFile"
            slot="upload-right"
            type="primary"
            size="mini"
            @click="handleClickUse"
          >将文件应用到其他标段(包)
          </el-button>
        </file-upload-single>
      </el-form-item>
      <el-form-item
        label="附件"
        prop="claimsFileAttVoList"
      >
        <purchase-file-annex
          v-model="form.claimsFileAttVoList"
          :subpackageCode="subpackageCode"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          @change="changeAtt"
        />
      </el-form-item>
      <el-form-item
        label="评审办法前附表"
        prop="reviewList"
      >
        <review-method
          v-model="form.reviewList"
          :subpackageCode="subpackageCode"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :remark="evaluationMethodRemark"
          @change="changeReviewMethod"
        />
      </el-form-item>
      <el-form-item
        label=" "
        prop="isAgree"
        v-if="checkPermi(['process:purchaseFile:upload'])"
      >
        <el-checkbox
          v-model="form.isAgree"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
        >
          同意发布，已阅读并确认文件内容
        </el-checkbox>
      </el-form-item>

      <el-form-item label="查看审批" v-if="!isEmpty(processStatus)">
        <el-button
          type="primary"
          size="mini"
          @click="handleDetail"
        >查看
        </el-button>
      </el-form-item>
      <el-form-item label="审批状态" v-if="!isEmpty(processStatus)">
        <dict-tag :options="dict.type.approval_process_status" :value="processStatus"/>
        <div class="td-tip" v-if="processStatus===0">
          审批意见：{{ remark || '/' }}
        </div>
      </el-form-item>

      <el-form-item
        label=" "
        v-if="checkPermi(['process:purchaseFile:upload'])&&![1,2].includes(processStatus)"
      >
        <submit-approval
          v-if="versionType==='bjxk'"
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :auditTitle="buyItemName+'（'+subpackageName+'）'+getPurchaseDict(purchaseMethodCode, 'purchaseFile')+'审批'"
          :verify-func="verifyForm"
          :submit-func="submitForm"
          @success="successSubmit"
        />
        <approval-form
          v-else
          :disabled="releaseStatus==='1'||[1,2].includes(processStatus)"
          :orgCode="orgCode"
          :bindDeptId="projectDeptId"
          :auditTitle="buyItemName+'（'+subpackageName+'）'+getPurchaseDict(purchaseMethodCode, 'purchaseFile')+'审批'"
          :filterStr="filterStr"
          bindKey="claims_file"
          :verify-func="verifyForm"
          :submit-func="submitForm"
          @success="successSubmit"
        />
      </el-form-item>
    </el-form>

    <el-dialog
      title="导入其他标段(包)文件"
      :visible.sync="isDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table :data="subpackageList" border>
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="标段(包)"
          align="center"
          prop="subpackageName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
          align="center"
          width="80"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.pdfFile"
              type="text"
              size="mini"
              @click="previewFile(row.pdfFile)"
            >预览
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          label="文件状态"
          align="center"
          prop="releaseStatus"
        >
          <template v-slot:default="{row}">
            <dict-tag :options="dict.type.purchase_file_status" :value="row.releaseStatus"/>
          </template>
        </el-table-column>
        <el-table-column
          label="报价表头是否一致"
          align="center"
          prop="areQuoteHeadEquals"
        >
          <template v-slot:default="{row}">
            {{ row.areQuoteHeadEquals === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.releaseStatus"
              :disabled="row.areQuoteHeadEquals!==1"
              type="text"
              size="mini"
              @click="importFile(row)"
            >导入
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer text-center">
        <el-button size="medium" @click="isDialog=false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="将文件应用到其他标段(包)"
      :visible.sync="isUseDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            :disabled="multiple"
            @click="handleUse()"
          >应用
          </el-button>
        </el-col>
      </el-row>
      <el-table
        :data="subpackageList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          label="标段(包)"
          align="center"
          prop="subpackageName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="getPurchaseDict(purchaseMethodCode, 'purchaseFile')"
          align="center"
          width="80"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.pdfFile"
              type="text"
              size="mini"
              @click="previewFile(row.pdfFile)"
            >预览
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          label="文件状态"
          align="center"
          prop="releaseStatus"
        >
          <template v-slot:default="{row}">
            <dict-tag
              :options="dict.type.purchase_file_status"
              :value="row.releaseStatus"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="报价表头是否一致"
          align="center"
          prop="areQuoteHeadEquals"
        >
          <template v-slot:default="{row}">
            {{ row.areQuoteHeadEquals === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80"
          class-name="small-padding fixed-width"
        >
          <template v-slot:default="{row}">
            <el-button
              :disabled="row.areQuoteHeadEquals!==1"
              type="text"
              size="mini"
              @click="handleUse(row)"
            >应用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer text-center">
        <el-button size="medium" @click="isUseDialog=false">关闭</el-button>
      </div>
    </el-dialog>

    <TipProgress
      ref="progress"
      :percentage="percentage"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  releaseAndStampPurchaseFile,
  getPurchaseFileList,
  importOtherFile,
  applyToOtherPackages,
  uploadPurchaseFilePdf
} from '@/api/purchaser/purchaseFile';
import { checkPermi } from '@/utils/permission';
import { checkFunction } from '@/utils/permission';
import { saveSource } from '@/api/purchaser/startReview';
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import { isEmpty } from '@/utils'
import submitApproval from '@/views/purchaser/components/SubmitApproval/index.vue'
import TipProgress from '@/components/TipProgress/index.vue'
import reviewMethod from '@/views/purchaser/process/purchaseFilePdf/reviewMethod.vue'
import purchaseFileAnnex from '@/views/purchaser/process/purchaseFilePdf/purchaseFileAnnex.vue'

export default {
  name: 'PurchaseFileItem',
  components: {
    submitApproval,
    approvalForm,
    TipProgress,
    reviewMethod,
    purchaseFileAnnex
  },
  dicts: ['purchase_file_status', 'approval_process_status'],
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    let validatorAgree = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请勾选'))
        return
      }
      callback();
    };
    return {
      releaseStatus: null, // 0已上传 1已发布
      form: {
        pdfFile: null,
        claimsFileAttVoList: [],
        reviewList: [],
        isAgree: false
      },
      rules: {
        pdfFile: [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        claimsFileAttVoList: [
          { required: false, message: '请上传', trigger: 'change' }
        ],
        reviewList: [
          { required: true, message: '符合性评审或者评分表至少填写一条', trigger: 'change' }
        ],
        isAgree: [
          { required: true, validator: validatorAgree, trigger: 'change' }
        ]
      },
      isDialog: false,
      subpackageList: [],
      isUseDialog: false,
      selectSubpackage: [],
      subpackageCodeList: [],
      multiple: true,
      priceTotalSource: null,
      approvalId: null,
      processStatus: null, // 0 拒绝 1 通过 2 审批中 3 撤销
      remark: null, // 审批意见
      resumable: null,
      percentage: 100,
      evaluationMethodRemark: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'orgCode',
      'purchaseMethodCode',
      'getPurchaseDict',
      'projectDeptId',
      'filterStr'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  mounted() {
  },
  methods: {
    isEmpty,
    checkPermi,
    checkFunction,
    changeReviewMethod() {
      this.$refs['form'].validateField('reviewList');
      this.$emit('update');
    },
    changeAtt() {
      this.$refs['form'].validateField('claimsFileAttVoList');
    },
    async handleUpload(data) {
      try {
        console.log(data.file)
        this.$modal.loading('正在上传文件，请稍候...')
        await uploadPurchaseFilePdf({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          file: data.file
        })
        this.$modal.msgSuccess('上传成功');
        this.$emit('update');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw e
      }
    },
    handleDetail() {
      this.$router.push({
        name: 'AlreadyApprovalDetail',
        params: {
          id: this.approvalId
        }
      })
    },
    async handleSaveScore() {
      try {
        let reg = /^\d\.([1-9]{1,2}|[0-9][1-9])$|^[1-9]\d{0,1}(\.\d{1,2}){0,1}$|^100(\.0{1,2}){0,1}$/
        if (!reg.test(this.priceTotalSource)) {
          this.$modal.msgError('价格总分，必须大于0，小于等于100，并且最多保留两位小数');
          return;
        }
        this.$modal.loading('数据提交中，请稍候...');
        await saveSource({
          priceTotalSource: Number(this.priceTotalSource),
          subpackageCode: this.subpackageCode
        })
        this.$emit('update');
        this.$modal.msgSuccess('提交成功');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleSelectionChange(selection) {
      this.selectSubpackage = selection;
      this.subpackageCodeList = selection.map(item => item.subpackageCode);
      this.multiple = !selection.length;
    },
    handleUse(row) {
      let subpackageCodeList = row ? [row.subpackageCode] : this.subpackageCodeList;
      this.$confirm('确定要将该标段(包)文件应用到其他该标段(包)', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '再考虑',
        confirmButtonText: '确认应用',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await applyToOtherPackages({
            applySubpackageCodeList: subpackageCodeList,
            subpackageCode: this.subpackageCode
          });
          this.$modal.msgSuccess('操作成功');
          this.isUseDialog = false;
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    importFile(row) {
      this.$confirm('确定要导入该标段(包)文件', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '再考虑',
        confirmButtonText: '确认导入',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await importOtherFile({
            importSubpackageCode: row.subpackageCode,
            subpackageCode: this.subpackageCode
          });
          this.$modal.msgSuccess('操作成功');
          this.isDialog = false;
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    async handleClickUse() {
      await this.getFileList();
      this.isUseDialog = true;
    },
    async handleClickImport() {
      await this.getFileList();
      this.isDialog = true;
    },
    async getFileList() {
      try {
        let { rows } = await getPurchaseFileList({
          pageNum: 1,
          entity: {
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode
          }
        })
        this.subpackageList = rows;
      } catch (e) {
        throw new Error(e)
      }
    },
    verifyForm() {
      return new Promise((resolve) => {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            let tipHtml = `<p>1、对本文件内容已确认无异议</p><p>2、本文件审核通过之后将无法撤回</p>`
            this.$confirm(tipHtml, '提示', {
              customClass: 'max-tip',
              dangerouslyUseHTMLString: true,
              cancelButtonText: '再考虑',
              confirmButtonText: '提交审批',
              type: 'warning'
            }).then(() => {
              resolve(true)
            }).catch(() => {
              resolve(false)
            })
          } else {
            resolve(false)
          }
        });
      })
    },
    successSubmit() {
      this.$emit('update');
    },
    submitForm(data) {
      return releaseAndStampPurchaseFile({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        subpackageCode: this.subpackageCode,
        ...this.form,
        ...data
      });
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    getData(val) {
      let data = val.data || {};
      let auditProcessDto = val.auditProcessDto || {};
      this.priceTotalSource = data.priceTotalSource;
      this.releaseStatus = data.releaseStatus;
      this.form.pdfFile = data.pdfFile;
      this.form.claimsFileAttVoList = data.claimsFileAttVoList || [];
      let evaluationMethod = data.evaluationMethod || {};
      let conformityReview = evaluationMethod.conformityReview || [];
      let scoreReview = evaluationMethod.scoreReview || [];
      this.form.reviewList = [].concat(conformityReview).concat(scoreReview);
      this.evaluationMethodRemark = evaluationMethod.remark;
      this.processStatus = auditProcessDto.status;
      this.form.isAgree = data.releaseStatus === '1' || [1, 2].includes(this.processStatus);
      this.remark = auditProcessDto.remark;
      this.approvalId = auditProcessDto.id;
    }
  }
}
</script>

<style scoped>

</style>
