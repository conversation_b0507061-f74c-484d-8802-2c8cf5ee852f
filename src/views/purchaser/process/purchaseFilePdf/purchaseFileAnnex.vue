<template>
  <div>
    <file-upload-single
      :showUploadBtn="checkPermi(['process:purchaseFile:upload'])"
      :disabled="disabled"
      :showTip="true"
      :autoUpload="false"
      :file-size="2048"
      accept=".doc, .docx, .xlsx, .xls, .pdf, .dwg, .png, .jpg, .jpeg"
      uploadName="responseFile"
      @onSuccess="handleUpload"
    >
      <el-button
        slot="upload-btn"
        type="primary"
        size="mini"
        :disabled="disabled">
        附件上传
      </el-button>
    </file-upload-single>

    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
        :key="index"
      >
        <el-tooltip
          effect="dark"
          :content="file.fileName"
          placement="top"
        >
          <div class="over-ellipsis mr10">
            <i class="el-icon-document"></i>
            {{ file.fileName }}
          </div>
        </el-tooltip>
        <div class="ele-upload-list__item-content-action">
          <el-link
            :underline="false"
            @click="handlePreview(file.fileKey)"
            type="primary"
          >
            预览
          </el-link>
          <el-link
            :underline="false"
            @click="handleDown(file.fileKey)"
            type="primary"
          >
            下载
          </el-link>
          <el-link
            :underline="false"
            @click="handleDelete(index,file)"
            type="danger"
            v-if="!disabled&&checkPermi(['process:purchaseFile:upload'])"
          >
            删除
          </el-link>
        </div>
      </li>
    </transition-group>

    <TipProgress
      ref="progress"
      :percentage="percentage"
    />
  </div>
</template>

<script>
import { getStore } from '@/utils';
import { mapGetters } from 'vuex';
import { checkFunction, checkPermi } from '@/utils/permission';
import Resumable from '@/plugins/resumable'
import { getToken } from '@/utils/auth'
import TipProgress from '@/components/TipProgress/index.vue'
import { delPurchaseAtt, getPurchaseAtt } from '@/api/purchaser/purchaseFile'

export default {
  name: 'PurchaseFileAnnex',
  components: {
    TipProgress
  },
  props: {
    value: {},
    disabled: { // 是否允许响应: 0-不允许，1-允许
      type: Boolean,
      default: false
    },
    subpackageCode: { // 是否允许响应: 0-不允许，1-允许
      type: String,
      default: null
    }
  },
  data() {
    return {
      resumable: null,
      percentage: 100,
      fileList: this.value || []
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode'
    ])
  },
  created() {
    // this.getAttList();
    this.setCurrentValue(this.value)
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  mounted() {
    this.initResumable();
  },
  methods: {
    checkPermi,
    checkFunction,
    setCurrentValue(value) {
      this.fileList = value || []
      this.$emit('input', value)
    },
    initResumable() {
      this.resumable = new Resumable({
        target: process.env.VUE_APP_BASE_API + '/bidding/purchase/claims/answer/up/att', // 替换为你的上传地址
        // 可选，额外的查询参数
        query: {
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode
        },
        headers: {
          'Authorization': 'Bearer ' + getToken(),
          'validToken': getStore('validToken')
        },
        withCredentials: true,
        // setChunkTypeFromFile: true,
        fileType: ['doc', 'docx', 'xlsx', 'xls', 'pdf', 'dwg', 'png', 'jpg', 'jpeg'], // 允许上传的文件类型
        chunkSize: 10 * 1024 * 1024, // 分块大小，单位字节，这里设置为10MB
        testChunks: false, // 是否测试分块完整性，默认为false
        throttleProgressCallbacks: 1, // 节流上传进度回调的频率（毫秒）
        simultaneousUploads: 4, // 同时上传的文件数
        fileParameterName: 'file', // 用于文件块的多部分请求参数的名称
        chunkNumberParameterName: 'chunkNumber', // 当前上传 POST 参数中用于文件块的块索引
        totalChunksParameterName: 'totalChunks', // 用于文件块的块总数 POST 参数的名称
        fileNameParameterName: 'filename', // 用于文件块的原始文件名 POST 参数的名称
        currentChunkSizeParameterName: 'currentChunkSize' // 用于文件块的当前块大小 POST 参数的名称
      });
      // 绑定文件选择器，如果你的上传按钮不是input[type=file]则需要调整选择器或手动添加文件。
      // this.resumable.assignBrowse(document.querySelector('input[type=file]'));
      // 文件添加时触发的事件处理函数
      this.resumable.on('fileAdded', this.onFileAdded);
      this.resumable.on('uploadStart', this.onUploadStart);
      this.resumable.on('fileRetry', this.onFileRetry);
      // 文件上传进度时触发的事件处理函数
      this.resumable.on('fileProgress', this.onFileProgress);
      // 文件上传成功时触发的事件处理函数
      this.resumable.on('fileSuccess', this.onFileSuccess);
      // 文件上传失败时触发的事件处理函数
      this.resumable.on('fileError', this.onFileError);
    },
    async handleUpload(data) {
      console.log('handleUpload', data.file)
      console.log(this.resumable)
      this.resumable.addFile(data.file);
    },
    onFileAdded(file, event) {
      console.log('File added:', file, event);
      this.$refs.progress.show();
      this.resumable.upload();
    },
    onUploadStart() {
      // 上传开始前触发，可以用来构造 FormData 和发送数据
      console.log('onUploadStart:', this.resumable.files)
    },
    onFileRetry(file) {
      console.log('File retry:', file);
    },
    onFileProgress(file) {
      console.log('File progress:', file.progress()); // 获取上传进度信息，范围从0到100。
      this.percentage = Math.round(file.progress() * 100)
    },
    async onFileSuccess(file, message) {
      console.log('File uploaded successfully:', file, message);
      this.resumable.cancel();
      this.$modal.msgSuccess('上传成功');
      try {
        await this.getAttList();
        this.$refs.progress.close();
      } catch (e) {
        this.$refs.progress.close();
        throw new Error(e);
      }
    },
    onFileError(file, message) {
      console.error('File upload error:', file, message);
      this.resumable.cancel();
      this.$modal.msgError(message);
      this.$refs.progress.close();
    },
    // 删除文件
    async handleDelete(index, file) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        await delPurchaseAtt([file.id])
        this.fileList.splice(index, 1);
        this.$emit('input', this.fileList)
        this.$emit('change', this.fileList);
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handlePreview(fileKey) {
      this.$download.previewFile(fileKey)
    },
    async getAttList() {
      try {
        let { data } = await getPurchaseAtt(this.subpackageCode);
        this.setCurrentValue(data || []);
        this.$emit('change', data || []);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
  padding-left: 5px;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action {
  flex-shrink: 0;

  .el-link {
    margin-right: 10px;
  }
}
</style>
