<template>
  <div class="container-wrap">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      size="mini"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="符合性评审" name="first" v-if="checkFunction(['purchaser_review_method_qualified'])">
          <el-table
            cell-class-name="form-cell"
            :data="form.reviewList_1"
            border
          >
            <el-table-column
              label="序号"
              type="index"
              align="center"
              width="55"
            >
            </el-table-column>
            <el-table-column
              align="center"
              label="审查因素"
              width="250"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'reviewList_1.'+scope.$index+'.reviewItem'"
                  :rules="rules.reviewItem"
                  size="mini">
                  <el-input
                    v-if="!disabled"
                    type="textarea"
                    resize="none"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    maxlength="500"
                    v-model.trim="scope.row.reviewItem"
                    placeholder="请输入">
                  </el-input>
                  <div class="table-text" v-else>
                    {{ scope.row.reviewItem }}
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="审查因素描述"
              class-name="text-left"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'reviewList_1.'+scope.$index+'.reviewCriteria'"
                  :rules="rules.reviewCriteria"
                  size="mini">
                  <el-input
                    v-if="!disabled"
                    type="textarea"
                    resize="none"
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    maxlength="65525"
                    v-model.trim="scope.row.reviewCriteria"
                    placeholder="请输入">
                  </el-input>
                  <div class="table-text" v-else>
                    {{ scope.row.reviewCriteria }}
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="主观分/客观分"
              width="180px"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'reviewList_1.'+scope.$index+'.isSubjective'"
                  :rules="rules.isSubjective"
                  size="mini">
                  <el-radio-group
                    v-model="scope.row.isSubjective"
                    :disabled="disabled"
                  >
                    <el-radio :label="1">主观分</el-radio>
                    <el-radio :label="0">客观分</el-radio>
                  </el-radio-group>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="操作"
              width="60px"
              v-if="!disabled"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="text-danger"
                  @click="removeReviewItem(1,scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="plus-box text-center text-primary" v-if="!disabled">
            <span class="pointer" @click="addReviewItem(1)">增加一行</span>
          </div>
        </el-tab-pane>

        <el-tab-pane label="评分表" name="second" v-if="checkFunction(['purchaser_review_method_score'])">
          <table class="score-table">
            <thead>
            <tr>
              <th width="90px">评分模块</th>
              <th width="60px">序号</th>
              <th width="250px">审查因素</th>
              <th>审查因素描述</th>
              <th width="90px">分值</th>
              <th width="180px">主观分/客观分</th>
              <th width="60px" v-if="!disabled">操作</th>
            </tr>
            </thead>
            <template v-for="(obj,key) in config.filter(v=>v.value!==1)">
              <tbody :key="'tbody_'+key">
              <tr
                class="tr-hover"
                v-for="(item,index) in form['reviewList_'+obj.value]"
                :key="key+'-'+index"
              >
                <td
                  class="form-cell"
                  :rowspan="disabled?form['reviewList_'+obj.value].length:form['reviewList_'+obj.value].length+1"
                  v-if="index===0">
                  <div class="cell">
                    {{ item.reviewModule }}
                  </div>
                </td>
                <td class="form-cell">
                  <div class="cell">
                    {{ index + 1 }}
                  </div>
                </td>
                <td class="form-cell">
                  <div class="cell">
                    <el-form-item
                      :prop="'reviewList_'+obj.value+'.'+index+'.reviewItem'"
                      :rules="rules.reviewItem"
                      size="mini">
                      <el-input
                        v-if="!disabled"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 2, maxRows: 6 }"
                        maxlength="500"
                        v-model.trim="item.reviewItem"
                        placeholder="请输入">
                      </el-input>
                      <div class="table-text" v-else>
                        {{ item.reviewItem }}
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="form-cell text-left">
                  <div class="cell">
                    <el-form-item
                      :prop="'reviewList_'+obj.value+'.'+index+'.reviewCriteria'"
                      :rules="rules.reviewCriteria"
                      size="mini">
                      <el-input
                        v-if="!disabled"
                        type="textarea"
                        resize="none"
                        :autosize="{ minRows: 2, maxRows: 6 }"
                        maxlength="65525"
                        v-model.trim="item.reviewCriteria"
                        placeholder="请输入">
                      </el-input>
                      <div class="table-text" v-else>
                        {{ item.reviewCriteria }}
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="form-cell">
                  <div class="cell">
                    <el-form-item
                      :prop="'reviewList_'+obj.value+'.'+index+'.reviewScore'"
                      :rules="rules.reviewScore"
                      size="mini">
                      <el-input
                        v-if="!disabled"
                        v-model.trim="item.reviewScore"
                        placeholder="请输入">
                      </el-input>
                      <div class="table-text" v-else>
                        {{ item.reviewScore }}
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="form-cell">
                  <div class="cell">
                    <el-form-item
                      :prop="'reviewList_'+obj.value+'.'+index+'.isSubjective'"
                      :rules="rules.isSubjective"
                      size="mini">
                      <el-radio-group
                        v-model="item.isSubjective"
                        :disabled="disabled"
                      >
                        <el-radio :label="1">主观分</el-radio>
                        <el-radio :label="0">客观分</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                </td>
                <td class="form-cell" v-if="!disabled">
                  <el-button
                    type="text"
                    class="text-danger"
                    @click="removeReviewItem(obj.value,index)">
                    删除
                  </el-button>
                </td>
              </tr>
              <tr v-if="!disabled">
                <td v-if="form['reviewList_'+obj.value].length===0">
                  {{ obj.label }}
                </td>
                <td colspan="6">
                  <span class="pointer text-primary" @click="addReviewItem(obj.value)">增加一行</span>
                </td>
              </tr>
              </tbody>
            </template>
          </table>
        </el-tab-pane>
      </el-tabs>

      <!--      <div style="margin-top: 30px;">-->
      <!--        <el-form-item label="补充说明：" prop="remark" label-width="85px">-->
      <!--          <el-input-->
      <!--            :disabled="disabled"-->
      <!--            type="textarea"-->
      <!--            resize="none"-->
      <!--            rows="5"-->
      <!--            maxlength="1024"-->
      <!--            v-model.trim="form.remark"-->
      <!--            placeholder="请输入">-->
      <!--          </el-input>-->
      <!--        </el-form-item>-->
      <!--      </div>-->

      <div class="text-center mt20" v-if="!disabled">
        <el-button type="primary" @click="submitForm">
          保存评审办法前附表
        </el-button>
      </div>

    </el-form>
  </div>
</template>

<script>
import reg from '@/utils/reg'
import { uploadEvaluation } from '@/api/purchaser/purchaseFile'
import { v4 as uuidv4 } from 'uuid';
import { checkFunction } from '@/utils/permission'

export default {
  name: 'ReviewMethod',
  props: {
    value: {},
    subpackageCode: {
      type: String,
      default: null
    },
    remark: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      config: [
        {
          label: '符合性评审',
          value: 1
        },
        {
          label: '技术',
          value: 2
        },
        {
          label: '商务',
          value: 3
        },
        {
          label: '价格',
          value: 4
        }
      ],
      form: {},
      rules: {
        isSubjective: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        remark: [
          { required: false, message: '请输入', trigger: 'blur' }
        ],
        reviewItem: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        reviewCriteria: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        reviewScore: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ]
      },
      activeName: 'first'
    }
  },
  created() {
    this.activeName = this.checkFunction(['purchaser_review_method_qualified']) ? 'first' : 'second';
    this.reset();
    this.setCurrentValue(this.value)
  },
  watch: {
    value: {
      handler(val) {
        this.setCurrentValue(val)
      },
      deep: true
      // immediate: true
    },
    remark: {
      handler(val) {
        this.form.remark = val;
      },
      immediate: true
    }
  },
  methods: {
    checkFunction,
    setCurrentValue(value) {
      let list = value || [];
      list.forEach(item => {
        let obj = this.config.find(v => v.value === item.reviewType);
        this.$set(item, 'reviewModule', obj ? obj.label : null);
      })
      this.$set(this.form, 'reviewList_1', list.filter(v => v.reviewType === 1));
      this.$set(this.form, 'reviewList_2', list.filter(v => v.reviewType === 2));
      this.$set(this.form, 'reviewList_3', list.filter(v => v.reviewType === 3));
      this.$set(this.form, 'reviewList_4', list.filter(v => v.reviewType === 4));
      this.$emit('input', list)
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            let conformityReview = [];
            let scoreReview = [];
            conformityReview = this._.cloneDeep(this.form['reviewList_' + 1])
            this.config
              .filter(v => v.value !== 1)
              .forEach(item => {
                let arr = this._.cloneDeep(this.form['reviewList_' + item.value]);
                scoreReview = scoreReview.concat(arr);
              });
            // if (this.checkFunction(['purchaser_review_method_qualified'])) {
            //   if (conformityReview.length === 0) {
            //     this.$modal.msgWarning('符合性评审至少填写一行');
            //     return
            //   }
            // }
            // if (this.checkFunction(['purchaser_review_method_score'])) {
            //   if (scoreReview.length === 0) {
            //     this.$modal.msgWarning('评分表至少填写一行');
            //     return
            //   }
            // }
            this.$modal.loading('数据提交中，请稍候...')
            await uploadEvaluation({
              evaluationMethod: {
                conformityReview,
                scoreReview,
                remark: this.form.remark
              },
              subpackageCode: this.subpackageCode
            })
            let list = [].concat(conformityReview).concat(scoreReview)
            this.$emit('input', list)
            this.$emit('change', list, this.form.remark);
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e)
          }
        }
      });
    },
    reset() {
      this.form = {
        reviewList_1: [],
        reviewList_2: [],
        reviewList_3: [],
        reviewList_4: [],
        remark: ''
      };
      this.resetForm('form');
    },
    removeReviewItem(reviewType, index) {
      this.form['reviewList_' + reviewType].splice(index, 1);
    },
    addReviewItem(reviewType) {
      this.form['reviewList_' + reviewType].push(this.defaultRow(reviewType));
    },
    defaultRow(reviewType) {
      let obj = this.config.find(v => v.value === reviewType);
      let uuid = uuidv4();
      return {
        reviewType: obj.value,
        reviewModule: obj.label,
        reviewItem: '',
        reviewCriteria: '',
        isSubjective: 1,
        reviewScore: null,
        scoreChapter: [],
        uuid: uuid
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-text {
  line-height: 1.5;
  padding: 0 10px;
  font-size: 12px;
}

.plus-box {
  border-left: 1px solid #dfe6ec;
  border-right: 1px solid #dfe6ec;
  border-bottom: 1px solid #dfe6ec;
  height: 50px;
  line-height: 50px;
}

.score-table {
  width: 100%;
  border-collapse: collapse;
  border-left: 1px solid #dfe6ec;
  border-top: 1px solid #dfe6ec;
  color: #606266;

  th {
    background: #EFEFEF;
    border-right-color: #ffffff !important;
    font-weight: 600;
    color: #4A4A4A;
  }

  tr.tr-hover:hover {
    td {
      background: #f5f7fa;
    }
  }

  th, td {
    border-right: 1px solid #dfe6ec;
    padding: 10px 0;
    vertical-align: middle;
    text-align: center;
    line-height: 1.5;
    position: relative;

    &:after {
      display: block;
      content: '';
      width: 100%;
      height: 1px;
      background: #dfe6ec;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 1;
    }
  }

  .td-div {
    padding: 5px 15px;
    margin: 14px 0;
  }
}
</style>
