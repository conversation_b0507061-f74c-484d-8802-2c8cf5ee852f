<template>
  <div>
    <div v-if="!showEditor">
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['process:bulletin:publish']"
          >新增
          </el-button>
        </el-col>
      </el-row>

      <div v-loading="loading">
        <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
          <el-collapse-item
            v-for="(item,index) in subpackageList"
            :key="index"
            :title="'标段(包)：'+item.subpackageName"
            :name="item.subpackageCode"
          >
            <bulletin-item
              :key="item.subpackageCode"
              :itemData="item.data"
              @update="getList"
              @toDetail="toDetail"
            />
          </el-collapse-item>
        </el-collapse>
        <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
          <el-tab-pane
            v-for="(item,index) in subpackageList"
            :key="index"
            :label="'标段(包)：'+item.subpackageName"
            :name="item.subpackageCode"
          >
            <bulletin-item
              v-if="activeName===item.subpackageCode"
              :key="item.subpackageCode"
              :itemData="item.data"
              @update="getList"
              @toDetail="toDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </div>

    </div>

    <bulletin-editor v-if="showEditor" :subpackageList="subpackageList" :bulletinId="bulletinId" :editType="editType" @publicSuccess="publicSuccess">
      <el-button type="primary" size="small" @click="handleBack">返回</el-button>
    </bulletin-editor>
  </div>
</template>

<script>
import bulletinEditor from './bulletinEditor/index'
import bulletinItem from './bulletinItem.vue';
import { listBulletinInner } from '@/api/purchaser/bulletin'
import { mapGetters } from 'vuex';

export default {
  name: 'BulletinItem',
  components: {
    bulletinEditor,
    bulletinItem
  },
  data() {
    return {
      subpackageList: [],
      loading: false,
      activeNames: [],
      activeName: null,
      showEditor: false,
      bulletinId: null,
      editType: null // 0:新增 1:修改 2查看
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    publicSuccess() {
      this.showEditor = false
      this.getList();
    },
    toDetail(data) {
      this.bulletinId = data.bulletinId;
      this.editType = data.editType;
      this.showEditor = true
    },
    handleAdd() {
      this.bulletinId = null;
      this.editType = 0;
      this.showEditor = true
    },
    handleBack() {
      this.showEditor = false
    },
    getList() {
      this.loading = true
      listBulletinInner(this.buyItemCode)
        .then(res => {
          this.subpackageList = res.data;
          this.activeNames = this.subpackageList.map(item => item.subpackageCode);
          this.activeName = this.subpackageList[0].subpackageCode;
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>

</style>
