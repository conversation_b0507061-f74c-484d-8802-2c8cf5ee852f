<template>
  <div>
    <el-form ref="form" size="small" :model="form" :rules="rules" label-position="top">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="18" :md="18" :lg="16" :xl="14">
          <el-form-item prop="bulletinContentHtml">
            <template-select
              :disabled="editType===2"
              v-if="initEditor"
              :height="800"
              :max-height="1000"
              :tmp-type="tmpType"
              :showSealTip="signatureOff==='0'"
              v-model="form.bulletinContentHtml"
              :getTemplateFunc="getTemplateFunc"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="8" :xl="10" style="max-width: 600px;">
          <el-form-item>
            <slot></slot>
          </el-form-item>
          <el-form-item label="公告类型" prop="bulletinType">
            <el-select
              :disabled="editType===2"
              v-model="form.bulletinType"
              placeholder="请选择公告类型"
              @change="changeNoticeType"
              class="block"
            >
              <el-option
                v-for="item in bulletin_list_publish"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="公告标题" prop="bulletinName">
            <el-input v-model.trim="form.bulletinName" maxlength="100" placeholder="请输入公告标题" :disabled="editType===2"/>
          </el-form-item>
          <el-form-item label="标段(包)" prop="subpackageCodeList">
            <el-select
              :disabled="editType===2"
              v-model="form.subpackageCodeList"
              multiple
              clearable
              placeholder="请选择标段(包)"
              class="block"
            >
              <el-option
                v-for="item in subpackageList"
                :key="item.subpackageCode"
                :label="item.subpackageName"
                :value="item.subpackageCode"
              ></el-option>
            </el-select>
          </el-form-item>

          <template v-if="form.bulletinType==='change_announcement'">
            <template v-for="(item,index) in form.bulletinTimeDtoList">
              <el-form-item
                :label="item.timeName"
                :prop="'bulletinTimeDtoList.'+index+'.time'"
                :key="'time'+index"
                :rules="rules[item.timeType]"
              >
                <el-date-picker
                  :disabled="editType===2"
                  class="block"
                  style="width: 100%"
                  v-model="item.time"
                  type="datetime"
                  placeholder="请选择"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  default-time="10:00:00"
                >
                </el-date-picker>
              </el-form-item>
            </template>
          </template>

          <el-form-item
            :label="getPurchaseDict(purchaseMethodCode, 'bidOpenAddress')"
            prop="bidOpenAddress"
            v-if="form.bulletinType==='change_announcement'"
          >
            <el-input v-model.trim="form.bidOpenAddress" maxlength="250" placeholder="请输入" :disabled="editType===2"/>
          </el-form-item>

          <el-form-item label="附件" prop="attachmentDtoList">
            <file-upload
              :disabled="editType===2"
              :showUploadBtn="editType!==2"
              v-model="form.attachmentDtoList"
              :showTip="true"
              :fileSize="20"
              :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
              :params="{
                fileTypeName: 'announcement_annex',
                buyItemCode: buyItemCode,
                yearMonthSplit: createYearMonth
              }"
              @preview="previewFile"
              @down="downFile"
            >
            </file-upload>
          </el-form-item>

          <el-form-item v-if="editType!==2">
            <publish-submit
              :submit-func="submitForm"
              :verify-func="verifyForm"
              @success="successSubmit"
              v-if="checkFunction(['purchaser_bulletin_not_approved']) "
            />
            <div v-else>
              <submit-approval
                v-if="versionType==='bjxk'"
                :auditTitle="form.bulletinName+'审批'"
                :verify-func="verifyForm"
                :submit-func="submitForm"
                @success="successSubmit"
              />
              <approval-form
                v-else
                bindKey="bulletin"
                :bindDeptId="projectDeptId"
                :orgCode="orgCode"
                :auditTitle="form.bulletinName+'审批'"
                :filterStr="filterStr"
                :verify-func="verifyForm"
                :submit-func="submitForm"
                @success="successSubmit"
                v-hasPermi="['process:bulletin:publish']"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fillTemplate, getBulletinInfo, publishBulletin } from '@/api/purchaser/bulletin'
import { checkFunction } from '@/utils/permission';
import approvalForm from '@/views/purchaser/components/ApprovalForm/index.vue'
import PublishSubmit from '@/views/purchaser/components/PublishSubmit/index.vue'
import { verifyEditor } from '@/utils/validate'
import submitApproval from '@/views/purchaser/components/SubmitApproval/index.vue'

export default {
  name: 'Index',
  components: {
    submitApproval,
    approvalForm,
    PublishSubmit
  },
  props: {
    subpackageList: {
      type: Array,
      default: () => []
    },
    bulletinId: {
      type: Number,
      default: null
    },
    editType: {
      type: Number, // 0:新增 1:再次提交 2查看
      default: null
    }
  },
  data() {
    return {
      tmpType: null,
      initEditor: true,
      form: {},
      rules: {
        bulletinContentHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ],
        bulletinName: [
          { required: true, message: '公告标题不能为空', trigger: 'blur' }
        ],
        bulletinType: [
          { required: true, message: '请选择公告类型', trigger: 'change' }
        ],
        subpackageCodeList: [
          { required: true, message: '请选择标段(包)', trigger: 'change' }
        ],
        registrationEndTime: [{ required: false, message: '请选择', trigger: 'change' }],
        responseFileEndTime: [{ required: false, message: '请选择', trigger: 'change' }],
        meetingTime: [{ required: false, message: '请选择', trigger: 'change' }],
        bidOpenAddress: [{ required: false, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'signatureOff',
      'bulletinList',
      'purchaseMethodCode',
      'getPurchaseDict',
      'orgCode',
      'buyItemCode',
      'buyItemName',
      'createYearMonth',
      'projectDeptId',
      'filterStr'
    ]),
    bulletin_list_publish() {
      return this.bulletinList.filter(item => item.tags.some(v => v === 'bulletin_publish_show'))
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.reset();
  },
  methods: {
    checkFunction,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    getTemplateFunc(data) {
      return fillTemplate({
        buyItemCode: this.buyItemCode,
        purchaseMethodCode: this.purchaseMethodCode,
        tmpKey: data.tmpKey,
        tmpType: data.tmpType
      });
    },
    async changeNoticeType(val) {
      try {
        let obj = this.bulletinList.find(item => item.value === val);
        this.form.bulletinTypeName = obj ? obj.label : null;
        this.form.bulletinContentHtml = '';
        this.form.bidOpenAddress = null;
        this.form.bulletinName = this.buyItemName + this.form.bulletinTypeName;
        this.tmpType = val;
        this.initEditor = false;
        this.$nextTick(() => {
          this.initEditor = true;
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    verifyForm() {
      return new Promise((resolve) => {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            if (this.form.bulletinType !== 'change_announcement') {
              this.form.bulletinTimeDtoList = [];
              this.form.bidOpenAddress = null;
            }
            resolve(true)
          } else {
            resolve(false)
          }
        });
      })
    },
    successSubmit() {
      this.$emit('publicSuccess');
    },
    submitForm(data) {
      return publishBulletin({
        orgCode: this.orgCode,
        buyItemCode: this.buyItemCode,
        ...this.form,
        ...data
      });
    },
    async getDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getBulletinInfo(this.bulletinId);
        for (let key in this.form) {
          this.form[key] = data[key];
        }
        this.form.subpackageCodeList = [data.subpackageCode];
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    // 表单重置
    async reset() {
      let timeList = [];
      if (this.versionType === 'wzlg') {
        timeList = [
          { timeType: 'registrationEndTime', timeName: null, time: null },
          { timeType: 'meetingTime', timeName: null, time: null }
        ];
      } else {
        timeList = [
          { timeType: 'registrationEndTime', timeName: null, time: null },
          { timeType: 'responseFileEndTime', timeName: null, time: null },
          { timeType: 'meetingTime', timeName: null, time: null }
        ];
      }
      timeList.forEach(item => {
        this.$set(item, 'timeName', this.getPurchaseDict(this.purchaseMethodCode, item.timeType))
      })
      this.form = {
        bulletinName: null,
        bulletinType: null,
        bulletinTypeName: null,
        bulletinContentHtml: '',
        attachmentDtoList: [],
        bulletinTimeDtoList: timeList,
        bidOpenAddress: null,
        subpackageCodeList: []
      };
      this.resetForm('form');
      console.log(this.form)
      if (!this.bulletinId) {
        let obj = this.bulletin_list_publish[0];
        this.form.bulletinType = obj ? obj.value : null;
        if (this.form.bulletinType) {
          await this.changeNoticeType(this.form.bulletinType);
        }
      } else {
        await this.getDetail()
      }
    }
  }
}
</script>

<style scoped>

</style>
