<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="110px">
      <el-form-item label="监标人" prop="monitorBidPersonId">
        <el-select
          style="width: 240px;"
          class="mr5"
          size="small"
          v-model="form.monitorBidPersonId"
          clearable
          filterable
          placeholder="请选择监标人"
          @change="changeMonitorBidPerson"
        >
          <el-option
            v-for="dict in monitorBidPersonList"
            :key="dict.userId"
            :label="dict.nickName"
            :value="dict.userId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label=" " v-if="checkPermi(['process:monitorBidPerson:save'])">
        <el-button type="primary" @click="submitForm">
          保存
        </el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { addMonitor } from '@/api/purchaser/monitorBidPerson'

export default {
  name: 'MonitorBidPersonItem',
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    },
    monitorBidPersonList: {
      type: Array,
      required: () => []
    }
  },
  data() {
    return {
      form: {
        monitorBidPersonId: null,
        monitorBidPerson: null
      },
      rules: {
        monitorBidPersonId: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'buyItemName',
      'orgCode'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    async submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await addMonitor({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode,
              subpackageName: this.subpackageName,
              ...this.form
            })
            this.$emit('update');
            this.$modal.msgSuccess('提交成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      });
    },
    changeMonitorBidPerson(val) {
      let obj = this.monitorBidPersonList.find(v => v.userId === val);
      this.form.monitorBidPerson = obj ? obj.nickName : null;
    },
    getData(val) {
      let data = val || {};
      this.form.monitorBidPersonId = data.monitorBidPersonId;
      this.form.monitorBidPerson = data.monitorBidPerson;
    }
  }
}
</script>

<style scoped>

</style>
