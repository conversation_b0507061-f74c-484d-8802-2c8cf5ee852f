<template>
  <div v-loading="loading">
    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <monitor-bid-person-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item"
          :monitorBidPersonList="monitorBidPersonList"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <monitor-bid-person-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item"
          :monitorBidPersonList="monitorBidPersonList"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import monitorBidPersonItem from '@/views/purchaser/process/monitorBidPerson/monitorBidPersonItem.vue';
import { listUser } from '@/api/system/user'
import { getMonitorInfo } from '@/api/purchaser/monitorBidPerson'

export default {
  name: 'MonitorBidPerson',
  components: { monitorBidPersonItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null,
      monitorBidPersonList: []
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getList();
    this.getMonitorBidPerson();
  },
  methods: {
    async getMonitorBidPerson() {
      try {
        let res = await this.getConfigKey('sys.monitorBidPerson.roleId');
        let roleId = Number(res.data);
        let orgCode = this.$cache.local.get('loginOrgCode');
        let { rows } = await listUser({ roleId, orgCode, status: '0' })
        let list = rows || [];
        this.monitorBidPersonList = list.map(item => {
          return {
            nickName: item.nickName,
            userId: item.userId
          }
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getMonitorInfo(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
