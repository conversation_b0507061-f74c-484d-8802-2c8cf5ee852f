<template>
  <div v-loading="loading" style="position: relative;">

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" class="fr">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新信息
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" style="width: 100%;" border>
      <el-table-column label="序号" type="index" width="50" align="center"/>
      <el-table-column label="供应商" align="center" prop="supplierSignUpInfo.bidderName" :show-overflow-tooltip="true"/>
      <el-table-column
        :label="getPurchaseDict(this.purchaseMethodCode, 'responseFile')+'制作IP'"
        align="center"
        prop="ip"
        :show-overflow-tooltip="true"
        v-if="ipShow==='1'">
        <template v-slot:default="{row}">
          {{ row.ip || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="签到" align="center" prop="bidOpeningSign">
        <template v-slot:default="{row}">
          <span v-if="row.bidOpeningSign==='1'" class="text-navy">已签到</span>
          <span v-else class="text-danger">未签到</span>
        </template>
      </el-table-column>
      <el-table-column
        label="解密状态"
        align="center"
        prop="bidOpeningDecrypt"
        v-if="checkFunction(['purchaser_response_file_encryption'])">
        <template v-slot:default="{row}">
          <span v-if="row.bidOpeningDecrypt==='1'" class="text-navy">已解密</span>
          <span v-else class="text-danger">未解密</span>
        </template>
      </el-table-column>
      <el-table-column label="是否签字" align="center" prop="bidOpeningRecordForm" v-if="checkFunction(['purchaser_bid_sing'])">
        <template v-slot:default="{row}">
          <span v-if="row.bidOpeningRecordForm==='1'" class="text-navy">已签字</span>
          <span v-else class="text-danger">未签字</span>
        </template>
      </el-table-column>
      <el-table-column label="报价表" align="center">
        <template v-slot:default="{row}">
          <el-button
            :disabled="quoteDisabled(row)"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleQuote(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否允许进入评审" align="center" prop="enterTheReview" min-width="130">
        <template v-slot:default="{row}">
          <el-radio-group v-model="row.enterTheReview" :disabled="!((status==='0'||status==='1')&&checkPermi(['process:bid:open']))">
            <el-radio
              v-for="(item,index) in dict.type.enter_the_review_status"
              :key="index"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-chat-dot-round"
            @click="handleUrge([row.supplierId])"
          >通知
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-alert
      v-if="checkFunction(['purchaser_bid_open_automatic'])&&autoBidOpenMsg"
      class="mt10"
      :title="autoBidOpenMsg"
      show-icon
      type="warning"
      effect="dark"
      :closable="false"
    >
    </el-alert>

    <div class="text-center mt30 mb20">
      <el-button
        type="primary"
        :disabled="!endOfCountdown"
        @click="handleStart"
        v-if="status==='-1'&&checkPermi(['process:bid:open'])&&!checkFunction(['purchaser_bid_open_automatic'])">
        {{ bidOpenLabel + '开始' }}
      </el-button>
      <el-button
        type="primary"
        @click="handleSing"
        v-if="status==='0'&&checkPermi(['process:bid:open'])&&checkFunction(['purchaser_bid_sing'])">
        唱标
      </el-button>
      <el-button
        type="primary"
        @click="handleEnd"
        v-if="status==='1'&&checkPermi(['process:bid:open'])&&checkFunction(['purchaser_bid_sing'])">
        {{ bidOpenLabel + '完成' }}
      </el-button>
      <el-button
        type="primary"
        @click="handleEnd"
        v-if="status==='0'&&checkPermi(['process:bid:open'])&&!checkFunction(['purchaser_bid_sing'])">
        {{ bidOpenLabel + '完成' }}
      </el-button>
      <el-button
        type="danger"
        @click="handleRestart"
        v-if="status!=='-1'&&checkPermi(['process:bidOpen:restart'])&&checkFunction(['purchaser_bid_open_restart'])">
        {{ '重新' + bidOpenLabel }}
      </el-button>
    </div>

    <div class="text-center mb30" v-if="status==='-1'">
      <div class="mb20 fontSize16" :style="{color: theme}" v-if="currentTime&&meetingTime">
        {{ bidOpenLabel + '开始' }}时间剩余
      </div>
      <count-down
        :end-time="meetingTime"
        :start-time="currentTime"
        v-if="currentTime&&meetingTime"
        @changeCountdown="changeCountdown">
      </count-down>
    </div>

    <div class="open-file-box" v-if="recordFile">
      <div class="open-file-icon mr30" :style="{'color': theme}">
        <svg-icon icon-class="pdf" class-name="icon-pdf" @click="previewFile(recordFile)"/>
        <div>{{ bidOpenLabel }}记录表</div>
      </div>
      <el-button size="small" type="success" @click="previewFile(recordFile)">预览</el-button>
      <el-button size="small" type="primary" @click="handleDown(recordFile)">下载</el-button>
    </div>

    <div class="explain-box">
      <h3>关于{{ purchaseLabel }}人{{ bidOpenLabel }}流程的操作说明：</h3>
      <p>
        1 - {{ purchaseLabel }}过程中{{ purchaseLabel }}流程实施人（以下简称{{ purchaseLabel }}人），在{{ bidOpenLabel }}流程中一共需要按顺序点击三个“确认”按钮：
        1- {{ bidOpenLabel }}开始；2- 唱标；3- {{ bidOpenLabel }}完成。
      </p>
      <p>
        2 - 第一步（{{ bidOpenLabel }}开始 ）：
        以{{ purchaseLabel }}人在{{ purchaseLabel }}公告中确定的{{ bidOpenLabel }}时间为节点，供应商在{{ bidOpenLabel }}时间前30分钟之内 “签到
        ” ，
        {{ purchaseLabel }}人可根据实际情况需要等待未签到的供应商，
        等待时间以{{ purchaseLabel }}人点击 “{{ bidOpenLabel }}开始” 为终止节点。
        {{ purchaseLabel }}人点击完成 “{{ bidOpenLabel }}开始” 按钮之后，未签到的供应商将不能参与本次{{ bidOpenLabel }}流程中的：签到、解密、签字
        。
      </p>
      <p>
        3 - 第二步（唱标）：
        {{ purchaseLabel }}人点击 “{{ bidOpenLabel }}开始” 按钮之后，供应商开始解密，解密时间以{{ purchaseLabel }}人确认 “唱标” 为终止节点。
        {{ purchaseLabel }}人点击 “唱标” 按钮之后，未解密的供应商将不能：解密、签字 。
      </p>
      <p>
        4 - 第三步（{{ bidOpenLabel }}完成）：
        {{ purchaseLabel }}人点击 “唱标” 按钮之后，供应商开始签字，签字时间以{{ purchaseLabel }}人确认 “{{ bidOpenLabel }}完成” 为终止节点。
        {{ purchaseLabel }}人点击 “{{ bidOpenLabel }}完成” 按钮之后，未签字的供应商将不能：签字。
      </p>
      <div>
        <el-image :src="require('@/assets/images/process.png')" fit="fill"></el-image>
      </div>
    </div>

    <el-dialog
      title="报价表"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      width="90%"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <u-table
        :data="bodyMaps"
        :height="500"
        use-virtual
        :row-height="55"
        border>
        <u-table-column
          label="序号"
          align="center"
          type="index"
          width="50"
        />
        <u-table-column
          v-for="(item,k) in heads"
          :key="k"
          align="center"
          :label="item.keyName"
          :show-overflow-tooltip="true"
        >
          <template v-slot:default="{row}">
            <span v-if="item.keyType!=='file'">{{ row[item.keyVal] }}</span>
            <div v-else>
              <el-button
                v-if="row[item.keyVal]"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="previewFile(row[item.keyVal])"
              >查看
              </el-button>
              <span v-else>/</span>
            </div>
          </template>
        </u-table-column>
      </u-table>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="isFinishDialog"
      custom-class="maxW800"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="text-danger fontSize16 mb10">以下供应商将进入评审，请确认是否{{ bidOpenLabel }}完成？</p>
      <el-table :data="reviewSuppliers" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column
          label="供应商"
          align="center"
          prop="supplierSignUpInfo.bidderName"
          :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="签到" align="center" prop="bidOpeningSign">
          <template v-slot:default="{row}">
            <span v-if="row.bidOpeningSign==='1'" class="text-navy">已签到</span>
            <span v-else class="text-danger">未签到</span>
          </template>
        </el-table-column>
        <el-table-column
          label="解密状态"
          align="center"
          prop="bidOpeningDecrypt"
          v-if="checkFunction(['purchaser_response_file_encryption'])">
          <template v-slot:default="{row}">
            <span v-if="row.bidOpeningDecrypt==='1'" class="text-navy">已解密</span>
            <span v-else class="text-danger">未解密</span>
          </template>
        </el-table-column>
        <el-table-column label="是否签字" align="center" prop="bidOpeningRecordForm" v-if="checkFunction(['purchaser_bid_sing'])">
          <template v-slot:default="{row}">
            <span v-if="row.bidOpeningRecordForm==='1'" class="text-navy">已签字</span>
            <span v-else class="text-danger">未签字</span>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="text-center">
        <el-button @click="isFinishDialog = false">取 消</el-button>
        <el-button type="primary" @click="bidFinish">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBidOpening,
  startBidOpening,
  endBidOpening,
  queryBidderQuote,
  singBidOpening,
  statusBidOpening,
  restartBidOpening
} from '@/api/purchaser/bidOpen';
import { getTimeBySubpackageCode } from '@/api/purchaser/bulletin';
import { mapGetters } from 'vuex';
import { isEmpty } from '@/utils';
import { checkFunction, checkPermi } from '@/utils/permission';

export default {
  name: 'BidOpenItem',
  dicts: ['msg_template', 'enter_the_review_status'],
  props: {
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      status: '-1', // 开标状态'-1'开标未开始，0-开标开始，1-唱标，2-开标完成
      recordFile: null, // 开标记录表
      currentTime: null,
      meetingTime: null,
      endOfCountdown: false, // 倒计时是否结束
      isDialog: false,
      heads: [],
      bodyMaps: [],
      isFinishDialog: false,
      reviewSuppliers: [],
      ipShow: null,
      autoBidOpenMsg: null
    }
  },
  computed: {
    ...mapGetters([
      'purchaseMethodCode',
      'getPurchaseDict',
      'buyItemCode',
      'buyItemName',
      'userId',
      'theme',
      'wsMsg'
    ]),
    bidOpenLabel() {
      return this.getPurchaseDict(this.purchaseMethodCode, 'bidOpen')
    },
    purchaseLabel() {
      return this.selectDictValue(this.purchaseMethodCode, 'purchase')
    }
  },
  created() {
    this.getConfigKey('sys.ip.show').then(response => {
      this.ipShow = response.data
    })
    this.getList();
    this.getStatus();
    this.getTime();
  },
  watch: {
    wsMsg: {
      handler(val) {
        if (val && val.displayEnums.includes('REFRESH') && val.businessTypeEnum === 'BID_OPENING') {
          this.getList();
          this.getStatus();
        }
      },
      deep: true
    }
  },
  methods: {
    checkFunction,
    checkPermi,
    refresh() {
      this.getList();
    },
    quoteDisabled(row) {
      let flag = false;
      if (this.checkFunction(['purchaser_response_file_encryption'])) {
        flag = row.bidOpeningDecrypt !== '1';
      } else {
        flag = !(row.bidOpeningSign === '1' && this.status !== '-1');
      }
      return flag
    },
    handleUrge(ids) {
      this.$msgNotify({
        prepend: `项目：${this.buyItemName}，标段：${this.subpackageName}，`,
        msgTemplate: this.dict.type.msg_template.map(item => item.value),
        handleConfirm: (val) => {
          this.$store.dispatch('websocket/websocketSend', {
            topic: '/app/chatOne',
            data: {
              fromUserId: this.userId,
              toUserIds: ids,
              msg: val,
              displayEnums: ['NOTIFY']
            }
          })
        }
      })
    },
    /** 重新开标 */
    handleRestart() {
      let text = '重新' + this.bidOpenLabel;
      let tipHtml = `<p style="color: #ff4949">${text}后，之前产生的数据将被删除并重置，是否确认${text}？</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await restartBidOpening({
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode,
            signUpList: this.tableData
          });
          await this.getList();
          await this.getStatus();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('操作成功');
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch(() => {
      });
    },
    /** 查看报价表 */
    async handleQuote(row) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryBidderQuote({
          subpackageCode: this.subpackageCode,
          supplierId: row.supplierId
        })
        let { purchaseQuoteForm, bodyMaps } = data;
        this.heads = purchaseQuoteForm.heads;
        this.bodyMaps = bodyMaps;
        this.$modal.closeLoading();
        this.isDialog = true;
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 调用开标结束接口 */
    async bidFinish() {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await endBidOpening({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          signUpList: this.tableData
        });
        await this.getStatus();
        this.isFinishDialog = false;
        this.$modal.closeLoading();
        this.$modal.msgSuccess('操作成功');
      } catch (e) {
        this.$modal.closeLoading();
        throw e
      }
    },
    /** 点击开标结束 */
    handleEnd() {
      let text = this.bidOpenLabel + '完成';
      if (this.tableData.some(item => isEmpty(item.enterTheReview))) {
        this.$modal.msgError('请选择所有供应商是否允许进入评审');
        return;
      }
      /** 判断进入评审的供应商数量 */
      let arr = this.tableData.filter(item => item.enterTheReview === '1');
      if (arr.length === 0) {
        let tipHtml = `<p style="color: #ff4949">${text}后，所有信息将无法再修改</p>
                       <p style="color: #ff4949">进入评审的供应商数量为0，是否确认${text}？</p>`
        this.$confirm(tipHtml, '提示', {
          customClass: 'max-tip',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(() => {
          this.bidFinish();
        }).catch(() => {
        });
        return
      }
      this.reviewSuppliers = arr;
      this.isFinishDialog = true;
    },
    /** 唱标 */
    handleSing() {
      let arr = this.tableData.filter(item => item.bidOpeningDecrypt === '1');
      if (arr.length === 0) {
        this.$tipDialog({
          tipSubText: '供应商解密人数为0，无法唱标',
          tipSubColor: '#ff4949',
          type: 'warning'
        })
        return
      }
      let tipHtml = `<p style="color: #ff4949">唱标后，未解密的供应商将无法再解密，是否确认唱标？</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await singBidOpening({
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode,
            signUpList: this.tableData
          });
          await this.getStatus();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('唱标成功');
        } catch (e) {
          this.$modal.closeLoading();
          throw e
        }
      }).catch(() => {
      });
    },
    /** 点击开标 */
    handleStart() {
      if (this.tableData.length === 0) {
        this.$tipDialog({
          tipSubText: '该标段(包)没有供应商',
          tipSubColor: '#ff4949',
          type: 'warning'
        })
        return
      }
      let text = this.bidOpenLabel + '开始';
      let tipHtml = `<p style="color: #ff4949">${text}后，未签到的供应商将无法再签到，是否确认${text}？</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await startBidOpening({
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode,
            signUpList: this.tableData
          });
          await this.getStatus();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('操作成功');
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch(() => {
      });
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    changeCountdown(data) {
      this.endOfCountdown = data.isEnd;
    },
    async getStatus() {
      try {
        let { data } = await statusBidOpening(this.subpackageCode);
        this.status = data.status;
        this.recordFile = data.recordFile;
        this.autoBidOpenMsg = data.autoBidOpenMsg;
      } catch (e) {
        throw new Error(e);
      }
    },
    async getTime() {
      try {
        let { data } = await getTimeBySubpackageCode(this.subpackageCode);
        this.meetingTime = data.meetingTime;
        this.currentTime = data.currentTime;
      } catch (e) {
        throw new Error(e);
      }
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listBidOpening(this.subpackageCode);
        this.tableData = data || [];
        this.tableData.forEach(item => {
          let enterTheReview = item.bidOpeningSign === '1' && item.bidOpeningDecrypt === '1' ? '1' : '0';
          this.$set(item, 'enterTheReview', item.enterTheReview || enterTheReview);
        })
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e;
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.open-file-box {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid $borderColor;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.open-file-icon {
  display: inline-block;
  text-align: center;

  i {
    font-size: 70px;
  }

  div {
    font-size: 14px;
  }
}

.explain-box {
  text-align: left;

  h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  p {
    text-indent: 2em;
    line-height: 1.5;
    font-size: 14px;
    margin-bottom: 5px;
  }
}
</style>
