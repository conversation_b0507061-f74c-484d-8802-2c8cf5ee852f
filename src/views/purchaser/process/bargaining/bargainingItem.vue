<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column label="序号" type="index" width="50" align="center"/>
      <el-table-column label="报价单位" align="center" prop="supplierCompanyName"/>
      <template v-for="(item,index) in roundList">
        <el-table-column :label="item.round===0?'初始报价表':'第'+numToChinese(item.round)+'轮议价'" align="center" :key="index">
          <template slot="header">
            <span>{{ item.round === 0 ? '初始报价表' : '第' + numToChinese(item.round) + '轮议价' }}</span>
            <el-popover trigger="click" placement="top" v-if="item.countdown||item.serviceRequire">
              <el-descriptions :column="1" border :labelStyle="{width:'110px'}" style="max-width: 600px;">
                <el-descriptions-item label="报价截止时间">
                  {{ item.countdown | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
                </el-descriptions-item>
                <el-descriptions-item label="备注">{{ item.serviceRequire }}</el-descriptions-item>
              </el-descriptions>
              <el-button
                v-if="item.countdown||item.serviceRequire"
                slot="reference"
                class="ml5"
                size="mini"
                type="text"
              >
                查看时间及备注
              </el-button>
            </el-popover>
          </template>
          <el-table-column label="报价详情" align="center">
            <template v-slot:default="{row}">
              <el-button
                v-if="row['round_' + item.round].length>0"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="previewQuotationSheet(row,row['round_' + item.round],row['allRowQuotationTotalPrice_' + item.round])"
              >
                查看
              </el-button>
              <span v-else>未报价</span>
            </template>
          </el-table-column>
          <el-table-column label="报价总价" align="center">
            <template v-slot:default="{row}">
              {{ !isEmpty(row['allRowQuotationTotalPrice_' + item.round]) ? row['allRowQuotationTotalPrice_' + item.round] : '/' }}
            </template>
          </el-table-column>
          <el-table-column label="降幅百分比" align="center">
            <template v-slot:default="{row}">
              {{ !isEmpty(row['percentageChange_' + item.round]) ? row['percentageChange_' + item.round] : '/' }}
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>

    <div
      class="text-center mt20"
      v-if="checkFunction(['purchaser_bargaining_operate'])&&checkPermi(['process:bargaining:start'])">
      <el-button type="primary" @click="onStart" v-if="launchStatus==='2'">
        发起第{{ numToChinese(round + 1) }}轮议价
      </el-button>
      <el-button type="danger" @click="onEnd" v-if="launchStatus==='1'">
        结束{{ round === 0 ? '初始' : '第' + numToChinese(round) }}轮议价
      </el-button>
    </div>

    <el-dialog
      title="报价表"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="min-height: 300px">
        <p class="mb10">{{ supplierCompanyName }}</p>
        <u-table
          :data="bodyMaps"
          :height="500"
          use-virtual
          :row-height="55"
          border>
          <u-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <u-table-column
            v-for="(item,k) in heads"
            :key="k"
            align="center"
            :label="item.keyName"
            :show-overflow-tooltip="true"
          >
            <template v-slot:default="{row}">
              <span v-if="item.keyType!=='file'">{{ row[item.keyVal] }}</span>
              <div v-else>
                <el-button
                  v-if="row[item.keyVal]"
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="previewFile(row[item.keyVal])"
                >查看
                </el-button>
                <span v-else>/</span>
              </div>
            </template>
          </u-table-column>
        </u-table>
        <div class="text-right pr10 fontSize16" style="line-height: 50px;">
          报价总价：{{ allRowQuotationTotalPrice }}
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="发起议价"
      :visible.sync="isOpen"
      custom-class="maxW500"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="form" :rules="rules" ref="form" size="small" label-width="160px">
        <el-form-item label="报价截止时间（分钟）" prop="sustainedTime">
          <el-input v-model.number.trim="form.sustainedTime" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注 " prop="serviceRequire">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.serviceRequire"
            placeholder="请输入"
            maxlength="250"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isOpen = false">关闭</el-button>
        <el-button type="primary" @click="onStartSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { numToChinese, isEmpty } from '@/utils';
import { startOrEndBargain } from '@/api/purchaser/bargaining';
import { checkPermi, checkFunction } from '@/utils/permission';

export default {
  name: 'BargainingItem',
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    let checkAge = (rule, value, callback) => {
      if (value && !Number.isInteger(value)) {
        callback(new Error('请输入数字值'));
      } else {
        callback();
      }
    };
    return {
      roundList: [], // 总轮数
      round: null, // 当前轮
      launchStatus: null, // 议价状态：1 发起 2 结束
      // countdown: null,
      // currentDate: null,
      heads: [],
      tableData: [],
      bodyMaps: [],
      supplierCompanyName: null,
      isDialog: false,
      isOpen: false,
      form: {},
      rules: {
        sustainedTime: [{ required: false, validator: checkAge, trigger: 'blur' }]
      },
      allRowQuotationTotalPrice: null
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    isEmpty,
    checkPermi,
    checkFunction,
    numToChinese,
    reset() {
      this.form = {
        sustainedTime: null,
        serviceRequire: null
      }
      this.resetForm('form');
    },
    onStart() {
      if (this.tableData.length === 0) {
        this.$modal.msgWarning('未查询到供应商');
        return
      }
      this.reset()
      this.isOpen = true;
    },
    async onStartSubmit() {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await startOrEndBargain({
          subpackageCode: this.subpackageCode,
          subpackageName: this.subpackageName,
          launchStatus: '1',
          round: this.round + 1,
          supplierIds: this.tableData.map(item => item.supplierId),
          ...this.form
        })
        this.$modal.msgSuccess('操作成功');
        this.isOpen = false;
        this.$emit('update');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    onEnd() {
      this.$confirm('确认结束议价？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await startOrEndBargain({
            subpackageCode: this.subpackageCode,
            subpackageName: this.subpackageName,
            launchStatus: '2',
            round: this.round,
            supplierIds: this.tableData.map(item => item.supplierId)
          })
          this.$modal.msgSuccess('操作成功');
          this.$emit('update');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    previewQuotationSheet(row, bodyMaps, allRowQuotationTotalPrice) {
      this.bodyMaps = bodyMaps;
      this.supplierCompanyName = row.supplierCompanyName;
      this.allRowQuotationTotalPrice = allRowQuotationTotalPrice;
      this.isDialog = true;
    },
    getData(data) {
      if (!data) {
        return
      }
      let supplierQuoteFormVo = data.supplierQuoteFormVo || {};
      this.heads = supplierQuoteFormVo.heads;
      this.round = data.round;
      this.launchStatus = data.launchStatus;
      this.roundList = this._.sortBy(data.requireDetailList || [], 'round');
      // let roundObj = this.roundList.find(v => v.round === this.round);
      // this.countdown = roundObj ? roundObj.countdown : null;
      // this.currentDate = new Date();
      let list = supplierQuoteFormVo.supplierQuoteFormList || [];
      this.tableData = list.map(item => {
        let obj = {};
        this.roundList.forEach(m => {
          let o = item.roundQuoteFormList.find(v => v.round === m.round);
          obj['round_' + m.round] = o ? (o.bodyMaps || []) : [];
          obj['percentageChange_' + m.round] = o ? o.percentageChange : null;
          obj['allRowQuotationTotalPrice_' + m.round] = o ? o.allRowQuotationTotalPrice : null;
        })
        return {
          supplierCompanyName: item.supplierCompanyName,
          supplierId: item.supplierId,
          ...obj
        }
      });
      console.log(this.tableData)
      console.log(this.roundList)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
