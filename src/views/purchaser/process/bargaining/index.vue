<template>
  <div v-loading="loading">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新信息
        </el-button>
      </el-col>
      <el-col :span="1.5" class="fr" v-if="checkPermi(['process:bargaining:export'])">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出议价结果表
        </el-button>
      </el-col>
    </el-row>

    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <bargaining-item
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item"
          @update="getList"
        />
      </el-collapse-item>
    </el-collapse>
    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="'标段(包)：'+item.subpackageName"
        :name="item.subpackageCode"
      >
        <bargaining-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :subpackageCode="item.subpackageCode"
          :subpackageName="item.subpackageName"
          :itemData="item"
          @update="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { exportExcelBargaining, listBargaining } from '@/api/purchaser/bargaining';
import { mapGetters } from 'vuex';
import bargainingItem from '@/views/purchaser/process/bargaining/bargainingItem.vue';
import { checkFunction, checkPermi } from '@/utils/permission'

export default {
  name: 'Bargaining',
  components: { bargainingItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    checkFunction,
    async handleExport() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await exportExcelBargaining(this.buyItemCode);
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    refresh() {
      this.getList();
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listBargaining(this.buyItemCode);
        this.subpackageList = data;
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
