<template>
  <div v-loading="loading">
    <el-collapse v-model="activeNames" class="process-collapse" v-if="showView">
      <el-collapse-item
        v-for="(item,index) in subpackageList"
        :key="index"
        :title="item.isSubpackage?'标段(包)：'+item.subpackageName:item.subpackageName"
        :name="item.subpackageCode"
      >
        <project-files-item
          :key="item.subpackageCode"
          :isSubpackage="item.isSubpackage"
          :subpackageCode="item.subpackageCode"
          :itemData="item.fileInfoVoList"
          @update="getFiles"
        />
      </el-collapse-item>
    </el-collapse>

    <el-tabs tab-position="left" v-model="activeName" v-if="!showView">
      <el-tab-pane
        v-for="(item,index) in subpackageList"
        :key="index"
        :label="item.isSubpackage?'标段(包)：'+item.subpackageName:item.subpackageName"
        :name="item.subpackageCode"
      >
        <project-files-item
          v-if="activeName===item.subpackageCode"
          :key="item.subpackageCode"
          :isSubpackage="item.isSubpackage"
          :subpackageCode="item.subpackageCode"
          :itemData="item.fileInfoVoList"
          @update="getFiles"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import projectFilesItem from '@/views/purchaser/process/projectFiles/projectFilesItem.vue';
import { getProjectFiles } from '@/api/purchaser/projectFiles'

export default {
  name: 'PurchaseFiles',
  components: { projectFilesItem },
  data() {
    return {
      loading: false,
      subpackageList: [],
      activeNames: [],
      activeName: null
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'showView'
    ])
  },
  created() {
    this.getFiles();
  },
  methods: {
    async getFiles() {
      try {
        let { data } = await getProjectFiles(this.buyItemCode);
        let list = data.subpackageFileVoList || [];
        this.subpackageList = list.map(v => {
          return {
            isSubpackage: true,
            ...v
          }
        })
        this.subpackageList.unshift({
          isSubpackage: false,
          subpackageName: data.buyItemName,
          subpackageCode: data.buyItemCode,
          fileInfoVoList: data.fileInfoVoList
        })
        this.activeNames = this.subpackageList.map(item => item.subpackageCode);
        this.activeName = this.subpackageList[0].subpackageCode;
      } catch (e) {
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
