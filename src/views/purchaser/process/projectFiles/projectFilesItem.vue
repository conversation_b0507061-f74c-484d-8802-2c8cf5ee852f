<template>
  <div>
    <div class="clearfix mb10">
      <file-upload-single
        style="float: right;"
        :file-size="50"
        :params="isSubpackage?{
          fileTypeName: 'buy_item_other_file',
          buyItemCode: buyItemCode,
          subpackageCode: subpackageCode,
          yearMonthSplit: createYearMonth,
        }:{
          fileTypeName: 'buy_item_other_file',
          buyItemCode: buyItemCode,
          yearMonthSplit: createYearMonth,
        }"
        @onSuccess="handleUpload"
      >
        <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
      </file-upload-single>
    </div>
    <el-descriptions :column="2" border>
      <el-descriptions-item
        v-for="(item,index) in fileList"
        :key="index"
        :label="item.fileName"
        contentClassName="my-label"
      >
        <el-button type="text" size="mini" @click="previewFile(item.fileKey)">预览</el-button>
        <el-button type="text" size="mini" @click="handleDown(item.fileKey)">下载</el-button>
        <el-button type="text" size="mini" @click="handleRemove(item.fileKey)">
          删除
        </el-button>
        <span class="ml20">{{ item.createTime }}</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { fileDelete } from '@/api/file';
import { mapGetters } from 'vuex'

export default {
  name: 'ProjectFiles',
  props: {
    itemData: {
      type: Array,
      default: () => []
    },
    subpackageCode: {
      type: String
    },
    isSubpackage: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'createYearMonth'
    ])
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    handleUpload() {
      this.$emit('update')
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handleRemove(fileKey) {
      let tipHtml = `<p style="color: #ff4949;margin-bottom: 10px;font-size: 18px;">确定要删除改文件？</p>
                     <p style="color: #ff4949;text-align: center;">注意：文件一旦删除，将无法恢复，请谨慎操作！！！</p>`
      this.$confirm(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await fileDelete(fileKey);
            this.$modal.msgSuccess('删除成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    getData(data) {
      this.fileList = data || [];
    }
  }
}
</script>

<style lang="scss">
.my-label {
  min-width: 165px;
}
</style>
