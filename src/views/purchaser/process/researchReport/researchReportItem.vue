<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column label="调研报告" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(row)"
            v-if="!row.reportFileKey&&checkPermi(['process:research:report'])"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.reportFileKey)"
            v-if="row.reportFileKey"
          >
            预览
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.reportFileKey)"
            v-if="row.reportFileKey"
          >
            下载
          </el-button>
          <el-button
            size="mini"
            type="text"
            class="btn-text-danger"
            icon="el-icon-delete"
            @click="handleRemove"
            v-if="row.reportFileKey&&checkPermi(['process:research:report'])"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="签章" align="center" v-if="checkFunction(['purchaser_research_report_sign'])">
        <template v-for="(item,index) in signInfoVoList">
          <el-table-column :label="item.signUserName" align="center" :key="index">
            <template v-slot:default="{row}">
              <div v-if="row.reportFileKey">
                <el-button
                  v-if="userId===item.signUserId&&item.signStatus!=='1'&&checkPermi(['process:research:report'])"
                  size="mini"
                  type="text"
                  icon="el-icon-s-check"
                  @click="handleSign"
                >
                  签章
                </el-button>
                <el-tag :type="item.signStatus === '1' ? 'success' : 'info'" v-else>
                  {{ item.signStatus === '1' ? '已签章' : '未签章' }}
                </el-tag>
              </div>
              <span v-else>/</span>
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      top="2vh"
      title="调研报告"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="" prop="researchReportText">
              <template-select v-if="open" tmp-type="research_report" v-model="form.researchReportText"></template-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label=" ">
              <el-button size="small" type="primary" @click="submitForm">提交</el-button>
              <el-button size="small" type="primary" @click="generatePDF">生成pdf</el-button>
              <el-button size="small" @click="open = false">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { checkFunction, checkPermi } from '@/utils/permission';
import { editReport, generateReportPDF, removeReport, signReport } from '@/api/purchaser/researchReport';

export default {
  name: 'ResearchReportItem',
  props: {
    itemData: {
      type: Object,
      default: () => {
      }
    },
    signOff: {
      type: String,
      default: null
    },
    signVersion: {
      type: String,
      default: null
    },
    subpackageCode: {
      type: String,
      required: true
    },
    subpackageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      signInfoVoList: [],
      open: false,
      form: {},
      rules: {
        researchReportText: [
          { required: true, message: '内容不能为空', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'phoneNumber',
      'userId'
    ])
  },
  watch: {
    itemData(data) {
      this.getData(data);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkFunction,
    checkPermi,
    reset() {
      this.form = {
        researchReportText: null,
        subpackageCode: null
      };
      this.form.subpackageCode = this.subpackageCode
      this.resetForm('form');
    },
    handleEdit(row) {
      this.reset();
      this.form.researchReportText = row.researchReportText;
      this.open = true;
    },
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await editReport(this.form);
            this.$modal.msgSuccess('提交成功');
            this.open = false;
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }
      });
    },
    generatePDF() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await generateReportPDF(this.form);
            this.$modal.msgSuccess('生成成功');
            this.open = false;
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }
      });
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handleRemove() {
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await removeReport(this.subpackageCode);
          this.$modal.msgSuccess('删除成功');
          this.$emit('update');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    handleSign() {
      if (this.signOff === '0' && this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.submitSignReport({ authCode, flowId });
          }
        })
      } else {
        this.submitSignReport();
      }
    },
    async submitSignReport(args = {}) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { authCode, flowId } = args;
        await signReport({ subpackageCode: this.subpackageCode, authCode, flowId });
        this.$modal.msgSuccess('签章成功');
        this.$emit('update');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    getData(val) {
      let data = val || {};
      this.signInfoVoList = data.signInfoVoList || [];
      this.tableData = [
        { reportFileKey: data.researchReportKey, researchReportText: data.researchReportText }
      ];
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
