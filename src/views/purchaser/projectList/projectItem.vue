<template>
  <div class="project-item">
    <div class="project-item-left" :style="{borderTop:index===1?'1px solid #dfe6ec':'none'}">{{ index }}</div>
    <div class="project-item-right" :style="{borderTop:index===1?'1px solid #dfe6ec':'none'}">
      <h3 class="project-name over-ellipsis">
        <span>【{{ purchaseMethodName }}】</span>
        <span v-if="item.innerCode">【{{ item.innerCode }}】</span>
        <span>{{ item.buyItemName }}</span>
      </h3>
      <el-table :data="tableData" border :show-header="false" :span-method="objectSpanMethod" style="width: 100%">
        <el-table-column align="center" width="110px">
          <template>
            <el-button
              class="mb5"
              type="primary"
              plain
              size="mini"
              @click="toEditProject(item.buyItemCode)">
              项目详情
            </el-button>
            <el-button
              class="mb5"
              style="margin-left: 0"
              type="primary"
              plain
              size="mini"
              v-if="checkPermi(['purchaser:project:reAdd'])"
              @click="toCopyProject(item.buyItemCode)"
            >
              {{ repurchaseLabel }}
            </el-button>
            <el-button
              class="mb5"
              style="margin-left: 0"
              type="danger"
              plain
              size="mini"
              v-if="checkPermi(['purchaser:project:remove'])"
              @click="delProject(item.buyItemCode,item.buyItemName)"
            >删除项目
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="subpackageName"></el-table-column>
        <el-table-column align="left" prop="timeNodeList" min-width="400px" class-name="step-cell">
          <template v-slot:default="{row}">
            <div class="mb5">
              <el-tag
                style="margin-right: 5px;"
                v-for="(tag,index) in row.jsonList"
                :key="index"
                type="success"
                size="small"
                :closable="checkPermi(['project:tag:remove'])"
                :disable-transitions="false"
                @close="removeTag(row,tag.jsonKey)">
                {{ tag.jsonLabel }}
              </el-tag>
              <el-button size="mini" @click="addTag(row)" v-if="checkPermi(['project:tag:add'])">+ 添加标签</el-button>
            </div>
            <el-steps :active="100" class="step-wrap" :style="{'--theme': theme}">
              <el-step v-for="(stepItem,stepKey) in row.timeNodeList" :key="stepKey">
                  <span slot="title" class="fontSize12 text-base" :class="{'step-content':!!stepItem.time}">
                    {{ getPurchaseDict(item.purchaseMethodCode, stepItem.timeKeyName) }}
                  </span>
                <span slot="description" class="fontSize12 text-base" :class="{'step-content':!!stepItem.time}">
                  {{ stepItem.time | formatTime('YYYY-MM-DD HH:mm:ss') }}
                </span>
              </el-step>
            </el-steps>

            <svg-icon icon-class="abandon-bid" class-name="project-icon" v-if="row.abandon==='1'" style="left:inherit;right: 0;"/>
            <svg-icon icon-class="finish" class-name="project-icon" v-if="item.end===1"/>
          </template>
        </el-table-column>
        <el-table-column align="center" width="110px" v-if="checkPermi(['project:process:query'])">
          <template v-slot:default="{row}">
            <el-button
              type="primary"
              plain
              size="mini"
              @click="toProcess(item,row.subpackageCode)"
            >进入流程
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>

import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { addLabel, delBuyItemInfo, delLabel } from '@/api/purchaser/projectList';

export default {
  name: 'ProjectItem',
  props: {
    index: {
      type: Number,
      default: 1
    },
    itemData: {
      type: Object,
      default: () => {
      }
    },
    purchaseMethodName: {
      type: String,
      default: null
    },
    repurchaseLabel: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      item: {},
      tableData: [],
      spanArr: [],
      pos: 0
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device',
      'getPurchaseDict'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  watch: {
    itemData(value) {
      this.getData(value);
    }
  },
  created() {
    this.getData(this.itemData);
  },
  methods: {
    checkPermi,
    async toProcess(item, subpackageCode) {
      if (!this.$auth.hasPermi('project:process:query')) {
        this.$modal.msgError('当前操作没有权限');
        return
      }
      await this.$store.dispatch('process/setBuyItemName', item.buyItemName)
      await this.$store.dispatch('process/setBuyItemCode', item.buyItemCode)
      await this.$store.dispatch('process/setPurchaseMethodCode', item.purchaseMethodCode)
      await this.$store.dispatch('process/setCreateYearMonth', this.formatTime(item.createAt, 'YYYYMM'))
      await this.$store.dispatch('process/setOrgCode', item.orgCode);
      await this.$store.dispatch('process/setProjectDeptId', item.deptId);
      await this.$store.dispatch('process/setBidType', item.bidType);
      await this.$store.dispatch('process/setFilterStr', { versionType: this.versionType, ...item });
      await this.$store.dispatch('process/setSubpackageCode', subpackageCode);
      await this.$store.dispatch('process/setBulletinList', { belongRole: '1', buyItemCode: item.buyItemCode })
      let res = await this.$store.dispatch('process/setProcessNodes', {
        buyItemCode: item.buyItemCode,
        subpackageCode: subpackageCode,
        belongRole: '1'
      })
      if (res.length === 0) {
        this.$modal.msgSuccess('未查询到功能配置');
        return
      }
      await this.$router.push({ path: res[0].path })
    },
    toCopyProject(buyItemCode) {
      this.$router.push({ name: 'CopyProject', params: { buyItemCode }})
    },
    toEditProject(buyItemCode) {
      this.$router.push({ name: 'EditProject', params: { buyItemCode }})
    },
    delProject(buyItemCode, buyItemName) {
      let tipHtml = `<p style="color: #ff4949;font-size: 18px;margin-bottom: 10px;">确定要删除该项目？</p>
           <p style="color: #ff4949;margin-bottom: 10px;">删除之后该项目数据将完全从系统中删除，且删除之后无法恢复！</p>
           <p style="color: #ff4949">请谨慎操作！！！</p>
      <p style="font-size: 14px;text-align: left;margin-top: 30px;">请确认要删除的项目名称</p>`
      this.$prompt(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showInput: true,
        inputPlaceholder: '请输入项目的名称',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && (value === buyItemName)
        },
        inputErrorMessage: '请输入项目的名称'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delBuyItemInfo(buyItemCode)
          this.$modal.msgSuccess('删除成功');
          this.$emit('update');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    addTag(row) {
      this.$prompt('', '请输入标签内容', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false;
          }
          const reg = '^[ ]+$';
          const re = new RegExp(reg);
          return !re.test(value) && value.length < 20;
        },
        inputErrorMessage: '请输入标签内容，最长20个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await addLabel({
            jsonLabel: value,
            subpackageName: row.subpackageName,
            subpackageCode: row.subpackageCode
          })
          this.$modal.msgSuccess('添加成功');
          this.$emit('update');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    removeTag(row, key) {
      this.$confirm('确定要删除该标签？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await delLabel({
              jsonKey: key,
              subpackageName: row.subpackageName,
              subpackageCode: row.subpackageCode
            })
            this.$modal.msgSuccess('删除成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    getData(data) {
      if (!data) {
        return
      }
      this.item = data;
      this.tableData = data.subpackageTimeVoList;
      this.getSpanArr(this.tableData);
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [0, 3];
      if (columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          this.spanArr[this.pos] += 1;
          this.spanArr.push(0);
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.project-item {
  display: flex;
  justify-content: space-between;
  align-items: stretch;

  .project-item-left {
    width: 40px;
    font-size: 14px;
    border: 1px solid #dfe6ec;
    border-right: none;
    border-top: none;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .project-item-right {
    width: calc(100% - 40px);

    .project-name {
      height: 40px;
      line-height: 40px;
      background: #f8f8f9;
      color: #515a6e;
      font-size: 14px;
      word-break: break-word;
      font-weight: 700;
      padding: 0 10px;
      border-left: 1px solid #dfe6ec;
      border-right: 1px solid #dfe6ec;
    }
  }
}
</style>
<style lang="scss">
.step-cell {
  .cell {
    line-height: normal;
  }

  .step-wrap {
    position: relative;

    .el-step__head {
      .el-step__line {
        height: 1px;

        .el-step__line-inner {
          border-width: 0 !important;
          height: 1px;
          background-color: #{'var(--theme)'};
        }
      }

      .el-step__icon {
        width: 20px;
        height: 20px;
        font-size: 13px;

        &.is-text {
          border: 1px solid;
        }

        .el-step__icon-inner {
          font-weight: normal;
        }
      }
    }

    .el-step__main {
      margin-top: 2px;

      .el-step__title {
        line-height: 1.5;
      }
    }

    .step-content {
      //font-weight: bold;
      color: #{'var(--theme)'};
    }
  }
}
</style>
