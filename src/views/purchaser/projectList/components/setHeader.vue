<template>
  <div class="set-header-wrap">
    <el-button type="primary" size="mini" @click="showColumn()">{{ btnText }}</el-button>
    <el-dialog
      :title="title"
      width="90%"
      custom-class="maxW600"
      :visible.sync="open"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      modal-append-to-body
      append-to-body>
      <select-heads
        v-model="currentValue"
        :groupType="groupType"
        @columnChange="columnChange"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="open=false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import selectHeads from '@/components/SelectHeads'

export default {
  name: 'SetHeader',
  components: {
    selectHeads
  },
  props: {
    value: {},
    groupType: {
      type: [String, Number],
      default: null
    },
    btnText: {
      type: String,
      default: '设置表头'
    },
    title: {
      type: String,
      default: '选择表头'
    }
  },
  data() {
    return {
      currentValue: this.value,
      // 是否显示弹出层
      open: false
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    setCurrentValue(value) {
      this.currentValue = value;
      this.$emit('input', value)
    },
    // 右侧列表元素变化
    columnChange(data) {
      this.setCurrentValue(data)
      this.$emit('columnChange', data)
    },
    // 打开显隐列dialog
    showColumn() {
      this.open = true
    }
  }
}
</script>

<style lang="scss" scoped>
.set-header-wrap {
  display: inline-block;
}

.el-button + .set-header-wrap {
  margin-left: 10px;
}

.set-header-wrap + .el-button {
  margin-left: 10px;
}
</style>
