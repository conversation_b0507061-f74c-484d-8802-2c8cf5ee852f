<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="145px">
      <el-card
        header="采购项目"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box project-card"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
              prop="buyItemName"
            >
              <el-input v-model.trim="form.buyItemName" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'purchaseMethod')"
              prop="purchaseMethodCode"
            >
              <el-select
                v-model="form.purchaseMethodCode"
                placeholder="请选择"
                class="block"
                :disabled="isDisabled || createType === 5 || isShop"
                @change="changePurchaseMethod"
              >
                <el-option
                  v-for="dict in purchase_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预算金额" prop="buyBudget">
              <el-input v-model.trim="form.buyBudget" placeholder="请输入" :disabled="isDisabled">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="使用科室" prop="useDept">
              <el-input v-model.trim="form.useDept" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" prop="buyClass">
              <el-select v-model="form.buyClass" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="项目负责人" prop="buyPerson">
              <el-input v-model.trim="form.buyPerson" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="联系电话" prop="concatNumber">
              <el-input v-model.trim="form.concatNumber" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="组织形式" prop="organizeType">
              <el-select v-model="form.organizeType" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_organize_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="备注" prop="buyRemark">
              <el-input v-model.trim="form.buyRemark" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

        </el-row>
      </el-card>

      <template v-for="(bsItem,key) in form.subpackageDtoList">
        <el-card
          shadow="never"
          :body-style="{'padding': '15px 10px 0px 10px'}"
          class="card-box"
          :key="key"
        >
          <div slot="header" class="clearfix">
            <span style="font-size: 14px;">第{{ numToChinese(key + 1) }}标段(包)</span>
            <el-button
              class="fr"
              size="mini"
              icon="el-icon-delete"
              type="danger"
              plain
              v-if="form.subpackageDtoList.length>1&&!isDisabled&&checkPermi(['purchaser:project:add','purchaser:project:edit'])"
              @click="removeBidSection(key)"
            >删除
            </el-button>
          </div>
          <el-row :gutter="10">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="标段(包)名称" :prop="'subpackageDtoList.'+key+'.subpackageName'" :rules="rules.subpackageName">
                <el-input v-model.trim="bsItem.subpackageName" placeholder="请输入" :disabled="isDisabled"/>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="标段内容" :prop="'subpackageDtoList.'+key+'.subpackageContent'" :rules="rules.subpackageContent">
                <el-input v-model.trim="bsItem.subpackageContent" placeholder="请输入" :disabled="isDisabled"/>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="标段(包)控制价" :prop="'subpackageDtoList.'+key+'.contractPrice'" :rules="rules.contractPrice">
                <el-input v-model.trim="bsItem.contractPrice" placeholder="请输入" :disabled="isDisabled">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-if="createType!==5&&!isShop">
              <el-form-item
                label="设置报价表表头"
                :prop="'subpackageDtoList.'+key+'.claimsFileQuoteFormCreatorDto.heads'"
                :rules="rules.heads">
                <set-header
                  v-model="bsItem.claimsFileQuoteFormCreatorDto.heads"
                  groupType="0"
                  @columnChange="columnChange(bsItem.claimsFileQuoteFormCreatorDto.heads, key)"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" class="mb10" v-if="bsItem.claimsFileQuoteFormCreatorDto.heads.length>0">
            <el-col :span="1.5" class="fr">
              <upload-excel
                size="mini"
                :defaultHeader="bsItem.claimsFileQuoteFormCreatorDto.heads.map(item=>{return {label: item.keyName, key: item.keyVal}})"
                @onSuccess="handleSuccess($event, key, bsItem.claimsFileQuoteFormCreatorDto.heads)">
              </upload-excel>
            </el-col>
          </el-row>
          <el-table
            :data="bsItem.claimsFileQuoteFormCreatorDto.bodyMaps"
            border
            v-if="bsItem.claimsFileQuoteFormCreatorDto.heads.length>0">
            <el-table-column label="序号" align="center" type="index" width="50"></el-table-column>
            <template v-for="(item,k) in bsItem.claimsFileQuoteFormCreatorDto.heads">
              <el-table-column :label="item.keyName" align="center" :key="k" class-name="form-cell">
                <template v-slot:default="{row,$index}">
                  <el-form-item
                    v-if="item.keyType!=='file'"
                    :prop="'subpackageDtoList.'+key+'.claimsFileQuoteFormCreatorDto.bodyMaps.'+$index+'.'+item.keyVal"
                    label-width="0"
                    size="mini"
                    :rules="[
                     { pattern: eval2(item.regex), message: item.remark||'格式不正确', trigger: ['blur', 'change'] }
                    ]"
                  >
                    <el-input
                      v-model.trim="row[item.keyVal]"
                      placeholder="请输入"
                      :disabled="isDisabled">
                    </el-input>
                  </el-form-item>
                  <span v-else class="text-danger">由供应商上传</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column
              label="操作"
              align="center"
              width="60"
              v-if="!isDisabled&&checkPermi(['purchaser:project:add','purchaser:project:edit'])"
            >
              <template v-slot:default="{$index}">
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  class="btn-text-danger"
                  type="text"
                  @click="removeDetail(bsItem.claimsFileQuoteFormCreatorDto.bodyMaps,$index)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div
            class="text-center pt10"
            v-if="bsItem.claimsFileQuoteFormCreatorDto.heads.length>0&&!isDisabled&&checkPermi(['purchaser:project:add','purchaser:project:edit'])"
          >
            <el-button
              type="text"
              size="mini"
              icon="el-icon-plus"
              @click="addDetail(bsItem.claimsFileQuoteFormCreatorDto.bodyMaps, bsItem.claimsFileQuoteFormCreatorDto.heads)"
            >
              添加一行
            </el-button>
          </div>

        </el-card>
      </template>

      <div class="text-center" v-if="!isDisabled&&checkPermi(['purchaser:project:add','purchaser:project:edit'])">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="addBidSection"
          plain
        >
          添加标段(包)
        </el-button>
      </div>

      <div class="text-center mt30" v-if="[0, 1, 3, 5, 6].some(item => item === createType)&&checkPermi(['purchaser:project:add'])">
        <el-button type="primary" @click="submitForm">
          保存
        </el-button>
      </div>

      <div class="text-center mt30" v-if="createType===2&&checkPermi(['purchaser:project:edit'])">
        <el-button type="primary" @click="submitForm">
          修改
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { numToChinese } from '@/utils'
import { checkReg, eval2 } from '@/utils/validate';
import {
  createProjectInfo,
  modifyProjectInfo,
  queryProjectInfo,
  queryPurchaseMethod
} from '@/api/purchaser/projectList'
import { checkPermi } from '@/utils/permission'
import setHeader from './setHeader.vue';
import reg from '@/utils/reg'
import { Base64 } from 'js-base64'
import { cartInfo, delCart } from '@/api/purchaser/shoppingMall'
import { getProjectAssignDetail } from '@/api/purchaser/projectInformation'

export default {
  name: 'ProjectDetail',
  dicts: ['project_category', 'project_organize_type', 'sys_dict_translate'],
  components: {
    setHeader
  },
  props: {
    createType: {
      type: Number,
      default: 0 // 0创建 1复制 2编辑 3申购项目创建 4申购项目详情查看 5商城项目创建  6分派项目创建 7分派项目详情查看
    }
  },
  data() {
    return {
      purchase_method: [],
      buyItemCode: null,
      form: {},
      rules: {
        buyItemName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' },
          { max: 100, message: '最长100个字符', trigger: 'blur' }
        ],
        purchaseMethodCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        buyBudget: [
          { required: false, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ],
        useDept: [
          { required: true, message: '请选择', trigger: 'blur' },
          { max: 100, message: '最长100个字符', trigger: 'blur' }
        ],
        buyClass: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        buyPerson: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 50, message: '最长50个字符', trigger: 'blur' }
        ],
        concatNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 16, message: '最长16个字符', trigger: 'blur' }
        ],
        organizeType: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        buyRemark: [
          { required: false, message: '请输入', trigger: 'blur' },
          { max: 500, message: '最长500个字符', trigger: 'blur' }
        ],
        subpackageName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' },
          { max: 100, message: '最长100个字符', trigger: 'blur' }
        ],
        subpackageContent: [
          { required: false, message: '请输入', trigger: 'blur' },
          { max: 500, message: '最长500个字符', trigger: 'blur' }
        ],
        contractPrice: [
          { required: false, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ],
        heads: [
          { required: true, message: '请设置', trigger: 'change' }
        ]
      },
      cartIds: [],
      isShop: false,
      id: null
    }
  },
  computed: {
    isDisabled() {
      return !(this.checkPermi(['purchaser:project:add', 'purchaser:project:edit']) && this.createType !== 4 && this.createType !== 7)
    }
  },
  created() {
    this.init();
  },
  methods: {
    checkPermi,
    numToChinese,
    checkReg,
    eval2,
    async init() {
      this.reset();
      await this.getPurchaseMethod();
      if ([1, 2].includes(this.createType)) {
        this.buyItemCode = this.$route.params.buyItemCode
        await this.queryProjectInfo()
      } else if ([5].includes(this.createType)) {
        let cartIds = this.$route.query.cartIds || [];
        this.cartIds = cartIds.map(id => parseInt(id))
        await this.getCartInfo()
      } else if ([6, 7].includes(this.createType)) {
        this.id = this.$route.params.id
        await this.queryProjectInfo()
      }
    },
    changePurchaseMethod(val) {
      let obj = this.purchase_method.find(v => v.value === val).raw;
      this.form.purchaseMethodType = obj.purchaseMethodType;
    },
    async getCartInfo() {
      try {
        let { data } = await cartInfo(this.cartIds);
        let list = data || [];
        let goodsCategoryList = this._.uniq(list.map(v => v.goodsCategory));
        this.form.subpackageDtoList = [];
        goodsCategoryList.forEach((item, index) => {
          let arr = list.filter(v => v.goodsCategory === item);
          let heads = arr[0].headeList;
          let bodyMaps = [];
          arr.forEach(v => {
            bodyMaps = bodyMaps.concat(v.bodyMaps)
          })
          this.form.subpackageDtoList.push({
            subpackageCode: null,
            subpackageName: `${this.prefixInteger(index + 1, 2)}包`,
            contractPrice: null,
            subpackageContent: null,
            shopJson: item,
            claimsFileQuoteFormCreatorDto: {
              heads: heads,
              bodyMaps: bodyMaps
            }
          });
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    defaultRow(heads) {
      let obj = {};
      heads.forEach(item => {
        obj[item.keyVal] = ''
      })
      return obj
    },
    columnChange(heads, key) {
      let keys = heads.map(item => item.keyVal);
      if (heads.length === 0) {
        this.form.subpackageDtoList[key].claimsFileQuoteFormCreatorDto.bodyMaps = [];
      } else {
        this.form.subpackageDtoList[key].claimsFileQuoteFormCreatorDto.bodyMaps.forEach(item => {
          let itemKeys = Object.keys(item)
          itemKeys.forEach(val => {
            if (!keys.some(v => v === val)) {
              this.$delete(item, val);
            }
          })
          keys.forEach(val => {
            if (!itemKeys.some(v => v === val)) {
              this.$set(item, val, '')
            }
          })
        })
      }
    },
    handleSuccess({ data }, key, heads) {
      data.forEach(item => {
        for (let itemKey in item) {
          let obj = heads.find(v => v.keyVal === itemKey)
          if (obj.keyType === 'file') {
            item[itemKey] = '';
          }
        }
      })
      this.form.subpackageDtoList[key].claimsFileQuoteFormCreatorDto.bodyMaps = data;
    },
    addDetail(arr, heads) {
      arr.push(this.defaultRow(heads))
    },
    removeDetail(arr, index) {
      arr.splice(index, 1)
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            let params = this._.cloneDeep(this.form);
            params.subpackageDtoList.forEach(item => {
              let heads = item.claimsFileQuoteFormCreatorDto.heads.map(v => {
                let regex = Base64.toBase64(v.regex);
                return {
                  ...v,
                  regex
                }
              });
              item.claimsFileQuoteFormCreatorDto.heads = heads;
            })
            params.allocateId = this.id;
            if ([0, 1, 3, 5, 6].includes(this.createType)) {
              await createProjectInfo(params);
              await delCart(this.cartIds);
              this.$modal.msgSuccess('保存成功');
              await this.$router.push({ name: 'ProjectList' })
            } else {
              await modifyProjectInfo(params);
              this.$modal.msgSuccess('修改成功');
              await this.queryProjectInfo()
            }
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    addBidSection() {
      this.form.subpackageDtoList.push({
        subpackageCode: null,
        subpackageName: `${this.prefixInteger(this.form.subpackageDtoList.length + 1, 2)}包`,
        contractPrice: null,
        subpackageContent: null,
        shopJson: null,
        claimsFileQuoteFormCreatorDto: {
          heads: [],
          bodyMaps: []
        }
      });
    },
    removeBidSection(index) {
      this.form.subpackageDtoList.splice(index, 1)
    },
    async queryProjectInfo() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        if (this.createType === 3 || this.createType === 4) {
          console.log(this.createType)
        } else if (this.createType === 6 || this.createType === 7) {
          let { data } = await getProjectAssignDetail(this.id);
          data.subpackageDtoList = [
            {
              subpackageCode: null,
              subpackageName: '01包',
              contractPrice: null,
              subpackageContent: null,
              shopJson: null,
              claimsFileQuoteFormCreatorDto: {
                heads: [],
                bodyMaps: []
              }
            }
          ]
          for (let key in this.form) {
            this.form[key] = data[key];
          }
          this.form.orgCode = this.$cache.local.get('loginOrgCode');
        } else {
          let { data } = await queryProjectInfo(this.buyItemCode);
          let obj = this.purchase_method.find(v => v.raw.purchaseMethodCode === data.purchaseMethodCode);
          this.isShop = obj.purchaseMethodType === 'shoppingMall';
          this.form = data;
        }
        // 0创建 1复制 2编辑 3申购项目创建 4只查看
        if (this.createType === 1) {
          this.$set(this.form, 'buyItemCode', null);
          this.form.subpackageDtoList.forEach(v => {
            this.$set(v, 'subpackageCode', null);
          })
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 查询采购方式列表 */
    async getPurchaseMethod() {
      try {
        let { data } = await queryPurchaseMethod()
        let list = data.methodKVList || [];
        this.purchase_method = list.map(item => {
          return {
            label: item.purchaseMethodName,
            value: item.purchaseMethodCode,
            raw: item
          }
        });
        if (this.createType === 5) {
          let obj = list.find(v => v.purchaseMethodType === 'shoppingMall');
          this.form.purchaseMethodCode = obj ? obj.purchaseMethodCode : null;
        }
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 表单重置 */
    reset() {
      this.form = {
        orgCode: null,
        buyItemCode: null,
        buyItemName: null,
        purchaseMethodCode: null,
        purchaseMethodType: null,
        buyClass: null,
        buyBudget: null,
        useDept: null,
        buyPerson: null,
        concatNumber: null,
        organizeType: null,
        buyRemark: null,
        subpackageDtoList: [
          {
            subpackageCode: null,
            subpackageName: '01包',
            contractPrice: null,
            subpackageContent: null,
            shopJson: null,
            claimsFileQuoteFormCreatorDto: {
              heads: [],
              bodyMaps: []
            }
          }
        ]
      }
      // 没有组织选择框时，默认选择第一个组织
      this.form.orgCode = this.$cache.local.get('loginOrgCode');
      this.resetForm('form');
    },
    prefixInteger(num, n) {
      return (Array(n).join('0') + num).slice(-n);
    },
    checkFunction(functionList, value) {
      if (value && value instanceof Array && value.length > 0) {
        const processFunction = functionList || [];
        const functionFlag = value
        const hasFunction = processFunction.some(val => {
          return functionFlag.includes(val)
        })
        if (!hasFunction) {
          return false
        }
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.project-card {
  overflow: visible;
}
</style>
