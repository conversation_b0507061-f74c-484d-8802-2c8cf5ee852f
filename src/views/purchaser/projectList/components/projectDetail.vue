<template>
  <div>
    <project-detail-smart :createType="createType" v-if="versionType==='smart'"/>
    <project-detail-jxzl :createType="createType" v-if="versionType==='jxzl'"/>
    <project-detail-syth :createType="createType" v-if="versionType==='syth'"/>
    <project-detail-whpr :createType="createType" v-if="versionType==='whpr'"/>
    <project-detail-bjxk :createType="createType" v-if="versionType==='bjxk'"/>
    <project-detail-wzlg :createType="createType" v-if="versionType==='wzlg'"/>
    <project-detail-xyzy :createType="createType" v-if="versionType==='xyzy'"/>
    <project-detail-fjnx :createType="createType" v-if="versionType==='fjnx'"/>
    <project-detail-whws :createType="createType" v-if="versionType==='whws'"/>
  </div>
</template>

<script>
import projectDetailSmart from '@/views/purchaser/projectList/components/projectDetail_smart.vue'
import projectDetailJxzl from '@/views/purchaser/projectList/components/projectDetail_jxzl.vue'
import projectDetailSyth from '@/views/purchaser/projectList/components/projectDetail_syth.vue'
import projectDetailWhpr from '@/views/purchaser/projectList/components/projectDetail_whpr.vue'
import projectDetailBjxk from '@/views/purchaser/projectList/components/projectDetail_bjxk.vue'
import projectDetailWzlg from '@/views/purchaser/projectList/components/projectDetail_wzlg.vue'
import projectDetailXyzy from '@/views/purchaser/projectList/components/projectDetail_xyzy.vue'
import projectDetailFjnx from '@/views/purchaser/projectList/components/projectDetail_fjnx.vue'
import projectDetailWhws from '@/views/purchaser/projectList/components/projectDetail_whws.vue'

export default {
  name: 'ProjectDetail',
  components: {
    projectDetailSmart,
    projectDetailJxzl,
    projectDetailSyth,
    projectDetailWhpr,
    projectDetailBjxk,
    projectDetailWzlg,
    projectDetailXyzy,
    projectDetailFjnx,
    projectDetailWhws
  },
  props: {
    createType: {
      type: Number,
      default: 0, // 0创建 1复制 2编辑 3申购项目创建 4申购项目详情查看 5商城项目创建  6分派项目创建 7分派项目详情查看
      validator: function (value) {
        // 这个值必须匹配下列字符串中的一个
        return [0, 1, 2, 3, 4, 5, 6, 7].includes(value)
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
