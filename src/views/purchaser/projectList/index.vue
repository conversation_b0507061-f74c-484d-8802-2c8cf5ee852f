<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item
        :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
        prop="entity.purchaseMethodCode"
      >
        <el-select
          v-model="queryParams.entity.purchaseMethodCode"
          :placeholder="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in purchase_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName" label-width="100px">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" prop="entity.buyClass" v-if="versionType!=='wzlg'">
        <el-select
          v-model="queryParams.entity.buyClass"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyClass')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.project_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="采购编号" prop="entity.innerCode" v-if="versionType==='jxzl'&&versionType!=='whws'">
        <el-input
          v-model="queryParams.entity.innerCode"
          placeholder="请输入采购编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预算金额" prop="entity.buyBudget" v-if="versionType!=='wzlg'&&versionType!=='whws'">
        <el-input
          v-model="queryParams.entity.buyBudget"
          placeholder="预算金额"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="使用科室" prop="entity.useDept" v-if="versionType!=='jxzl'&&versionType!=='wzlg'&&versionType!=='whws'">
        <el-input
          v-model="queryParams.entity.useDept"
          placeholder="请输入使用科室"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目状态" prop="entity.end">
        <el-select
          v-model="queryParams.entity.end"
          placeholder="项目状态"
          clearable
          style="width: 240px"
        >
          <el-option label="全部" :value="null"/>
          <el-option label="未完成" :value="0"/>
          <el-option label="已完成" :value="1"/>
          <el-option label="已归档" :value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5" v-if="checkPermi(['purchaser:project:add'])">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleCreate"
        >创建
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="checkPermi(['project:report:export'])">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleGenerate"
        >生成项目报表
        </el-button>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >下载项目报表
        </el-button>
      </el-col>
    </el-row>

    <ul v-loading="loading">
      <li v-for="(item,index) in tableData" :key="index">
        <project-item
          :itemData="item"
          :index="index+1"
          :repurchaseLabel="selectDictValue(dict.type.sys_dict_translate,'repurchase')"
          :purchaseMethodName="purchaseMethodFormat(item.purchaseMethodCode)"
          @update="update"
        />
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-empty description="暂无数据" v-show="total===0"></el-empty>

    <el-dialog
      title="导出项目报表"
      center
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="expertForm" :rules="rules" ref="expertForm" size="small" label-width="100px">
        <el-form-item label="创建时间" prop="createAt">
          <el-date-picker
            v-model="expertForm.createAt"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00','23:59:59']"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目状态" prop="end">
          <el-select
            v-model="expertForm.end"
            placeholder="项目状态"
            clearable
          >
            <el-option label="全部" :value="null"/>
            <el-option label="未完成" :value="0"/>
            <el-option label="已完成" :value="1"/>
            <el-option label="已归档" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
          prop="purchaseMethodType"
        >
          <el-select
            v-model="expertForm.purchaseMethodType"
            :placeholder="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
            clearable
          >
            <el-option
              v-for="dict in purchase_method"
              :key="dict.value"
              :label="dict.label"
              :value="dict.raw.purchaseMethodType"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">关闭</el-button>
        <el-button type="primary" @click="submitGenerate">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  downloadProjectReport,
  generateProjectReport,
  queryProjectList,
  queryPurchaseMethod
} from '@/api/purchaser/projectList'
import { mapGetters } from 'vuex'
import { checkPermi } from '@/utils/permission';
import ProjectItem from '@/views/purchaser/projectList/projectItem.vue';

export default {
  name: 'ProjectList',
  components: { ProjectItem },
  dicts: ['project_category', 'sys_dict_translate'],
  data() {
    return {
      purchase_method: [],
      // 查询参数
      queryParams: {
        entity: {
          purchaseMethodCode: null,
          buyItemName: null,
          buyClass: null,
          buyBudget: null,
          useDept: null,
          orgCode: null,
          innerCode: null,
          end: 0
        },
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      loading: false,
      orgCode: null,
      isDialog: false,
      expertForm: {
        createAt: [],
        end: null
      },
      rules: {
        createAt: [{ required: false, message: '请选择', trigger: 'change' }]
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近六个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一年',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getPurchaseMethod()
    this.getList()
  },
  computed: {
    ...mapGetters([
      'theme',
      'device',
      'getPurchaseDict'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  methods: {
    checkPermi,
    purchaseMethodFormat(purchaseMethodCode) {
      return this.selectDictLabel(this.purchase_method, purchaseMethodCode);
    },
    submitGenerate() {
      this.$refs['expertForm'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await generateProjectReport(this.expertForm);
            this.$modal.msgSuccess('操作成功')
            this.isDialog = false;
            this.$modal.closeLoading()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e)
          }
        }
      })
    },
    handleGenerate() {
      this.resetForm('expertForm');
      this.isDialog = true;
    },
    async handleExport() {
      try {
        this.$modal.loading('数据下载中，请稍候...');
        let res = await downloadProjectReport();
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    update() {
      this.getList()
    },
    handleCreate() {
      this.$router.push({ name: 'CreateProject' })
    },
    async getPurchaseMethod() {
      try {
        let { data } = await queryPurchaseMethod()
        let list = data.methodKVList || [];
        this.purchase_method = list.map(item => {
          return {
            label: item.purchaseMethodName,
            value: item.purchaseMethodCode,
            raw: item
          }
        });
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        this.queryParams.entity.orgCode = this.orgCode;
        let { rows, total } = await queryProjectList(this.queryParams)
        this.tableData = rows || []
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
