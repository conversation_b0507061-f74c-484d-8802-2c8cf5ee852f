<template>
  <div class="app-container cart-container" v-loading="loading">

    <div class="chartTotal">全部商品 {{ total }}</div>
    <el-table ref="multipleTable" :data="tableData" @selection-change="handleSelectionChange" max-height="600">
      <el-table-column align="center" type="selection" width="50" :selectable="checkbox_select"></el-table-column>
      <el-table-column label="商品" align="left">
        <template slot="header">
          <div style="text-align: center;">商品</div>
        </template>
        <template v-slot:default="{row}">
          <div class="goods-info">
            <el-image
              class="goods-img"
              :src="row.mainImgUrl"
              fit="contain"
              :preview-src-list="[row.mainImgUrl]">
            </el-image>
            <div class="goods-item mr30">
              <p class="ellipsis-more-3 pointer" @click="toDetail(row.goodsId)">{{ row.goodsName }}</p>
              <el-tag type="info" size="mini" class="mt5" v-if="row.goodsStatus!==1">商品已失效</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName"/>
      <el-table-column label="商品分类" align="center" prop="goodsCategory" width="120px">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.goods_key_group" :value="row.goodsCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="商品单价(元)" align="center" prop="goodsUnitPrice" width="120px">
        <template v-slot:default="{row}">
          {{ row.goodsUnitPrice | formatMoney(2) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120px">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            class="btn-text-danger"
            icon="el-icon-delete"
            @click="removeCart(row.cartId)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="cart-bottom-bar" v-if="total!==0">
      <div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-button type="danger" plain size="mini" class="ml20" @click="removeCart()">删除选中的商品</el-button>
      </div>
      <div class="cart-bottom-r">
        <span class="fontSize12 mr20">已选择 <span class="text-danger fontSize14">{{ checkedCount }}</span> 件商品</span>
        <el-button type="primary" @click="toPurchase">去采购</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { fileDownByGoods } from '@/api/file';
import { cartList, delCart } from '@/api/purchaser/shoppingMall'

export default {
  name: 'ShopCart',
  dicts: ['goods_key_group'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      selectGoodsList: [],
      isIndeterminate: false,
      checkAll: false,
      checkedCount: 0
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleCheckAllChange(val) {
      this.$refs.multipleTable.toggleAllSelection();
      this.isIndeterminate = false;
    },
    toPurchase() {
      let cartIds = this.selectGoodsList.map(item => item.cartId);
      if (cartIds.length === 0) {
        this.$message.warning('请选择需要采购的商品');
        return;
      }
      this.$router.push({ name: 'ShopProjectCreate', query: { cartIds }})
    },
    toDetail(goodsId) {
      this.$router.push({ name: 'GoodsDetail', params: { goodsId }})
    },
    handleSelectionChange(val) {
      console.log('selectGoodsList', val)
      this.selectGoodsList = val;
      this.checkedCount = val.length;
      this.checkAll = this.checkedCount === this.tableData.length;
      this.isIndeterminate = this.checkedCount > 0 && this.checkedCount < this.tableData.length;
    },
    checkbox_select(row) {
      return row.goodsStatus === 1
    },
    removeCart(cartId) {
      const cartIds = cartId ? [cartId] : this.selectGoodsList.map(v => v.cartId);
      if (cartIds.length === 0) {
        this.$message.warning('请选择需要删除的商品');
        return
      }
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delCart(cartIds);
          this.$message.success('删除成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await cartList({ pageNum: 1, pageSize: 1000 });
        this.tableData = rows || [];
        this.total = total;
        let mainList = this.tableData.map(item => {
          let mainImg = item.goodsPicList.find(v => v.picType === 0);
          return this.getImgUrl(mainImg.picFile)
        })
        let urlList = await Promise.all(mainList);
        this.tableData.forEach((item, index) => {
          this.$set(item, 'mainImgUrl', urlList[index]);
        })

        this.tableData = this._.orderBy(this.tableData, ['goodsCategory', 'cartId'], ['asc', 'asc']);
        console.log(this.tableData)
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByGoods(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cart-container {
  //position: relative;
  //margin-bottom: 80px;
}

.chartTotal {
  font-size: 18px;
  font-weight: 700;
  color: $colorDanger;
  margin-bottom: 5px;
}

.goods-info {
  display: flex;
  align-items: center;

  .goods-img {
    width: 80px;
    height: 80px;
    vertical-align: middle;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .goods-item {
    line-height: 1.5;
    font-size: 14px;
  }
}

.cart-bottom-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  border: 1px solid $borderColor;
  padding: 15px 15px;
  //background: #FFFFFF;
  //position: fixed;
  //bottom: 0;
  //right: 0;
  //z-index: 99;
  //width: calc(100% - 200px);
  //box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);

  .cart-bottom-r {

  }
}
</style>
