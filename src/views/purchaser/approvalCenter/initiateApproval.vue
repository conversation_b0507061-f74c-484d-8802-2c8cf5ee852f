<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-position="top" style="max-width: 600px;">
      <el-form-item label="选择采购项目" prop="buyItemCode" :rules="rules.buyItemCode">
        <el-select
          @change="selectProject"
          class="block"
          v-model="form.buyItemCode"
          filterable
          remote
          clearable
          reserve-keyword
          placeholder="请输入关键词"
          default-first-option
          :remote-method="remoteMethod"
          :loading="isLoading">
          <el-option
            v-for="(item,index) in projectList"
            :key="index"
            :label="item.buyItemName"
            :value="item.buyItemCode">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="审批标题" prop="auditProcessDto.auditTitle" :rules="rules.auditTitle">
        <el-input v-model.trim="form.auditProcessDto.auditTitle" maxlength="125" placeholder="请输入审批标题"/>
      </el-form-item>

      <el-form-item label="审批摘要" prop="auditProcessDto.remark" :rules="rules.remark">
        <el-input
          type="textarea"
          :rows="3"
          resize="none"
          v-model.trim="form.auditProcessDto.remark"
          placeholder="请输入审批摘要"
          maxlength="250"
          show-word-limit
        />
      </el-form-item>

      <template v-for="(item,index) in form.dataFieldList">
        <el-form-item
          :label="item.title"
          :prop="'dataFieldList.'+index+'.content'"
          :rules="rules.content"
          :key="index"
        >
          <div style="display: flex;">
            <el-input v-model.trim="item.content" maxlength="125" placeholder="请输入"/>
            <el-button type="danger" icon="el-icon-delete" plain class="ml10" @click="removeOtherItem(index)"></el-button>
          </div>
        </el-form-item>
      </template>

      <div class="text-center pt10">
        <el-button
          plain
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="addItemSingle"
        >
          添加一项
        </el-button>
        <el-button
          plain
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="addItemMultiple"
        >选择多项
        </el-button>
      </div>

      <el-form-item label="附件" prop="attList">
        <file-upload
          :disabled="!yearMonthSplit"
          v-model="form.attList"
          :showTip="true"
          :fileSize="20"
          :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
          :params="{
            fileTypeName: 'approval_annex',
            buyItemCode: form.buyItemCode,
            yearMonthSplit: yearMonthSplit
          }"
          @preview="previewFile"
          @down="downFile"
        >
        </file-upload>
      </el-form-item>

      <el-form-item label="抄送人" prop="auditProcessDto.readOnlyUserInfoList" :rules="rules.readOnlyUserInfoList">
        <approval-c-c
          :orgCode="orgCode"
          :excludeRoleIds="excludeRoleIds"
          v-model="form.auditProcessDto.readOnlyUserInfoList"
        />
      </el-form-item>

      <el-form-item label="审批人" prop="auditProcessDto.auditUserInfoList" :rules="rules.auditUserInfoList">
        <approval-by
          :orgCode="orgCode"
          :excludeRoleIds="excludeRoleIds"
          v-model="form.auditProcessDto.auditUserInfoList"
          @change="changeAuditor"
        />
      </el-form-item>

      <el-form-item class="text-center">
        <el-button type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </el-form>

    <el-dialog
      title="选择属性"
      :visible.sync="isAttrDialog"
      top="2vh"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :inline="true" size="small">
        <el-form-item label="类别" prop="groupType">
          <el-select
            v-model="groupType"
            placeholder="类别"
            clearable
            style="width: 240px"
            @change="selectGroupType"
          >
            <el-option
              v-for="(dict,index) in allAttribute"
              :key="index"
              :label="dict.groupName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-table :data="attributeList" @selection-change="handleSelectionChange" border max-height="500">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="属性" align="center" prop="title"/>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isAttrDialog = false">取消</el-button>
        <el-button type="primary" @click="submitSelectForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import approvalBy from '@/views/purchaser/approvalCenter/components/approvalBy.vue'
import approvalCC from '@/views/purchaser/approvalCenter/components/approvalCC.vue'
import { initiateApproval } from '@/api/approval'
import { queryProjectList } from '@/api/purchaser/projectList'

export default {
  name: 'InitiateApproval',
  components: {
    approvalCC,
    approvalBy
  },
  data() {
    return {
      form: {},
      rules: {
        buyItemCode: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        content: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 250, message: '最长250个字符', trigger: 'blur' }
        ],
        auditTitle: [{ required: true, message: '请输入', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入', trigger: 'blur' }],
        readOnlyUserInfoList: [{ required: false, message: '请选择', trigger: 'change' }],
        auditUserInfoList: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      excludeRoleIds: [],
      orgCode: null,
      isLoading: false,
      projectList: [],
      yearMonthSplit: null,
      isAttrDialog: false,
      allAttribute: [],
      groupType: null,
      attributeList: [],
      selectList: []
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.reset();
    this.getPurchaserExcludeRoleIds();
  },
  methods: {
    addItemMultiple() {
      this.getConfigKey('sys.approval.attr').then(response => {
        this.allAttribute = JSON.parse(response.data);
        this.groupType = null;
        this.attributeList = [];
        this.selectList = [];
        this.isAttrDialog = true;
      })
    },
    selectGroupType(val) {
      let obj = this.allAttribute.find(v => v.id === val)
      this.attributeList = obj ? obj.children : [];
      this.selectList = [];
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectList = selection.map(item => {
        return {
          title: item.title,
          content: null
        }
      })
    },
    submitSelectForm() {
      this.form.dataFieldList = this.form.dataFieldList.concat(this.selectList);
      this.isAttrDialog = false;
    },
    removeOtherItem(index) {
      this.form.dataFieldList.splice(index, 1)
    },
    addItemSingle() {
      this.$prompt('请输入标题', '添加', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入标题',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 32
        },
        inputErrorMessage: '请输入标题，最多32个字符'
      }).then(async ({ value }) => {
        this.form.dataFieldList.push({
          title: value,
          content: null
        })
      }).catch(() => {
      })
    },
    selectProject(val) {
      let obj = this.projectList.find(v => v.buyItemCode === val);
      this.yearMonthSplit = obj ? this.formatTime(obj.createAt, 'YYYYMM') : null;
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.isLoading = true;
        try {
          let { rows } = await queryProjectList({
            entity: {
              orgCode: this.orgCode,
              buyItemName: query,
              purchaseMethodCode: null,
              buyClass: null,
              buyBudget: null,
              useDept: null
            },
            pageNum: 1,
            pageSize: 1000
          })
          console.log(rows)
          this.projectList = rows || [];
          this.isLoading = false;
        } catch (e) {
          this.isLoading = false;
          throw new Error(e);
        }
      } else {
        this.projectList = [];
      }
    },
    async getPurchaserExcludeRoleIds() {
      try {
        let { data } = await this.getConfigKey('purchaser.exclude.roleIds');
        this.excludeRoleIds = data.split(',');
      } catch (e) {
        throw new Error(e);
      }
    },
    changeAuditor() {
      this.$refs['form'].validateField('auditProcessDto.auditUserInfoList');
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await initiateApproval(this.form);
            this.$modal.msgSuccess('提交成功');
            this.$modal.closeLoading();
            await this.$router.push({ name: 'ApprovalCenter' });
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    // 表单重置
    async reset() {
      this.form = {
        buyItemCode: null,
        attList: [],
        dataFieldList: [],
        auditProcessDto: {
          auditTitle: null,
          remark: null,
          readOnlyUserInfoList: [],
          auditUserInfoList: []
        }
      };
      this.resetForm('form');
    }
  }
}
</script>

<style scoped>

</style>
