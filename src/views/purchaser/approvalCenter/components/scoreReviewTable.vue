<template>
  <div>
    <el-table
      :data="tableData"
      border
      :span-method="objectSpanMethod"
    >
      <el-table-column
        label="评分模块"
        prop="reviewModule"
        align="center"
        width="90"
      />
      <el-table-column
        label="序号"
        type="index"
        align="center"
        width="55"
      />
      <el-table-column
        align="center"
        label="审查因素"
        prop="reviewItem"
        width="180px"
      />
      <el-table-column
        align="center"
        label="审查因素描述"
        class-name="text-left"
        prop="reviewCriteria"
      />
      <el-table-column
        align="center"
        label="分值"
        prop="reviewScore"
        width="90px"
      />
      <el-table-column
        align="center"
        label="主观分/客观分"
        prop="isSubjective"
        width="180px"
      >
        <template slot-scope="scope">
          <el-radio-group
            v-model="scope.row.isSubjective"
            :disabled="true"
          >
            <el-radio :label="1">主观分</el-radio>
            <el-radio :label="0">客观分</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

export default {
  name: 'ScoreReviewTable',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: [
        {
          label: '符合性评审',
          value: 1
        },
        {
          label: '技术',
          value: 2
        },
        {
          label: '商务',
          value: 3
        },
        {
          label: '价格',
          value: 4
        }
      ],
      tableData: [],
      spanArr: [],
      pos: 0
    }
  },
  watch: {
    list: {
      handler(val) {
        console.log(val)
        let arr = val || [];
        arr.forEach(item => {
          let obj = this.config.find(v => v.value === item.reviewType);
          this.$set(item, 'reviewModule', obj ? obj.label : null);
        })
        this.tableData = arr;
        this.getSpanArr(this.tableData);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].reviewType === data[i - 1].reviewType) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
