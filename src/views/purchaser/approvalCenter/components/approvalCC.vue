<template>
  <div>
    <el-tag
      class="mr10 mb5"
      v-for="(tag,index) in ccList"
      :key="index"
      :closable="!disabled"
      :disable-transitions="false"
      @close="handleRemoveCC(index)">
      {{ tag.nickName }}
    </el-tag>
    <select-users
      v-if="!disabled"
      class="mb5"
      :value="ccList.map(v=>{ return {userId: v.userId,nickName: v.nickName} })"
      :excludeRoleIds="excludeRoleIds"
      :orgCode="orgCode"
      @columnChange="columnChangeCC"
    />
  </div>
</template>

<script>
import selectUsers from '@/views/purchaser/approvalCenter/components/selectUsers.vue';

export default {
  name: 'ApprovalCC',
  components: {
    selectUsers
  },
  props: {
    value: {},
    orgCode: {
      type: String,
      default: null
    },
    excludeRoleIds: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      ccList: this.value || []
    }
  },
  created() {

  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(value) {
        this.setCurrentValue(value)
      }
    }
  },
  methods: {
    setCurrentValue(value) {
      this.ccList = value || [];
      this.$emit('input', value);
    },
    columnChangeCC(data) {
      let list = data.map(v => {
        return {
          nickName: v.nickName,
          userId: v.userId
        }
      })
      this.setCurrentValue(list)
      this.$emit('change', list);
    },
    handleRemoveCC(index) {
      this.ccList.splice(index, 1);
      this.$emit('input', this.ccList);
      this.$emit('change', this.ccList);
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
