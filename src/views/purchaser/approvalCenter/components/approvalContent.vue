<template>
  <el-card header="审批信息" shadow="never" :body-style="{'padding': '15px 10px 10px 10px'}" class="card-box">
    <div slot="header" class="clearfix">
      <span>审批信息</span>
      <el-button class="fr" type="primary" size="mini" plain icon="el-icon-download" @click="handleExport">下载审批单
      </el-button>
    </div>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="审批编号">{{ detail.auditCode }}</el-descriptions-item>
      <el-descriptions-item label="审批标题">{{ detail.auditTitle }}</el-descriptions-item>
      <el-descriptions-item label="审批摘要">{{ detail.remark }}</el-descriptions-item>
      <el-descriptions-item label="审批类型">
        <dict-tag :options="dict.type.approval_type" :value="detail.auditType"/>
      </el-descriptions-item>
      <template v-for="(item,index) in contentList">
        <el-descriptions-item :label="item.name" :key="index">
          <div v-if="item.type==='File'">
            <el-button type="text" size="mini" @click="handlePreview(item.value)">预览</el-button>
            <el-button type="text" size="mini" @click="handleDown(item.value)">下载</el-button>
          </div>
          <span v-else-if="item.type==='Date'">{{ item.value | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          <div v-else-if="item.type==='json'&&item.key==='attList'">
            <file-list-view
              :fileList="JSON.parse(item.value)"
              :handlePreview="previewFile"
              :handleDown="downFile"
            />
          </div>
          <div v-else-if="item.type==='json'&&item.key==='conformityReview'">
            <conformity-review-table :list="JSON.parse(item.value)"/>
          </div>
          <div v-else-if="item.type==='json'&&item.key==='scoreReview'">
            <score-review-table :list="JSON.parse(item.value)"/>
          </div>
          <span v-else>{{ item.value }}</span>
        </el-descriptions-item>
      </template>
      <el-descriptions-item label="抄送人">{{ ccUsers }}</el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>

<script>

import { exportApprovalForm } from '@/api/approval'
import conformityReviewTable from '@/views/purchaser/approvalCenter/components/conformityReviewTable.vue'
import scoreReviewTable from '@/views/purchaser/approvalCenter/components/scoreReviewTable.vue'

export default {
  name: 'ApprovalContent',
  dicts: ['approval_type'],
  components: {
    conformityReviewTable,
    scoreReviewTable
  },
  props: {
    detail: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    contentList() {
      return this.detail.contentList || [];
    },
    ccUsers() {
      let list = this.detail.auditRecipientList || [];
      return list.map(v => v.copyName).join('、');
    }
  },
  created() {
  },
  methods: {
    previewFile(data) {
      this.$download.previewFile(data)
    },
    downFile(data) {
      this.$download.downloadFile(data)
    },
    async handleExport() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await exportApprovalForm(this.detail.auditCode);
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    handlePreview(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
