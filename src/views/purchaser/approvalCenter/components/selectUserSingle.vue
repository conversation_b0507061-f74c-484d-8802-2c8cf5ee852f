<template>
  <div class="app-container">

    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="归属部门" prop="deptId">
        <treeselect
          v-model.trim="queryParams.deptId"
          :options="deptOptions"
          :show-count="true"
          placeholder="请选择归属部门"
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList" border highlight-current-row @current-change="handleCurrentChange">
      <el-table-column type="index" width="50" align="center"/>
      <el-table-column
        label="用户昵称"
        align="center"
        key="nickName"
        prop="nickName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="部门"
        align="center"
        key="deptName"
        prop="dept.deptName"
        :show-overflow-tooltip="true"
      />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listUser } from '@/api/system/user'
import { treeselect } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'User',
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  props: {
    excludeRoleIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 部门树选项
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '0',
        nickName: undefined,
        deptId: undefined,
        orgCode: undefined
      },
      orgCode: null,
      currentRow: null
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getTreeselect()
    this.getList()
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.excludeRoleIds = this.excludeRoleIds;
      this.queryParams.orgCode = this.orgCode;
      console.log(this.queryParams)
      listUser(this.queryParams).then(response => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.deptOptions = response.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.deptId = undefined;
      this.handleQuery()
    },
    handleCurrentChange(val) {
      console.log(val)
      this.currentRow = val;
    }
  }
}
</script>
