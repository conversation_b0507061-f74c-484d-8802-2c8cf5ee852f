<template>
  <div class="set-header-wrap">
    <el-button
      plain
      size="mini"
      icon="el-icon-plus"
      @click="showColumn()"
    >
      {{ btnText }}
    </el-button>
    <el-dialog
      :title="title"
      width="90%"
      custom-class="maxW600"
      :visible.sync="open"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      modal-append-to-body
      append-to-body>
      <el-transfer
        :titles="['未选择', '已选择']"
        v-model="selectValue"
        :data="columns"
        :props="{
           key: 'userId',
           label: 'nickName'
        }"
        @change="dataChange"
        filterable
        :filter-method="filterMethod"
        filter-placeholder="请输入名称"
      ></el-transfer>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="open=false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurchaserUser } from '@/api/system/user';

export default {
  name: 'SetHeader',
  props: {
    value: {},
    excludeRoleIds: {
      type: Array,
      default: () => []
    },
    orgCode: {
      type: String
    },
    btnText: {
      type: String,
      default: '选择抄送人'
    },
    title: {
      type: String,
      default: '选择抄送人'
    }
  },
  data() {
    return {
      columns: [],
      currentValue: this.value,
      // 显隐数据
      selectValue: [],
      // 是否显示弹出层
      open: false,
      filterMethod(query, item) {
        return item.nickName.indexOf(query) > -1;
      }
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    setCurrentValue(value) {
      this.currentValue = value;
      this.selectValue = value.map(item => item.userId);
      this.$emit('input', value)
    },
    // 右侧列表元素变化
    dataChange(data) {
      let list = this.columns.filter(item => data.includes(item.userId))
      console.log(list)
      this.setCurrentValue(list)
      this.$emit('columnChange', list)
    },
    // 打开显隐列dialog
    async showColumn() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await listPurchaserUser({
          // orgCode: this.orgCode
        })
        let list = data || [];
        this.columns = list.map(item => {
          return {
            userId: item.userId,
            nickName: item.nickName,
            disabled: false
          }
        })
        this.$modal.closeLoading();
        this.open = true;
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.set-header-wrap {
  display: inline-block;
}

::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}

::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}

.el-button + .set-header-wrap {
  margin-left: 10px;
}

.set-header-wrap + .el-button {
  margin-left: 10px;
}
</style>
