<template>
  <div>
    <div>
      <sp-steps>
        <sp-step
          v-for="(item,index) in auditorList"
          :key="index"
          :nodeTitle="item.nickName"
          title="审批人"
        >
          <p class="step-text">{{ item.nickName }}</p>
          <p class="step-text">要求：{{ item.requirement }}</p>
          <div class="mt5" v-if="!disabled">
            <el-button
              size="mini"
              type="primary"
              plain
              icon="el-icon-edit"
              @click="handleEditStep(item,index)">
              修改
            </el-button>
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-delete"
              @click="handleRemoveStep(index)">
              删除
            </el-button>
          </div>
        </sp-step>
      </sp-steps>
      <el-button
        size="mini"
        plain
        icon="el-icon-plus"
        @click="handleAddStep"
        v-if="!disabled"
      >
        添加审批人
      </el-button>
    </div>

    <el-dialog
      title="选择审批人"
      :visible.sync="isDialog"
      custom-class="maxW600"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="approvalForm"
        :model="approvalForm"
        :rules="rules"
        label-width="80px"
        @submit.native.prevent
      >
        <el-form-item label="姓名" prop="selectUserId">
          <el-select
            class="block"
            v-model="approvalForm.selectUserId"
            clearable
            filterable
            remote
            reserve-keyword
            placeholder="请输入名字"
            :remote-method="remoteMethod"
            :loading="loading">
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName+(item.dept?' - '+item.dept.deptName: '')"
              :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="要求" prop="requirement">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="approvalForm.requirement"
            placeholder="请输入"
            maxlength="500"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="medium"
          @click="isDialog = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="medium"
          @click="submitAddUser"
          :disabled="!isDialog"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import spSteps from '@/views/purchaser/approvalCenter/components/spSteps.vue';
import spStep from '@/views/purchaser/approvalCenter/components/spStep.vue';
import { listPurchaserUser } from '@/api/system/user';

export default {
  name: 'ApprovalBy',
  components: {
    spSteps,
    spStep
  },
  props: {
    value: {},
    orgCode: {
      type: String,
      default: null
    },
    excludeRoleIds: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      auditorList: this.value || [],
      rules: {
        selectUserId: [{ required: true, message: '请选择', trigger: 'change' }],
        requirement: [{ required: false, message: '请输入', trigger: 'blur' }]
      },
      isDialog: false,
      loading: false,
      approvalForm: {},
      userList: [],
      isEdit: false,
      editIndex: null
    }
  },
  created() {
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(value) {
        this.setCurrentValue(value)
      }
    }
  },
  methods: {
    setCurrentValue(value) {
      this.auditorList = value || [];
      this.$emit('input', value);
    },
    submitAddUser() {
      this.$refs['approvalForm'].validate(valid => {
        if (valid) {
          this.isDialog = false;
          if (!this.isEdit) {
            this.auditorList.push({
              nickName: this.userList.find(item => item.userId === this.approvalForm.selectUserId).nickName,
              userId: this.approvalForm.selectUserId,
              requirement: this.approvalForm.requirement
            });
          } else {
            this.auditorList[this.editIndex] = {
              nickName: this.userList.find(item => item.userId === this.approvalForm.selectUserId).nickName,
              userId: this.approvalForm.selectUserId,
              requirement: this.approvalForm.requirement
            }
          }
          this.$emit('input', this.auditorList);
          this.$emit('change', this.auditorList);
        }
      })
    },
    async handleEditStep(item, index) {
      this.resetApprovalForm();
      this.userList = [];
      await this.remoteMethod(item.nickName);
      this.approvalForm.selectUserId = item.userId;
      this.approvalForm.requirement = item.requirement;
      this.editIndex = index;
      this.isEdit = true;
      this.isDialog = true;
    },
    handleRemoveStep(index) {
      this.auditorList.splice(index, 1);
      this.$emit('input', this.auditorList);
      this.$emit('change', this.auditorList);
    },
    handleAddStep() {
      this.resetApprovalForm();
      this.userList = [];
      this.isEdit = false;
      this.isDialog = true;
    },
    resetApprovalForm() {
      this.approvalForm = {
        selectUserId: null,
        requirement: null
      }
      this.resetForm('approvalForm')
    },
    async remoteMethod(query) {
      if (!query) {
        this.userList = [];
        return
      }
      this.loading = true;
      try {
        let { data } = await listPurchaserUser({
          // orgCode: this.orgCode,
          nickName: query
        })
        this.loading = false;
        this.userList = data || [];
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.step-text {
  font-size: 14px;
  color: #999999;
}
</style>
