<template>
  <el-card header="审批流程" shadow="never" :body-style="{'padding': '15px 10px 15px 10px'}" class="card-box">
    <svg-icon icon-class="refuse" class-name="status-icon" v-if="processStatus===0"/>
    <svg-icon icon-class="passed" class-name="status-icon" v-if="processStatus===1"/>
    <svg-icon icon-class="revoked" class-name="status-icon" v-if="processStatus===3"/>
    <sp-steps>
      <sp-step :nodeTitle="createBy" title="发起审批" :showSuccess="true">
        <p class="step-text">
          <span>{{ createBy }}</span>
          <span class="ml10 step-time" v-if="createAt">{{ createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </p>
      </sp-step>
      <sp-step
        v-for="(item,index) in stepList"
        :key="index"
        :nodeTitle="item.userName"
        title="审批人"
        :showSuccess="item.status===1"
        :showError="item.status===0"
        :showWarning="item.isCurrent"
      >
        <p class="step-text">
          <span>{{ item.userName }}</span>
          <span v-if="item.status===1" class="text-success">（已通过）</span>
          <span v-if="item.status===0" class="text-danger">（已拒绝）</span>
        </p>
        <p class="step-text text-danger" v-if="item.requirement">要求：{{ item.requirement }}</p>

        <div class="mt10 mb10" v-if="item.userId===userId&&item.isCurrent&&processStatus===2&&checkPermi(['purchaser:approval:audit'])">
          <el-button
            size="mini"
            type="danger"
            @click="handleAudit(0,item)"
          >
            拒绝
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click="handleAudit(1,item)"
          >
            通过
          </el-button>
        </div>

        <div class="mb10" v-if="item.remark">
          <div class="step-text space-between">
            <span>审批意见：</span>
            <span class="step-time" v-if="item.createAt">{{ item.createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
          </div>
          <p class="comment-content">{{ item.remark }}</p>
        </div>

        <div class="step-text">
          <span
            v-if="item.attList.length>0||(item.attList.length===0&&item.userId===userId&&(item.isCurrent||item.status!==2)&&processStatus!==3)">
            附件：
          </span>
          <file-upload
            class="mt10"
            :showUploadBtn="item.userId===userId&&(item.isCurrent||item.status!==2)&&processStatus!==3&&checkPermi(['purchaser:approval:upload'])"
            :disabled="!(item.userId===userId&&(item.isCurrent||item.status!==2)&&processStatus!==3)"
            v-model="item.attList"
            :fileSize="20"
            :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
            :showTip="true"
            btnText="上传文件"
            :params="{
              fileTypeName: 'approval_annex',
              buyItemCode: buyItemCode,
              subpackageCode: subpackageCode,
              yearMonthSplit: yearMonthSplit,
              auditPersonId: item.id,
            }"
            @change="uploadFile"
            @preview="previewFile"
            @down="downFile"
          >
          </file-upload>
        </div>

        <ul class="comment-main overflow" v-if="item.commentVoList.length>0">
          <li v-for="(v,key) in item.commentVoList" :key="key" class="comment-item">
            <div class="comment-title space-between">
              <div>
                <span class="text-primary">{{ v.createBy }}</span>
                <span class="ml5">添加了评论：</span>
              </div>
              <span class="step-time">{{ v.createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
            </div>
            <p class="comment-content">{{ v.comment }}</p>
          </li>
        </ul>
        <div class="mt10" v-if="(item.status===0||item.status===1)&&checkPermi(['purchaser:approval:comment'])">
          <el-button size="mini" type="primary" @click="handleComment(item)">评论</el-button>
        </div>
      </sp-step>
    </sp-steps>
  </el-card>
</template>

<script>
import spSteps from '@/views/purchaser/approvalCenter/components/spSteps.vue';
import spStep from '@/views/purchaser/approvalCenter/components/spStep.vue';
import { mapGetters } from 'vuex';
import { auditApproval, commentApproval, saveFileApproval } from '@/api/approval';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'ApprovalStep',
  dicts: ['msg_template_approval_yes', 'msg_template_approval_no'],
  components: {
    spSteps,
    spStep
  },
  props: {
    createBy: {
      type: String,
      default: null
    },
    createAt: {
      type: String,
      default: null
    },
    buyItemCode: {
      type: String,
      default: null
    },
    subpackageCode: {
      type: String,
      default: null
    },
    yearMonthSplit: {
      type: String,
      default: null
    },
    processStatus: {
      type: Number,
      default: null //  // 0 拒绝 1 通过 2 审批中 3 撤销
    },
    auditorList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    stepList() {
      let list = this._.cloneDeep(this.auditorList)
      list.forEach((item, index, arr) => {
        // status 0-不同意，1-同意 2待审批
        this.$set(item, 'attList', item.attList || []);
        if (index === 0 && item.status === 2) {
          this.$set(item, 'isCurrent', true);
        } else if (index !== 0 && item.status === 2 && arr[index - 1].status === 1) {
          this.$set(item, 'isCurrent', true);
        } else {
          this.$set(item, 'isCurrent', false);
        }
      })
      console.log(list);
      return list;
    }
  },
  methods: {
    checkPermi,
    async uploadFile(keys, { fileList, params }) {
      try {
        this.$modal.loading('数据提交中，请稍候...')
        await saveFileApproval({
          attList: fileList,
          auditPersonId: params.auditPersonId
        })
        this.$modal.msgSuccess('上传成功');
        this.$emit('change');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handleComment(item) {
      this.$msgNotify({
        title: '评论',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        handleConfirm: (val) => {
          this.$modal.loading('数据提交中，请稍候...')
          commentApproval({ auditInfoId: item.auditInfoId, auditRecordId: item.auditRecordsId, comment: val })
            .then(() => {
              this.$emit('change');
              this.$modal.msgSuccess('提交成功');
              this.$modal.closeLoading();
            })
            .catch(() => {
              this.$modal.closeLoading();
            })
        }
      })
    },
    handleAudit(status, item) {
      this.$msgNotify({
        title: status === 0 ? '审批拒绝意见' : '审批通过意见',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        msgTemplate: status === 0 ? this.dict.type.msg_template_approval_no.map(item => item.value) : this.dict.type.msg_template_approval_yes.map(item => item.value),
        handleConfirm: (val) => {
          this.$modal.loading('数据提交中，请稍候...')
          auditApproval({ auditInfoId: item.auditInfoId, auditPersonId: item.id, remark: val, status })
            .then(() => {
              this.$store.dispatch('setApprovalCount');
              this.$emit('change');
              this.$modal.msgSuccess('提交成功');
              this.$modal.closeLoading();
            })
            .catch(() => {
              this.$modal.closeLoading();
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.space-between {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.step-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 5px;
}

.comment-main {
  margin-top: 20px;
  font-size: 13px;
}

.comment-item {
  margin-bottom: 5px;
}

.comment-title {
  margin-bottom: 5px;
}

.step-time {
  font-size: 13px;
  color: #999999;
}

.comment-content {
  font-size: 13px;
  background: #f2f2f2;
  padding: 5px 10px;
  line-height: 25px;
}

.card-box {
  position: relative;
  overflow: initial;
}

.status-icon {
  position: absolute;
  top: 0;
  right: 50%;
  transform: translate(-50%, 0);
  font-size: 150px;
  opacity: 0.8;
  z-index: 99;
}
</style>
