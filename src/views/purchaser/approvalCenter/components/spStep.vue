<template>
  <div class="sp-step">
    <div class="sp-step-node">
      <div class="sp-step-node-title">
        <slot name="nodeTitle">{{ nodeTitle }}</slot>
      </div>

      <div class="sp-step-line"></div>
      <i class="el-icon-success" v-if="showSuccess"></i>
      <i class="el-icon-error" v-if="showError"></i>
      <i class="el-icon-warning" v-if="showWarning"></i>
    </div>

    <div class="sp-step-main">
      <div class="sp-step-main-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="sp-step-main-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpStep',
  props: {
    title: {
      type: String
    },
    nodeTitle: {
      type: String
    },
    showSuccess: {
      type: Boolean,
      default: false
    },
    showError: {
      type: Boolean,
      default: false
    },
    showWarning: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
$baseColor: #1890ff;
.sp-step {
  padding-bottom: 25px;
  display: flex;

  .sp-step-node {
    position: relative;
  }

  .sp-step-node-title {
    width: 50px;
    height: 50px;
    background: $baseColor;
    border-radius: 5px;
    color: #ffffff;
    font-size: 13px;
    word-wrap: break-word;
    word-break: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    line-height: normal;
  }

  .sp-step-line {
    position: absolute;
    left: 23px;
    height: 100%;
    border-left: 3px solid #cccccc;
  }

  .el-icon-success {
    position: absolute;
    top: -5px;
    right: -5px;
    z-index: 10;
    font-size: 16px;
    color: $colorSuccess;
    background: #ffffff;
    border-radius: 50%;
  }

  .el-icon-error {
    position: absolute;
    top: -5px;
    right: -5px;
    z-index: 10;
    font-size: 16px;
    color: $colorDanger;
    background: #ffffff;
    border-radius: 50%;
  }

  .el-icon-warning {
    position: absolute;
    top: -5px;
    right: -5px;
    z-index: 10;
    font-size: 16px;
    color: $colorWarning;
    background: #ffffff;
    border-radius: 50%;
  }

  .sp-step-main {
    padding-left: 20px;
  }

  .sp-step-main-title {
    font-size: 14px;
    line-height: normal;
  }

  .sp-step-main-content {
    margin-top: 5px;
    line-height: normal;
  }

  &:last-child {
    .sp-step-line {
      border-left: none;
    }
  }
}
</style>
