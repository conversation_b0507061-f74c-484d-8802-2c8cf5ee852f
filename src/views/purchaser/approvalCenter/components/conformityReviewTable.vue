<template>
  <div>
    <el-table
      :data="list"
      border
    >
      <el-table-column
        label="序号"
        type="index"
        align="center"
        width="55"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="审查因素"
        prop="reviewItem"
        width="250"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="审查因素描述"
        class-name="text-left"
        prop="reviewCriteria"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="主观分/客观分"
        prop="isSubjective"
        width="180px"
      >
        <template slot-scope="scope">
          <el-radio-group
            v-model="scope.row.isSubjective"
            :disabled="true"
          >
            <el-radio :label="1">主观分</el-radio>
            <el-radio :label="0">客观分</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

export default {
  name: 'ConformityReviewTable',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
