<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="已发起" name="first">
        <already-approval v-if="activeName==='first'"></already-approval>
      </el-tab-pane>
      <el-tab-pane label="我的审批" name="second">
        <pending-approval v-if="activeName==='second'"></pending-approval>
      </el-tab-pane>
      <el-tab-pane label="抄送给我" name="three">
        <copy-approval v-if="activeName==='three'"></copy-approval>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import alreadyApproval from '@/views/purchaser/approvalCenter/alreadyApproval.vue';
import pendingApproval from '@/views/purchaser/approvalCenter/pendingApproval.vue';
import copyApproval from '@/views/purchaser/approvalCenter/copyApproval.vue';

export default {
  name: 'Index',
  components: {
    alreadyApproval,
    pendingApproval,
    copyApproval
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  created() {
  },
  methods: {}
}
</script>

<style scoped>

</style>
