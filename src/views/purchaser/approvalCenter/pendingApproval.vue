<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="审批标题" prop="entity.auditTitle">
        <el-input
          v-model.trim="queryParams.entity.auditTitle"
          placeholder="请输入审批标题"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批类型" prop="entity.auditType">
        <el-select
          v-model="queryParams.entity.auditType"
          placeholder="审批类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.approval_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起人" prop="entity.createBy">
        <el-input
          v-model.trim="queryParams.entity.createBy"
          placeholder="发起人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发起日期" prop="entity.createAtRange">
        <el-date-picker
          v-model="queryParams.entity.createAtRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00','23:59:59']"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="流程状态" prop="entity.status">
        <el-select
          v-model="queryParams.entity.status"
          placeholder="流程状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.approval_process_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border :row-class-name="tableRowClassName">
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="审批标题" align="center" prop="auditTitle"/>
      <el-table-column label="审批摘要" align="center" prop="remark" :show-overflow-tooltip="true"/>
      <el-table-column label="审批编号" align="center" prop="auditCode"/>
      <el-table-column label="审批类型" align="center" prop="auditCode">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.approval_type" :value="row.auditType"/>
        </template>
      </el-table-column>
      <el-table-column label="发起人" align="center" prop="createBy"/>
      <el-table-column label="发起时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="当前审批人" align="center" prop="auditName">
        <template v-slot:default="{row}">
         {{ row.auditName || '/'}}
        </template>
      </el-table-column>
      <el-table-column label="流程状态" align="center" prop="status">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.approval_process_status" :value="row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>

import { getPendingApproval } from '@/api/approval';
import { mapGetters } from 'vuex'

export default {
  name: 'PendingApproval',
  dicts: ['approval_process_status', 'approval_type'],
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        entity: {
          auditTitle: null,
          auditType: null,
          createBy: null,
          status: null,
          createAtRange: []
        },
        pageNum: 1,
        pageSize: 10
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三天',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      console.log(row.auditId === this.userId)
      if (row.auditId === this.userId) {
        return 'warning-row';
      }
      return '';
    },
    handleDetail(row) {
      this.$router.push({ name: 'PendingApprovalDetail', params: { id: row.id }})
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.queryParams.entity.createAtRange = [];
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await getPendingApproval(this.queryParams);
        this.tableData = rows;
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss">
.el-table .warning-row {
  background: oldlace;
}
</style>
