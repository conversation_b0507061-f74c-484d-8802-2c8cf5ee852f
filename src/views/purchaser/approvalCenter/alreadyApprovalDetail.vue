<template>
  <div class="app-container">
    <approval-content :detail="approvalDetail"/>

    <approval-step
      :processStatus="approvalDetail.status"
      :createBy="approvalDetail.createBy"
      :createAt="approvalDetail.createAt"
      :buyItemCode="approvalDetail.buyItemCode"
      :subpackageCode="approvalDetail.subpackageCode"
      :yearMonthSplit="approvalDetail.yearMonthSplit"
      :auditorList="auditorList"
      @change="getDetail"
    />

  </div>
</template>

<script>
import approvalStep from '@/views/purchaser/approvalCenter/components/approvalStep.vue';
import approvalContent from '@/views/purchaser/approvalCenter/components/approvalContent.vue';
import { getApprovalDetail } from '@/api/approval';

export default {
  name: 'AlreadyApprovalDetail',
  components: {
    approvalContent,
    approvalStep
  },
  data() {
    return {
      id: null,
      approvalDetail: {},
      auditorList: []
    }
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    async getDetail() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getApprovalDetail(this.id);
        this.approvalDetail = data.auditInfoVo || {};
        this.auditorList = data.auditPersonList || [];
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
