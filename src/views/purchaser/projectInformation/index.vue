<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName" label-width="100px">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="归口管理" prop="entity.managementDept">
        <el-input
          v-model="queryParams.entity.managementDept"
          placeholder="请输入归口管理"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" align="center" prop="buyItemName"/>
      <el-table-column label="归口管理" align="center" prop="managementDept"/>
      <el-table-column label="使用科室" align="center" prop="useDept"/>
      <el-table-column label="使用科室负责人" align="center" prop="useDeptPerson"/>
      <el-table-column label="推送时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="项目资料" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="downloadFileByOther(row.projectData)"
          >下载
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="分派详情" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row.id)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template v-slot:default="{row}">
          <el-button
            v-if="checkPermi(['project:information:assign'])"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAssign(row)"
          >分派
          </el-button>
          <el-button
            v-if="checkPermi(['project:information:remove'])"
            size="mini"
            type="text"
            icon="el-icon-delete"
            class="btn-text-danger"
            @click="handleRemove(row.id)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="项目分派"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW500"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="form" :rules="rules" ref="form" size="small" label-width="80px">
        <el-form-item label="采购人" prop="allocateId">
          <el-select
            v-model="form.allocateId"
            class="block"
            clearable
            filterable
            placeholder="请选择采购人"
            @change="changePurchaser"
          >
            <el-option
              v-for="dict in purchaserList"
              :key="dict.userId"
              :label="dict.nickName"
              :value="dict.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="附件" prop="attachmentDtoList">
          <file-upload
            v-model="form.attachmentDtoList"
            :showTip="true"
            :fileSize="20"
            uploadUrl="/epcfile/nonProjectFile/upload"
            :params="{
              fileTypeName: 'project_assign_annex'
            }"
            @preview="previewFileByOther"
            @down="downloadFileByOther"
          >
          </file-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.remark"
            placeholder="请输入备注"
            maxlength="250"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="text-center">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="分派详情"
      :visible.sync="isOpen"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table :data="assignList" border v-loading="isLoading">
        <el-table-column label="序号" align="center" type="index" width="50"/>
        <el-table-column label="采购人" align="center" prop="allocateName"/>
        <el-table-column label="附件" align="center">
          <template v-slot:default="{row}">
            <file-list-view
              :fileList="row.attachmentDtoList"
              :handlePreview="previewFileByOther"
              :handleDown="downloadFileByOther"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark">
          <template v-slot:default="{row}">
            {{ row.remark || '/' }}
          </template>
        </el-table-column>
        <el-table-column label="分派人" align="center" prop="createBy"/>
        <el-table-column label="分派时间" align="center" prop="createAt">
          <template v-slot:default="{row}">
            {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="采购状态" align="center" width="90">
          <template v-slot:default="{row}">
            <el-tag :type="row.allocateWhetherCreate == 1 ? 'success' : 'info'">
              {{ row.allocateWhetherCreate === 1 ? '已采购' : '未采购' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template v-slot:default="{row}">
            <el-button
              v-if="checkPermi(['project:assign:remove'])"
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="btn-text-danger"
              @click="handleRemoveAssign(row.id)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import {
  getProjectAssignInfo,
  delProjectInformation,
  getProjectInformationList,
  delProjectAssign,
  addProjectAssign
} from '@/api/purchaser/projectInformation';
import { isString } from '@/utils/validate'
import { listUser } from '@/api/system/user'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null,
          managementDept: null
        }
      },
      total: 0,
      tableData: [],
      loading: false,
      isDialog: false,
      form: {},
      rules: {
        allocateId: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        attachmentDtoList: [{ required: true, message: '请上传', trigger: 'blur' }],
        remark: [{ required: false, message: '请输入', trigger: 'blur' }]
      },
      externalProjectDataId: null,
      isOpen: false,
      assignList: [],
      isLoading: false,
      purchaserList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    handleRemoveAssign(id) {
      let ids = [id];
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delProjectAssign(ids);
          this.$modal.msgSuccess('删除成功');
          await this.getAssignList();
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    handleDetail(id) {
      this.externalProjectDataId = id;
      this.isOpen = true;
      this.getAssignList();
    },
    async getAssignList() {
      try {
        this.isLoading = true;
        let { data } = await getProjectAssignInfo(this.externalProjectDataId);
        this.assignList = data || [];
        this.isLoading = false;
      } catch (e) {
        this.isLoading = false;
        throw e
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await addProjectAssign(this.form);
            this.$modal.msgSuccess('提交成功');
            this.isDialog = false;
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    changePurchaser(val) {
      let obj = this.purchaserList.find(v => v.userId === val);
      this.form.allocateName = obj ? obj.nickName : null;
    },
    handleAssign(row) {
      this.reset();
      this.form.externalProjectDataId = row.id;
      this.getPurchaserList();
      this.isDialog = true;
    },
    async getPurchaserList() {
      try {
        let res = await this.getConfigKey('sys.purchaser.roleId');
        let roleId = Number(res.data);
        let orgCode = this.$cache.local.get('loginOrgCode');
        let { rows } = await listUser({ roleId, orgCode, status: '0' })
        let list = rows || [];
        this.purchaserList = list.map(item => {
          return {
            nickName: item.nickName,
            userId: item.userId
          }
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    reset() {
      this.form = {
        externalProjectDataId: null,
        allocateId: null,
        allocateName: null,
        attachmentDtoList: [],
        remark: null
      };
      this.resetForm('form');
    },
    handleRemove(id) {
      let ids = [id];
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delProjectInformation(ids)
          this.$modal.msgSuccess('删除成功');
          await this.getList();
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    previewFileByOther(data) {
      if (isString(data)) {
        this.$download.previewFileByOther(data)
      } else {
        this.$pdfViewDialog({ data: data, type: 'blob' })
      }
    },
    downloadFileByOther(data) {
      if (isString(data)) {
        this.$download.downloadFileByOther(data)
      } else {
        this.$download.saveAs(data, data.name)
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await getProjectInformationList(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
