<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="项目名称" prop="entity.buyItemName">
        <el-input
          v-model="queryParams.entity.buyItemName"
          placeholder="请输入项目名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商名称" prop="entity.supplierCompanyName">
        <el-input
          v-model="queryParams.entity.supplierCompanyName"
          placeholder="请输入供应商名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00','23:59:59']"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10" v-if="checkPermi(['meeting:project:operate'])">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          icon="el-icon-edit"
          :disabled="multiple"
          @click="handleUpBargain()"
        >修改议价时间
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      :span-method="objectSpanMethod"
      @selection-change="handleSelectionChange"
      max-height="650"
    >
      <el-table-column type="selection" width="50" align="center" fixed/>
      <el-table-column label="序号" align="center" prop="index" width="55"/>
      <el-table-column label="项目名称" align="center" prop="buyItemName"/>
      <el-table-column label="标段(包)" align="center" prop="subpackageName"/>
      <el-table-column label="报价截止时间" align="center" prop="countdown">
        <template v-slot:default="{row}">
          {{ row.countdown | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
        </template>
      </el-table-column>
      <el-table-column label="议价备注" align="center" prop="serviceRequire">
        <template v-slot:default="{row}">
          {{ row.serviceRequire || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')"
        align="center"
        prop="infoReporterName"
      />
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
        align="center"
        prop="infoReporterContactNumber"
      />
      <el-table-column label="报价表" align="center" width="70">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePrice(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="报价表"
      :visible.sync="isPriceDialog"
      custom-class="maxW1200"
      width="90%"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="min-height: 300px">
        <p class="mb10">{{ supplierCompanyName }}</p>
        <u-table
          :data="bodyMaps"
          :height="500"
          use-virtual
          :row-height="55"
          border>
          <u-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <u-table-column
            v-for="(item,k) in heads"
            :key="k"
            align="center"
            :label="item.keyName"
            :show-overflow-tooltip="true"
          >
            <template v-slot:default="{row}">
              <span v-if="item.keyType!=='file'">{{ row[item.keyVal] }}</span>
              <div v-else>
                <el-button
                  v-if="row[item.keyVal]"
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="previewFile(row[item.keyVal])"
                >查看
                </el-button>
                <span v-else>/</span>
              </div>
            </template>
          </u-table-column>
        </u-table>
      </div>
    </el-dialog>

    <el-dialog
      title="修改谈判时间"
      :visible.sync="isOpen"
      custom-class="maxW500"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="form" :rules="rules" ref="form" size="small" label-width="170px">
        <el-form-item label="报价截止时间（分钟）" prop="countdown">
          <el-input v-model.number.trim="form.countdown" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注 " prop="serviceRequire">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.serviceRequire"
            placeholder="请输入"
            maxlength="250"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isOpen = false">关闭</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { queryMeetingProject, upBargain } from '@/api/purchaser/meetingProject';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'MeetingProject',
  dicts: ['sys_dict_translate'],
  data() {
    let checkAge = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'));
        return
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'));
        return
      }
      callback();
    };
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null,
          supplierCompanyName: null,
          createStartTime: null,
          createEndTime: null
        }
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三天',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24 - 1);
              const start = new Date(new Date(new Date().toLocaleDateString()).getTime() + 3600 * 1000 * 24);
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      loading: false,
      total: 0,
      tableData: [],
      spanArr: [],
      pos: 0,
      isPriceDialog: false,
      supplierCompanyName: null,
      heads: [],
      bodyMaps: [],
      isOpen: false,
      form: {},
      rules: {
        countdown: [{ required: true, validator: checkAge, trigger: 'blur' }]
      },
      selectList: [],
      multiple: true
    }
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleUpBargain(row) {
      this.reset();
      console.log(this.form)
      if (row) {
        this.form.subpackageList = [row.subpackageCode];
      } else {
        this.form.subpackageList = this.selectList;
      }
      this.isOpen = true;
    },
    handleSelectionChange(selection) {
      this.selectList = this._.uniq(selection.map(item => item.subpackageCode));
      this.multiple = !selection.length
    },
    onSubmit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            let params = this.form.subpackageList.map(v => {
              return {
                countdown: this.form.countdown,
                serviceRequire: this.form.serviceRequire,
                subpackageCode: v
              }
            })
            await upBargain(params);
            this.$modal.msgSuccess('操作成功');
            this.isOpen = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    },
    reset() {
      this.form = {
        countdown: null,
        serviceRequire: null,
        subpackageList: []
      }
      this.resetForm('form');
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handlePrice(row) {
      this.supplierCompanyName = row.supplierCompanyName;
      this.heads = row.heads;
      this.bodyMaps = row.bodyMaps || [];
      this.isPriceDialog = true;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        this.queryParams.entity.createStartTime = this.dateRange && this.dateRange.length === 2 ? this.dateRange[0] : null;
        this.queryParams.entity.createEndTime = this.dateRange && this.dateRange.length === 2 ? this.dateRange[1] : null;
        let { rows, total } = await queryMeetingProject(this.queryParams);
        this.total = total;
        let list = rows || [];
        this.tableData = []
        list.forEach((item, index) => {
          let supplierInfoVoList = item.supplierInfoVoList || [];
          if (supplierInfoVoList.length > 0) {
            supplierInfoVoList.forEach(v => {
              this.tableData.push({
                index: index + 1,
                ...item,
                ...v
              })
            })
          } else {
            this.tableData.push({
              index: index + 1,
              ...item
            })
          }
        })
        console.log(this.tableData)
        this.getSpanArr(this.tableData);
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [6, 7, 8, 9];
      if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].subpackageCode === data[i - 1].subpackageCode) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
