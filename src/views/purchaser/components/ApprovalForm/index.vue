<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      :icon="icon"
      :size="size"
      :plain="plain"
      :disabled="disabled"
      :type="type"
      @click="handleShow()"
    >{{ btnText }}
    </el-button>

    <el-dialog
      top="2vh"
      title="发起审批"
      :visible.sync="isDialog"
      width="90%"
      :custom-class="showEditor?'maxW1200':'maxW600'"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18" v-if="showEditor">
            <el-form-item label="" prop="contentHtml">
              <template-select
                v-if="isDialog&&initEditor"
                :height="800"
                :max-height="1000"
                :tmp-type="form.auditType"
                v-model="form.contentHtml"
                :getTemplateFunc="getTemplateFunc"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="showEditor?7:24" :md="showEditor?6:24" :lg="showEditor?6:24" :xl="showEditor?6:24">
            <el-form-item label="审批类型" prop="auditType" v-if="showCustomize">
              <el-select
                v-model="form.auditType"
                placeholder="请选择审批类型"
                @change="changeAuditType"
                class="block"
                clearable
              >
                <el-option
                  v-for="dict in dict.type.approval_type.filter(item=>item.raw.remark!=='nonUniversal')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="是否需要文本编辑器" prop="hasEditor" v-if="showCustomize">
               <span slot="label">
                <el-tooltip content="文本编辑器中内容最终会生成 pdf 格式文件" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                是否需要文本编辑器
              </span>
              <el-switch
                v-model="form.hasEditor"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="changeHasEditor"
              >
              </el-switch>
            </el-form-item>

            <el-form-item label="审批标题" prop="auditProcessDto.auditTitle" :rules="rules.auditTitle">
              <el-input v-model.trim="form.auditProcessDto.auditTitle" maxlength="125" placeholder="请输入审批标题"/>
            </el-form-item>

            <el-form-item label="审批摘要" prop="auditProcessDto.remark" :rules="rules.remark">
              <el-input
                type="textarea"
                :rows="3"
                resize="none"
                v-model.trim="form.auditProcessDto.remark"
                placeholder="请输入审批摘要"
                maxlength="250"
                show-word-limit
              />
            </el-form-item>

            <template v-for="(item,index) in form.dataFieldList">
              <el-form-item
                :label="item.title"
                :prop="'dataFieldList.'+index+'.content'"
                :rules="rules.content"
                :key="index"
              >
                <div style="display: flex;">
                  <el-input v-model.trim="item.content" maxlength="125" placeholder="请输入"/>
                  <el-button type="danger" icon="el-icon-delete" plain class="ml10" @click="removeOtherItem(index)"></el-button>
                </div>
              </el-form-item>
            </template>

            <div class="text-center pt10" v-if="showCustomize">
              <el-button
                plain
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addItemSingle"
              >
                添加一项
              </el-button>
              <el-button
                plain
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addItemMultiple"
              >选择多项
              </el-button>
            </div>

            <el-form-item label="附件" prop="attList" v-if="showAnnex">
              <file-upload
                v-model="form.attList"
                :showTip="true"
                :fileSize="20"
                :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
                :params="{
                  fileTypeName: 'approval_annex',
                  buyItemCode: params.buyItemCode,
                  subpackageCode: params.subpackageCode,
                  yearMonthSplit: params.yearMonthSplit
                }"
                @preview="previewFile"
                @down="downFile"
              >
              </file-upload>
            </el-form-item>

            <el-form-item label="审批模板" prop="approvalTemplate" v-if="approvalTemplateList.length > 1">
              <el-select
                v-model="form.approvalTemplateId"
                placeholder="审批模板"
                clearable
                style="width: 240px"
                @change="changeApprovalTemplate"
              >
                <el-option
                  v-for="(dict,index) in approvalTemplateList"
                  :key="index"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="抄送人" prop="auditProcessDto.readOnlyUserInfoList" :rules="rules.readOnlyUserInfoList">
              <approval-c-c
                :orgCode="orgCode"
                :excludeRoleIds="excludeRoleIds"
                v-model="form.auditProcessDto.readOnlyUserInfoList"
              />
            </el-form-item>

            <el-form-item label="审批人" prop="auditProcessDto.auditUserInfoList" :rules="rules.auditUserInfoList">
              <approval-by
                :orgCode="orgCode"
                :excludeRoleIds="excludeRoleIds"
                v-model="form.auditProcessDto.auditUserInfoList"
                @change="changeAuditor"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm">提交审批</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="选择属性"
      :visible.sync="isAttrDialog"
      top="2vh"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :inline="true" size="small">
        <el-form-item label="类别" prop="groupType">
          <el-select
            v-model="groupType"
            placeholder="类别"
            clearable
            style="width: 240px"
            @change="selectGroupType"
          >
            <el-option
              v-for="(dict,index) in allAttribute"
              :key="index"
              :label="dict.groupName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-table :data="attributeList" @selection-change="handleSelectionChange" border max-height="500">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="属性" align="center" prop="title"/>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isAttrDialog = false">取消</el-button>
        <el-button type="primary" @click="submitSelectForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import approvalBy from '@/views/purchaser/approvalCenter/components/approvalBy.vue'
import approvalCC from '@/views/purchaser/approvalCenter/components/approvalCC.vue'
import { getApprovalTemplateByKey } from '@/api/system/approvalTemplate'
import { verifyEditor } from '@/utils/validate'
import { fillTemplate } from '@/api/purchaser/bulletin'

export default {
  dicts: ['approval_type'],
  name: 'ApprovalForm',
  props: {
    auditTitle: {
      type: String,
      default: ''
    },
    btnText: {
      type: String,
      default: '提交审批'
    },
    size: {
      type: String,
      default: 'medium'
    },
    icon: {
      type: String,
      default: null
    },
    plain: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'primary'
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    orgCode: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showAnnex: {
      type: Boolean,
      default: false
    },
    showCustomize: {
      type: Boolean,
      default: false
    },
    submitFunc: {
      type: Function,
      required: true
    },
    verifyFunc: {
      type: Function,
      required: true
    },
    bindKey: {
      type: String,
      default: null
    },
    bindDeptId: {
      type: [String, Number],
      default: null
    },
    filterStr: {
      type: String,
      default: null
    }
  },
  components: {
    approvalCC,
    approvalBy
  },
  data() {
    return {
      excludeRoleIds: [],
      approvalTemplateList: [],
      isDialog: false,
      form: {},
      rules: {
        auditTitle: [{ required: true, message: '请输入', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入', trigger: 'blur' }],
        auditType: [{ required: true, message: '请选择', trigger: 'change' }],
        readOnlyUserInfoList: [{ required: false, message: '请选择', trigger: 'change' }],
        auditUserInfoList: [{ required: true, message: '请选择', trigger: 'change' }],
        content: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 250, message: '最长250个字符', trigger: 'blur' }
        ],
        contentHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ]
      },
      isAttrDialog: false,
      allAttribute: [],
      groupType: null,
      attributeList: [],
      selectList: [],
      initEditor: true
    }
  },
  created() {
    this.reset();
  },
  computed: {
    showEditor() {
      return this.showCustomize && this.form.hasEditor
    }
  },
  methods: {
    getTemplateFunc(data) {
      return fillTemplate({
        buyItemCode: this.params.buyItemCode,
        purchaseMethodCode: this.params.purchaseMethodCode,
        tmpKey: data.tmpKey,
        tmpType: data.tmpType
      });
    },
    changeHasEditor(val) {
      console.log(val)
      this.form.contentHtml = '';
    },
    changeAuditType(val) {
      this.form.contentHtml = '';
      this.form.auditType = val;
      this.getApprovalTemplate()
      this.initEditor = false;
      this.$nextTick(() => {
        this.initEditor = true;
      })
    },
    addItemMultiple() {
      this.getConfigKey('sys.approval.attr').then(response => {
        this.allAttribute = JSON.parse(response.data);
        this.groupType = null;
        this.attributeList = [];
        this.selectList = [];
        this.isAttrDialog = true;
      })
    },
    selectGroupType(val) {
      let obj = this.allAttribute.find(v => v.id === val)
      this.attributeList = obj ? obj.children : [];
      this.selectList = [];
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectList = selection.map(item => {
        return {
          title: item.title,
          content: null
        }
      })
    },
    submitSelectForm() {
      this.form.dataFieldList = this.form.dataFieldList.concat(this.selectList);
      this.isAttrDialog = false;
    },
    removeOtherItem(index) {
      this.form.dataFieldList.splice(index, 1)
    },
    addItemSingle() {
      this.$prompt('请输入标题', '添加', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入标题',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 32
        },
        inputErrorMessage: '请输入标题，最多32个字符'
      }).then(async ({ value }) => {
        this.form.dataFieldList.push({
          title: value,
          content: null
        })
      }).catch(() => {
      })
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    async handleShow() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let res = await this.verifyFunc(this.params);
        console.log('verifyFunc', res);
        if (res) {
          await this.getPurchaserExcludeRoleIds();
          this.reset();
          this.form.auditProcessDto.auditTitle = this.auditTitle;
          this.form.auditType = this.bindKey;
          if (this.form.auditType) {
            await this.getApprovalTemplate()
          }
          this.isDialog = true;
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e)
      }
    },
    async getPurchaserExcludeRoleIds() {
      try {
        let { data } = await this.getConfigKey('purchaser.exclude.roleIds');
        this.excludeRoleIds = data.split(',');
      } catch (e) {
        throw new Error(e);
      }
    },
    changeAuditor() {
      this.$refs['form'].validateField('auditProcessDto.auditUserInfoList');
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await this.submitFunc({
              ...this.params,
              ...this.form
            });
            this.$modal.msgSuccess('提交成功');
            this.isDialog = false;
            this.$modal.closeLoading();
            this.$emit('success');
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    // 表单重置
    reset() {
      this.form = {
        hasEditor: true,
        approvalTemplateId: null,
        attList: [],
        dataFieldList: [],
        auditProcessDto: {
          auditTitle: null,
          remark: null,
          readOnlyUserInfoList: [],
          auditUserInfoList: []
        }
      };
      this.resetForm('form');
    },
    changeApprovalTemplate(val) {
      let obj = this.approvalTemplateList.find(item => item.id === val);
      this.setApprovalTemplate(obj ? obj.readUserInfoVoList : [], obj ? obj.auditUserInfoVoList : []);
    },
    setApprovalTemplate(readUsers = [], auditUsers = []) {
      this.form.auditProcessDto.readOnlyUserInfoList = readUsers.map(v => {
        return {
          nickName: v.nickName,
          userId: v.userId
        }
      })
      this.form.auditProcessDto.auditUserInfoList = auditUsers || [];
    },
    async getApprovalTemplate() {
      try {
        let { data } = await getApprovalTemplateByKey({ key: this.form.auditType, bindDeptId: this.bindDeptId });
        let list = data || [];
        console.log('this.filterStr', this.filterStr);
        if (this.filterStr) {
          this.approvalTemplateList = list.filter(v => v.name.includes(this.filterStr));
        } else {
          this.approvalTemplateList = list;
        }
        if (this.approvalTemplateList.length > 0) {
          let obj = this.approvalTemplateList[0];
          this.form.approvalTemplateId = obj.id;
          this.setApprovalTemplate(obj.readUserInfoVoList, obj.auditUserInfoVoList);
        } else {
          this.setApprovalTemplate([], []);
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
