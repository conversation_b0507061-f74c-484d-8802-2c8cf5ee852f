<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      :icon="icon"
      :size="size"
      :plain="plain"
      :disabled="disabled"
      :type="type"
      @click="handleShow()"
    >{{ btnText }}
    </el-button>

  </div>
</template>

<script>

export default {
  name: 'PublishSubmit',
  props: {
    btnText: {
      type: String,
      default: '发布'
    },
    size: {
      type: String,
      default: 'medium'
    },
    icon: {
      type: String,
      default: null
    },
    plain: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'primary'
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    submitFunc: {
      type: Function,
      required: true
    },
    verifyFunc: {
      type: Function,
      required: true
    }
  },
  data() {
    return {}
  },
  methods: {
    async handleShow() {
      let res = await this.verifyFunc(this.params);
      if (!res) {
        return
      }
      this.$confirm('确认发布？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await this.submitFunc({
            ...this.params
          });
          this.$modal.msgSuccess('提交成功');
          this.$modal.closeLoading();
          this.$emit('success');
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped>

</style>
