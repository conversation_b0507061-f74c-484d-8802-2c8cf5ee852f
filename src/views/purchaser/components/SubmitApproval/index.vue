<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      :icon="icon"
      :size="size"
      :plain="plain"
      :disabled="disabled"
      :type="type"
      @click="handleShow()"
    >{{ btnText }}
    </el-button>

    <el-dialog
      top="2vh"
      title="发起审批"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-form-item label="标题" prop="auditProcessDto.auditTitle" :rules="rules.auditTitle">
          <el-input v-model.trim="form.auditProcessDto.auditTitle" maxlength="125" placeholder="请输入标题"/>
        </el-form-item>

        <el-form-item label="内容说明" prop="auditProcessDto.content" :rules="rules.content">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.auditProcessDto.content"
            placeholder="请输入内容说明"
            maxlength="250"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="备注" prop="auditProcessDto.remark" :rules="rules.remark">
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.auditProcessDto.remark"
            placeholder="请输入备注"
            maxlength="250"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm">提交审批</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'SubmitApproval',
  props: {
    auditTitle: {
      type: String,
      default: ''
    },
    btnText: {
      type: String,
      default: '提交审批'
    },
    size: {
      type: String,
      default: 'medium'
    },
    icon: {
      type: String,
      default: null
    },
    plain: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'primary'
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    submitFunc: {
      type: Function,
      required: true
    },
    verifyFunc: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      isDialog: false,
      form: {},
      rules: {
        auditTitle: [{ required: true, message: '请输入', trigger: 'blur' }],
        content: [{ required: true, message: '请输入', trigger: 'blur' }],
        remark: [{ required: false, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.reset();
  },
  methods: {
    async handleShow() {
      try {
        let res = await this.verifyFunc(this.params);
        console.log('verifyFunc', res);
        if (res) {
          await this.reset();
          this.form.auditProcessDto.auditTitle = this.auditTitle
          this.isDialog = true;
        }
      } catch (e) {
        throw new Error(e)
      }
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await this.submitFunc({
              ...this.params,
              ...this.form
            });
            this.$modal.msgSuccess('提交成功');
            this.isDialog = false;
            this.$modal.closeLoading();
            this.$emit('success');
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        } else {
          return false
        }
      });
    },
    // 表单重置
    async reset() {
      this.form = {
        auditProcessDto: {
          auditTitle: null,
          content: null,
          remark: null
        }
      };
      this.resetForm('form');
    }
  }
}
</script>

<style scoped>

</style>
