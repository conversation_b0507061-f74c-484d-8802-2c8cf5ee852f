<template>
  <div class="app-container" :style="{'--theme': theme}">
    <div style="max-width: 1000px">
      <file-upload-select
        v-model="fileList"
        :fileSize="50"
        :fileType="['pdf']"
        :preview="previewFile"
        @change="uploadFile"
        @remove="removeFile"
      >
      </file-upload-select>
      <el-card header="肢解项⽬检查" shadow="never" class="mt20 result-card" v-if="fileList.length>=2">
        <div slot="header" class="result-card-header">
          <span class="cart-title">肢解项⽬检查结果</span>
          <el-button type="primary" size="mini" @click="handleReview">开始检查</el-button>
        </div>
        <div class="result-box">
          <el-descriptions direction="vertical" :column="1" border>
            <el-descriptions-item v-for="(item,index) in records" :key="index" :label="item.projectName">
              <el-alert
                v-if="item.trueFlag===0"
                :title="item.purchaser+'，'+item.procurementContent+'，'+item.procurementType+'，'+item.procurementMethod+'，'+item.procurementAmount+'万元，'+item.procurementTime"
                type="success"
                :closable="false"
                show-icon
              >
              </el-alert>
              <el-alert
                v-if="item.trueFlag===1"
                :title="item.purchaser+'，'+item.procurementContent+'，'+item.procurementType+'，'+item.procurementMethod+'，'+item.procurementAmount+'万元，'+item.procurementTime"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
              <el-alert
                v-if="item.trueFlag===2"
                title="解析失败"
                type="info"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
          </el-descriptions>
          <div class="result-icon-box" v-if="records.length>0">
            <el-result icon="error" sub-title="疑似肢解项目" v-if="trueFlag===1"></el-result>
            <el-result icon="success" sub-title="检查正常" v-else></el-result>
          </div>
          <el-descriptions direction="vertical" :column="1" border labelClassName="text-center" v-if="trueFlag===1">
            <el-descriptions-item label="同一采购人">
              <el-alert
                :title="result.purchaser"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
            <el-descriptions-item label="相同采购内容">
              <el-alert
                :title="result.procurementContent"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
            <el-descriptions-item label="项目采购类型">
              <el-alert
                :title="result.procurementType"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
            <el-descriptions-item label="累计金额">
              <el-alert
                :title="result.procurementAmount+'万元'"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
            <el-descriptions-item label="采购时间">
              <el-alert
                :title="result.procurementTime"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
            <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')">
              <el-alert
                :title="result.procurementMethod"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { aiReviewZJXM } from '@/api/aiReview'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      fileList: [],
      result: {},
      records: [],
      trueFlag: null
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  created() {
    this.reset();
  },
  methods: {
    reset() {
      this.result = {
        purchaser: null,
        procurementContent: null,
        procurementType: null,
        procurementAmount: null,
        procurementTime: null,
        procurementMethod: null
      }
    },
    async handleReview() {
      try {
        this.$modal.loading('正在检查文件，请稍候...')
        let { data } = await aiReviewZJXM({ file: this.fileList.map(item => item.file) });
        let { records, trueFlag } = data;
        this.records = records || [];
        this.trueFlag = trueFlag;
        this.reset();
        for (const key in this.result) {
          this.result[key] = data[key];
        }
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    previewFile(data) {
      this.$pdfViewDialog({ data: data, type: 'blob' })
    },
    removeFile(data) {
      console.log(data);
      this.records = [];
      this.trueFlag = null;
      this.reset();
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
    }
  }
}
</script>

<style lang="scss" scoped>
.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .cart-title {
    font-weight: bold;
    font-size: 14px;
  }
}

.result-box {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .result-icon-box {
    width: 150px;
    text-align: center;
    flex-shrink: 0;
  }
}
</style>
<style lang="scss">
.result-card {
  .el-card__header {
    padding: 5px 15px;
  }
}
</style>
