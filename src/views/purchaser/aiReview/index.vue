<template>
  <div class="app-container" :style="{'--theme': theme}">
    <h1 class="ai-title text-center">AI审查 - 采购（招标）文件</h1>
    <ul class="ai-ul">
      <li>
        <div class="ai-li-item" @click="handleReview(1)">
          <svg-icon icon-class="bid" class-name="ai-icon"/>
          <p class="ai-name">应招未招检查</p>
        </div>
      </li>
      <li>
        <div class="ai-li-item" @click="handleReview(2)">
          <svg-icon icon-class="skill" class-name="ai-icon"/>
          <p class="ai-name">资格条件检查</p>
        </div>
      </li>
      <li>
        <div class="ai-li-item" @click="handleReview(3)">
          <svg-icon icon-class="transaction" class-name="ai-icon"/>
          <p class="ai-name">评分办法检查</p>
        </div>
      </li>
      <li>
        <div class="ai-li-item" @click="handleReview(4)">
          <svg-icon icon-class="tree-table" class-name="ai-icon"/>
          <p class="ai-name">肢解项⽬检查</p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>

import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  created() {
  },
  methods: {
    handleReview(type) {
      if (type === 4) {
        this.$router.push({ name: 'AiReviewDetailMultiple', params: { type }})
      } else {
        this.$router.push({ name: 'AiReviewDetailSingle', params: { type }})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-title {
  margin-top: 40px;
  margin-bottom: 80px;
  font-size: 30px;
}

.ai-ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1000px;
  margin: 0 auto;

  li {
    width: calc(100% / 2);
    padding: 40px;
    text-align: center;

    .ai-li-item {
      display: inline-block;
      cursor: pointer;
      transition: all 0.6s;

      &:hover {
        transform: scale(1.2, 1.2);
        position: relative;
        z-index: 9;
      }
    }

    .ai-icon {
      font-size: 70px;
      color: #{'var(--theme)'};
    }

    .ai-name {
      line-height: 1.5;
      font-size: 22px;
      margin-top: 20px;
      text-align: center;
    }
  }
}
</style>
