<template>
  <div class="app-container" :style="{'--theme': theme}">
    <el-row :gutter="10">
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <file-upload-select
          v-model="fileList"
          :limit="1"
          :fileSize="50"
          :fileType="['pdf']"
          @change="uploadFile"
          @remove="removeFile"
        >
        </file-upload-select>
        <div class="mt20 mb5 text-right" v-if="pdfData">
          <el-button type="primary" size="mini" @click="handleReviewYZWZ">应招未招检查</el-button>
          <el-button type="primary" size="mini" @click="handleReviewZGTJ">资格条件检查</el-button>
          <el-button type="primary" size="mini" @click="handleReviewPFBF">评分办法检查</el-button>
        </div>
        <pdf-view :src="pdfData" type="blob" height="700px"/>
      </el-col>
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <el-card
          header="应招未招检查结果"
          shadow="never"
          class="mb10 result-card"
          v-if="showResult1"
          :body-style="{'max-height':'600px','overflow':'auto'}"
        >
          <div slot="header" class="result-card-header">
            <span class="cart-title">应招未招检查结果</span>
            <el-button
              plain
              type="danger"
              size="mini"
              icon="el-icon-close"
              circle
              @click="handleRemoveResult(1)"
            >
            </el-button>
          </div>
          <el-descriptions direction="vertical" :column="1" border labelClassName="text-center" contentClassName="text-center">
            <el-descriptions-item label="采购类型">{{ result1.procurementType || '暂无数据' }}</el-descriptions-item>
            <el-descriptions-item label="采购金额">
              {{ result1.procurementAmount ? result1.procurementAmount + '万元' : '暂无数据' }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
            >
              {{ result1.procurementMethod || '暂无数据' }}
            </el-descriptions-item>
            <el-descriptions-item label="应招未招" v-if="result1.trueFlag===0">
              <el-alert
                :title="result1.procurementTypeBasic"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
          </el-descriptions>
          <el-alert
            class="mt10"
            v-if="result1.trueFlag===1"
            center
            title="检查正常"
            type="success"
            :closable="false"
            show-icon
          >
          </el-alert>
        </el-card>

        <el-card
          header="资格条件检查结果"
          shadow="never"
          class="mb10 result-card"
          v-if="showResult2"
          :body-style="{'max-height':'600px','overflow':'auto'}"
        >
          <div slot="header" class="result-card-header">
            <span class="cart-title">资格条件检查结果</span>
            <el-button
              plain
              type="danger"
              size="mini"
              icon="el-icon-close"
              circle
              @click="handleRemoveResult(2)"
            >
            </el-button>
          </div>
          <el-descriptions direction="vertical" :column="1" border>
            <el-descriptions-item v-for="(item,index) in result2" :key="index" :label="item.content">
              <el-alert
                v-if="!item.trueFlag"
                :title="item.basic"
                type="error"
                :closable="false"
                show-icon
              >
              </el-alert>
              <el-alert
                v-else
                title="检查正常"
                type="success"
                :closable="false"
                show-icon
              >
              </el-alert>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card
          header="评分办法检查结果"
          shadow="never"
          class="mb10 result-card"
          v-if="showResult3"
          :body-style="{'max-height':'600px','overflow':'auto'}"
        >
          <div slot="header" class="result-card-header">
            <span class="cart-title">评分办法检查结果</span>
            <el-button
              plain
              type="danger"
              size="mini"
              icon="el-icon-close"
              circle
              @click="handleRemoveResult(3)"
            >
            </el-button>
          </div>
          <div class="result-html" v-html="getResultHtmlPFBF(result3)"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { aiReviewPFBF, aiReviewYZWZ, aiReviewZGTJ } from '@/api/aiReview'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      fileList: [],
      pdfData: null,
      result1: null,
      result2: null,
      result3: null,
      showResult1: false,
      showResult2: false,
      showResult3: false
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  created() {
  },
  methods: {
    handleRemoveResult(type) {
      this['showResult' + type] = false;
      this['result' + type] = null;
    },
    async handleReviewYZWZ() {
      try {
        this.$modal.loading('正在检查文件，请稍候...')
        let { data } = await aiReviewYZWZ({ file: this.pdfData })
        this.result1 = data;
        this.showResult1 = true;
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    async handleReviewZGTJ() {
      try {
        this.$modal.loading('正在检查文件，请稍候...')
        let { data } = await aiReviewZGTJ({ file: this.pdfData })
        this.result2 = data;
        this.showResult2 = true;
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    async handleReviewPFBF() {
      try {
        this.$modal.loading('正在检查文件，请稍候...')
        let { data } = await aiReviewPFBF({ file: this.pdfData })
        this.result3 = data;
        this.showResult3 = true;
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    getResultHtmlPFBF(data) {
      let html = '<ul>'
      data.forEach(item => {
        if (item.scoreList.length > 0) {
          html += `<li>
                    <div class="title-box ${item.trueFlag ? 'check-true' : 'check-false'}" title="${item.trueFlag ? '' : item.basic}">
                      <i class="title-icon ${item.trueFlag ? 'el-icon-success' : 'el-icon-error'}"></i>
                      <div class="title">
                        <span>${item.title}</span>
                        <span>（${item.score}分）</span>
                      </div>
                    </div>
                    ${this.getResultHtmlPFBF(item.scoreList)}
                  </li>`;
        } else {
          html += `<li>
                    <div class="title-box ${item.trueFlag ? 'check-true' : 'check-false'}" title="${item.trueFlag ? '' : item.basic}">
                      <i class="title-icon ${item.trueFlag ? 'el-icon-success' : 'el-icon-error'}"></i>
                      <div class="title">
                        <span>${item.title}</span>
                        <span>（${item.score}分）</span>
                      </div>
                    </div>
                    <div class="content-box ${item.trueFlag ? 'check-true' : 'check-false'}">${item.scoreContent}</div>
                   </li>`;
        }
      })
      html += '</ul>';
      return html
    },
    removeFile(data) {
      console.log(data);
      this.result1 = null;
      this.result2 = null;
      this.result3 = null;
      this.showResult1 = false;
      this.showResult2 = false;
      this.showResult3 = false;
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
      let data = fileList.length > 0 ? fileList[0].file : null;
      if (data) {
        this.pdfData = data;
      } else {
        this.pdfData = null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .cart-title {
    font-weight: bold;
    font-size: 14px;
  }
}
</style>
<style lang="scss">
.result-card {
  .el-card__header {
    padding: 5px 15px;
  }
}

.result-html {
  ul {
    li {
      display: flex;

      .title-box, .content-box {
        border: 2px solid #ffffff;
        font-size: 14px;
        line-height: 1.5;
        padding: 8px;

        &.check-true {
          background: #e7faf0;
          color: #13ce66;
        }

        &.check-false {
          background: #ffeded;
          color: #ff4949;
        }
      }

      .title-box {
        width: 120px;
        flex-shrink: 0;
        display: flex;
        align-items: center;

        .title-icon {
          font-size: 20px;
          margin-right: 5px;
        }

        .title {
          display: flex;
          flex-direction: column;
          text-align: center;
        }
      }

      .content-box {
        flex: 1;
      }
    }
  }
}
</style>
