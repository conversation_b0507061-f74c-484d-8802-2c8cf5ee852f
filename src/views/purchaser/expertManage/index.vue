<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="账号/工号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入账号/工号"
          clearable
          style="width: 240px"
          maxlength="20"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评委姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入评委姓名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类别" prop="type">
        <el-select
          v-model="queryParams.type"
          filterable
          clearable
          reserve-keyword
          placeholder="请输入类别"
          allow-create
          default-first-option
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.expert_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
        prop="department"
        v-if="expertDeptShow!=='0'">
        <el-select
          v-model="queryParams.department"
          filterable
          clearable
          reserve-keyword
          placeholder="请输入"
          allow-create
          default-first-option
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.expert_dept"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组织" prop="orgCode" label-width="40" v-if="!orgCode">
        <el-select
          v-model="queryParams.orgCode"
          placeholder="组织"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in organizeList"
            :key="dict.orgCode"
            :label="dict.orgName"
            :value="dict.orgCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账号状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="账号状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['purchaser:expert:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:user:import']"
        >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:expert:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          v-hasPermi="['system:user:batchRemove']"
        >删除
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="50"
      />
      <el-table-column
        label="账号/工号"
        align="center"
        key="userName"
        prop="userName"
        :show-overflow-tooltip="true"
        width="115"/>
      <el-table-column
        label="评委姓名"
        align="center"
        key="nickName"
        prop="nickName"
        :show-overflow-tooltip="true"
        width="115"/>
      <el-table-column
        label="身份证号码"
        align="center"
        key="idNumber"
        prop="idNumber"
        :show-overflow-tooltip="true"
      >
        <template v-slot:default="{row}">
          {{ row.idNumber | formatIdCard() }}
        </template>
      </el-table-column>
      <el-table-column
        label="手机号码"
        align="center"
        key="phonenumber"
        prop="phonenumber"
        width="120"
      />
      <el-table-column
        label="类别"
        align="center"
        key="type"
        prop="type"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="expertDeptShow!=='0'"
        :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
        align="center"
        key="department"
        prop="department"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="组织"
        align="center"
        key="orgName"
        prop="orgName"
        :show-overflow-tooltip="true"
      >
        <template v-slot:default="{row}">
          {{ row.sysOrganizes | filterSysOrganizes(orgCode) }}
        </template>
      </el-table-column>
      <el-table-column label="评标统计" align="center">
        <el-table-column
          label="被抽取次数"
          align="center"
          key="extractNum"
          prop="extractNum"
          :show-overflow-tooltip="true"></el-table-column>
        <el-table-column
          label="实际参与评标次数"
          align="center"
          key="projectNum"
          prop="projectNum"
          :show-overflow-tooltip="true"></el-table-column>
        <el-table-column
          label="未参与次数"
          align="center"
          key="notParticipateNum"
          prop="notParticipateNum"
          :show-overflow-tooltip="true"></el-table-column>
      </el-table-column>
      <el-table-column
        label="印章"
        align="center"
        key="psnSealBlob"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.psnSealBlob"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleCreateSeal(scope.row)"
            v-hasPermi="['expert:seal:add']"
          >创建
          </el-button>
          <div v-else>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleLookSeal(scope.row)"
            >查看
            </el-button>
            <el-button
              class="btn-text-danger"
              v-hasPermi="['expert:seal:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteSeal(scope.row)"
            >删除
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="账号状态"
        align="center"
        key="status"
        width="80"
      >
        <template slot-scope="scope">
          <el-switch
            :disabled="!checkPermi(['system:user:status'])"
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope" v-if="scope.row.userId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['purchaser:expert:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleResetPwd(scope.row)"
            v-hasPermi="['system:user:resetPwd']"
          >重置密码
          </el-button>
          <el-button
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user:batchRemove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="730px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号/工号" prop="userName">
              <el-input v-model.trim="form.userName" placeholder="请输入账号/工号" maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model.trim="form.password" placeholder="请输入用户密码" type="password" show-password/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="评委姓名" prop="nickName">
              <el-input v-model.trim="form.nickName" placeholder="请输入评委姓名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号码" prop="idNumber">
              <el-input v-model.trim="form.idNumber" placeholder="请输入身份证号码" maxlength="25"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类别" prop="typeList">
              <el-select
                @change="changeType"
                class="block"
                v-model="form.typeList"
                multiple
                filterable
                clearable
                reserve-keyword
                placeholder="请输入类别"
                allow-create
                default-first-option
              >
                <el-option
                  v-for="dict in dict.type.expert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="expertDeptShow!=='0'">
            <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')" prop="departmentList">
              <el-select
                @change="changeDepartment"
                class="block"
                v-model="form.departmentList"
                multiple
                filterable
                clearable
                reserve-keyword
                placeholder="请输入"
                allow-create
                default-first-option
              >
                <el-option
                  v-for="dict in dict.type.expert_dept"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model.trim="form.phonenumber" placeholder="请输入手机号码" maxlength="11"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model.trim="form.email" placeholder="请输入邮箱" maxlength="50"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别" class="block">
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="!orgCode">
            <el-form-item label="归属组织" prop="orgCodeList">
              <el-select
                v-model="form.orgCodeList"
                placeholder="请选择归属组织"
                clearable
                multiple
                class="block"
              >
                <el-option
                  v-for="item in organizeList"
                  :key="item.orgCode"
                  :label="item.orgName"
                  :value="item.orgCode"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model.trim="form.deptId"
                :options="deptOptions"
                :show-count="true"
                placeholder="请选择归属部门"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate"
          >下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  addUserByExpert,
  changeUserStatus,
  delUser,
  listExpert,
  resetUserPwd,
  updateSupplierOrExpert
} from '@/api/system/user'
import { addSeal, removeSeal } from '@/api/sealManage';
import { getToken } from '@/utils/auth'
import reg from '@/utils/reg'
import { checkPermi } from '@/utils/permission';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { treeselect } from '@/api/system/dept';
import { listOrganize } from '@/api/system/organize'

export default {
  name: 'ExpertManage',
  dicts: ['sys_normal_disable', 'sys_user_sex', 'expert_type', 'expert_dept', 'sys_dict_translate'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 日期范围
      dateRange: [],
      // 默认密码
      initPassword: undefined,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 部门树选项
      deptOptions: undefined,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        type: undefined,
        department: undefined,
        phonenumber: undefined,
        status: undefined,
        roleId: undefined,
        orgCode: null
      },
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 表单参数
      form: {},
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData'
      },
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '账号/工号不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '账号/工号长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '评委姓名不能为空', trigger: 'blur' },
          { min: 2, max: 4, message: '评委姓名长度必须介于 2 和 4 之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { pattern: reg.password, message: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&', trigger: 'blur' }
        ],
        phonenumber: [
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        idNumber: [
          { required: true, message: '身份证号码不能为空', trigger: 'blur' },
          { pattern: reg.idNumber, message: '身份证号码格式不正确', trigger: 'blur' }
        ],
        typeList: [
          { required: true, message: '类别不能为空', trigger: ['blur', 'change'] }
        ],
        departmentList: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ],
        orgCodeList: [
          { required: true, message: '请选择归属组织', trigger: 'change' }
        ]
      },
      organizeList: [],
      orgCode: null,
      expertDeptShow: null
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getTreeselect()
    this.getOrganizeList()
    this.getConfigKey('expert.dept.show').then(response => {
      this.expertDeptShow = response.data
    })
    this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.data
    })
    this.getConfigKey('sys.expert.roleId').then(response => {
      this.queryParams.roleId = Number(response.data)
      this.getList()
    })
  },
  filters: {
    filterSysOrganizes(arr, orgCode) {
      if (!arr || arr.length === 0) return ''
      if (!orgCode) {
        return arr.map(v => v.orgName).join('、');
      }
      return arr.filter(v => v.orgCode === orgCode).map(v => v.orgName).join('、')
    }
  },
  methods: {
    checkPermi,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.multiple = !selection.length
    },
    changeType(val) {
      this.form.type = val.join('；');
    },
    changeDepartment(val) {
      this.form.department = val.join('；');
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/judgeExport', {
        ...this.queryParams
      }, `expert_${new Date().getTime()}.xlsx`)
    },
    /** 查询组织列表 */
    getOrganizeList() {
      listOrganize({}).then(response => {
        this.organizeList = response.data;
      })
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.deptOptions = response.data
      })
    },
    handleDeleteSeal(row) {
      this.$alert('确定要删除印章？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        this.$modal.loading('数据提交中，请稍候...')
        await removeSeal({
          personal: true,
          sealId: row.sealId,
          userId: row.userId
        })
        await this.getList();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleLookSeal(row) {
      this.$imgView(`data:image/png;base64,${row.psnSealBlob}`)
    },
    async handleCreateSeal(row) {
      try {
        if (!row.idNumber) {
          this.$modal.msgError('请点击修改按钮，先完善身份证号码信息，再创建印章');
          return
        }
        this.$modal.loading('数据提交中，请稍候...')
        await addSeal({
          userId: row.userId,
          psn: true,
          name: row.nickName,
          uniqueId: row.idNumber
        });
        await this.getList();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('创建成功');
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.orgCode = this.orgCode || this.queryParams.orgCode;
      listExpert(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function () {
        return changeUserStatus(row.userId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        userName: undefined,
        password: undefined,
        nickName: undefined,
        idNumber: undefined,
        phonenumber: undefined,
        email: undefined,
        typeList: [],
        type: undefined,
        departmentList: [],
        department: undefined,
        sex: undefined,
        status: '0',
        deptId: undefined,
        remark: undefined,
        orgCodeList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加用户'
      this.form.password = this.initPassword
      this.$set(this.form, 'orgCodeList', this.orgCode ? [this.orgCode] : []);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      for (let key in this.form) {
        this.form[key] = row[key]
      }
      this.form.typeList = this.form.type ? this.form.type.split('；') : [];
      this.form.departmentList = this.form.department ? this.form.department.split('；') : [];
      this.form.password = '';
      let sysOrganizes = row.sysOrganizes || [];
      this.$set(this.form, 'orgCodeList', sysOrganizes.map(v => v.orgCode));
      this.open = true
      this.title = '修改用户'
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValue: this.initPassword,
        inputPattern: reg.password,
        inputErrorMessage: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&'
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess('修改成功，新密码是：' + value)
        })
      }).catch(() => {
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            if (this.form.orgCodeList.length === 0) {
              this.$modal.msgError('未查询到组织信息，不允许添加')
              return
            }
            this.$modal.loading('数据提交中，请稍候...');
            if (this.form.userId != undefined) {
              await updateSupplierOrExpert(this.form);
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            } else {
              await addUserByExpert(this.form);
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            }
          } catch (e) {
            throw new Error(e);
          } finally {
            this.$modal.closeLoading();
          }
        }
      })
    },
    handleBatchDelete() {
      this.$modal
        .confirm('是否确认删除选中用户？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await delUser(this.ids, this.orgCode);
            this.$modal.msgSuccess('删除成功');
            this.getList()
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      let tipMsg = '请输入该用户的账号+姓名，如：12345678张三';
      let tipHtml = `<p style="color: #ff4949;font-size: 18px;margin-bottom: 10px;">是否确认删除该用户数据？</p>
           <p style="color: #ff4949">删除之后该用户数据将完全从系统中删除，包括用户登录账号、用户信息等，且删除之后无法恢复！</p>
      <p style="font-size: 14px;text-align: left;margin-top: 30px;">${tipMsg}</p>`
      this.$prompt(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showInput: true,
        inputPlaceholder: '',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && (value === row.userName + row.nickName)
        },
        inputErrorMessage: `${tipMsg}`
      }).then(async ({ value }) => {
        try {
          if (value !== (row.userName + row.nickName)) {
            this.$modal.msgError(tipMsg);
            return
          }
          this.$modal.loading('数据提交中，请稍候...');
          await delUser(userIds, this.orgCode);
          this.getList()
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert('<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;\'>' + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped>

</style>
