<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24" v-if="showDept">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item label="登录账号" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入登录账号"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户昵称" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入用户昵称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input
              v-model="queryParams.phonenumber"
              placeholder="请输入手机号码"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="组织" prop="orgCode" label-width="40" v-if="!orgCode">
            <el-select
              v-model="queryParams.orgCode"
              placeholder="组织"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in organizeList"
                :key="dict.orgCode"
                :label="dict.orgName"
                :value="dict.orgCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="角色" prop="roleId" label-width="40">
            <el-select
              v-model="queryParams.roleId"
              placeholder="用户角色"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in roleList"
                :key="dict.roleId"
                :label="dict.roleName"
                :value="dict.roleId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="40">
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb10">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >导入
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
            >导出
            </el-button>
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
            :columns="columns"
            @columnChange="columnChange"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" border>
          <el-table-column label="序号" type="index" width="50" align="center"/>
          <el-table-column
            label="登录账号"
            align="center"
            key="userName"
            prop="userName"
            v-if="columns[0].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="用户昵称"
            align="center"
            key="nickName"
            prop="nickName"
            v-if="columns[1].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="身份证号码"
            align="center"
            key="idNumber"
            prop="idNumber"
            :show-overflow-tooltip="true"
            v-if="columns[2].visible"
          >
            <template v-slot:default="{row}">
              {{ row.idNumber | formatIdCard() }}
            </template>
          </el-table-column>
          <el-table-column
            label="手机号码"
            align="center"
            key="phonenumber"
            prop="phonenumber"
            v-if="columns[3].visible"
            width="110"
          />
          <el-table-column
            label="角色"
            align="center"
            key="roleName"
            prop="roleName"
            v-if="columns[4].visible"
            :show-overflow-tooltip="true"
          >
            <template v-slot:default="{row}">
              {{ row.roles.map(item => item.roleName).join('、') }}
            </template>
          </el-table-column>
          <el-table-column
            label="部门"
            align="center"
            key="deptName"
            prop="dept.deptName"
            v-if="columns[5].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="组织"
            align="center"
            key="orgName"
            prop="orgName"
            v-if="columns[6].visible"
            :show-overflow-tooltip="true"
          >
            <template v-slot:default="{row}">
              {{ row.sysOrganizes | filterSysOrganizes(orgCode) }}
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            align="center"
            key="status"
            v-if="columns[7].visible"
            width="80"
          >
            <template slot-scope="scope">
              <el-switch
                :disabled="!checkPermi(['system:user:status'])"
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            label="印章"
            align="center"
            key="psnSealBlob"
            width="110"
            v-if="columns[8].visible"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!scope.row.psnSealBlob"
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleCreateSeal(scope.row)"
                v-hasPermi="['system:user:sealAdd']"
              >创建
              </el-button>
              <div v-else>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleLookSeal(scope.row)"
                >查看
                </el-button>
                <el-button
                  class="btn-text-danger"
                  v-hasPermi="['system:user:sealRemove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteSeal(scope.row)"
                >删除
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            key="createTime"
            v-if="columns[9].visible"
            width="160"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:user:edit']"
              >修改
              </el-button>
              <el-button
                class="btn-text-danger"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:user:remove']"
              >删除
              </el-button>
              <el-dropdown
                size="mini"
                @command="(command) => handleCommand(command, scope.row)"
                v-hasPermi="['system:user:resetPwd', 'system:user:edit']"
              >
                <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="handleResetPwd"
                    icon="el-icon-key"
                    v-hasPermi="['system:user:resetPwd']"
                  >重置密码
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleAuthRole"
                    icon="el-icon-circle-check"
                    v-hasPermi="['system:user:edit']"
                  >分配角色
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW800"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="userName">
              <el-input v-model.trim="form.userName" placeholder="请输入登录账号" maxlength="30"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model.trim="form.password" placeholder="请输入用户密码" type="password" show-password/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model.trim="form.nickName" placeholder="请输入用户昵称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号码" prop="idNumber">
              <el-input v-model.trim="form.idNumber" placeholder="请输入身份证号码" maxlength="25"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model.trim="form.phonenumber" placeholder="请输入手机号码" maxlength="11"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model.trim="form.email" placeholder="请输入邮箱" maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别" class="block" clearable>
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!orgCode">
            <el-form-item label="归属组织" prop="orgCodeList">
              <el-select
                v-model="form.orgCodeList"
                placeholder="请选择归属组织"
                clearable
                multiple
                class="block"
              >
                <el-option
                  v-for="item in organizeList"
                  :key="item.orgCode"
                  :label="item.orgName"
                  :value="item.orgCode"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="showDept">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model.trim="form.deptId"
                :options="deptOptions"
                :show-count="true"
                placeholder="请选择归属部门"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-select
                v-model="form.postIds"
                multiple
                placeholder="请选择岗位"
                class="block"
                clearable
              >
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select
                v-model="form.roleIds"
                clearable
                multiple
                placeholder="请选择角色"
                class="block"
              >
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate"
          >下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus } from '@/api/system/user'
import { getToken } from '@/utils/auth'
import { treeselect } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import reg from '@/utils/reg';
import { checkPermi } from '@/utils/permission';
import { listRole } from '@/api/system/role';
import { listOrganize } from '@/api/system/organize';
import { addSeal, removeSeal } from '@/api/sealManage'
import { mapGetters } from 'vuex'

export default {
  name: 'User',
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
        roleId: undefined,
        orgCode: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `登录账号`, visible: true },
        { key: 1, label: `用户昵称`, visible: true },
        { key: 2, label: `身份证号码`, visible: true },
        { key: 3, label: `手机号码`, visible: true },
        { key: 4, label: `角色`, visible: true },
        { key: 5, label: `部门`, visible: true },
        { key: 6, label: `组织`, visible: true },
        { key: 7, label: `状态`, visible: true },
        { key: 8, label: `印章`, visible: true },
        { key: 9, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '登录账号不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '登录账号长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '用户昵称不能为空', trigger: 'blur' },
          { min: 2, max: 25, message: '用户昵称长度必须介于 2 和 25 之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { pattern: reg.password, message: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        phonenumber: [
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        idNumber: [
          { required: false, message: '身份证号码不能为空', trigger: 'blur' },
          { pattern: reg.idNumber, message: '身份证号码格式不正确', trigger: 'blur' }
        ],
        orgCodeList: [
          { required: true, message: '请选择归属组织', trigger: 'change' }
        ],
        roleIds: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      roleList: [],
      organizeList: [],
      excludeRoleIds: [],
      orgCode: null
    }
  },
  computed: {
    ...mapGetters([
      'deptId'
    ]),
    showDept() {
      if (this.checkPermi(['system:user:queryAll'])) {
        return true;
      } else {
        return !this.checkPermi(['system:user:queryDept']);
      }
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getTreeselect()
    this.getOrganizeList()
    this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.data
    })
    this.getConfigKey('purchaser.exclude.roleIds').then(response => {
      this.excludeRoleIds = response.data.split(',').map(v => Number(v));
      this.getRoleList()
      this.getList()
    })
  },
  filters: {
    filterSysOrganizes(arr, orgCode) {
      if (!arr || arr.length === 0) return ''
      if (!orgCode) {
        return arr.map(v => v.orgName).join('、');
      }
      return arr.filter(v => v.orgCode === orgCode).map(v => v.orgName).join('、')
    }
  },
  methods: {
    checkPermi,
    handleDeleteSeal(row) {
      this.$alert('确定要删除印章？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        this.$modal.loading('数据提交中，请稍候...')
        await removeSeal({
          personal: true,
          sealId: row.sealId,
          userId: row.userId
        })
        this.$modal.msgSuccess('删除成功');
        await this.getList();
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleLookSeal(row) {
      this.$imgView(`data:image/png;base64,${row.psnSealBlob}`)
    },
    async handleCreateSeal(row) {
      try {
        if (!row.idNumber) {
          this.$modal.msgError('身份证号码信息未完善，无法创建印章');
          return
        }
        this.$modal.loading('数据提交中，请稍候...')
        await addSeal({
          userId: row.userId,
          psn: true,
          name: row.nickName,
          uniqueId: row.idNumber
        });
        this.$modal.msgSuccess('创建成功');
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    columnChange(data) {
      this.columns = data
    },
    /** 查询组织列表 */
    getOrganizeList() {
      listOrganize({}).then(response => {
        this.organizeList = response.data;
      })
    },
    /** 查询角色列表 */
    getRoleList() {
      listRole().then(response => {
        let list = response.rows || [];
        this.roleList = list.filter(item => !this.excludeRoleIds.includes(item.roleId));
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.orgCode = this.orgCode || this.queryParams.orgCode;
      this.queryParams.excludeRoleIds = this.excludeRoleIds;
      if (!this.showDept) {
        this.queryParams.deptId = this.deptId;
      }
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.deptOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.handleQuery()
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function () {
        return changeUserStatus(row.userId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        idNumber: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: '0',
        remark: undefined,
        postIds: [],
        roleIds: [],
        orgCodeList: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleAuthRole':
          this.handleAuthRole(row)
          break
        default:
          break
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.$modal.loading('数据查询中，请稍候...');
      this.getTreeselect()
      getUser()
        .then(response => {
          this.$modal.closeLoading();
          this.postOptions = response.posts
          this.roleOptions = response.roles.filter(item => !this.excludeRoleIds.includes(item.roleId))
          this.open = true
          this.title = '添加用户'
          this.form.password = this.initPassword
          this.$set(this.form, 'orgCodeList', this.orgCode ? [this.orgCode] : []);
          if (!this.showDept) {
            this.$set(this.form, 'deptId', this.deptId);
          }
        })
        .catch(() => {
          this.$modal.closeLoading();
        })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      const userId = row.userId || this.ids
      getUser(userId).then(response => {
        this.form = response.data
        this.postOptions = response.posts
        this.roleOptions = response.roles.filter(item => !this.excludeRoleIds.includes(item.roleId))
        this.$set(this.form, 'postIds', response.postIds);
        this.$set(this.form, 'roleIds', response.roleIds);
        let sysOrganizes = response.data.sysOrganizes || [];
        this.$set(this.form, 'orgCodeList', sysOrganizes.map(v => v.orgCode));
        this.open = true
        this.title = '修改用户'
        this.form.password = ''
      })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValue: this.initPassword,
        inputPattern: reg.password,
        inputErrorMessage: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&'
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess('修改成功，新密码是：' + value)
        })
      }).catch(() => {
      })
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId
      this.$router.push('/system/user-auth/role/' + userId)
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (this.form.userId != undefined) {
              await updateUser(this.form);
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            } else {
              await addUser(this.form);
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            }
          } catch (e) {
            throw new Error(e);
          } finally {
            this.$modal.closeLoading();
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      let tipMsg = '请输入该用户的登录账号+用户呢称，如：12345678张三';
      let tipHtml = `<p style="color: #ff4949;font-size: 18px;margin-bottom: 10px;">是否确认删除该用户数据？</p>
           <p style="color: #ff4949">删除之后该用户数据将完全从系统中删除，包括用户登录账号、用户信息等，且删除之后无法恢复！</p>
      <p style="font-size: 14px;text-align: left;margin-top: 30px;">${tipMsg}</p>`
      this.$prompt(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showInput: true,
        inputPlaceholder: '',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && (value === row.userName + row.nickName)
        },
        inputErrorMessage: `${tipMsg}`
      }).then(async ({ value }) => {
        try {
          if (value !== (row.userName + row.nickName)) {
            this.$modal.msgError(tipMsg);
            return
          }
          this.$modal.loading('数据提交中，请稍候...');
          await delUser(userIds, this.orgCode);
          this.getList()
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert('<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;\'>' + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>
