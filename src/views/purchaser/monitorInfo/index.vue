<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" align="center" prop="buyItemName"/>
      <el-table-column label="标段(包)" align="center" prop="subpackageName"/>
      <el-table-column label="监标事项" align="center" prop="monitorBidType">
        <template v-slot:default="{row}">
          {{ row.monitorBidType === '1' ? '监督开标' : '监督评标' }}
        </template>
      </el-table-column>
      <el-table-column label="文件" align="center" prop="fileKey">
        <template v-slot:default="{row}">
          <el-button
            type="text"
            size="mini"
            v-if="row.fileKey"
            icon="el-icon-view"
            @click="previewFile(row.fileKey)"
          >预览
          </el-button>
          <el-button
            type="text"
            size="mini"
            v-if="row.fileKey"
            icon="el-icon-download"
            @click="downloadFile(row.fileKey)"
          >下载
          </el-button>
          <span v-if="!row.fileKey">/</span>
        </template>
      </el-table-column>
      <el-table-column label="签章" align="center">
        <template v-slot:default="{row}">
          <div v-if="row.fileKey">
            <el-tag type="success" v-if="row.signStatus === 1">已签章</el-tag>
            <el-tag type="success" v-if="row.signStatus === 0&&!checkPermi(['monitor:info:sign'])">未签章</el-tag>
            <el-button
              v-if="row.signStatus===0&&checkPermi(['monitor:info:sign'])"
              size="mini"
              type="text"
              icon="el-icon-s-check"
              @click="handleSign(row)"
            >签章
            </el-button>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import { monitorInfoList, signMonitor } from '@/api/purchaser/monitorBidPerson'
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null
        }
      },
      total: 0,
      tableData: [],
      loading: false,
      signOff: null,
      signVersion: null
    }
  },
  created() {
    this.getConfigKey('sys.signature.off').then(response => {
      this.signOff = response.data;
    })
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList()
  },
  computed: {
    ...mapGetters([
      'phoneNumber'
    ])
  },
  methods: {
    checkPermi,
    handleSign(row) {
      if (this.signOff === '0' && this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.submitSignMonitor({ authCode, flowId, monitorId: row.id });
          }
        })
      } else {
        this.submitSignMonitor({ monitorId: row.id });
      }
    },
    async submitSignMonitor(args = {}) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { authCode, flowId, monitorId } = args;
        await signMonitor({ monitorId, authCode, flowId });
        this.$modal.msgSuccess('签章成功');
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    downloadFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await monitorInfoList(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
