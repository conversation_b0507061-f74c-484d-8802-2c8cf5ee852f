<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="公告名称" prop="bulletinName">
        <el-input
          v-model="queryParams.bulletinName"
          placeholder="请输入公告名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告类型" prop="bulletinType">
        <el-select v-model="queryParams.bulletinType" placeholder="公告类型" clearable>
          <el-option
            v-for="dict in dict.type.entrust_bulletin_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代理机构名称" prop="createUserName" label-width="100px">
        <el-input
          v-model="queryParams.createUserName"
          placeholder="请输入代理机构名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="审核状态" clearable>
          <el-option
            v-for="dict in dict.type.entrust_bulletin_audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="公告名称" align="center" prop="bulletinName"/>
      <el-table-column label="公告类型" align="center" prop="bulletinType">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.entrust_bulletin_type" :value="row.bulletinType"/>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName"/>
      <el-table-column label="公告内容" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="代理机构名称" align="center" prop="createUserName"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template v-slot:default="{row}">
          <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss', '/') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditUserName" width="100">
        <template v-slot:default="{row}">
          <span>{{ row.auditUserName || '/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime	" width="160">
        <template v-slot:default="{row}">
          <span>{{ row.auditTime | formatTime('YYYY-MM-DD HH:mm:ss', '/') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核" align="center" prop="auditStatus" width="170">
        <template v-slot:default="{row}">
          <div v-if="row.auditStatus===0">
            <el-button
              v-if="checkPermi(['entrust:bulletinAudit:audit'])"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleAudit(row,1)"
            >审核通过
            </el-button>
            <el-button
              v-if="checkPermi(['entrust:bulletinAudit:audit'])"
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleAudit(row,2)"
            >审核不通过
            </el-button>
          </div>
          <dict-tag :options="dict.type.entrust_bulletin_audit_status" :value="row.auditStatus" v-else/>
          <div class="td-tip" v-if="row.auditStatus===2">
            理由：{{ row.failureDescription || '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['entrust:bulletinAudit:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            class="btn-text-danger"
            @click="handleDelete(row)"
            v-hasPermi="['entrust:bulletinAudit:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否展示" align="center" key="showStatus" width="90px">
        <template v-slot:default="{row}">
          <el-switch
            :disabled="row.auditStatus!==1 || !checkPermi(['entrust:bulletinAudit:show'])"
            v-model="row.showStatus"
            :active-value="1"
            :inactive-value="2"
            @change="handleShowChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      top="2vh"
      title="详情"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top" :disabled="!isEdit">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="公告内容" prop="bulletinText">
              <Tinymce
                ref="editor"
                v-model="form.bulletinText"
                height="650"
                :readonly="!isEdit"
                v-if="open&&initEditor"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label="公告名称" prop="bulletinName">
              <el-input v-model="form.bulletinName" maxlength="100" placeholder="请输入公告名称"/>
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model.trim="form.projectName" maxlength="100" placeholder="请输入项目名称"/>
            </el-form-item>
            <el-form-item label="公告类型" prop="bulletinType">
              <el-select v-model="form.bulletinType" class="block" placeholder="请选择公告类型">
                <el-option
                  v-for="dict in dict.type.entrust_bulletin_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label=" " v-if="isEdit">
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="cancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  delEntrustBulletinByReviewer,
  listEntrustBulletinByReviewer,
  changeEntrustBulletinShow,
  reviewEntrustBulletin,
  updateEntrustBulletin
} from '@/api/entrustBulletin';
import { checkPermi } from '@/utils/permission';
import { verifyEditor } from '@/utils/validate'

export default {
  name: 'EntrustBulletinAudit',
  dicts: ['entrust_bulletin_audit_status', 'entrust_bulletin_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 公告表格数据
      tableData: [],
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bulletinName: null,
        projectName: null,
        bulletinType: null,
        createUserName: null,
        auditStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        bulletinName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        bulletinText: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ],
        bulletinType: [
          { required: true, message: '请选择', trigger: ['change'] }
        ]
      },
      initEditor: true
    };
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    // 表单重置
    reset() {
      this.form = {
        id: null,
        bulletinName: null,
        bulletinText: null,
        projectName: null,
        bulletinType: null
      };
      this.resetForm('form');
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.isEdit = false;
      this.reset();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key]
      }
      this.open = true;
      this.isEdit = true;
      this.title = '修改';
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateEntrustBulletin(this.form).then(response => {
            this.$modal.msgSuccess('修改成功');
            this.open = false;
            this.getList();
          });
        }
      });
    },
    handleAudit(row, auditStatus) {
      this.$prompt(auditStatus === 1 ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: auditStatus !== 1,
        inputType: 'textarea',
        inputPlaceholder: auditStatus !== 1 ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (auditStatus === 1) {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 100
        },
        inputErrorMessage: '请输入不通过理由，最多100个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await reviewEntrustBulletin({
            id: row.id,
            auditStatus: auditStatus,
            failureDescription: value
          })
          this.getList()
          this.$modal.msgSuccess('操作成功')
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading()
          throw e
        }
      }).catch(() => {
      })
    },
    handleShowChange(row) {
      let text = row.showStatus === 1 ? '展示' : '不展示'
      this.$modal.confirm(`确认要${text}该公告吗？`).then(function () {
        return changeEntrustBulletinShow({
          id: row.id,
          showStatus: row.showStatus
        })
      }).then(() => {
        this.$modal.msgSuccess('操作成功')
        this.getList()
      }).catch(() => {
        row.showStatus = row.showStatus === 1 ? 2 : 1
      })
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listEntrustBulletinByReviewer(this.queryParams).then(response => {
        let { data } = response;
        this.tableData = data.records;
        this.total = data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleDetail(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key]
      }
      this.open = true;
      this.isEdit = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function () {
        return delEntrustBulletinByReviewer({ id: row.id });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    }
  }
};
</script>
