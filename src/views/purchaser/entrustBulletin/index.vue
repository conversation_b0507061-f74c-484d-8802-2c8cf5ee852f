<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="公告名称" prop="bulletinName">
        <el-input
          v-model="queryParams.bulletinName"
          placeholder="请输入公告名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告类型" prop="bulletinType">
        <el-select v-model="queryParams.bulletinType" placeholder="公告类型" clearable>
          <el-option
            v-for="dict in dict.type.entrust_bulletin_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="审核状态" clearable>
          <el-option
            v-for="dict in dict.type.entrust_bulletin_audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['entrust:bulletin:add']"
        >新增
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="公告名称" align="center" prop="bulletinName"/>
      <el-table-column label="项目名称" align="center" prop="projectName"/>
      <el-table-column label="公告类型" align="center" prop="bulletinType">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.entrust_bulletin_type" :value="row.bulletinType"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template v-slot:default="{row}">
          <span>{{ row.createTime | formatTime('YYYY-MM-DD HH:mm:ss', '/') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditUserName" width="100">
        <template v-slot:default="{row}">
          <span>{{ row.auditUserName || '/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="160">
        <template v-slot:default="{row}">
          <span>{{ row.auditTime | formatTime('YYYY-MM-DD HH:mm:ss', '/') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="100">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.entrust_bulletin_audit_status" :value="row.auditStatus"/>
          <div class="td-tip" v-if="row.auditStatus===2">
            理由：{{ row.failureDescription || '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="110">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            :disabled="row.auditStatus===1"
            @click="handleUpdate(row)"
            v-hasPermi="['entrust:bulletin:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            :disabled="row.auditStatus!==0"
            @click="handleDelete(row)"
            v-hasPermi="['entrust:bulletin:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog
      top="2vh"
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="公告内容" prop="bulletinText">
              <template-select v-if="open" tmp-type="entrust_bulletin" v-model="form.bulletinText"></template-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model.trim="form.projectName" maxlength="100" placeholder="请输入项目名称"/>
            </el-form-item>
            <el-form-item label="公告名称" prop="bulletinName">
              <el-input v-model="form.bulletinName" maxlength="100" placeholder="请输入公告名称"/>
            </el-form-item>
            <el-form-item label="公告类型" prop="bulletinType">
              <el-select v-model="form.bulletinType" class="block" placeholder="请选择公告类型">
                <el-option
                  v-for="dict in dict.type.entrust_bulletin_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label=" ">
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="cancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  addEntrustBulletin,
  delEntrustBulletin,
  listEntrustBulletin,
  updateEntrustBulletin
} from '@/api/entrustBulletin';

export default {
  name: 'EntrustBulletin',
  dicts: ['entrust_bulletin_audit_status', 'entrust_bulletin_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 公告表格数据
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bulletinName: null,
        projectName: null,
        bulletinType: null,
        auditStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        bulletinName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        bulletinText: [
          { required: true, message: '内容不能为空', trigger: ['blur', 'change'] }
        ],
        bulletinType: [
          { required: true, message: '请选择', trigger: ['change'] }
        ]
      },
      initEditor: true
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listEntrustBulletin(this.queryParams).then(response => {
        let { data } = response;
        this.tableData = data.records;
        this.total = data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        bulletinName: null,
        bulletinText: null,
        projectName: null,
        bulletinType: null
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    async handleAdd() {
      try {
        this.reset();
        this.open = true;
        this.title = '添加';
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key]
      }
      this.open = true;
      this.title = '修改';
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateEntrustBulletin(this.form).then(response => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addEntrustBulletin(this.form).then(response => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function () {
        return delEntrustBulletin({ id: row.id });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    }
  }
};
</script>
