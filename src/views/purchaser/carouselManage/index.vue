<template>
  <div class="app-container">
    <ul class="banner-wrap">
      <li class="banner-item" v-for="(item,k) in bannerList" :key="k">
        <img :src="item.imgUrl" alt="">
        <div class="hover-wrap">
          <i class="el-icon-zoom-in" @click="preview(item.imgUrl)"></i>
          <i class="el-icon-delete" @click="removeImg(item.fileCode)" v-hasPermi="['purchaser:carouselManage:remove']"></i>
        </div>
      </li>
      <li v-hasPermi="['purchaser:carouselManage:upload']">
        <el-upload
          class="banner-upload"
          ref="banner"
          action="banner"
          :accept="accept"
          list-type="picture-card"
          :show-file-list="false"
          :limit="1"
          :before-upload="beforeUpload"
          :http-request="uploadSectionFile"
        >
          <div class="avatar-banner">
            <i class="el-icon-plus"></i>
          </div>
        </el-upload>
      </li>
    </ul>

    <div class="prompt-wrap">
      <span>温馨提示：</span>
      <div>
        <p>1、上传图片格式为 {{ accept }} 文件</p>
        <p>2、上传图片大小为 1920 * 480 像素</p>
        <p>3、上传图片大小建议 100kb ～ 500kb，不能超过1MB</p>
        <p>4、图片轮播顺序按照上传的排列顺序轮播</p>
      </div>
    </div>

  </div>
</template>

<script>
import {
  fileDeleteByOther,
  fileDownByPublic,
  getFileListByPublic,
  fileUploadByCarousel
} from '@/api/file';
import { isEmpty } from '@/utils';

export default {
  name: 'Index',
  data() {
    return {
      bannerList: [],
      accept: '.png, .jpg, .jpeg'
    };
  },
  created() {
    this.getBannerList();
  },
  methods: {
    async getBannerList() {
      try {
        let { data } = await getFileListByPublic({
          fileTypeName: 'carousel'
        });
        this.bannerList = data;
        this.bannerList.forEach(item => {
          this.getImgUrl(item.fileCode).then(res => {
            this.$set(item, 'imgUrl', res);
          })
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    async removeImg(fileKey) {
      try {
        await fileDeleteByOther(fileKey);
        this.$modal.msgSuccess('删除成功');
        await this.getBannerList();
      } catch (e) {
        throw new Error(e);
      }
    },
    preview(url) {
      this.$imgView(url);
    },
    // 图片上传前
    beforeUpload(file) {
      if (!isEmpty(this.accept)) {
        let rules = this._.map(this.accept.split(','), this._.trim);
        let index = file.name.lastIndexOf('.');
        let format = file.name.substring(index).toLowerCase();
        if (this._.indexOf(rules, format) === -1) {
          this.$modal.msgError(`只能上传${rules.join(' ')}格式的文件`);
          return false
        }
      }

      let fileSize = file.size
      if (fileSize <= 0) {
        this.$modal.msgError('上传文件大小不能为空!')
        return false
      }
      if (fileSize > 1 * 1024 * 1024) {
        this.$modal.msgError('上传文件大小不能大于1MB!')
        return false
      }
    },
    // 图片上传
    async uploadSectionFile(param) {
      try {
        let file = param.file;
        await fileUploadByCarousel({ file });
        this.$modal.msgSuccess('上传成功');
        this.$refs.banner.clearFiles(); // 清空上传文件列表
        await this.getBannerList();
      } catch (e) {
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByPublic(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$bannerHeight: 78.75px;
.banner-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .banner-item {
    width: 240px;
    height: $bannerHeight;
    margin: 10px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    .hover-wrap {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;

      i {
        color: #FFFFFF;
        cursor: pointer;
        font-size: 20px;

        & + i {
          margin-left: 15px;
        }
      }
    }

    &:hover {
      .hover-wrap {
        display: flex;
      }
    }
  }
}

.prompt-wrap {
  display: flex;
  margin-top: 30px;
  padding-left: 20px;
  color: #606266;
}
</style>
<style lang="scss">
$bannerHeight: 78.75px;
.banner-upload {
  margin: 10px;

  .el-upload--picture-card {
    width: 240px;
    height: $bannerHeight;
    line-height: $bannerHeight - 2px;
  }

  .avatar-banner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: $bannerHeight - 2px;
  }
}
</style>
