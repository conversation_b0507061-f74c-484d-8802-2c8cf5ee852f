<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="供应商名称" prop="entity.supplierName">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="entity.goodsName">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.goodsName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="entity.goodsBrand">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.goodsBrand"
          placeholder="请输入品牌"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产地" prop="entity.goodsOrigin">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.goodsOrigin"
          placeholder="请输入产地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品分类" prop="entity.goodsCategory">
        <el-select
          v-model="queryParams.entity.goodsCategory"
          placeholder="请选择商品分类"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.goods_key_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <ul class="goods-list">
      <li v-for="(item,index) in tableData" :key="'li_'+index" class="goods-card">
        <el-card shadow="hover" :body-style="{ padding: '0px' }">
          <div @click="toDetail(item.goodsId)" class="pointer goods-box-1">
            <el-image class="goods-main-img" :src="item.mainImgUrl" fit="contain"></el-image>
            <div class="ellipsis-more-2 goods-name" :title="item.goodsName">
              {{ item.goodsName }}
            </div>
          </div>
          <div class="goods-box-2">
            <div>
              <el-tag type="info" class="over-ellipsis" style="max-width: 100%">{{ item.supplierName }}</el-tag>
            </div>
            <div class="goods-price-box">
              <div class="goods-price">
                <span class="goods-price-icon">￥</span>
                <span class="goods-price-text">{{ item.goodsUnitPrice | formatMoney(2) }}</span>
              </div>
              <el-button type="primary" icon="el-icon-shopping-cart-2" circle @click="addCart(item)"></el-button>
            </div>
          </div>
        </el-card>
      </li>
    </ul>

    <el-empty description="暂无数'" v-show="total===0"></el-empty>

    <pagination
      class="text-center"
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listGoodsAudit } from '@/api/purchaser/goodsAduit';
import { fileDownByGoods } from '@/api/file';
import { checkPermi } from '@/utils/permission'
import { addCart } from '@/api/purchaser/shoppingMall'
import { mapGetters } from 'vuex'

export default {
  name: 'ShoppingMall',
  dicts: ['goods_key_group'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        entity: {
          supplierName: null,
          goodsName: null,
          goodsCategory: null,
          goodsBrand: null,
          goodsOrigin: null,
          goodsStatus: 1
        }
      }
    };
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  methods: {
    checkPermi,
    toDetail(goodsId) {
      this.$router.push({ name: 'GoodsDetail', params: { goodsId }})
    },
    async addCart(item) {
      try {
        await addCart({
          buyerId: this.userId,
          goodsId: item.goodsId,
          sellerId: item.supplierId
        })
        this.$modal.msgSuccess('加入购物车成功');
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await listGoodsAudit(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        let mainList = this.tableData.map(item => {
          let mainImg = item.goodsPicList.find(v => v.picType === 0);
          return this.getImgUrl(mainImg.picFile)
        })
        let urlList = await Promise.all(mainList);
        this.tableData.forEach((item, index) => {
          this.$set(item, 'mainImgUrl', urlList[index]);
        })
        console.log(this.tableData)
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByGoods(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.goods-list {
  display: flex;
  flex-wrap: wrap;

  .goods-card {
    width: 240px;
    margin: 10px;
  }
}

.goods-box-1 {
  .goods-main-img {
    display: block;
    width: 100%;
    height: 238px;
    margin-bottom: 5px;
  }

  .goods-name {
    font-size: 14px;
    line-height: 24px;
    height: 48px;
    padding: 0 15px;
  }

}

.goods-box-2 {
  padding: 10px 15px 20px 15px;
}

.goods-price-box {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .goods-price {
    color: $colorDanger;

    .goods-price-icon {
      font-size: 14px;
    }

    .goods-price-text {
      font-size: 20px;
      font-weight: 700;
    }
  }

}

</style>
