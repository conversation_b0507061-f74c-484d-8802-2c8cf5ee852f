<template>
  <div class="app-container">
    <div class="container">
      <div class="goods-main-1">

        <div class="goods-left">
          <el-carousel trigger="click" height="350px" indicator-position="outside" :autoplay="false">
            <el-carousel-item v-for="(url,index) in mainImgList" :key="index">
              <el-image class="img-w img-h" :src="url" :preview-src-list="[url]" fit="contain">
                <div slot="error" class="image-slot fontSize30">
                  <i class="el-icon-picture-outline"></i>
                </div>
                <div slot="placeholder" class="image-slot fontSize16">
                  加载中<span class="dot">...</span>
                </div>
              </el-image>
            </el-carousel-item>
          </el-carousel>
        </div>

        <div class="goods-right">
          <h3 class="goods-name">
            {{ detail.goodsName }}
          </h3>

          <div class="goods-info-box overflow">
            <div class="goods-flex clearfix">
              <div class="goods-flex-label">商品单价</div>
              <div class="goods-flex-content goods-price">
                <span class="goods-price-icon">￥</span>
                <span class="goods-price-text">{{ detail.goodsUnitPrice | formatMoney(2) }}</span>
              </div>
            </div>
            <div class="goods-flex clearfix">
              <div class="goods-flex-label">商品分类</div>
              <div class="goods-flex-content">{{ detail.goodsCategory | dictFormat(dict.type.goods_key_group) }}</div>
            </div>
            <div class="goods-flex clearfix">
              <div class="goods-flex-label">品牌</div>
              <div class="goods-flex-content">{{ detail.goodsBrand }}</div>
            </div>
            <div class="goods-flex clearfix">
              <div class="goods-flex-label">产地</div>
              <div class="goods-flex-content">{{ detail.goodsOrigin }}</div>
            </div>
            <div class="goods-flex clearfix">
              <div class="goods-flex-label">供应商</div>
              <div class="goods-flex-content">{{ detail.supplierName }}</div>
            </div>
          </div>

          <div class="mt20" v-hasPermi="['shopping:mall:buy']">
            <el-button type="primary" icon="el-icon-shopping-cart-2" @click="addCart">加入购物车</el-button>
          </div>
        </div>
      </div>

      <div class="goods-main-2">
        <el-tabs type="border-card" v-model="activeName">
          <el-tab-pane label="商品介绍" name="first">
            <el-descriptions>
              <template v-for="(item,index) in goodsDetail">
                <el-descriptions-item :label="item.keyName" :key="index">
                  <span v-if="item.keyType!=='file'">{{ item.value || '/' }}</span>
                  <div v-if="item.keyType==='file'">
                    <el-button
                      v-if="item.value"
                      type="text"
                      size="mini"
                      @click="previewFileByGoods(item.value)"
                    >预览
                    </el-button>
                    <span v-else>/</span>
                  </div>
                </el-descriptions-item>
              </template>
            </el-descriptions>

            <el-divider></el-divider>

            <div class="goods-detail-img">
              <template v-for="(url,index) in detailImgList">
                <img :src="url" alt="" :key="index" class="img-w">
              </template>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { addCart, getGoodsInfo } from '@/api/purchaser/shoppingMall';
import { fileDownByGoods } from '@/api/file';
import { checkPermi } from '@/utils/permission'
import { mapGetters } from 'vuex'

export default {
  name: 'GoodsDetail',
  dicts: ['goods_key_group'],
  data() {
    return {
      // 遮罩层
      loading: false,
      goodsId: null,
      mainImgList: [],
      detailImgList: [],
      goodsDetail: [],
      detail: {},
      activeName: 'first'
    };
  },
  created() {
    this.goodsId = this.$route.params.goodsId
    this.getDetail();
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  methods: {
    checkPermi,
    async addCart() {
      try {
        await addCart({
          buyerId: this.userId,
          goodsId: this.goodsId,
          sellerId: this.detail.supplierId
        })
        this.$modal.msgSuccess('加入购物车成功');
      } catch (e) {
        throw new Error(e);
      }
    },
    previewFileByGoods(data) {
      this.$download.previewFileByGoods(data)
    },
    async getDetail() {
      try {
        this.loading = true;
        let { data } = await getGoodsInfo(this.goodsId);
        this.detail = data;
        this.goodsDetail = data.goodsDetail;
        let goodsPicList = data.goodsPicList;
        let urlList = await Promise.all(goodsPicList.map(v => this.getImgUrl(v.picFile)));
        goodsPicList.forEach((item, index) => {
          this.$set(item, 'url', urlList[index]);
        })
        this.mainImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 0), 'picOrder', 'asc').map(v => v.url);
        this.detailImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 1), 'picOrder', 'asc').map(v => v.url);
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByGoods(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.goods-main-1 {
  display: flex;
  margin-bottom: 20px;

  .goods-left {
    width: 350px;
  }

  .goods-right {
    margin-left: 30px;

    .goods-name {
      font-size: 16px;
      font-weight: 700;
      line-height: 28px;
      padding-top: 10px;
      margin-bottom: 10px;
    }

    .goods-info-box {
      background: #f3f3f3;
      padding: 15px 10px 10px 15px;
    }

    .goods-flex {
      display: flex;
      align-items: baseline;
      margin-bottom: 10px;

      .goods-flex-label {
        width: 60px;
        color: #999999;
        text-align: justify;
        text-align-last: justify;
        word-break: break-all;
        margin-right: 20px;
        line-height: 22px;
        font-size: 14px;
        flex-shrink: 0;
      }

      .goods-flex-content {
        font-size: 14px;

        &.goods-price {
          color: $colorDanger;

          .goods-price-text {
            font-size: 22px;
          }
        }
      }
    }
  }
}

.goods-detail-img {
  width: 80%;
  margin: 0 auto;
}
</style>
<style lang="scss">
.goods-left {
  .el-carousel__container {
    border: 1px solid #eee;
  }
}

.goods-main-2 {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
