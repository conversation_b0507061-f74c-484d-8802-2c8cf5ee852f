<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName" label-width="100px">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="归口管理" prop="entity.managementDept">
        <el-input
          v-model="queryParams.entity.managementDept"
          placeholder="请输入归口管理"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" align="center" prop="buyItemName"/>
      <el-table-column label="归口管理" align="center" prop="managementDept"/>
      <el-table-column label="使用科室" align="center" prop="useDept"/>
      <el-table-column label="使用科室负责人" align="center" prop="useDeptPerson"/>
      <el-table-column label="分派人" align="center" prop="createBy"/>
      <el-table-column label="分派时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center">
        <template v-slot:default="{row}">
          <file-list-view
            :fileList="row.attachmentDtoList"
            :handlePreview="previewFileByOther"
            :handleDown="downloadFileByOther"
          />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark">
        <template v-slot:default="{row}">
          {{ row.remark || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="采购状态" align="center" width="90">
        <template v-slot:default="{row}">
          <el-tag :type="row.allocateWhetherCreate == 1 ? 'success' : 'info'">
            {{ row.allocateWhetherCreate === 1 ? '已采购' : '未采购' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="90">
        <template v-slot:default="{row}">
          <div v-if="checkPermi(['project:assign:purchase'])">
            <el-button
              v-if="row.allocateWhetherCreate!==1"
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="toCreateProject(row.id)"
            >去采购
            </el-button>
            <span v-else>已采购</span>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import { getProjectAssignList } from '@/api/purchaser/projectInformation'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null,
          managementDept: null
        }
      },
      total: 0,
      tableData: [],
      loading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    toCreateProject(id) {
      this.$router.push({ name: 'AssignProjectCreate', params: { id }})
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await getProjectAssignList(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
