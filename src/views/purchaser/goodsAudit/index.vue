<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="供应商名称" prop="entity.supplierName">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="entity.goodsName">
        <el-input
          style="width: 240px"
          v-model="queryParams.entity.goodsName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品分类" prop="entity.goodsCategory">
        <el-select
          v-model="queryParams.entity.goodsCategory"
          placeholder="请选择商品分类"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.goods_key_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态" prop="entity.goodsStatus">
        <el-select
          v-model="queryParams.entity.goodsStatus"
          placeholder="请选择商品状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.goods_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="55"/>
      <el-table-column align="center" label="商品图片" prop="mainImgUrl" width="80">
        <template v-slot:default="{row}">
          <el-image
            style="width: 50px; height: 50px;vertical-align: middle;"
            :src="row.mainImgUrl"
            :preview-src-list="[row.mainImgUrl]"
            fit="contain"
          >
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="goodsName"/>
      <el-table-column label="商品单价(元)" align="center" prop="goodsUnitPrice">
        <template v-slot:default="{row}">
          {{ row.goodsUnitPrice | formatMoney(2) }}
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" prop="supplierName"/>
      <el-table-column label="商品分类" align="center" prop="goodsCategory">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.goods_key_group" :value="row.goodsCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="商品详情" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          <span>{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" prop="goodsStatus">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.goods_status" :value="row.goodsStatus"/>
          <div class="td-tip" v-if="row.goodsStatus==2">
            理由：{{ row.auditRemark || '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="170">
        <template v-slot:default="{row}">
          <div v-if="row.goodsStatus==0&&checkPermi(['goods:audit:audit'])">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleAudit(1,row)"
            >审核通过
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleAudit(2,row)"
            >审核不通过
            </el-button>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      top="5vh"
      title="商品参数"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1000"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-descriptions :column="1" border labelClassName="goods-label">
        <el-descriptions-item label="商品分类">{{ detail.goodsCategory | dictFormat(dict.type.goods_key_group) }}</el-descriptions-item>
        <template v-for="(item,index) in detail.goodsDetail">
          <el-descriptions-item :label="item.keyName" :key="index">
            <span v-if="item.keyType!=='file'">{{ item.value || '/' }}</span>
            <div v-if="item.keyType==='file'">
              <el-button
                v-if="item.value"
                type="text"
                size="mini"
                @click="previewFileByGoods(item.value)"
              >预览
              </el-button>
              <span v-else>/</span>
            </div>
          </el-descriptions-item>
        </template>
        <el-descriptions-item label="商品主图">
          <image-upload-select
            v-model="detail.mainImgList"
            :disabled="true"
            :isShowTip="false"
            @preview="previewGoodsImg"
          >
          </image-upload-select>
        </el-descriptions-item>
        <el-descriptions-item label="商品详情图">
          <image-upload-select
            v-model="detail.detailImgList"
            :disabled="true"
            :isShowTip="false"
            @preview="previewGoodsImg"
          >
          </image-upload-select>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open =false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { auditGoods, listGoodsAudit } from '@/api/purchaser/goodsAduit';
import { fileDownByGoods } from '@/api/file';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'GoodsAudit',
  dicts: ['goods_status', 'goods_key_group'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          supplierName: null,
          goodsName: null,
          goodsCategory: null,
          goodsStatus: null
        }
      },
      detail: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    previewFileByGoods(data) {
      this.$download.previewFileByGoods(data)
    },
    handleAudit(status, row) {
      this.$prompt(status == 1 ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: status != 1,
        inputType: 'textarea',
        inputPlaceholder: status != 1 ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (status == 1) {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 100
        },
        inputErrorMessage: '请输入不通过理由，最多100个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await auditGoods({
            auditRemark: value,
            goodsStatus: status,
            goodsId: row.goodsId
          })
          this.$modal.msgSuccess('审核成功');
          await this.getList()
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    /** 查看详情 */
    async handleDetail(row) {
      try {
        this.$modal.loading('数据查询中，请稍候...')
        this.detail = {
          goodsCategory: null,
          goodsDetail: [],
          mainImgList: [],
          detailImgList: []
        };
        for (let key in this.detail) {
          this.detail[key] = row[key];
        }
        let goodsPicList = row.goodsPicList;
        let urlList = await Promise.all(goodsPicList.map(v => this.getImgUrl(v.picFile)));
        goodsPicList.forEach((item, index) => {
          this.$set(item, 'url', urlList[index]);
        })
        this.detail.mainImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 0), 'picOrder', 'asc').map(v => {
          return {
            file: v.picFile,
            name: v.picFile,
            url: v.url
          }
        });
        this.detail.detailImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 1), 'picOrder', 'asc').map(v => {
          return {
            file: v.picFile,
            name: v.picFile,
            url: v.url
          }
        });
        console.log(this.detail)
        this.$modal.closeLoading()
        this.open = true;
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    previewGoodsImg(file) {
      this.$imgView(file.url)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await listGoodsAudit(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        let mainList = this.tableData.map(item => {
          let mainImg = item.goodsPicList.find(v => v.picType === 0);
          return this.getImgUrl(mainImg.picFile)
        })
        let urlList = await Promise.all(mainList);
        this.tableData.forEach((item, index) => {
          this.$set(item, 'mainImgUrl', urlList[index]);
        })
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByGoods(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
<style lang="scss">
.goods-label{
  min-width: 150px;
}
</style>
