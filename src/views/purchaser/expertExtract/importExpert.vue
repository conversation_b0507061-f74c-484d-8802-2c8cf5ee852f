<template>
  <div class="el-button el-button--text" style="padding: 0;border: none;">
    <el-button
      type="text"
      icon="el-icon-download"
      size="mini"
      @click="handleShow"
    >导入到项目
    </el-button>

    <el-dialog
      title="批量导入专家"
      center
      top="5vh"
      :visible.sync="isVisible"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px" @submit.native.prevent>
        <el-form-item label="项目名称" prop="entity.buyItemName">
          <el-input
            v-model="queryParams.entity.buyItemName"
            placeholder="请输入项目名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        max-height="250"
        ref="table"
        @row-click="clickRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55"></el-table-column>
        <el-table-column label="序号" align="center" type="index" width="55"/>
        <el-table-column label="项目名称" align="center" prop="buyItemName" :show-overflow-tooltip="true"/>
        <el-table-column label="标段名称" align="center" prop="subpackageName" :show-overflow-tooltip="true"/>
        <el-table-column label="是否添加专家" align="center" prop="whetherExtract" width="100">
          <template v-slot:default="{row}">
            {{ row.whetherExtract ? '是' : '否' }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <el-table :data="judgeList" border class="mt15">
        <el-table-column label="评委姓名" align="center" prop="judgeName"/>
        <el-table-column label="账号/工号" align="center" prop="judgeAccount"/>
        <el-table-column label="类别" align="center" prop="specialtyCategory" :show-overflow-tooltip="true"/>
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="dept"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="身份证号码" align="center" prop="idNumber" width="170">
          <template v-slot:default="{row}">
            {{ row.idNumber | formatIdCard() }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="expertCellphone"/>
        <el-table-column label="抽取时间" align="center" prop="extractTime" width="155">
          <template v-slot:default="{row}">
            {{ row.extractTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isVisible = false">关闭</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { batchSaveExtractJudgeInfo, queryExpertInfo, queryExtractBuyItemInfo } from '@/api/purchaser/expertExtract'

export default {
  name: 'ImportExpert',
  dicts: ['sys_dict_translate'],
  props: {
    extractLongName: {
      type: String,
      default: null
    },
    expertDeptShow: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isVisible: false,
      loading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null,
          end: 0,
          orgCode: null
        }
      },
      tableData: [],
      total: 0,
      selectList: [],
      judgeList: []
    }
  },
  methods: {
    handleShow() {
      this.getList();
      this.getJudgeList();
      this.isVisible = true;
    },
    async submit() {
      if (this.selectList.length === 0) {
        this.$modal.msgError('请选择要导入的项目');
        return;
      }
      if (this.judgeList.length === 0) {
        this.$modal.msgError('专家人数不能为0');
        return;
      }
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await batchSaveExtractJudgeInfo({
          subDtoList: this.selectList.map(item => {
            return {
              buyItemCode: item.buyItemCode,
              subpackageCode: item.subpackageCode,
              voList: this.judgeList
            }
          })
        });
        this.$modal.msgSuccess('导入成功');
        this.isVisible = false;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectList = selection;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      this.selectList = [];
      this.queryParams.pageNum = 1;
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.selectList = [];
      this.resetForm('queryForm');
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        this.queryParams.entity.orgCode = this.$cache.local.get('loginOrgCode');
        let { rows, total } = await queryExtractBuyItemInfo(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    },
    async getJudgeList() {
      try {
        let { data } = await queryExpertInfo({
          extractLogName: this.extractLongName
        })
        let list = data || [];
        this.judgeList = list.filter(v => v.whetherRefuse === 1);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
