<template>
  <div class="app-container">

    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="90px"
      @submit.native.prevent
    >
      <el-form-item label="抽取记录名" prop="entity.extractLongName">
        <el-input
          v-model="queryParams.entity.extractLongName"
          placeholder="抽取记录名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['purchaser:expertExtract:add']"
        >新增
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column
        label="序号"
        align="center"
        type="index"
      />
      <el-table-column
        label="抽取记录名"
        align="center"
        prop="extractLongName"
      />
      <el-table-column
        label="人数"
        align="center"
        prop="number"
        width="90"
      />
      <el-table-column
        label="抽取时间"
        align="center"
        prop="extractTime"
        width="180"
      >
        <template v-slot:default="{row}">
          {{ row.extractTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
      >
        <template v-slot:default="{row}">
          {{ row.remark || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        label="详情"
        align="center"
        width="100"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="170"
      >
        <template v-slot:default="{row}">
          <el-button
            v-if="checkPermi(['purchaser:expertExtract:copy'])"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleCopy(row)"
          >重新应用
          </el-button>
          <import-expert
            :extractLongName="row.extractLongName"
            :expertDeptShow="expertDeptShow"
            v-if="checkPermi(['project:expertExtract:import'])"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--新建抽取-->
    <el-dialog
      title="抽取"
      :visible.sync="isOpen"
      top="5vh"
      custom-class="maxW900"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="抽取记录名" prop="buyItemName">
              <el-select
                class="block"
                v-model="form.buyItemName"
                filterable
                remote
                clearable
                reserve-keyword
                placeholder="请输入关键词"
                allow-create
                default-first-option
                :remote-method="remoteMethod"
                :loading="isLoading">
                <el-option
                  v-for="(item,index) in projectList"
                  :key="index"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="versionType!=='jxzl'&&expertDeptShow!=='0'">
          <el-col :span="24">
            <el-form-item
              :label="'回避'+selectDictValue(dict.type.sys_dict_translate,'expertDept')"
                          prop="avoidDeptList"
            >
              <el-select
                class="block"
                v-model="form.avoidDeptList"
                filterable
                clearable
                multiple
                reserve-keyword
                placeholder="请输入"
                allow-create
                default-first-option
              >
                <el-option
                  v-for="dict in dict.type.expert_dept"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                :rows="2"
                resize="none"
                v-model.trim="form.remark"
                placeholder="请输入"
                maxlength="250"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-table :data="form.conditionsList" border>
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          >
          </el-table-column>
          <el-table-column
            label="类别"
            align="center"
            class-name="form-cell"
          >
            <template v-slot:default="{row,$index}">
              <el-form-item
                label-width="0"
                size="mini"
                :prop="'conditionsList.'+$index+'.specialtyCategory'"
                :rules="rules.specialtyCategory"
              >
                <el-select
                  class="block"
                  v-model="row.specialtyCategory"
                  filterable
                  clearable
                  reserve-keyword
                  placeholder="请输入类别"
                  allow-create
                  default-first-option
                >
                  <el-option
                    v-for="dict in dict.type.expert_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
            align="center"
            class-name="form-cell"
            v-if="versionType!=='jxzl'&&expertDeptShow!=='0'"
          >
            <template v-slot:default="{row,$index}">
              <el-form-item
                label-width="0"
                size="mini"
                :prop="'conditionsList.'+$index+'.dept'"
                :rules="rules.dept"
              >
                <el-select
                  class="block"
                  v-model="row.dept"
                  filterable
                  clearable
                  reserve-keyword
                  placeholder="请输入"
                  allow-create
                  default-first-option
                >
                  <el-option
                    v-for="dict in dict.type.expert_dept"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="人数"
            align="center"
            class-name="form-cell"
          >
            <template v-slot:default="{row,$index}">
              <el-form-item
                label-width="0"
                size="mini"
                :prop="'conditionsList.'+$index+'.numberOfPerson'"
                :rules="rules.numberOfPerson"
              >
                <el-input v-model.number="row.numberOfPerson" placeholder="请输入" maxlength="5"/>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="60"
          >
            <template v-slot:default="{$index}">
              <el-button
                size="mini"
                icon="el-icon-delete"
                class="btn-text-danger"
                type="text"
                :disabled="form.conditionsList.length<=1"
                @click="removeItem($index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="text-center mt10 mb15">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="addItem()"
            plain
          >
            添加条件
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleExtract"
          >
            抽取
          </el-button>
        </div>

      </el-form>
      <el-table :data="extractList" border>
        <el-table-column
          label="评委姓名"
          align="center"
          prop="judgeName"
        />
        <el-table-column
          label="账号/工号"
          align="center"
          prop="judgeAccount"
        />
        <el-table-column
          label="类别"
          align="center"
          prop="specialtyCategory"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="dept"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="身份证号码"
          align="center"
          prop="idNumber"
          width="170"
        >
          <template v-slot:default="{row}">
            {{ row.idNumber | formatIdCard() }}
          </template>
        </el-table-column>
        <el-table-column
          label="联系方式"
          align="center"
          prop="expertCellphone"
        />
      </el-table>
      <div slot="footer" class="text-center">
        <el-button
          @click="isOpen = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSaveExtract"
          v-if="extractList.length>0"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="详情"
      :visible.sync="isDetailOpen"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row :gutter="10" class="mb10" v-if="checkPermi(['purchaser:expertExtract:add'])">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            size="mini"
            @click="handleAddExpert"
          >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            size="mini"
            :disabled="multiple"
            @click="handleDetailAgain()"
          >重新抽取
          </el-button>
        </el-col>
      </el-row>
      <el-table :data="detailList" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column label="评委姓名" align="center" prop="judgeName"/>
        <el-table-column label="账号/工号" align="center" prop="judgeAccount"/>
        <el-table-column label="类别" align="center" prop="specialtyCategory" :show-overflow-tooltip="true"/>
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="dept"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="身份证号码" align="center" prop="idNumber" width="170">
          <template v-slot:default="{row}">
            {{ row.idNumber | formatIdCard() }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="expertCellphone"/>
        <el-table-column label="抽取时间" align="center" prop="extractTime" width="155">
          <template v-slot:default="{row}">
            {{ row.extractTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="whetherRefuse">
          <template v-slot:default="{row}">
            <el-tag :type="row.whetherRefuse === 1 ? 'success' : 'info'">
              {{ row.whetherRefuse === 1 ? '到场' : '不到场' }}
            </el-tag>
            <div class="td-tip" v-if="row.whetherRefuse===0">
              理由：{{ row.refuseReason || '/' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          v-if="checkPermi(['purchaser:expertExtract:add'])"
          width="170"
        >
          <template v-slot:default="{row}">
            <el-button
              v-if="row.whetherRefuse === 1"
              size="mini"
              type="text"
              @click="handleModifyState(row)"
            >不到场
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleDetailAgain(row)"
            >重新抽取
            </el-button>
            <el-button
              v-if="checkPermi(['purchaser:expertExtract:remove'])"
              class="btn-text-danger"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDetailDelete(row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isDetailOpen = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="评委抽取"
      :visible.sync="isAgainDialog"
      top="5vh"
      custom-class="maxW1000"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="extractParams"
        ref="extractParams"
        size="small"
        :inline="true"
        label-width="48px"
      >
        <el-form-item label="类别" prop="specialtyCategory">
          <el-select
            style="width: 240px"
            v-model="extractParams.specialtyCategory"
            filterable
            clearable
            reserve-keyword
            placeholder="请输入类别"
            allow-create
            default-first-option
          >
            <el-option
              v-for="dict in dict.type.expert_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          prop="dept"
          v-if="versionType!=='jxzl'&&expertDeptShow!=='0'"
        >
          <el-select
            style="width: 240px"
            v-model="extractParams.dept"
            filterable
            clearable
            reserve-keyword
            placeholder="请输入"
            allow-create
            default-first-option
          >
            <el-option
              v-for="dict in dict.type.expert_dept"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人数" prop="numberOfPerson" v-if="isAdd">
          <el-input v-model.number="extractParams.numberOfPerson" placeholder="请输入" maxlength="5"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getAgainExtract">抽取</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="againExtractList" border>
        <el-table-column label="评委姓名" align="center" prop="judgeName"/>
        <el-table-column label="账号/工号" align="center" prop="judgeAccount"/>
        <el-table-column label="类别" align="center" prop="specialtyCategory" :show-overflow-tooltip="true"/>
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="dept"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="身份证号码" align="center" prop="idNumber" width="170">
          <template v-slot:default="{row}">
            {{ row.idNumber | formatIdCard() }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="expertCellphone"/>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isAgainDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAgain" :disabled="againExtractList.length===0">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="重新应用"
      :visible.sync="isCopyDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="copyForm" size="small" label-width="90px">
        <el-form-item label="抽取记录名" prop="buyItemName">
          <el-select
            style="width: 500px;"
            v-model="extractLongName"
            filterable
            remote
            clearable
            reserve-keyword
            placeholder="请输入关键词"
            allow-create
            default-first-option
            :remote-method="remoteMethod"
            :loading="isLoading">
            <el-option
              v-for="(item,index) in projectList"
              :key="index"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            :rows="2"
            resize="none"
            v-model.trim="remark"
            placeholder="请输入"
            maxlength="250"
            style="width: 500px;"/>
        </el-form-item>
      </el-form>
      <el-table :data="copyList" border>
        <el-table-column label="评委姓名" align="center" prop="judgeName"/>
        <el-table-column label="账号/工号" align="center" prop="judgeAccount"/>
        <el-table-column label="类别" align="center" prop="specialtyCategory" :show-overflow-tooltip="true"/>
        <el-table-column
          v-if="expertDeptShow!=='0'"
          :label="selectDictValue(dict.type.sys_dict_translate,'expertDept')"
          align="center"
          prop="dept"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="身份证号码" align="center" prop="idNumber" width="170">
          <template v-slot:default="{row}">
            {{ row.idNumber | formatIdCard() }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" prop="expertCellphone"/>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isCopyDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveCopy" v-if="checkPermi(['purchaser:expertExtract:add'])">保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  againExtract,
  delJudgeLog,
  expertExtract,
  getExtractRecordList,
  markJudge,
  queryExpertInfo,
  queryProjectName,
  saveExpertExtract
} from '@/api/purchaser/expertExtract';
import reg from '@/utils/reg';
import { checkPermi } from '@/utils/permission';
import importExpert from '@/views/purchaser/expertExtract/importExpert.vue'

export default {
  dicts: ['expert_type', 'expert_dept', 'sys_dict_translate'],
  name: 'Index',
  components: {
    importExpert
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          extractLongName: null
        }
      },
      isOpen: false,
      form: {},
      rules: {
        buyItemName: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: ['blur', 'change'] },
          { max: 200, message: '最长200个字符', trigger: ['blur', 'change'] }
        ],
        specialtyCategory: [{ required: false, message: '请输入', trigger: 'blur' }],
        dept: [{ required: false, message: '请输入', trigger: 'blur' }],
        numberOfPerson: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.number, message: '只能输入数字', trigger: 'blur' }
        ]
      },
      extractList: [],
      isDetailOpen: false,
      detailList: [],
      extractLongName: null,
      remark: null,
      selectIds: [],
      multiple: true,
      isAgainDialog: false,
      extractParams: {},
      isLoading: false,
      projectList: [],
      isCopyDialog: false,
      copyList: [],
      // 重新抽取到的评委列表
      againExtractList: [],
      isAdd: false,
      expertDeptShow: null
    }
  },
  created() {
    this.getConfigKey('expert.dept.show').then(response => {
      this.expertDeptShow = response.data
    })
    this.getList();
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  methods: {
    checkPermi,
    async handleSaveCopy() {
      try {
        if (!this.extractLongName) {
          this.$modal.msgError('抽取记录名不能为空');
          return
        }
        if (this.copyList.length === 0) {
          this.$modal.msgError('评委不能为空');
          return
        }
        this.$modal.loading('数据提交中，请稍候...');
        await saveExpertExtract({
          repair: false,
          extractLongName: this.extractLongName,
          remark: this.remark,
          voList: this.copyList
        })
        this.isCopyDialog = false;
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw e
      }
    },
    // 点击重新应用按钮
    async handleCopy(row) {
      try {
        let { data } = await queryExpertInfo({
          extractLogName: row.extractLongName
        })
        let list = data || [];
        this.copyList = list.filter(v => v.whetherRefuse === 1);
        this.extractLongName = null;
        this.remark = null;
        this.projectList = [];
        this.isCopyDialog = true;
      } catch (e) {
        throw new Error(e);
      }
    },
    handleModifyState(row) {
      this.$prompt('请输入理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入理由',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 100
        },
        inputErrorMessage: '请输入理由，最多100个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await markJudge({
            extractLongName: this.extractLongName,
            judgeIdList: [row.judgeId],
            refuseReason: value
          })
          this.$modal.msgSuccess('操作成功');
          await this.getDetailList()
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    // 保存重新抽取专家
    async handleSaveAgain() {
      try {
        if (this.againExtractList.length === 0) {
          return
        }
        this.$modal.loading('数据提交中，请稍候...');
        await saveExpertExtract({
          repair: true,
          extractLongName: this.extractLongName,
          remark: this.remark,
          voList: this.againExtractList
        })
        this.isAgainDialog = false;
        await this.getDetailList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw e
      }
    },
    // 抽取专家
    async getAgainExtract() {
      try {
        let { data } = await againExtract(this.extractParams);
        this.againExtractList = data || [];
      } catch (e) {
        throw new Error(e);
      }
    },
    // 点击添加评委
    handleAddExpert() {
      this.resetExtractParams();
      this.extractParams.currentJudgeIdList = [];
      this.extractParams.otherJudgeIdList = this.detailList.map(item => item.judgeId);
      this.extractParams.numberOfPerson = null;
      this.againExtractList = [];
      this.isAgainDialog = true;
      this.isAdd = true;
    },
    // 点击重新抽取
    handleDetailAgain(row) {
      this.resetExtractParams();
      this.extractParams.currentJudgeIdList = row ? [row.judgeId] : this.selectIds;
      this.extractParams.otherJudgeIdList = this.detailList.map(item => item.judgeId);
      this.extractParams.numberOfPerson = this.extractParams.currentJudgeIdList.length;
      this.againExtractList = [];
      this.isAgainDialog = true;
      this.isAdd = false;
    },
    resetExtractParams() {
      this.extractParams = {
        currentJudgeIdList: [],
        otherJudgeIdList: [],
        specialtyCategory: null,
        dept: null,
        numberOfPerson: null,
        judgeName: null
      }
    },
    handleSelectionChange(selection) {
      this.selectIds = selection.map(item => item.judgeId);
      this.multiple = !selection.length
    },
    // 删除详情专家
    handleDetailDelete(row) {
      this.$alert('确定要删除？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        this.$modal.loading('数据提交中，请稍候...')
        await delJudgeLog({
          extractLongName: this.extractLongName,
          judgeIdList: [row.judgeId]
        })
        this.$modal.msgSuccess('删除成功');
        await this.getDetailList();
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    // 点击详情按钮
    async handleDetail(row) {
      try {
        this.extractLongName = row.extractLongName;
        this.remark = row.remark;
        await this.getDetailList();
        this.isDetailOpen = true;
      } catch (e) {
        throw new Error(e);
      }
    },
    async getDetailList() {
      try {
        let { data } = await queryExpertInfo({
          extractLogName: this.extractLongName
        })
        this.detailList = this._.orderBy(data || [], 'whetherRefuse', 'desc');
      } catch (e) {
        throw new Error(e);
      }
    },
    /** 以下是首次抽取 */
    handleSaveExtract() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            if (this.extractList.length === 0) {
              this.$modal.msgError('专家人数不能为0');
              return
            }
            this.$modal.loading('数据提交中，请稍候...');
            await saveExpertExtract({
              repair: false,
              extractLongName: this.form.buyItemName,
              remark: this.form.remark,
              voList: this.extractList
            })
            this.isOpen = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }
      })
    },
    handleExtract() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            let { data } = await expertExtract(this.form);
            this.extractList = data;
          } catch (e) {
            throw new Error(e);
          }
        }
      })
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.isLoading = true;
        try {
          let { data } = await queryProjectName({ buyItemName: query });
          this.isLoading = false;
          this.projectList = data;
        } catch (e) {
          this.isLoading = false;
          throw new Error(e);
        }
      } else {
        this.projectList = [];
      }
    },
    addItem() {
      this.form.conditionsList.push({
        specialtyCategory: null,
        dept: null,
        numberOfPerson: null
      })
    },
    removeItem(index) {
      this.form.conditionsList.splice(index, 1)
    },
    // 新增抽取
    handleAdd() {
      this.isOpen = true;
      this.extractList = [];
      this.reset();
    },
    reset() {
      this.form = {
        buyItemName: null,
        remark: null,
        avoidDeptList: [],
        conditionsList: [
          {
            specialtyCategory: null,
            dept: null,
            numberOfPerson: null
          }
        ]
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await getExtractRecordList(this.queryParams)
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
