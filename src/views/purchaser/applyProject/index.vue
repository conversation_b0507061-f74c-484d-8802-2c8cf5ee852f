<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="审批编号" prop="businessId">
        <el-input
          v-model="queryParams.businessId"
          placeholder="请输入审批编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="apply">
        <el-input
          v-model="queryParams.apply"
          placeholder="请输入申请人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请科室" prop="applyDept">
        <el-input
          v-model="queryParams.applyDept"
          placeholder="请输入申请科室"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预算号/课题号" prop="budgetNumber">
        <el-input
          v-model="queryParams.budgetNumber"
          placeholder="请输入预算号/课题号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" prop="buyClass">
        <el-select v-model="queryParams.buyClass" placeholder="请选择" style="width: 240px" clearable>
          <el-option
            v-for="dict in dict.type.project_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="审批编号" align="center" prop="businessId"/>
      <el-table-column label="创建人" align="center" prop="creator" width="90"/>
      <el-table-column label="创建人部门" align="center" prop="creatorDept"/>
      <el-table-column label="申请人" align="center" prop="apply" width="90"/>
      <el-table-column label="申请科室" align="center" prop="applyDept"/>
      <el-table-column label="预算号/课题号" align="center" prop="budgetNumber"/>
      <el-table-column label="采购标的" align="center" prop="procurementType"/>
      <el-table-column :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" align="center" prop="buyClass">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.project_category" :value="row.buyClass"/>
        </template>
      </el-table-column>
      <el-table-column label="内容审核" align="center" width="90">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.apply_project_confirm" :value="row.confirm"/>
        </template>
      </el-table-column>
      <el-table-column label="项目状态" align="center" width="90">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.apply_project_status" :value="row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220">
        <template v-slot:default="{row}">
          <el-button
            v-if="checkPermi(['project:apply:query'])"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row.id,false)"
          >详情
          </el-button>
          <el-button
            v-if="row.confirm!==1&&checkPermi(['project:apply:edit'])"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(row.id)"
          >修改
          </el-button>
          <el-button
            v-if="row.confirm!==1&&checkPermi(['project:apply:audit'])"
            size="mini"
            type="text"
            icon="el-icon-circle-check"
            @click="handleDetail(row.id,true)"
          >确认
          </el-button>
          <el-button
            v-if="row.status!==1&&checkPermi(['project:apply:purchase'])"
            :disabled="row.confirm!==1"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="toCreateProject(row.id)"
          >去采购
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      top="2vh"
      title="详情"
      :visible.sync="isDialog"
      width="90%"
      custom-class="maxW800"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="申请人">{{ detail.apply }}</el-descriptions-item>
        <el-descriptions-item label="申请科室">{{ detail.applyDept }}</el-descriptions-item>
        <el-descriptions-item label="预算号/课题号">{{ detail.budgetNumber }}</el-descriptions-item>
        <el-descriptions-item label="采购目的">{{ detail.buyPurpose }}</el-descriptions-item>
        <el-descriptions-item label="采购标的">{{ detail.procurementType }}</el-descriptions-item>
        <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')">
          {{ detail.procurementType | dictFormat(dict.type.project_category) }}
        </el-descriptions-item>
        <el-descriptions-item label="货物采购类型">
          {{ detail.goodsType | dictFormat(dict.type.project_goods_type) }}
        </el-descriptions-item>
        <el-descriptions-item label="是否超过5万">
          {{ detail.middleAmount | dictFormat([{ label: '否', value: '0' }, { label: '是', value: '1' }]) }}
        </el-descriptions-item>
        <el-descriptions-item label="预算金额（元）">{{ detail.buyBudget }}</el-descriptions-item>
        <el-descriptions-item label="是否涉及大额资金（20万元以上）">
          {{ detail.largeAmount | dictFormat([{ label: '否', value: '0' }, { label: '是', value: '1' }]) }}
        </el-descriptions-item>
        <el-descriptions-item label="资金来源">
          {{ detail.capitalSource | dictFormat(dict.type.project_fund_source) }}
        </el-descriptions-item>
        <el-descriptions-item label="预算类型">
          {{ detail.budgetType | dictFormat(dict.type.project_budget_type) }}
        </el-descriptions-item>
        <el-descriptions-item label="项目/课题负责人">{{ detail.buyPerson }}</el-descriptions-item>
        <el-descriptions-item label="课题号">{{ detail.classNum }}</el-descriptions-item>
        <el-descriptions-item label="详细参数附件">
          <file-list-view :fileList="detail.paramsAttList || []" :handleDown="downFile" :handlePreview="previewFile"/>
        </el-descriptions-item>
        <el-descriptions-item label="院长办公会纪要">
          <file-list-view :fileList="detail.meetingAttList || []" :handleDown="downFile" :handlePreview="previewFile"/>
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{ detail.buyRemark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="text-center">
        <el-button @click="isDialog = false">关闭</el-button>
        <el-button type="primary" @click="submitConfirm" v-if="isConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  confirmApplyProjectInfoByDingTalk,
  queryApplyProjectInfoByDingTalk,
  queryApplyProjectListByDingTalk
} from '@/api/purchaser/projectList';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'Index',
  dicts: ['project_category', 'project_goods_type', 'project_fund_source', 'project_budget_type', 'sys_dict_translate', 'apply_project_confirm', 'apply_project_status'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessId: null,
        apply: null,
        applyDept: null,
        budgetNumber: null,
        buyClass: null
      },
      total: 0,
      tableData: [],
      loading: false,
      isDialog: false,
      detail: {},
      isConfirm: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    toCreateProject(id) {
      this.$router.push({ name: 'ApplyProjectCreate', params: { id }})
    },
    handleEdit(id) {
      this.$router.push({ name: 'ApplyProjectDetail', params: { id }})
    },
    previewFile(data) {
      this.$download.previewFileByGeneral(data)
    },
    downFile(data) {
      this.$download.downloadFileByGeneral(data)
    },
    async submitConfirm() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        await confirmApplyProjectInfoByDingTalk(this.detail.dingTalkId);
        this.$modal.msgSuccess('操作成功');
        this.isDialog = false;
        await this.getList()
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async handleDetail(id, isConfirm) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryApplyProjectInfoByDingTalk(id);
        this.resetDetail();
        for (let key in this.detail) {
          this.detail[key] = data[key];
        }
        this.detail.dingTalkId = id;
        this.isConfirm = isConfirm;
        this.isDialog = true;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    resetDetail() {
      this.detail = {
        dingTalkId: null,
        apply: null,
        applyDept: null,
        budgetNumber: null,
        buyPurpose: null,
        procurementType: null,
        buyClass: null,
        goodsType: null,
        middleAmount: null,
        buyBudget: null,
        largeAmount: null,
        capitalSource: null,
        budgetType: null,
        buyPerson: null,
        classNum: null,
        paramsAttList: [],
        meetingAttList: [],
        buyRemark: null
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await queryApplyProjectListByDingTalk(this.queryParams)
        this.tableData = rows || []
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
