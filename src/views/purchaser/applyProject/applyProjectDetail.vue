<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
      <el-card
        header="详细信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box project-card"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="申请人" prop="apply">
              <el-input v-model.trim="form.apply" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="申请科室" prop="applyDept">
              <el-input v-model.trim="form.applyDept" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预算号/课题号" prop="budgetNumber">
              <el-input v-model.trim="form.budgetNumber" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="采购目的" prop="buyPurpose">
              <el-input v-model.trim="form.buyPurpose" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="采购标的" prop="procurementType">
              <el-input v-model.trim="form.procurementType" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" prop="buyClass">
              <el-select v-model="form.buyClass" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="货物采购类型" prop="goodsType">
              <el-select v-model="form.goodsType" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_goods_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="是否超过5万" prop="middleAmount">
              <el-select v-model="form.middleAmount" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预算金额" prop="buyBudget">
              <el-input v-model.trim="form.buyBudget" placeholder="请输入" :disabled="isDisabled">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="是否涉及大额资金（20万元以上）" prop="largeAmount">
               <span slot="label">
                 是否涉及大额资金
                <el-tooltip content="20万元以上" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-select v-model="form.largeAmount" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="资金来源" prop="capitalSource">
              <el-select v-model="form.capitalSource" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_fund_source"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预算类型" prop="budgetType">
              <el-select v-model="form.budgetType" placeholder="请选择" class="block" :disabled="isDisabled">
                <el-option
                  v-for="dict in dict.type.project_budget_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="项目/课题负责人" prop="buyPerson">
              <el-input v-model.trim="form.buyPerson" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="课题号" prop="classNum">
              <el-input v-model.trim="form.classNum" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="详细参数附件" prop="paramsAttList">
              <file-upload-select
                :disabled="isDisabled"
                v-model="form.paramsAttList"
                :fileSize="20"
                :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
                @change="uploadFile"
                :preview="previewFile"
                :down="downFile"
              >
              </file-upload-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="院长办公会纪要" prop="meetingAttList">
              <file-upload-select
                :disabled="isDisabled"
                v-model="form.meetingAttList"
                :fileSize="20"
                :fileType="['doc','docx','xls','xlsx','pdf','png','jpg','jpeg']"
                @change="uploadFile"
                :preview="previewFile"
                :down="downFile"
              >
              </file-upload-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="备注" prop="buyRemark">
              <el-input v-model.trim="form.buyRemark" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>

        </el-row>
      </el-card>
      <div class="text-center mt30" v-if="checkPermi(['project:apply:edit'])">
        <el-button type="primary" @click="submitForm">
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { isString } from '@/utils/validate';
import {
  editApplyProjectInfoByDingTalk,
  queryApplyProjectInfoByDingTalk
} from '@/api/purchaser/projectList'
import { checkPermi } from '@/utils/permission'
import reg from '@/utils/reg'

export default {
  name: 'ApplyProjectDetail',
  dicts: ['project_category', 'sys_dict_translate', 'project_fund_source', 'project_goods_type', 'project_budget_type'],
  data() {
    return {
      form: {},
      rules: {
        apply: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        applyDept: [
          { required: true, message: '请选择', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        budgetNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        buyPurpose: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        procurementType: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        buyClass: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        middleAmount: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        buyBudget: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.money, message: '格式不对（0.00~99999999999.99）', trigger: 'blur' }
        ],
        largeAmount: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        capitalSource: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        budgetType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        buyPerson: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        classNum: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 32, message: '最长32个字符', trigger: 'blur' }
        ],
        paramsAttList: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        meetingAttList: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        buyRemark: [
          { required: false, message: '请输入', trigger: 'blur' },
          { max: 500, message: '最长500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    isDisabled() {
      return !(this.checkPermi(['project:apply:edit']))
    }
  },
  created() {
    this.init();
  },
  methods: {
    checkPermi,
    async init() {
      this.reset();
      this.form.dingTalkId = this.$route.params.id;
      await this.queryInfo();
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
    },
    previewFile(data) {
      if (isString(data)) {
        this.$download.previewFileByGeneral(data)
      } else {
        this.$pdfViewDialog({ data: data, type: 'blob' })
      }
    },
    downFile(data) {
      if (isString(data)) {
        this.$download.downloadFileByGeneral(data)
      } else {
        this.$download.saveAs(data, data.name)
      }
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await editApplyProjectInfoByDingTalk(this.form);
            this.$modal.msgSuccess('修改成功');
            await this.queryInfo()
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    async queryInfo() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await queryApplyProjectInfoByDingTalk(this.form.dingTalkId);
        for (let key in this.form) {
          this.form[key] = data[key];
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 表单重置 */
    reset() {
      this.form = {
        dingTalkId: null,
        apply: null,
        applyDept: null,
        budgetNumber: null,
        buyPurpose: null,
        procurementType: null,
        buyClass: null,
        goodsType: null,
        middleAmount: null,
        buyBudget: null,
        largeAmount: null,
        capitalSource: null,
        budgetType: null,
        buyPerson: null,
        classNum: null,
        paramsAttList: [],
        meetingAttList: [],
        buyRemark: null
      }
      this.resetForm('form');
    }
  }
}
</script>

<style lang="scss" scoped>
.project-card {
  overflow: visible;
}
</style>
