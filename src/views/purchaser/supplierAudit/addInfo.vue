<template>
  <div class="mb10">
    <el-button
      type="primary"
      plain
      icon="el-icon-plus"
      size="mini"
      @click="handleAdd"
    >新增
    </el-button>

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="1vh"
      width="90%"
      modal-append-to-body
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-card header="账号信息" shadow="never" :body-style="{'padding': '15px 10px 0px 10px'}" class="card-box">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="登录账号（手机号）" prop="userName">
                <el-input v-model.trim="form.userName" placeholder="请输入" maxlength="30"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item v-if="form.id == undefined" label="用户密码" prop="password">
                <el-input v-model.trim="form.password" placeholder="请输入" type="password" show-password/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="!orgCode">
              <el-form-item label="归属组织" prop="orgCodeList">
                <el-select v-model="form.orgCodeList" placeholder="请选择归属组织" clearable multiple class="block">
                  <el-option
                    v-for="item in organizeList"
                    :key="item.orgCode"
                    :label="item.orgName"
                    :value="item.orgCode"
                    :disabled="item.status == 1"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="入库状态" prop="storageType">
                <el-select
                  v-model="form.storageType"
                  placeholder="入库状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.supplier_storage_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card header="主体信息" shadow="never" :body-style="{'padding': '15px 10px 0px 10px'}" class="card-box">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="公司名称" prop="bidderName">
                <el-input v-model.trim="form.bidderName" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="统一社会信用代码" prop="licNumber">
                <el-input v-model.trim="form.licNumber" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="类别" prop="bidTypeList">
                <el-select
                  v-model="form.bidTypeList"
                  multiple
                  :multiple-limit="bidTypeNum"
                  placeholder="请选择"
                  class="block"
                >
                  <el-option
                    v-for="dict in dict.type.supplier_bid_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="行政区域" prop="regionCode">
                <el-cascader

                  placeholder="请选择"
                  class="block"
                  v-model="form.regionCode"
                  :options="cityList"
                  :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="详细地址" prop="contactAddress">
                <el-input v-model.trim="form.contactAddress" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model.trim="form.email" placeholder="请输入" maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType!=='whws'">
              <el-form-item label="供应商类型" prop="unitNature">
                <el-select v-model="form.unitNature" placeholder="请选择" class="block">
                  <el-option
                    v-for="dict in dict.type.supplier_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="开户银行" prop="openingBank">
                <el-input v-model.trim="form.openingBank" placeholder="请输入" maxlength="30"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="基本账户账号" prop="basicAccount">
                <el-input v-model.trim="form.basicAccount" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="注册资本" prop="registeredCapital">
                <el-input v-model.trim="form.registeredCapital" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="营业期限" prop="operatingPeriod">
                <el-input v-model.trim="form.operatingPeriod" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="成立日期" prop="dateOfEstablishment">
                <el-input v-model.trim="form.dateOfEstablishment" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="登记机关" prop="registrationAndAuthority">
                <el-input v-model.trim="form.registrationAndAuthority" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="是否中小微企业" prop="whetherMicroEnterprises">
                <el-radio-group v-model="form.whetherMicroEnterprises">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="form.whetherMicroEnterprises===1">
              <el-form-item label="中小微企业" prop="microEnterprises">
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".png, .jpg, .jpeg, .pdf"
                  uploadName="microEnterprises"
                  :params="{
                    fileTypeName: 'microEnterprises'
                  }"
                  @onSuccess="handleUploadByOther"

                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="form.microEnterprises"
                    @click="previewFileByOther(form.microEnterprises)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="营业执照或组织机构代码证件扫描件" prop="businessLicense">
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".png, .jpg, .jpeg, .pdf"
                  uploadName="businessLicense"
                  :params="{
                    fileTypeName: 'businessLicense'
                  }"
                  @onSuccess="handleUploadByOther"

                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="form.businessLicense"
                    @click="previewFileByOther(form.businessLicense)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="附件（安全许可证、企业资质等）" prop="supplierAttachmentList">
                <file-upload
                  v-model="form.supplierAttachmentList"
                  :showTip="true"
                  :fileSize="50"
                  :fileType="['pdf','png','jpg','jpeg']"
                  uploadUrl="/epcfile/nonProjectFile/upload"
                  :params="{
                  fileTypeName: 'supplier_info_attachment'
                }"
                  @preview="previewFileByOther"
                  @down="downloadFileByOther"
                >
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card header="法定代表人信息" shadow="never" :body-style="{'padding': '15px 10px 0px 10px'}" class="card-box">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人姓名" prop="certificateName">
                <el-input v-model.trim="form.certificateName" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人证件类型" prop="certificate">
                <el-select v-model="form.certificate" placeholder="请选择" class="block">
                  <el-option
                    v-for="dict in dict.type.id_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人证件号码" prop="certificateCode">
                <el-input v-model.trim="form.certificateCode" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="经营负责人手机号码（法定代表人或实际控制人，不得使用其他联系人号码）" prop="contactNumber">
                <el-input v-model.trim="form.contactNumber" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label="法定代表人身份证扫描件（请将身份证正反面合并为同一份PDF或图片后上传）"
                prop="legalRepresentativeIdentityCertificate">
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".png, .jpg, .jpeg, .pdf"
                  uploadName="legalRepresentativeIdentityCertificate"
                  :params="{
                    fileTypeName: 'legalRepresentativeIdentityCertificate'
                  }"
                  @onSuccess="handleUploadByOther"

                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="form.legalRepresentativeIdentityCertificate"
                    @click="previewFileByOther(form.legalRepresentativeIdentityCertificate)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')" prop="infoReporterName">
                <el-input v-model.trim="form.infoReporterName" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporter')" prop="infoReporter">
                <el-select v-model="form.infoReporter" placeholder="请选择" class="block">
                  <el-option
                    v-for="dict in dict.type.id_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterCode')" prop="infoReporterCode">
                <el-input v-model.trim="form.infoReporterCode" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
                prop="infoReporterContactNumber">
                <el-input v-model.trim="form.infoReporterContactNumber" placeholder="请输入"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="text-center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import reg from '@/utils/reg';
import cityList from '@/assets/data/cityList';
import { fileUploadByOther } from '@/api/file';
import { listOrganize } from '@/api/system/organize'
import { addSupplier } from '@/api/purchaser/supplierAudit'

export default {
  name: 'AddInfo',
  dicts: ['supplier_type', 'organize_audit_status', 'sys_dict_translate', 'supplier_bid_type', 'id_type', 'sys_normal_disable', 'supplier_storage_type'],
  props: {
    userId: {
      type: Number,
      default: null
    },
    orgCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      // 是否显示弹出层
      isDialog: false,
      title: '',
      cityList,
      form: {},
      rules: {
        userName: [
          { required: true, trigger: 'blur', message: '请输入您的手机号' },
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { pattern: reg.password, message: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&', trigger: 'blur' }
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        orgCodeList: [{ required: true, message: '请选择归属组织', trigger: 'change' }],
        storageType: [{ required: true, message: '请选择入库状态', trigger: 'change' }],
        bidderName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '公司名称长度必须介于 2 和 50 之间', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        licNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.unifiedCreditCode, message: '格式不对', trigger: 'blur' }
        ],
        contactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        regionCode: [{ required: true, message: '请选择', trigger: 'change' }],
        contactAddress: [{ required: true, message: '请输入', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        openingBank: [{ required: true, message: '请输入', trigger: 'blur' }],
        basicAccount: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.bankCardNumber, message: '格式不对', trigger: 'blur' }
        ],
        unitNature: [{ required: true, message: '请选择', trigger: 'change' }],
        businessLicense: [{ required: true, message: '请上传', trigger: 'change' }],
        legalRepresentativeIdentityCertificate: [{ required: true, message: '请上传', trigger: 'change' }],
        certificate: [{ required: true, message: '请输入', trigger: 'change' }],
        certificateCode: [{ required: true, message: '请输入', trigger: 'blur' }],
        certificateName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        bidTypeList: [{ required: true, message: '请输入', trigger: 'change' }],
        registeredCapital: [{ required: true, message: '请输入', trigger: 'blur' }],
        operatingPeriod: [{ required: true, message: '请输入', trigger: 'blur' }],
        dateOfEstablishment: [{ required: true, message: '请输入', trigger: 'blur' }],
        registrationAndAuthority: [{ required: true, message: '请输入', trigger: 'blur' }],
        whetherMicroEnterprises: [{ required: true, message: '请选择', trigger: 'change' }],
        infoReporterName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        infoReporter: [{ required: true, message: '请输入', trigger: 'change' }],
        infoReporterCode: [{ required: true, message: '请输入', trigger: 'blur' }],
        infoReporterContactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ]
      },
      bidTypeNum: 0,
      initPassword: null,
      organizeList: []
    }
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('select.bidType.num').then(response => {
      this.bidTypeNum = Number(response.data);
    })
    this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.data
    })
    this.getOrganizeList()
  },
  methods: {
    /** 查询组织列表 */
    getOrganizeList() {
      listOrganize({}).then(response => {
        this.organizeList = response.data;
      })
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    async handleUploadByOther(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...')
            this.form.bidType = this.form.bidTypeList.join(',');
            this.form.nickName = this.form.bidderName;
            this.form.idNumber = this.form.licNumber;
            if (!this.form.id) {
              await addSupplier(this.form)
            } else {
              // await updateSupplierInfo(this.form)
            }
            this.$modal.msgSuccess(`提交成功`)
            this.isDialog = false
            this.$emit('success');
            this.$modal.closeLoading()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    },
    cancel() {
      this.isDialog = false;
      this.reset();
    },
    /** 点击报名 */
    async handleAdd() {
      this.reset()
      this.form.password = this.initPassword;
      this.form.orgCodeList = this.orgCode ? [this.orgCode] : [];
      this.title = '添加供应商';
      this.isDialog = true;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userName: null,
        nickName: null,
        idNumber: null,
        password: null,
        status: '0',
        orgCodeList: [],
        storageType: null,
        bidderName: null,
        licNumber: null,
        contactNumber: null,
        regionCode: null,
        contactAddress: null,
        email: null,
        openingBank: null,
        basicAccount: null,
        unitNature: null,
        businessLicense: null,
        legalRepresentativeIdentityCertificate: null,
        microEnterprises: null,
        certificate: null,
        certificateCode: null,
        certificateName: null,
        bidType: null,
        bidTypeList: [],
        registeredCapital: null,
        operatingPeriod: null,
        dateOfEstablishment: null,
        registrationAndAuthority: null,
        whetherMicroEnterprises: null,
        infoReporterName: null,
        infoReporter: null,
        infoReporterCode: null,
        infoReporterContactNumber: null,
        supplierAttachmentList: []
      }
      this.resetForm('form')
    }
  }
}
</script>

<style scoped>

</style>
