<template>
  <div class="app-container">
    <el-alert
      class="mb10"
      v-if="orgAuditStatus==='0'&&!reason"
      title="请审核"
      type="error"
      :closable="false"
      show-icon
    >
    </el-alert>
    <el-alert
      class="mb10"
      v-if="orgAuditStatus==='0'&&reason"
      title="请审核"
      type="error"
      :closable="false"
      :description="'上次审核未通过理由：'+reason"
      show-icon
    >
    </el-alert>
    <el-alert
      class="mb10"
      v-if="orgAuditStatus==='1'"
      title="审核未通过"
      type="error"
      :closable="false"
      :description="'不通过理由：'+reason"
      show-icon
    >
    </el-alert>

    <el-card
      header="主体信息"
      shadow="never"
      :body-style="{'padding': '15px 10px 10px 10px'}"
      class="card-box"
    >
      <el-descriptions direction="vertical" :column="3" border>
        <el-descriptions-item label="公司名称">
          {{ infoData.bidderName || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="统一社会信用代码">
          {{ infoData.licNumber || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="类别">
          <dict-tag :options="dict.type.supplier_bid_type" :value="infoData.bidType"/>
        </el-descriptions-item>
        <el-descriptions-item label="行政区域">
          {{ getParents(cityList, infoData.regionCode, 'code', 'name').join(' / ') || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="详细地址">
          {{ infoData.contactAddress || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="电子邮箱">
          {{ infoData.email || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="供应商类型" v-if="versionType!=='whws'">
          <dict-tag :options="dict.type.supplier_type" :value="infoData.unitNature"/>
        </el-descriptions-item>
        <el-descriptions-item label="开户银行">
          {{ infoData.openingBank || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="基本账户账号">
          {{ infoData.basicAccount || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="注册资本">
          {{ infoData.registeredCapital || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="营业期限">
          {{ infoData.operatingPeriod || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="成立日期">
          {{ infoData.dateOfEstablishment || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="登记机关">
          {{ infoData.registrationAndAuthority || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="是否中小微企业">
          {{ infoData.whetherMicroEnterprises === 1 ? '是' : '否' }}
        </el-descriptions-item>
        <el-descriptions-item label="中小微企业" v-if="infoData.whetherMicroEnterprises===1">
          <el-button
            type="primary"
            size="mini"
            v-if="infoData.microEnterprises"
            @click="previewFileByOther(infoData.microEnterprises)"
          >预览
          </el-button>
          <span v-else>/</span>
        </el-descriptions-item>
        <el-descriptions-item label="营业执照或组织机构代码证件扫描件">
          <el-button
            type="primary"
            size="mini"
            v-if="infoData.businessLicense"
            @click="previewFileByOther(infoData.businessLicense)"
          >
            预览
          </el-button>
          <span v-else>/</span>
        </el-descriptions-item>
        <el-descriptions-item label="附件（安全许可证、企业资质等）">
          <file-list-view
            v-if="infoData.supplierAttachmentList&&infoData.supplierAttachmentList.length>0"
            :fileList="infoData.supplierAttachmentList"
            :handlePreview="previewFileByOther"
            :handleDown="downloadFileByOther"
          />
          <span v-else>/</span>
        </el-descriptions-item>
        <el-descriptions-item label="入库申请单" v-if="versionType==='wzlg'">
          <el-button
            type="primary"
            size="mini"
            v-if="infoData.storageApplicationForm"
            @click="previewFileByOther(infoData.storageApplicationForm)"
          >
            预览
          </el-button>
          <span v-else>/</span>
        </el-descriptions-item>
        <el-descriptions-item label="龙港是否有分公司" v-if="versionType==='wzlg'">
          {{ infoData.lgWhetherBranchCompany === 1 ? '是' : infoData.lgWhetherBranchCompany === 0 ? '否' : '/' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card
      v-if="isCertificate==='1'"
      header="资格证书"
      shadow="never"
      :body-style="{'padding': '15px 10px 0px 10px'}"
      class="card-box"
    >
      <template v-for="(item,index) in infoData.supplierCertificateList">
        <el-descriptions direction="vertical" :column="3" border :key="index" class="mb10">
          <el-descriptions-item label="证书名称">
            {{ item.certificateName || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="证书到期时间">
            {{ item.certificateExpirationTime || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="证书等级">
            {{ item.certificateGrade || '/' }}
          </el-descriptions-item>
          <el-descriptions-item label="证书文件">
            <el-button
              type="primary"
              size="mini"
              v-if="item.certificateFile"
              @click="previewFileByOther(item.certificateFile)"
            >预览
            </el-button>
            <span v-else>/</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </el-card>

    <el-card
      header="法定代表人信息"
      shadow="never"
      :body-style="{'padding': '15px 10px 10px 10px'}"
      class="card-box"
    >
      <el-descriptions direction="vertical" :column="3" border>
        <el-descriptions-item label="法定代表人姓名">
          {{ infoData.certificateName || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="法定代表人证件类型">
          <dict-tag :options="dict.type.id_type" :value="infoData.certificate"/>
        </el-descriptions-item>
        <el-descriptions-item label="法定代表人证件号码">
          {{ infoData.certificateCode | formatIdCard() }}
        </el-descriptions-item>
        <el-descriptions-item label="经营负责人手机号码（法定代表人或实际控制人，不得使用其他联系人号码）">
          {{ infoData.contactNumber || '/' }}
        </el-descriptions-item>
        <el-descriptions-item label="法定代表人身份证扫描件">
          <el-button
            type="primary"
            size="mini"
            v-if="infoData.legalRepresentativeIdentityCertificate"
            @click="previewFileByOther(infoData.legalRepresentativeIdentityCertificate)"
          >预览
          </el-button>
          <span v-else>/</span>
        </el-descriptions-item>
        <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')">
          {{ infoData.infoReporterName || '/' }}
        </el-descriptions-item>
        <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporter')">
          <dict-tag :options="dict.type.id_type" :value="infoData.infoReporter"/>
        </el-descriptions-item>
        <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterCode')">
          {{ infoData.infoReporterCode | formatIdCard() }}
        </el-descriptions-item>
        <el-descriptions-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')">
          {{ infoData.infoReporterContactNumber || '/' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <div
      class="text-center mt30"
      v-if="orgAuditStatus==='0'&&checkPermi(['purchaser:supplier:audit'])"
    >
      <el-button type="primary" @click="submitAudit('2')">审核通过</el-button>
      <el-button type="danger" @click="submitAudit('1')">审核不通过</el-button>
    </div>

    <el-dialog
      title="入库状态"
      :visible.sync="auditDialog"
      width="90%"
      custom-class="maxW400"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="auditForm" :model="auditForm" :rules="rules" label-width="95px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="入库状态" prop="storageType">
              <el-select
                v-model="auditForm.storageType"
                placeholder="入库状态"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.supplier_storage_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAuditWhws">确 定</el-button>
        <el-button @click="auditDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { querySupplierInfoByAudit } from '@/api/purchaser/supplierAudit'
import { getParents } from '@/utils'
import cityList from '@/assets/data/cityList'
import { auditOrganize } from '@/api/system/organize';
import { checkPermi } from '@/utils/permission'

export default {
  name: 'SupplierInfoAudit',
  dicts: ['supplier_type', 'sys_dict_translate', 'supplier_bid_type', 'id_type', 'supplier_storage_type'],
  data() {
    return {
      cityList,
      supplierId: null,
      orgCode: null,
      orgAuditStatus: null, // 0-等待平台进行审核，1-审核失败，2-审核通过
      reason: null, // 审核不通过理由
      infoData: {
        bidderName: null,
        licNumber: null,
        contactNumber: null,
        regionCode: null,
        contactAddress: null,
        email: null,
        openingBank: null,
        basicAccount: null,
        unitNature: null,
        businessLicense: null,
        storageApplicationForm: null,
        lgWhetherBranchCompany: null,
        legalRepresentativeIdentityCertificate: null,
        microEnterprises: null,
        certificate: null,
        certificateCode: null,
        certificateName: null,
        bidType: null,
        registeredCapital: null,
        operatingPeriod: null,
        dateOfEstablishment: null,
        registrationAndAuthority: null,
        whetherMicroEnterprises: null,
        infoReporterName: null,
        infoReporter: null,
        infoReporterCode: null,
        infoReporterContactNumber: null,
        supplierAttachmentList: [],
        supplierCertificateList: []
      },
      auditDialog: false,
      auditForm: {
        storageType: null
      },
      rules: {
        storageType: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      isCertificate: null // 1 有证书功能
    }
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('supplier.certificate.function')
      .then(response => {
        this.isCertificate = response.data;
      })
    this.orgCode = this.$route.params.orgCode;
    this.supplierId = Number(this.$route.params.supplierId)
    this.queryInfo()
  },
  methods: {
    checkPermi,
    getParents,
    submitAuditWhws() {
      this.$refs['auditForm'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await auditOrganize({
              orgAuditStatus: '2',
              orgCode: this.orgCode,
              userId: this.supplierId,
              storageType: this.auditForm.storageType
            })
            this.$modal.msgSuccess('审核成功');
            this.auditDialog = false;
            await this.queryInfo()
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    submitAudit(orgAuditStatus) {
      this.$prompt(orgAuditStatus === '2' ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: orgAuditStatus !== '2',
        inputType: 'textarea',
        inputPlaceholder: orgAuditStatus !== '2' ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (orgAuditStatus === '2') {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 100
        },
        inputErrorMessage: '请输入不通过理由，最多100个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await auditOrganize({
            orgAuditStatus,
            orgCode: this.orgCode,
            userId: this.supplierId,
            reason: value
          })
          this.$modal.msgSuccess('审核成功')
          await this.queryInfo()
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    async queryInfo() {
      try {
        let { data } = await querySupplierInfoByAudit(this.supplierId);
        if (!data) {
          return
        }
        let obj = data.userOrgVoList.find(item => item.orgCode === this.orgCode);
        this.orgAuditStatus = obj ? obj.orgAuditStatus : null;
        this.reason = obj ? obj.reason : null;
        for (let key in this.infoData) {
          this.infoData[key] = data[key]
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
