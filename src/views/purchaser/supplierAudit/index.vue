<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="用户账号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户账号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入公司名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系方式" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入联系方式"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类别" prop="bidTypeList">
        <el-select
          v-model="queryParams.bidTypeList"
          multiple
          placeholder="类别"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.supplier_bid_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账号状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="账号状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="orgAuditStatus">
        <el-select
          v-model="queryParams.orgAuditStatus"
          placeholder="审核状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.organize_audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否黑名单" prop="blacklistStatus">
        <el-select
          v-model="queryParams.blacklistStatus"
          placeholder="是否黑名单"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.black_list_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入库状态" prop="storageType" v-if="versionType==='whws'">
        <el-select
          v-model="queryParams.storageType"
          placeholder="入库状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.supplier_storage_type"
            :key="dict.value"
            :label="dict.label"
            :value="Number(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <add-info
          v-if="checkPermi(['system:supplier:add'])&&versionType==='whws'"
          :orgCode="orgCode"
          @success="resetQuery"
        />
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:supplier:export']"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="userList" :span-method="objectSpanMethod" border>
      <el-table-column label="序号" align="center" prop="itemIndex" width="50"/>
      <el-table-column label="用户账号" align="center" key="userName" prop="userName" :show-overflow-tooltip="true"/>
      <el-table-column label="公司名称" align="center" key="nickName" prop="nickName" :show-overflow-tooltip="true"/>
      <el-table-column label="统一社会信用代码" align="center" key="idNumber" prop="idNumber" :show-overflow-tooltip="true"/>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')"
        align="center"
        key="infoReporterName"
        prop="infoReporterName"
        width="75"
      />
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
        align="center"
        key="infoReporterContactNumber"
        prop="infoReporterContactNumber"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="电子邮箱" align="center" key="email" prop="email" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商类型" align="center" key="unitNature" prop="unitNature" v-if="versionType!=='whws'">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.supplier_type" :value="row.unitNature"/>
        </template>
      </el-table-column>
      <el-table-column label="组织" align="center" key="orgName" prop="orgName" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          {{ row.orgName || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="orgAuditStatus" width="105">
        <template v-slot:default="{row}">
          <!--0-等待平台进行审核，1-审核失败，2-审核通过-->
          <dict-tag
            v-if="orgAuditStatusFormat(row)"
            :options="dict.type.organize_audit_status"
            :value="row.orgAuditStatus"
          />
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="企业信息" align="center" width="75">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(row)"
            v-hasPermi="['purchaser:supplier:query']"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="信用中国" align="center" width="75">
        <template v-slot:default="{row}">
          <el-link
            type="primary"
            :href="`https://www.creditchina.gov.cn/xinyongxinxixiangqing/xyDetail.html?keyword=${row.nickName}&tyshxydm=${row.idNumber}`"
            target="_blank">
            查看
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="入库状态" align="center" width="80" prop="storageType" v-if="versionType==='whws'">
        <template v-slot:default="{row}">
          <el-switch
            :disabled="!checkPermi(['system:supplier:storageType'])"
            v-model="row.storageType"
            :active-value="0"
            :inactive-value="1"
            @change="handleStorageTypeChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="信用等级" align="center" width="75" prop="supplierScore" v-if="commentLevelOff==='1'">
        <template v-slot:default="{row}">
          <dict-tag
            :options="dict.type.credit_rating"
            :value="row.supplierScore"
          />
        </template>
      </el-table-column>
      <el-table-column label="项目统计" align="center">
        <el-table-column label="参与报名次数" align="center" key="signCount" prop="signCount" width="75"></el-table-column>
        <el-table-column label="中标次数" align="center" key="winCount" prop="winCount" width="75"></el-table-column>
        <el-table-column label="邀约未报名次数" align="center" key="inviteNum" prop="inviteNum" width="75"></el-table-column>
      </el-table-column>
      <el-table-column label="账号状态" align="center" key="status" width="75">
        <template slot-scope="scope">
          <el-switch
            :disabled="!checkPermi(['system:user:status'])"
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="是否申请停用账号" align="center" key="state" width="75" v-if="versionType==='wzlg'">
        <template slot-scope="scope">
          {{ scope.row.state === 0 ? '该供应商已申请停用账号' : '/' }}
        </template>
      </el-table-column>
      <el-table-column label="是否黑名单" align="center" key="blacklistStatus" width="70">
        <template v-slot:default="{row}">
          <dict-tag
            v-if="orgAuditStatusFormat(row)"
            :options="dict.type.black_list_status"
            :value="row.blacklistStatus || 0"
          />
          <el-popover trigger="click" placement="top" v-if="row.blacklistStatus===1">
            <el-descriptions :column="1" border :labelStyle="{width:'110px'}" style="max-width: 600px;">
              <el-descriptions-item label="开始时间">
                {{ row.blacklistLiftStartTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
              </el-descriptions-item>
              <el-descriptions-item label="截止时间">
                {{ row.blacklistLiftEndTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
              </el-descriptions-item>
              <el-descriptions-item label="处罚措施">{{ row.blacklistReason }}</el-descriptions-item>
              <el-descriptions-item label="操作人">{{ row.addBlacklistUser }}</el-descriptions-item>
            </el-descriptions>
            <el-button
              v-if="row.blacklistStatus===1"
              slot="reference"
              size="mini"
              type="text"
            >查看详情
            </el-button>
          </el-popover>

        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="135">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope" v-if="scope.row.userId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleComment(scope.row)"
            v-hasPermi="['purchaser:supplier:comment']"
          >查看评论
          </el-button>
          <el-button
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user:remove']"
          >删除
          </el-button>
          <el-dropdown
            size="mini"
            @command="(command) => handleCommand(command, scope.row)"
            v-hasPermi="['system:user:resetPwd', 'purchaser:supplier:modifyUser','supplier:black:operate','system:supplier:grade']"
          >
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="handleResetPwd"
                icon="el-icon-key"
                v-hasPermi="['system:user:resetPwd']"
              >重置密码
              </el-dropdown-item>
              <el-dropdown-item
                command="handleUpdate"
                icon="el-icon-edit"
                v-hasPermi="['purchaser:supplier:modifyUser']"
              >修改账号
              </el-dropdown-item>
              <el-dropdown-item
                command="handleAddBlack"
                icon="el-icon-circle-close"
                v-hasPermi="['supplier:black:operate']"
              >加入黑名单
              </el-dropdown-item>
              <el-dropdown-item
                command="handleRemoveBlack"
                icon="el-icon-circle-check"
                v-hasPermi="['supplier:black:operate']"
              >移出黑名单
              </el-dropdown-item>
              <el-dropdown-item
                command="handleSetGrade"
                icon="el-icon-edit"
                v-if="commentLevelOff==='1'&&checkPermi(['system:supplier:grade'])"
              >设置信用等级
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog title="修改用户账号" :visible.sync="isDialog" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" @submit.native.prevent>
        <el-row>
          <el-col>
            <el-form-item label="登录账号" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入登录账号" maxlength="30"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="'【'+supplierName+'】评论详情'"
      :visible.sync="isOpen"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-table :data="commentList" height="400" border>
        <el-table-column label="序号" align="center" type="index" width="55"/>
        <el-table-column label="评论内容" align="center" prop="commentContent"/>
        <el-table-column label="项目名称" align="center" prop="buyItemName"/>
        <el-table-column label="标段(包)" align="center" prop="subpackageName"/>
        <el-table-column label="评论人" align="center" prop="createBy" width="120"/>
        <el-table-column label="评论时间" align="center" width="160">
          <template v-slot:default="{row}">
            {{ row.commentTime | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="加入黑名单"
      :visible.sync="openDialog"
      width="90%"
      custom-class="maxW600"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="blackForm" :model="blackForm" :rules="rules" label-width="95px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="截止时间" prop="blacklistLiftEndTime">
              <el-date-picker
                class="block"
                style="width: 100%"
                v-model="blackForm.blacklistLiftEndTime"
                type="datetime"
                placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处罚措施" prop="blacklistReason">
              <el-input v-model.trim="blackForm.blacklistReason" type="textarea" :rows="3" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddBlack">确 定</el-button>
        <el-button @click="openDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="设置信用等级"
      :visible.sync="gradeDialog"
      width="90%"
      custom-class="maxW400"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form ref="gradeForm" :model="gradeForm" :rules="rules" label-width="95px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="信用等级" prop="grade">
              <el-select
                v-model="gradeForm.grade"
                placeholder="信用等级"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.credit_rating"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGrade">确 定</el-button>
        <el-button @click="gradeDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { changeUserStatus, delUser, getUser, resetUserPwd, updateBlacklist, updateUser } from '@/api/system/user'
import { editSupplierGrade, querySupplierList, updateStorageType } from '@/api/purchaser/supplierAudit';
import { checkPermi } from '@/utils/permission';
import reg from '@/utils/reg';
import { getSupplierCommentAll } from '@/api/commentSupplier'
import addInfo from '@/views/purchaser/supplierAudit/addInfo.vue'

export default {
  name: 'SupplierAudit',
  dicts: ['sys_normal_disable', 'organize_audit_status', 'supplier_bid_type', 'black_list_status', 'credit_rating', 'sys_dict_translate', 'supplier_storage_type', 'supplier_type'],
  components: {
    addInfo
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 日期范围
      dateRange: [],
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bidTypeList: [],
        userName: undefined,
        nickName: undefined,
        phonenumber: undefined,
        status: undefined,
        blacklistStatus: undefined,
        orgAuditStatus: undefined,
        storageType: undefined
      },
      isDialog: false,
      form: {},
      rules: {
        userName: [
          { required: true, message: '登录账号不能为空', trigger: 'blur' },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        blacklistLiftEndTime: [
          { required: true, message: '请设置时间', trigger: 'change' }
        ],
        blacklistReason: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 250, message: '最长250个字符', trigger: 'blur' }
        ],
        grade: [
          { required: true, message: '请输入', trigger: 'change' }
        ]
      },
      // 默认密码
      initPassword: undefined,
      orgCode: null,
      spanArr: [],
      pos: 0,
      supplierName: null,
      isOpen: false,
      commentList: [],
      openDialog: false,
      blackForm: {},
      gradeForm: {
        grade: null,
        supplierId: null
      },
      gradeDialog: false,
      commentLevelOff: '0'
    }
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getList()
    this.getConfigKey('sys.user.initPassword').then(response => {
      this.initPassword = response.data
    })
    this.getConfigKey('sys.commentLevel.off').then(response => {
      this.commentLevelOff = response.data || '0'
    })
  },
  methods: {
    checkPermi,
    // 设置入库状态
    handleStorageTypeChange(row) {
      let text = row.storageType === 0 ? '入库' : '非入库';
      this.$modal.confirm('确认要将该供应商设置为"' + text + '"吗').then(function () {
        return updateStorageType({ userId: row.userId, storageType: row.storageType });
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.storageType = row.storageType === 1 ? 0 : 1;
      })
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleUpdate':
          this.handleUpdate(row)
          break
        case 'handleAddBlack':
          this.handleAddBlack(row)
          break
        case 'handleRemoveBlack':
          this.handleRemoveBlack(row)
          break
        case 'handleSetGrade':
          this.handleSetGrade(row)
          break
        default:
          break
      }
    },
    submitGrade() {
      this.$refs['gradeForm'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await editSupplierGrade(this.gradeForm);
            this.$modal.msgSuccess('设置成功');
            this.gradeDialog = false;
            await this.getList()
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    handleSetGrade(row) {
      this.resetForm('gradeForm');
      this.gradeForm.grade = row.grade;
      this.gradeForm.supplierId = row.userId;
      this.gradeDialog = true;
    },
    handleAddBlack(row) {
      this.openDialog = true;
      this.resetBlackForm();
      this.blackForm.userId = row.userId;
    },
    async handleRemoveBlack(row) {
      try {
        await updateBlacklist({ userId: row.userId })
        this.$modal.msgSuccess('移出黑名单成功')
        await this.getList()
      } catch (e) {
        throw new Error(e);
      }
    },
    submitAddBlack() {
      this.$refs['blackForm'].validate(valid => {
        if (valid) {
          updateBlacklist(this.blackForm).then(response => {
            this.$modal.msgSuccess('加入黑名单成功')
            this.openDialog = false
            this.getList()
          })
        }
      })
    },
    resetBlackForm() {
      this.blackForm = {
        userId: null,
        blacklistLiftEndTime: null,
        blacklistReason: null
      }
      this.resetForm('blackForm')
    },
    orgAuditStatusFormat(row) {
      return this.selectDictLabel(this.dict.type.organize_audit_status, row.orgAuditStatus)
    },
    async handleComment(row) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getSupplierCommentAll(row.userId);
        this.commentList = data || [];
        this.supplierName = row.nickName;
        this.isOpen = true;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/supplierExport ', {
        storageType: this.queryParams.storageType
      }, `供应商_${new Date().getTime()}.xlsx`)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateUser(this.form).then(response => {
            this.$modal.msgSuccess('修改成功')
            this.isDialog = false
            this.getList()
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.isDialog = false
      this.reset()
    },
    /** 修改账号按钮操作 */
    handleUpdate(row) {
      this.reset()
      const userId = row.userId
      getUser(userId).then(response => {
        this.form = response.data
        this.form.postIds = response.postIds;
        this.form.roleIds = response.roleIds;
        let sysOrganizes = response.data.sysOrganizes || [];
        this.$set(this.form, 'orgCodeList', sysOrganizes.map(v => v.orgCode));
        this.isDialog = true
        this.form.password = '';
      })
    },
    reset() {
      this.form = {
        userId: undefined,
        userName: undefined
      }
      this.resetForm('form')
    },
    handleLook(row) {
      this.$router.push({ name: 'SupplierInfoAudit', params: { supplierId: row.userId, orgCode: row.orgCode }})
    },
    /** 查询用户列表 */
    async getList() {
      if (this.dateRange.length > 0) {
        this.queryParams.createTimeStart = this.dateRange[0]
        this.queryParams.createTimeEnd = this.dateRange[1]
      } else {
        this.queryParams.createTimeStart = null
        this.queryParams.createTimeEnd = null
      }
      try {
        this.loading = true
        let { rows, total } = await querySupplierList({
          orgCode: checkPermi(['purchaser:supplier:queryAll']) ? null : this.orgCode,
          ...this.queryParams
        });
        let list = rows || [];
        this.userList = [];
        list.forEach((item, index) => {
          if (item.userOrgVoList.length > 0) {
            item.userOrgVoList.forEach(v => {
              this.userList.push({
                ...item,
                orgCode: v.orgCode,
                orgName: v.orgName,
                orgAuditStatus: v.orgAuditStatus,
                itemIndex: index + 1
              })
            })
          } else {
            this.userList.push({
              ...item,
              orgCode: null,
              orgName: null,
              orgAuditStatus: null,
              itemIndex: index + 1
            })
          }
        })
        this.getSpanArr(this.userList);
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false
        throw new Error(e);
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      let tipMsg = '请输入该用户的用户账号+公司名称，如：12345678易建采科技（武汉）有限公司';
      let tipHtml = `<p style="color: #ff4949;font-size: 18px;margin-bottom: 10px;">是否确认删除该用户数据？</p>
           <p style="color: #ff4949">删除之后该用户数据将完全从系统中删除，包括用户登录账号、用户信息等，且删除之后无法恢复！</p>
      <p style="font-size: 14px;text-align: left;margin-top: 30px;">${tipMsg}</p>`
      this.$prompt(tipHtml, '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showInput: true,
        inputPlaceholder: '',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && (value === row.userName + row.nickName)
        },
        inputErrorMessage: `${tipMsg}`
      }).then(async ({ value }) => {
        try {
          if (value !== (row.userName + row.nickName)) {
            this.$modal.msgError(tipMsg);
            return
          }
          this.$modal.loading('数据提交中，请稍候...');
          await delUser(userIds, this.orgCode);
          await this.getList()
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    /** 用户状态修改 */
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '"该用户吗？').then(function () {
        return changeUserStatus(row.userId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValue: this.initPassword,
        inputPattern: reg.password,
        inputErrorMessage: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&'
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess('修改成功，新密码是：' + value)
        })
      }).catch(() => {
      })
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [8, 9, 10]
      if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].userId === data[i - 1].userId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
