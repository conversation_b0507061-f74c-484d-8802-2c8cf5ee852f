<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
    >
      <el-form-item
        label="会议室名称"
        prop="name"
      >
        <el-input
          v-model="queryParams.name"
          placeholder="请输入会议室名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="状态"
        prop="status"
      >
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['meeting:room:add']"
        >
          新增
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
    >
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="55"
      />
      <el-table-column
        label="会议室名称"
        align="center"
        prop="name"
      />
      <!--      <el-table-column-->
      <!--        label="开放时间段"-->
      <!--        align="center"-->
      <!--        width="300px"-->
      <!--      >-->
      <!--        <template v-slot:default="{row}">-->
      <!--          {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}-->
      <!--          - -->
      <!--          {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column
        label="状态"
        align="center"
        prop="status"
      >
        <template v-slot:default="{row}">
          <el-switch
            :disabled="!checkPermi(['meeting:room:edit'])"
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
      >
        <template v-slot:default="{row}">
          {{ row.remark || '/' }}
        </template>
      </el-table-column>
      <el-table-column
        label="预定情况"
        align="center"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleReservation(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['meeting:room:edit']"
            @click="handleEdit(row)"
          >修改
          </el-button>
          <el-button
            class="btn-text-danger"
            v-hasPermi="['meeting:room:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="会议室"
      :visible.sync="isOpen"
      custom-class="maxW600"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        size="small"
        label-width="100px"
      >
        <el-form-item
          label="会议室名称"
          prop="name"
        >
          <el-input v-model.trim="form.name" placeholder="请输入"></el-input>
        </el-form-item>
        <!--        <el-form-item-->
        <!--          label="开放时间"-->
        <!--          prop="dateRange"-->
        <!--        >-->
        <!--          <el-date-picker-->
        <!--            v-model="form.dateRange"-->
        <!--            value-format="yyyy-MM-dd HH:mm:ss"-->
        <!--            type="datetimerange"-->
        <!--            range-separator="至"-->
        <!--            start-placeholder="开始时间"-->
        <!--            end-placeholder="结束时间"-->
        <!--          ></el-date-picker>-->
        <!--        </el-form-item>-->
        <el-form-item
          label="状态"
          prop="status"
        >
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.remark"
            placeholder="请输入"
            maxlength="250"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isOpen = false">关闭</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="预定情况"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="5vh"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="mb10">会议室名称：{{ roomName }}</p>
      <!--      <el-form-->
      <!--        :model="queryReservation"-->
      <!--        ref="queryReservation"-->
      <!--        size="small"-->
      <!--        :inline="true"-->
      <!--      >-->
      <!--        <el-form-item-->
      <!--          label="审核状态"-->
      <!--          prop="audit"-->
      <!--        >-->
      <!--          <el-select-->
      <!--            v-model="queryReservation.audit"-->
      <!--            placeholder="请选择审核状态"-->
      <!--            clearable-->
      <!--            style="width: 240px"-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="dict in dict.type.meeting_room_audit_status"-->
      <!--              :key="dict.value"-->
      <!--              :label="dict.label"-->
      <!--              :value="dict.value"-->
      <!--            />-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--        <el-form-item>-->
      <!--          <el-button-->
      <!--            type="primary"-->
      <!--            icon="el-icon-search"-->
      <!--            size="mini"-->
      <!--            @click="handleReservationQuery"-->
      <!--          >-->
      <!--            搜索-->
      <!--          </el-button>-->
      <!--          <el-button-->
      <!--            icon="el-icon-refresh"-->
      <!--            size="mini"-->
      <!--            @click="resetReservationQuery"-->
      <!--          >-->
      <!--            重置-->
      <!--          </el-button>-->
      <!--        </el-form-item>-->
      <!--      </el-form>-->
      <el-table
        v-loading="reservationLoading"
        :data="reservationList"
        border
      >
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <el-table-column
          label="预定人"
          align="center"
          prop="nikeName"
        />
        <el-table-column
          label="用途"
          align="center"
          prop="reason"
        />
        <el-table-column
          label="使用时间段"
          align="center"
          width="300px"
        >
          <template v-slot:default="{row}">
            {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
            -
            {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          label="审核状态"-->
        <!--          align="center"-->
        <!--          prop="audit"-->
        <!--        >-->
        <!--          <template v-slot:default="{row}">-->
        <!--            <dict-tag-->
        <!--              :options="dict.type.meeting_room_audit_status"-->
        <!--              :value="row.audit"-->
        <!--            />-->
        <!--            <div-->
        <!--              class="td-tip"-->
        <!--              v-if="row.audit=='2'"-->
        <!--            >-->
        <!--              理由：{{ row.statement || '/' }}-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column-->
        <!--          label="操作"-->
        <!--          align="center"-->
        <!--          v-if="checkPermi(['meeting:room:audit'])"-->
        <!--        >-->
        <!--          <template v-slot:default="{row}">-->
        <!--            <div v-if="row.audit=='0'">-->
        <!--              <el-button-->
        <!--                size="mini"-->
        <!--                type="text"-->
        <!--                icon="el-icon-check"-->
        <!--                @click="handleAudit(row,'1')"-->
        <!--              >审核通过-->
        <!--              </el-button>-->
        <!--              <el-button-->
        <!--                size="mini"-->
        <!--                type="text"-->
        <!--                icon="el-icon-close"-->
        <!--                @click="handleAudit(row,'2')"-->
        <!--              >审核不通过-->
        <!--              </el-button>-->
        <!--            </div>-->
        <!--            <span v-else>/</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination
        v-show="reservationTotal>0"
        :total="reservationTotal"
        :page.sync="queryReservation.pageNum"
        :limit.sync="queryReservation.pageSize"
        @pagination="getReservationList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import {
  addRoom,
  auditReservation,
  editRoom,
  enabledRoom,
  queryRoomList,
  queryRoomReservation,
  removeRoom
} from '@/api/purchaser/meetingRoom'
import { mapGetters } from 'vuex'

export default {
  name: 'MeetingRoom',
  dicts: ['sys_normal_disable', 'meeting_room_audit_status'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      loading: false,
      total: 0,
      tableData: [],
      isOpen: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        // dateRange: [{ required: true, message: '请选择', trigger: 'change' }],
        status: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      isDialog: false,
      reservationLoading: false,
      queryReservation: {
        pageNum: 1,
        pageSize: 10,
        audit: null
      },
      reservationList: [],
      reservationTotal: 0,
      roomId: null,
      roomName: null
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleAudit(row, audit) {
      this.$prompt(audit == '1' ? '确定审核通过?' : '请输入不通过理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: audit != '1',
        inputType: 'textarea',
        inputPlaceholder: audit != '1' ? '请输入不通过理由' : '',
        inputValidator: (value) => {
          if (audit == '1') {
            return true
          }
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 200
        },
        inputErrorMessage: '请输入不通过理由，最多200个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...')
          await auditReservation({
            roomId: row.roomId,
            roomScheduleId: row.roomScheduleId,
            audit: audit,
            statement: value
          })
          this.$modal.msgSuccess('操作成功');
          this.queryReservation.pageNum = 1;
          await this.getReservationList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading()
          throw e
        }
      }).catch(() => {
      })
    },
    handleReservation(row) {
      this.roomId = row.roomId;
      this.roomName = row.name;
      this.queryReservation.pageNum = 1;
      this.isDialog = true;
      this.getReservationList();
    },
    /** 搜索按钮操作 */
    handleReservationQuery() {
      this.queryReservation.pageNum = 1
      this.getReservationList()
    },
    /** 重置按钮操作 */
    resetReservationQuery() {
      this.resetForm('queryReservation');
      this.handleReservationQuery()
    },
    async getReservationList() {
      try {
        this.reservationLoading = true;
        let { rows, total } = await queryRoomReservation({
          ...this.queryReservation,
          roomId: this.roomId
        })
        this.reservationList = rows || [];
        this.reservationTotal = total;
        this.reservationLoading = false;
      } catch (e) {
        this.reservationLoading = false;
        throw new Error(e);
      }
    },
    handleStatusChange(row) {
      let text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"会议室吗？').then(function () {
        return enabledRoom({ roomId: row.roomId, status: row.status })
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function () {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    handleDelete(row) {
      this.$alert('确定要删除该会议室？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        this.$modal.loading('数据提交中，请稍候...')
        await removeRoom([row.roomId])
        this.$modal.msgSuccess('删除成功');
        await this.getList();
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleAdd() {
      this.reset();
      this.isOpen = true;
    },
    handleEdit(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }
      // this.form.dateRange = [this.formatTime(row.startTime, 'YYYY-MM-DD HH:mm:ss'), this.formatTime(row.endTime, 'YYYY-MM-DD HH:mm:ss')];
      this.form.startTime = null;
      this.form.endTime = null;
      this.isOpen = true;
    },
    onSubmit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            // this.form.startTime = this.form.dateRange && this.form.dateRange.length == 2 ? this.form.dateRange[0] : null;
            // this.form.endTime = this.form.dateRange && this.form.dateRange.length == 2 ? this.form.dateRange[1] : null;
            if (!this.form.roomId) {
              await addRoom(this.form);
            } else {
              await editRoom(this.form);
            }
            this.$modal.msgSuccess('操作成功');
            this.isOpen = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    },
    reset() {
      this.form = {
        roomId: null,
        name: null,
        status: '0',
        remark: null,
        // dateRange: [],
        startTime: null,
        endTime: null
      }
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await queryRoomList(this.queryParams);
        this.total = total;
        this.tableData = rows || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
