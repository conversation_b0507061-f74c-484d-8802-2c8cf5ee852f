<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
    >
      <el-form-item
        label="会议室名称"
        prop="name"
      >
        <el-input
          v-model="queryParams.name"
          placeholder="请输入会议室名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleMyReservation"
        >
          我的预定
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
    >
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="55"
      />
      <el-table-column
        label="会议室名称"
        align="center"
        prop="name"
      />
      <!--      <el-table-column-->
      <!--        label="开放时间段"-->
      <!--        align="center"-->
      <!--      >-->
      <!--        <template v-slot:default="{row}">-->
      <!--          {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }} - {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column
        label="预定情况"
        align="center"
      >
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleReservation(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template v-slot:default="{row}">
          <el-button
            v-hasPermi="['room:reservation:add']"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleApply(row)"
          >预定
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="预定"
      :visible.sync="isOpen"
      custom-class="maxW600"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        size="small"
        label-width="100px"
      >
        <el-form-item
          label="使用时间"
          prop="dateRange"
        >
          <el-date-picker
            v-model="form.dateRange"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item
          label="用途"
          prop="reason"
        >
          <el-input
            type="textarea"
            :rows="3"
            resize="none"
            v-model.trim="form.reason"
            placeholder="请输入"
            maxlength="250"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="isOpen = false"
        >
          关闭
        </el-button>
        <el-button
          type="primary"
          @click="onSubmit"
        >
          确定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="预定情况"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="5vh"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="mb10">会议室名称：{{ roomName }}</p>
      <el-table
        v-loading="reservationLoading"
        :data="reservationList"
        border
      >
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <el-table-column
          label="预定人"
          align="center"
          prop="nikeName"
        />
        <el-table-column
          label="用途"
          align="center"
          prop="reason"
        />
        <el-table-column
          label="使用时间段"
          align="center"
          width="300px"
        >
          <template v-slot:default="{row}">
            {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
            -
            {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          label="审核状态"-->
        <!--          align="center"-->
        <!--          prop="audit"-->
        <!--        >-->
        <!--          <template v-slot:default="{row}">-->
        <!--            <dict-tag-->
        <!--              :options="dict.type.meeting_room_audit_status"-->
        <!--              :value="row.audit"-->
        <!--            />-->
        <!--            <div-->
        <!--              class="td-tip"-->
        <!--              v-if="row.audit=='2'"-->
        <!--            >-->
        <!--              理由：{{ row.statement || '/' }}-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination
        v-show="reservationTotal>0"
        :total="reservationTotal"
        :page.sync="queryReservation.pageNum"
        :limit.sync="queryReservation.pageSize"
        @pagination="getReservationList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="我的预定"
      :visible.sync="isMyDialog"
      custom-class="maxW1200"
      top="5vh"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table
        :data="myReservation"
        border
      >
        <el-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <el-table-column
          label="会议室名称"
          align="center"
          prop="name"
        />
        <el-table-column
          label="用途"
          align="center"
          prop="reason"
        />
        <el-table-column
          label="使用时间段"
          align="center"
          width="300px"
        >
          <template v-slot:default="{row}">
            {{ row.startTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
            -
            {{ row.endTime | formatTime('YYYY-MM-DD HH:mm:ss','/') }}
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          label="审核状态"-->
        <!--          align="center"-->
        <!--          prop="audit"-->
        <!--        >-->
        <!--          <template v-slot:default="{row}">-->
        <!--            <dict-tag-->
        <!--              :options="dict.type.meeting_room_audit_status"-->
        <!--              :value="row.audit"-->
        <!--            />-->
        <!--            <div-->
        <!--              class="td-tip"-->
        <!--              v-if="row.audit=='2'"-->
        <!--            >-->
        <!--              理由：{{ row.statement || '/' }}-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column
          label="操作"
          align="center"
        >
          <template v-slot:default="{row}">
            <el-button
              class="btn-text-danger"
              v-hasPermi="['room:reservation:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isMyDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import { addReservation, queryMyReservation, queryRoomList, queryRoomReservation, removeReservation } from '@/api/purchaser/meetingRoom'
import { mapGetters } from 'vuex'

export default {
  name: 'Reservation',
  dicts: ['meeting_room_audit_status'],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: '0'
      },
      loading: false,
      total: 0,
      tableData: [],
      isOpen: false,
      form: {},
      rules: {
        dateRange: [{ required: true, message: '请选择', trigger: 'change' }],
        reason: [{ required: false, message: '请输入', trigger: 'blur' }]
      },
      isDialog: false,
      reservationLoading: false,
      queryReservation: {
        pageNum: 1,
        pageSize: 10
      },
      reservationList: [],
      reservationTotal: 0,
      roomId: null,
      roomName: null,
      isMyDialog: false,
      myReservation: []
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await removeReservation([row.roomScheduleId]);
          await this.handleMyReservation();
          this.$modal.msgSuccess('删除成功');
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw e;
        }
      }).catch(() => {
      });
    },
    async handleMyReservation() {
      try {
        let { data } = await queryMyReservation()
        this.myReservation = data || [];
        this.isMyDialog = true;
      } catch (e) {
        throw new Error(e);
      }
    },
    handleReservation(row) {
      this.roomId = row.roomId;
      this.roomName = row.name;
      this.isDialog = true;
      this.getReservationList();
    },
    async getReservationList() {
      try {
        this.reservationLoading = true;
        let { rows, total } = await queryRoomReservation({
          ...this.queryReservation,
          roomId: this.roomId
        })
        this.reservationList = rows || [];
        this.reservationTotal = total;
        this.reservationLoading = false;
      } catch (e) {
        this.reservationLoading = false;
        throw new Error(e);
      }
    },
    handleApply(row) {
      this.reset();
      this.form.roomId = row.roomId;
      this.isOpen = true;
    },
    onSubmit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.startTime = this.form.dateRange && this.form.dateRange.length == 2 ? this.form.dateRange[0] : null;
            this.form.endTime = this.form.dateRange && this.form.dateRange.length == 2 ? this.form.dateRange[1] : null;
            await addReservation(this.form);
            this.$modal.msgSuccess('操作成功');
            this.isOpen = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    },
    reset() {
      this.form = {
        roomId: null,
        reason: null,
        dateRange: [],
        startTime: null,
        endTime: null
      }
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await queryRoomList(this.queryParams);
        this.total = total;
        this.tableData = rows || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
