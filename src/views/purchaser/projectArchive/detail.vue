<template>
  <div class="app-container">
    <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
    <el-checkbox-group v-model="checkedList" @change="handleCheckedCitiesChange">
      <el-collapse v-model="activeNames" class="process-collapse descriptions-wrap">
        <el-collapse-item :title="buyItemName" :name="buyItemCode">
          <el-descriptions :column="2" border>
            <el-descriptions-item
              v-for="(item,index) in projectFileList"
              :key="index"
              :label="item.fileName"
              contentClassName="my-label"
            >
              <template slot="label">
                <el-checkbox :label="item.fileKey" :key="item.fileKey">{{ item.fileName }}</el-checkbox>
              </template>
              <el-button type="text" size="mini" @click="previewFile(item.fileKey)">预览</el-button>
              <el-button type="text" size="mini" @click="handleDown(item.fileKey)">下载</el-button>
              <span class="ml20">{{ item.createTime }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>

        <el-collapse-item
          v-for="(item,index) in subpackageList"
          :key="index"
          :title="item.subpackageName"
          :name="item.subpackageCode"
        >
          <el-descriptions :column="2" border>
            <el-descriptions-item
              v-for="(item,index) in item.fileInfoVoList"
              :key="index"
              :label="item.fileName"
              contentClassName="my-label"
            >
              <template slot="label">
                <el-checkbox :label="item.fileKey" :key="item.fileKey">{{ item.fileName }}</el-checkbox>
              </template>
              <el-button type="text" size="mini" @click="previewFile(item.fileKey)">预览</el-button>
              <el-button type="text" size="mini" @click="handleDown(item.fileKey)">下载</el-button>
              <span class="ml20">{{ item.createTime }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
    </el-checkbox-group>
    <div class="text-center mt30 mb50" v-hasPermi="['project:archive:pack']">
      <el-button type="primary" @click="handleBatchDown">批量下载</el-button>
      <el-button type="primary" @click="handlePack">一键打包</el-button>
    </div>
  </div>
</template>

<script>
import { downloadFileList, projectFilesByZip } from '@/api/purchaser/projectArchive';
import { getProjectFiles } from '@/api/purchaser/projectFiles'
import { blobValidate } from '@/utils/ruoyi'

export default {
  name: 'ProjectArchiveDetail',
  data() {
    return {
      buyItemCode: null,
      buyItemName: null,
      yearMonthSplit: null,
      projectFileList: [],
      subpackageList: [],
      activeNames: [],
      activeName: null,
      isIndeterminate: true,
      checkAll: false,
      checkedList: [],
      allFileList: []
    }
  },
  created() {
    this.buyItemCode = this.$route.params.buyItemCode;
    this.getDetail();
  },
  computed: {},
  methods: {
    handleCheckAllChange(val) {
      console.log(val)
      console.log(this.allFileList)
      this.checkedList = val ? this.allFileList : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.allFileList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.allFileList.length;
      console.log(this.checkedList)
    },
    async handleBatchDown() {
      try {
        if (this.checkedList.length === 0) {
          this.$modal.msgWarning('请选择下载需要下载的文件');
          return
        }
        this.$modal.loading('数据下载中，请稍候...');
        let res = await downloadFileList({
          buyItemCode: this.buyItemCode,
          fileKeyList: this.checkedList
        });
        this.$modal.closeLoading()
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          const blob = new Blob([res.data])
          this.$download.saveAs(blob, this.$download.getFileName(res))
        } else {
          await this.$download.printErrMsg(res.data)
        }
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    async handlePack() {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { msg } = await projectFilesByZip({
          buyItemCode: this.buyItemCode,
          yearMonthSplit: this.yearMonthSplit
        });
        this.$modal.msgSuccess(msg);
        await this.getDetail();
        this.$modal.closeLoading()
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    async getDetail() {
      try {
        let { data } = await getProjectFiles(this.buyItemCode);
        this.buyItemName = data.buyItemName;
        this.yearMonthSplit = data.yearMonthSplit;
        this.projectFileList = data.fileInfoVoList;
        this.subpackageList = data.subpackageFileVoList || [];
        this.activeNames = [this.buyItemCode].concat(this.subpackageList.map(item => item.subpackageCode));
        this.activeName = this.activeNames[0];
        this.allFileList = this.projectFileList.map(item => item.fileKey);
        this.subpackageList.forEach(item => {
          let fileInfoVoList = item.fileInfoVoList || [];
          let list = fileInfoVoList.map(v => v.fileKey)
          this.allFileList = this.allFileList.concat(list)
        })
      } catch (e) {
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss">
.descriptions-wrap {
  .el-descriptions__header {
    margin-bottom: 0;
    background: #fafafa;
    border: 1px solid #e6ebf5;
    border-bottom: none;
    padding: 10px;
    line-height: 1.5;

    .el-descriptions__title {
      font-weight: normal;
      font-size: 14px;
    }
  }

  .my-label {
    min-width: 150px;
  }

  .el-checkbox {
    white-space: normal !important;
    display: flex;
  }

  .el-checkbox__label {
    white-space: normal !important;
  }
}
</style>
