<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
        prop="entity.purchaseMethodCode"
      >
        <el-select
          v-model="queryParams.entity.purchaseMethodCode"
          :placeholder="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in purchase_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')" prop="entity.buyClass" v-if="versionType!=='wzlg'">
        <el-select
          v-model="queryParams.entity.buyClass"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyClass')"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.project_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="tableData" border max-height="600">
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
        align="center"
        prop="buyItemName"
        :show-overflow-tooltip="true"/>
      <el-table-column label="项目编号" align="center" prop="buyItemCode" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          {{ row.innerCode || row.buyItemCode }}
        </template>
      </el-table-column>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate, 'purchaseMethod')"
        align="center"
        prop="purchaseMethodCode"
        :show-overflow-tooltip="true"
      >
        <template v-slot:default="{row}">
          {{ purchaseMethodFormat(row.purchaseMethodCode) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="versionType!=='wzlg'"
        :label="selectDictValue(dict.type.sys_dict_translate,'buyClass')"
        align="center"
        prop="buyClass"
        :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.project_category" :value="row.buyClass"/>
        </template>
      </el-table-column>
      <el-table-column
        label="使用科室"
        align="center"
        prop="useDept"
        :show-overflow-tooltip="true"
        v-if="versionType!=='jxzl'&&versionType!=='wzlg'&&versionType!=='whws'"
      />
      <el-table-column label="项目详情" align="center" width="90">
        <template v-slot:default="{row}">
          <el-button
            v-hasPermi="['project:archive:query']"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { queryPurchaseMethod } from '@/api/purchaser/projectList';
import { queryProjectArchiveList } from '@/api/purchaser/projectArchive'

export default {
  name: 'ProjectArchive',
  dicts: ['project_category', 'sys_dict_translate'],
  data() {
    return {
      purchase_method: [],
      orgCode: null,
      queryParams: {
        entity: {
          buyItemName: null,
          orgCode: null,
          purchaseMethodCode: null,
          buyClass: null,
          end: 1 // 完成并归档[0-未完成,1-已完成，未归档,2-已归档]
        },
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      tableData: [],
      total: 0,
      end: 1
    }
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('sys.archive.end').then(response => {
      this.end = response.data === '1' ? 1 : null;
    })
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getPurchaseMethod();
    this.getList();
  },
  methods: {
    purchaseMethodFormat(value) {
      return this.selectDictLabel(this.purchase_method, value);
    },
    async getPurchaseMethod() {
      try {
        let { data } = await queryPurchaseMethod()
        let list = data.methodKVList || [];
        this.purchase_method = list.map(item => {
          return {
            label: item.purchaseMethodName,
            value: item.purchaseMethodCode,
            raw: item
          }
        });
      } catch (e) {
        throw new Error(e);
      }
    },
    handleDetail(row) {
      this.$router.push({
        name: 'ProjectArchiveDetail',
        params: { buyItemCode: row.buyItemCode }
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true;
        this.queryParams.entity.orgCode = this.orgCode;
        this.queryParams.entity.end = this.end;
        let { rows, total } = await queryProjectArchiveList(this.queryParams)
        this.tableData = rows || [];
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
