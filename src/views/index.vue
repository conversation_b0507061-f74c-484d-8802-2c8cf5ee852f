<template>
  <div class="app-container home">
    <el-alert
      v-if="blacklistStatus===1"
      :title="'您的账号已被拉入供应商黑名单，将无法进行项目报名，截止时间为'+blacklistLiftEndTime+'，详情请联系采购人'"
      type="warning"
      show-icon
      effect="dark"
      :closable="false"
    >
    </el-alert>
    <template v-for="(item,index) in certificateList">
      <el-alert
        class="mt10"
        :key="index"
        v-if="item.isExpire || item.expiringSoon"
        :title="'您上传的证书'+'“'+item.certificateName+'”'+(item.isExpire?'已过期':item.expiringSoon?'即将到期':'')+'，请及时更新'"
        type="warning"
        show-icon
        effect="dark"
        :closable="false"
      >
      </el-alert>
    </template>
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>{{ getTitle() }}</h2>
        <p>
          欢迎来到{{ getTitle() }}
        </p>
        <p>若使用过程中遇到问题</p>
        <p class="fontSize15">
          请进入帮助中心
          <router-link to="/homeHelpCenter" style="color: #409eff;">>☛☛点我进入帮助中心☚☚</router-link>
        </p>
        <p class="fontSize25" v-hasRole="['supplier']">
          招标采购中心电话：{{ telephone }}
        </p>
      </el-col>
    </el-row>
    <el-divider/>
    <el-row :gutter="20" ref="main">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span class="fontSize16" style="font-weight: bold;">常见问题</span>
          </div>
          <el-collapse accordion v-loading="loading">
            <template v-for="(item,index) in noticeList">
              <el-collapse-item :key="index">
                <template slot="title">
                  <div class="fontSize15">
                    <span style="font-weight: bold;">{{ item.noticeTitle }}</span>
                  </div>
                </template>
                <Tinymce v-model="item.noticeContent" :readonly="true" class="tinymce-view"/>
                <div class="mt10" v-if="item.noticeFiles&&item.noticeFiles.length>0">
                  <div class="mb5">附件：</div>
                  <file-list-view :fileList="item.noticeFiles" :handleDown="downFile"/>
                </div>
              </el-collapse-item>
            </template>
          </el-collapse>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getNoticeList"
          />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span class="fontSize16" style="font-weight: bold;">视频教程</span>
          </div>
          <div>
            <template v-for="(item,index) in dict.type.sys_video_url">
              <div class="dv-wrap" :key="index">
                <h3 class="dv-title">{{ item.label }}</h3>
                <div class="dv-content">
                  <video-player
                    v-if="item.value"
                    style="margin-bottom: 20px"
                    :video-url="item.value"
                  ></video-player>
                  <p>{{ item.raw.remark }}</p>
                </div>
              </div>
            </template>

          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listNotice } from '@/api/system/notice';
import { getCertificateList } from '@/api/supplier/supplierInfo'
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  dicts: ['sys_video_url'],
  data() {
    return {
      telephone: null,
      videoUrl: null,
      loading: false,
      noticeList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '0'
      },
      certificateList: []
    };
  },
  computed: {
    ...mapGetters([
      'blacklistStatus',
      'blacklistLiftEndTime'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('sys.purchaser.telephone')
      .then(response => {
        this.telephone = response.data;
      })
    this.getNoticeList();

    if (this.$auth.hasRole('supplier')) {
      this.getConfigKey('supplier.certificate.function')
        .then(response => {
          if (response.data === '1') {
            this.getCertificate();
          }
        })
    }
  },
  methods: {
    async getCertificate() {
      try {
        let { data } = await getCertificateList();
        let list = data || [];
        this.certificateList = list.map(item => {
          let currentTime = this.$moment().valueOf();
          let endTime = this.$moment(item.certificateExpirationTime).valueOf();
          let reminderTime = this.$moment(item.certificateExpirationTime).subtract(30, 'days').valueOf();
          return {
            ...item,
            expiringSoon: reminderTime <= currentTime,
            isExpire: endTime <= currentTime
          }
        })
      } catch (e) {
        throw new Error(e)
      }
    },
    downFile(fileKey) {
      this.$download.downloadFileByPublic(fileKey)
    },
    getTitle() {
      return process.env.VUE_APP_TITLE
    },
    /** 查询通知公告列表 */
    async getNoticeList() {
      try {
        this.loading = true;
        let { rows, total } = await listNotice({
          ...this.queryParams,
          noticeType: '1'
        });
        this.noticeList = rows;
        this.total = total;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    },
    goTarget(href) {
      window.open(href, '_blank');
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .dv-wrap {
    margin-bottom: 50px;
    padding: 0 10px;

    .dv-title {
      text-align: center;
      font-size: 25px;
      font-weight: 400;
      margin-bottom: 10px;
    }

    .dv-content {
      p {
        text-indent: 2em;
        line-height: 1.5;
        margin-bottom: 10px;
        font-size: 16px;
      }
    }
  }
}
</style>
<style lang="scss">

</style>
