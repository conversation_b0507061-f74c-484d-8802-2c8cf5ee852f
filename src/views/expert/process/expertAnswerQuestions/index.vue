<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-tabs tab-position="left" v-model="supplierId" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item,index) in supplierList"
        :key="index"
        :label="item.supplierCompanyName"
        :name="item.supplierId+''"
      >
        <expert-answer-questions-item
          v-if="item.supplierId===Number(supplierId)"
          :supplierId="item.supplierId"
          :supplierCompanyName="item.supplierCompanyName"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { listReviewSupplier } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import expertAnswerQuestionsItem from '@/views/expert/process/expertAnswerQuestions/expertAnswerQuestionsItem.vue'

export default {
  name: 'ExpertAnswerQuestions',
  components: {
    expertAnswerQuestionsItem
  },
  data() {
    return {
      supplierList: [],
      supplierId: null
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode'
    ])
  },
  created() {
    this.getSupplierList();
  },
  methods: {
    handleClick() {
    },
    async getSupplierList() {
      try {
        let { data } = await listReviewSupplier(this.subpackageCode);
        this.supplierList = data;
        this.supplierId = this.supplierList[0].supplierId + '';
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
