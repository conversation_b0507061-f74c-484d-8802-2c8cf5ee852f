<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      size="mini"
      style="max-width: 600px;"
      v-if="checkPermi(['expert:process:operate'])">
      <el-form-item prop="askContent">
        <el-input
          type="textarea"
          maxlength="200"
          resize="none"
          :rows="2"
          placeholder="请输入内容"
          v-model.trim="form.askContent">
        </el-input>
      </el-form-item>
      <div class="text-right">
        <el-button type="primary" size="small" @click="handlePut()">
          提问
        </el-button>
      </div>
    </el-form>

    <ul class="qa-list">
      <li class="overflow" v-for="(item,index) in tableData" :key="index">
        <div class="qa-item">
          <span>【问】{{ item.askContent }}</span>
          <span class="qa-time">提交时间：{{ item.createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </div>
        <div class="qa-item">
          <span>【答】{{ item.answerContent }}</span>
          <span class="qa-time" v-if="item.updateAt">
                回复时间：{{ item.updateAt | formatTime('YYYY-MM-DD HH:mm') }}
              </span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { listAnswerQuestions, putAnswerQuestions } from '@/api/answerQuestions';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'ExpertAnswerQuestionsItem',
  props: {
    supplierId: {
      type: Number,
      default: null
    },
    supplierCompanyName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      form: {
        askContent: null
      },
      rules: {
        askContent: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'userId',
      'nickName'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handlePut() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await putAnswerQuestions({
              subpackageCode: this.subpackageCode,
              askUserId: this.userId,
              askUserName: this.nickName,
              askUserType: '3',
              askContent: this.form.askContent,
              answerUserId: this.supplierId,
              answerUserName: this.supplierCompanyName
            })
            this.$modal.msgSuccess('提问成功');
            this.form.askContent = null;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listAnswerQuestions({
          subpackageCode: this.subpackageCode,
          askUserType: '3'
        });
        let list = data || [];
        this.tableData = list.filter(v => v.answerUserId === this.supplierId);
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.qa-list {

  li {
    font-size: 14px;
    line-height: 1.5;
    border-bottom: 1px solid #dfe6ec;
    padding: 10px 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .qa-item {
    margin-bottom: 5px;

    span {
      word-wrap: break-word;
      word-break: break-all;
    }

    .qa-time {
      font-size: 12px;
      color: #808080;
      margin-left: 15px;
    }
  }
}
</style>
