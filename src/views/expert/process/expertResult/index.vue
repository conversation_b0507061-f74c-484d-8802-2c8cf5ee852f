<template>
  <div v-loading="loading" element-loading-text="数据加载中">

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新数据
        </el-button>
      </el-col>
    </el-row>
    <div v-if="incompleteJudges.length>0">
      <p class="mb5 text-danger text-center fontSize18">以下评委未完成评审</p>
      <el-table :data="incompleteJudges" border key="0">
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="评委" align="center" prop="judgeName"/>
        <el-table-column label="未评审供应商" align="center" prop="supplierCompanyName"/>
      </el-table>
    </div>

    <div v-else>
      <el-table :data="tableData" border :span-method="objectSpanMethod" key="1">
        <el-table-column label="序号" prop="index" width="50" align="center"/>
        <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
        <el-table-column label="评审结果" align="center" prop="qualifiedResult" key="qualifiedResult" v-if="isType===0">
          <template v-slot:default="{row}">
            {{ row.qualifiedResult ? '合格' : '不合格' }}
          </template>
        </el-table-column>
        <el-table-column label="评审结果" align="center" prop="scoreResult" key="scoreResult" v-if="isType===1"/>
        <el-table-column label="价格分" align="center" prop="priceSource" v-if="isType===1"/>
        <el-table-column label="评委评审明细" align="center">
          <el-table-column label="评委" align="center" prop="judgeName"/>
          <el-table-column label="评审结果" align="center" prop="judgeResult"/>
          <el-table-column label="评审详情" align="center">
            <template v-slot:default="{row}">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="推荐名次" align="center">
          <template v-slot:default="{row}">
            <el-select
              v-model="originalList.find(v=>v.supplierId===row.supplierId).rank"
              placeholder="请选择"
              :disabled="!isLeader || !checkPermi(['expert:process:operate'])"
              clearable>
              <el-option
                v-for="item in originalList.length"
                :key="item"
                :label="'第'+numToChinese(item)+'候选人'"
                :value="item">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>

      <div class="report-wrap" :style="{'--theme': theme}" v-if="reportFileKey">
        <div class="mr50">
          <div class="report-file">
            <div class="mr5 report-file-icon" @click="previewFile(reportFileKey)">
              <svg-icon icon-class="pdf" class-name="icon-pdf"/>
              <p class="fontSize14">评审报告</p>
            </div>
            <el-button icon="el-icon-view" type="text" @click="previewFile(reportFileKey)">查看</el-button>
            <el-button
              icon="el-icon-s-check"
              type="text"
              @click="handleSign"
              v-if="signatureOff==='0'&&checkPermi(['expert:process:operate'])"
            >
              {{ isSign === 0 ? '签字' : '重新签字' }}
            </el-button>
          </div>
        </div>
        <ul class="report-expert" v-if="signatureOff==='0'">
          <li v-for="(item,index) in judgeSignInfo" :key="index">
            <span class="mr5">{{ item.judgeName }}</span>
            <div>
              <span :style="{'color': theme}" v-if="item.isSign===1"><i class="el-icon-success"></i>已签字</span>
              <span class="text-danger" v-else><i class="el-icon-error"></i>未签字</span>
            </div>
          </li>
        </ul>
      </div>

      <div class="text-center mt20" v-if="isLeader&&checkPermi(['expert:process:operate'])&&!reportFileKey">
        <el-button type="primary" @click="handleSubmitResults">设置供应商排名</el-button>
      </div>
    </div>

    <el-dialog
      title="评审详情"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <p class="mb5">{{ supplierCompanyName }}</p>
      <qualified-table ref="qualifiedTable" :reviewInfo="reviewInfo" :isType="isType"></qualified-table>
      <p v-if="isType===1" class="mt10 fontSize16 text-right pr20">评审结果：{{ scoreResult }}</p>
    </el-dialog>
  </div>
</template>

<script>
import qualifiedTable from '../components/qualifiedTable.vue';
import {
  expertSigne,
  setResultRanking,
  queryReviewResults
} from '@/api/expert/reviewManage';
import { numToChinese } from '@/utils';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'ReviewResults',
  components: {
    qualifiedTable
  },
  data() {
    return {
      loading: false,
      isType: 0, // 1-评分   0-合格
      incompleteJudges: [],
      originalList: [],
      tableData: [],
      spanArr: [],
      pos: 0,
      reviewInfo: [],
      isDialog: false,
      supplierCompanyName: null,
      scoreResult: null,
      reportFileKey: null,
      judgeSignInfo: [],
      isSign: 0, // 0-未签字，1-已签字
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'signatureOff',
      'theme',
      'userId',
      'isLeader',
      'subpackageCode'
    ])
  },
  created() {
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList();
  },
  methods: {
    checkPermi,
    numToChinese,
    refresh() {
      this.getList();
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleSign() {
      if (this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.$modal.loading('数据提交中，请稍候...');
            expertSigne({
              subpackageCode: this.subpackageCode,
              fileKey: this.reportFileKey,
              authCode,
              flowId
            }).then(() => {
              this.$modal.msgSuccess('签章成功');
              this.getList();
              this.$modal.closeLoading();
            }).catch((e) => {
              this.$modal.closeLoading();
              throw new Error(e);
            })
          }
        })
      } else {
        this.$modal.loading('数据提交中，请稍候...');
        expertSigne({
          subpackageCode: this.subpackageCode,
          fileKey: this.reportFileKey
        }).then(() => {
          this.$modal.msgSuccess('签章成功');
          this.getList();
          this.$modal.closeLoading();
        }).catch((e) => {
          this.$modal.closeLoading();
          throw new Error(e);
        })
      }
    },
    handleSubmitResults() {
      if (!this.originalList.some(item => item.rank === 1)) {
        this.$modal.msgError('至少选择一个第一候选人');
        return
      }
      this.$prompt('请输入评审说明', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入评审说明',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 500
        },
        inputErrorMessage: '请输入评审说明，最多500个字符'
      }).then(async ({ value }) => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await setResultRanking({
            subpackageCode: this.subpackageCode,
            rankDtoList: this.originalList.map(v => {
              return {
                rank: v.rank,
                supplierId: v.supplierId
              }
            }),
            remarksBidRejection: value
          })
          this.$modal.msgSuccess('提交成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    handleDetail(row) {
      this.reviewInfo = row.judgesVoList || [];
      this.supplierCompanyName = row.supplierCompanyName;
      this.scoreResult = row.scoreResult;
      this.isDialog = true;
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await queryReviewResults(this.subpackageCode);
        this.incompleteJudges = data.incompleteJudges || [];
        this.originalList = data.supplierDetailsVoList || [];
        this.isType = this.originalList.length > 0 ? this.originalList[0].isType : null;
        this.tableData = [];
        this.originalList.forEach((item, index) => {
          let judgesVoList = item.judgesVoList || [];
          judgesVoList.forEach(v => {
            this.tableData.push({
              ...item,
              index: index + 1,
              judgeId: v.judgeId,
              judgeName: v.judgeName,
              judgeResult: this.isType === 0 ? (v.qualifiedResult ? '合格' : '不合格') : v.scoreResult
            })
          })
        })
        this.getSpanArr(this.tableData);

        let judgesSign = data.judgesSign || {};
        this.reportFileKey = judgesSign.reportFileKey;
        this.judgeSignInfo = judgesSign.judgeSignInfo || [];
        this.isSign = this.judgeSignInfo.find(v => v.judgeId === this.userId).isSign;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [3, 4]
      if (this.isType === 1) {
        columnList = [4, 5];
      }
      if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].supplierId === data[i - 1].supplierId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.report-wrap {
  padding-left: 20px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .report-file {
    display: inline-flex;
    align-items: center;

    .report-file-icon {
      display: inline-block;
      text-align: center;
    }

    .icon-pdf {
      font-size: 46px;
      margin-right: 3px;
    }
  }

  .report-expert {
    li {
      display: inline-flex;
      align-items: center;
      margin-right: 15px;

      span {
        display: flex;
        align-items: center;

        i {
          font-size: 18px;
        }
      }
    }
  }
}

</style>
