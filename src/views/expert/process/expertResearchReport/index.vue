<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="mini"
          @click="refresh"
        >获取最新数据
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" border>
      <el-table-column label="调研报告" align="center">
        <template v-slot:default="{row}">
          <div v-if="row.reportFileKey">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(row.reportFileKey)"
            >
              预览
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDown(row.reportFileKey)"
            >
              下载
            </el-button>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="签章" align="center">
        <template v-for="(item,index) in signInfoVoList">
          <el-table-column :label="item.signUserName" align="center" :key="index">
            <template v-slot:default="{row}">
              <div v-if="row.reportFileKey">
                <el-button
                  v-if="userId===item.signUserId&&item.signStatus!=='1'&&checkPermi(['expert:process:operate'])"
                  size="mini"
                  type="text"
                  icon="el-icon-s-check"
                  @click="handleSign(row.reportFileKey)"
                >
                  签章
                </el-button>
                <el-tag :type="item.signStatus === '1' ? 'success' : 'info'" v-else>
                  {{ item.signStatus === '1' ? '已签章' : '未签章' }}
                </el-tag>
              </div>
              <span v-else>/</span>
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import {
  queryReportByExpert
} from '@/api/purchaser/researchReport';
import { expertSigne } from '@/api/expert/reviewManage'

export default {
  name: 'ExpertResearchReport',
  data() {
    return {
      loading: false,
      tableData: [],
      signInfoVoList: [],
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'theme',
      'subpackageCode',
      'userId'
    ])
  },
  created() {
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getData()
  },
  methods: {
    checkPermi,
    refresh() {
      this.getData();
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handleSign(fileKey) {
      if (this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.$modal.loading('数据提交中，请稍候...');
            expertSigne({
              subpackageCode: this.subpackageCode,
              fileKey,
              authCode,
              flowId
            }).then(() => {
              this.$modal.msgSuccess('签章成功');
              this.getData()
              this.$modal.closeLoading();
            }).catch((e) => {
              this.$modal.closeLoading();
              throw new Error(e);
            })
          }
        })
      } else {
        this.$modal.loading('数据提交中，请稍候...');
        expertSigne({
          subpackageCode: this.subpackageCode,
          fileKey
        }).then(() => {
          this.$modal.msgSuccess('签章成功');
          this.getData()
          this.$modal.closeLoading();
        }).catch((e) => {
          this.$modal.closeLoading();
          throw new Error(e);
        })
      }
    },
    async getData() {
      try {
        this.loading = true;
        let { data } = await queryReportByExpert(this.subpackageCode);
        this.signInfoVoList = data.signInfoVoList || [];
        this.tableData = [
          { reportFileKey: data.researchReportKey }
        ];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
