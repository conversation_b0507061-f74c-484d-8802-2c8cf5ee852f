<template>
  <div>
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <drop-down
          :dropArray="supplierList"
          v-model="supplierId"
          @command="changeSupplier"
        >
        </drop-down>
      </el-col>
      <el-col
        :span="1.5"
        class="fr mr20"
        v-if="hasNext()&&checkPermi(['expert:process:operate'])"
      >
        <el-button type="success" @click="handleNext">
          下一步
        </el-button>
      </el-col>
      <el-col
        :span="1.5"
        class="fr mr20"
        v-if="checkPermi(['expert:process:operate'])"
      >
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="small"
          @click="handleExport"
        >
          下载资格性符合性评审响应对照表
        </el-button>
      </el-col>
    </el-row>

    <conformity-review-pdf-item
      class="mb20"
      :supplierId="supplierId"
      :supplierName="supplierName"
      @handlePosition="handlePosition"
      @saveReview="saveReview"
    >
    </conformity-review-pdf-item>

    <review-files-pdf
      ref="reviewFiles"
      :supplierList="supplierList"
      v-model="supplierId"
      @changeSupplier="changeSupplier">
    </review-files-pdf>

    <el-dialog
      title="确认评审结果"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5" class="fr">
          <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="getNextData"
          >获取最新信息
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="tableData" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
        <el-table-column label="评审结果" align="center" prop="qualifiedResult">
          <template v-slot:default="{row}">
            {{ row.qualifiedResult ? '未淘汰' : '淘汰' }}
          </template>
        </el-table-column>
        <el-table-column label="评审情况" align="center">
          <template v-slot:default="{row}">
            <el-button
              size="mini"
              type="text"
              @click="handleDetail(row)"
            >详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="text-center">
        <el-button @click="isDialog=false">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="isLeader">确认提交</el-button>
        <el-button type="primary" @click="toNextProcess" v-else>确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="评审详情"
      :visible.sync="isInfoDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="mb5">{{ supplierCompanyName }}</div>
      <qualified-table
        :reviewInfo="reviewInfo"
        :isType="0"
        v-if="isInfoDialog"
      >
      </qualified-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listReviewSupplier,
  clickNextStep,
  groupLeaderOk,
  downReviewPdf
} from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import reviewFilesPdf from '../components/reviewFiles/reviewFilesPdf.vue';
import conformityReviewPdfItem from './conformityReviewPdfItem.vue';
import dropDown from '../components/dropDown';
import qualifiedTable from '@/views/expert/process/components/qualifiedTable.vue';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'ConformityReview',
  components: {
    reviewFilesPdf,
    conformityReviewPdfItem,
    dropDown,
    qualifiedTable
  },
  data() {
    return {
      supplierList: [],
      supplierId: null,
      supplierName: null,
      allSave: true, // 是否全部保存
      isDialog: false,
      tableData: [],
      isInfoDialog: false,
      reviewInfo: [],
      supplierCompanyName: null
    }
  },
  computed: {
    ...mapGetters([
      'isLeader',
      'subpackageCode',
      'expertProcess'
    ])
  },
  created() {
    this.getSupplierList()
  },
  methods: {
    checkPermi,
    async handleExport() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await downReviewPdf({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId,
          type: 0
        });
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    hasNext() {
      const route = this.$route;
      const { path } = route;
      let obj = this.expertProcess.find(item => item.path === path);
      return obj && obj.nextStep === 1;
    },
    async handleSubmit() {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await groupLeaderOk({
          subpackageCode: this.subpackageCode,
          supplierIds: this.tableData.filter(v => !v.qualifiedResult).map(v => v.supplierId)
        });
        this.$modal.msgSuccess('提交成功');
        this.isDialog = false;
        this.$modal.closeLoading();
        await this.toNextProcess();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleDetail(data) {
      this.reviewInfo = data.judgesVoList || [];
      this.supplierCompanyName = data.supplierCompanyName;
      this.isInfoDialog = true;
    },
    async handleNext() {
      try {
        await this.getNextData();
        // 1、判断所有评委是否评审完成
        if (!this.allSave) {
          let tipText = `<table class="custom-table">`;
          tipText += `<tr><th>评委</th><th>未评审供应商</th></tr>`;
          this.incompleteJudge.forEach(v => {
            tipText += `<tr><td>${v.judgeName}</td><td>${v.supplierCompanyName}</td></tr>`;
          })
          tipText += `</table>`;
          this.$tipDialog({
            tipTitle: '以下评委未完成评审',
            tipTitleColor: '#ff4949',
            tipText: tipText,
            type: 'warning'
          })
          return
        }
        // if (!this.isLeader) {
        //   await this.toNextProcess();
        // } else {
        //   this.isDialog = true;
        // }
        this.isDialog = true;
      } catch (e) {
        throw new Error(e);
      }
    },
    async getNextData() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await clickNextStep(this.subpackageCode);
        this.incompleteJudge = data.incompleteJudge || [];
        this.allSave = this.incompleteJudge.length === 0;
        this.tableData = data.supplierDetailsVoList || [];
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async toNextProcess() {
      try {
        let res = await this.$store.dispatch('expertProcess/setExpertProcess', this.subpackageCode);
        let path = this.$route.path;
        let index = res.findIndex(item => item.path === path);
        if (res[index + 1].disabled) {
          this.$modal.msgWarning('请等待组长点击下一步更新流程');
          return;
        }
        await this.$router.push({ path: res[index + 1].path });
      } catch (e) {
        throw new Error(e);
      }
    },
    saveReview(supplierId) {
      this.getSupplierList(supplierId);
    },
    handlePosition(val) {
      this.$refs.reviewFiles.positionChapter(val);
    },
    changeSupplier(supplierId, item) {
      this.supplierName = item.supplierCompanyName;
    },
    async getSupplierList(supplierId) {
      try {
        let { data } = await listReviewSupplier(this.subpackageCode);
        this.supplierList = data;
        if (supplierId) {
          this.supplierId = supplierId;
          this.supplierName = this.supplierList.find(v => v.supplierId === supplierId).supplierCompanyName;
        } else {
          this.supplierId = this.supplierList[0].supplierId;
          this.supplierName = this.supplierList[0].supplierCompanyName;
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
