<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-form ref="formData" :rules="rules" :model="formData" size="mini">
      <el-table :data="formData.tableData" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="审查因素" align="center" prop="reviewItem"/>
        <el-table-column label="审查因素描述" align="center" prop="reviewCriteria" min-width="200px"/>
        <el-table-column label="导航" align="center" prop="content" width="150">
          <template v-slot:default="{row}">
            <div
              v-for="(item,k) in row.scoreChapter"
              :key="k"
              @click="clickPosition(item)"
              :style="{'--theme': theme}"
              class="position">
              <el-tooltip effect="dark" :content="'章节 '+item" placement="top">
                <i class="el-icon-aim"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评审结果" align="center" prop="qualified" width="180" class-name="form-cell">
          <template v-slot:default="{row,$index}">
            <el-form-item :prop="'tableData.' + $index + '.qualified'" :rules="rules.qualified">
              <el-radio-group
                v-model="row.qualified"
                :disabled="!checkPermi(['expert:process:operate'])"
                @change="changeQualified">
                <el-radio :label="1">合格</el-radio>
                <el-radio :label="0">不合格</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-right mt10 pr20" v-if="checkPermi(['expert:process:operate'])">
        <el-button type="primary" @click="oneKeyQualified">一键合格</el-button>
        <el-button type="primary" @click="saveReview">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { saveReviewData, getEvaluationMethod } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'ConformityReviewItem',
  props: {
    supplierId: {
      type: Number,
      default: null
    },
    supplierName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isType: 0, // 合格制type=0，打分制 type=1
      loading: false,
      formData: {
        tableData: []
      },
      rules: {
        qualified: { required: true, message: '请选择', trigger: 'change' }
      }
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'userId',
      'nickName',
      'theme',
      'qualifiedData'
    ])
  },
  watch: {
    supplierId: {
      immediate: true,
      handler(val) {
        if (this.supplierId) {
          this.getList();
        }
      }
    }
  },
  created() {

  },
  methods: {
    checkPermi,
    changeQualified() {
      let list = this._.cloneDeep(this.qualifiedData);
      let reviewList = this.formData.tableData.map(v => {
        return { uuid: v.uuid, qualified: v.qualified }
      });
      if (list.some(item => item.supplierId === this.supplierId)) {
        list.forEach(item => {
          if (item.supplierId === this.supplierId) {
            item.reviewList = reviewList
          }
        })
      } else {
        list.push({
          supplierId: this.supplierId,
          reviewList: reviewList
        })
      }
      this.$store.dispatch('expertProcess/setQualifiedData', list);
    },
    oneKeyQualified() {
      this.formData.tableData.forEach(item => {
        this.$set(item, 'qualified', 1);
      });
      this.changeQualified();
    },
    saveReview() {
      this.$refs['formData'].validate(async (valid) => {
        if (valid) {
          this.$confirm('<p>请确认是否提交？</p>', '提示', {
            customClass: 'max-tip',
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }).then(async () => {
            try {
              this.$modal.loading('数据提交中，请稍候...');
              await saveReviewData({
                subpackageCode: this.subpackageCode,
                supplierCompanyName: this.supplierName,
                supplierId: this.supplierId,
                isType: this.isType,
                judgeId: this.userId,
                judgeName: this.nickName,
                resultBoList: this.formData.tableData.map(item => {
                  return {
                    uuid: item.uuid,
                    qualified: item.qualified,
                    reviewItem: item.reviewItem
                    // reviewCriteria: item.reviewCriteria
                  }
                })
              });
              await this.getList();
              this.$emit('saveReview', this.supplierId);
              this.$modal.msgSuccess('提交成功');
              this.$modal.closeLoading()
            } catch (e) {
              this.$modal.closeLoading()
              throw new Error(e);
            }
          })
        }
      })
    },
    // 点击得分点定位
    clickPosition(item) {
      this.$emit('handlePosition', item)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getEvaluationMethod({
          subpackageCode: this.subpackageCode,
          type: this.isType, // 1-评分   0-符合性评审
          supplierId: this.supplierId
        })
        this.formData.tableData = data || [];
        console.log('this.formData.tableData', data);
        let obj = this.qualifiedData.find(v => v.supplierId === this.supplierId);
        console.log('obj', obj)
        if (obj) {
          this.formData.tableData.forEach(item => {
            let obj1 = obj.reviewList.find(v => v.uuid === item.uuid)
            if (obj1) {
              this.$set(item, 'qualified', obj1.qualified)
            }
          })
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.position {
  display: inline-block;
  color: #{'var(--theme)'};
  font-size: 25px;
  cursor: pointer;
  vertical-align: middle;

  & + .position {
    margin-left: 15px;
  }
}
</style>
