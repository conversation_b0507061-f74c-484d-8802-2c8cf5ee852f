<template>
  <el-table :data="tableData" border max-height="600">
    <el-table-column label="序号" type="index" width="50" align="center"/>
    <el-table-column label="审查因素" align="center" prop="reviewItem"/>
    <!--<el-table-column label="审查因素描述" align="center" prop="reviewCriteria" min-width="200px"/>-->
    <template v-for="(item,k) in expertList">
      <el-table-column :label="item.judgeName" align="center" :key="k" width="100">
        <template v-slot:default="{row}">
          <div v-if="isType===0">
            <span v-if="item['qualified_' + row.uuid]===0">不合格</span>
            <span v-else-if="item['qualified_' + row.uuid]===1">合格</span>
            <span v-else>/</span>
          </div>
          <span v-else>{{ item['score_' + row.uuid] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script>
export default {
  name: 'QualifiedTable',
  props: {
    reviewInfo: {
      type: Array,
      default: () => []
    },
    isType: {
      type: [Number, String],
      default: null,
      required: true
    }
  },
  data() {
    return {
      expertList: [],
      tableData: []
    }
  },
  watch: {
    reviewInfo: {
      deep: true,
      // immediate: true,
      handler(val) {
        this.setData(val);
      }
    }
  },
  created() {
    this.setData(this.reviewInfo);
  },
  methods: {
    setData(arr) {
      if (!(arr && this._.isArray(arr))) {
        this.tableData = [];
        this.expertList = [];
      } else {
        let list = arr[0].reviewDetailsVoList || [];
        this.tableData = list.map(item => {
          return {
            reviewItem: item.reviewItem,
            reviewCriteria: item.reviewCriteria,
            uuid: item.uuid
          }
        });
        this.expertList = arr || [];
        this.expertList.forEach(item => {
          let reviewDetailsVoList = item.reviewDetailsVoList || [];
          reviewDetailsVoList.forEach(v => {
            this.$set(item, 'qualified_' + v.uuid, v.qualified)
            this.$set(item, 'score_' + v.uuid, v.score)
          })
        });
      }
    }
  }
}
</script>

<style scoped>

</style>
