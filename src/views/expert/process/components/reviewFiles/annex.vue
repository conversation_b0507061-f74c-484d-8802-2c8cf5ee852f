<template>
  <div class="file-wrap">
    <ul class="file-list" :style="{'--theme': theme}">
      <li v-for="(item,index) in files" :key="index">
        <div class="file-box">
          <div class="file-icon" @click="previewFile(item.fileKey)">
            <i class="el-icon-document"></i>
          </div>
          <div class="file-mask">
            <el-button type="primary" size="mini" @click="previewFile(item.fileKey)">预览</el-button>
            <el-button type="danger" size="mini" @click="handleDown(item.fileKey)">下载</el-button>
          </div>
        </div>

        <p class="file-name ellipsis-more-2" :title="item.fileName">{{ item.fileName }}</p>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'Annex',
  props: {
    files: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  methods: {
    handleDown(fileKey) {
      this.$download.downloadFileByAnnex(fileKey)
    },
    previewFile(fileKey) {
      if (!fileKey) {
        this.$modal.msgError('未查询到文件');
        return
      }
      this.$download.previewFileByAnnex(fileKey)
    }
  }
}
</script>

<style lang="scss" scoped>
$width: 130px;
.file-wrap {
  padding: 15px 20px;
}

.file-list {
  display: flex;
  flex-wrap: wrap;

  li {
    width: $width;
    margin: 0 10px 10px;

    .file-box {
      position: relative;

      &:hover {
        .file-mask {
          display: flex;
        }
      }
    }

    .file-icon {
      width: $width;
      height: $width;
      border: 3px solid  #{'var(--theme)'};
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .el-icon-document {
        font-size: 80px;
        color: #{'var(--theme)'};
      }
    }

    .file-name {
      margin-top: 5px;
      text-align: center;
      font-size: 14px;
      line-height: 1.3;
    }

    .file-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: none;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
