<template>
  <div>
    <el-container v-loading="loading" element-loading-text="数据加载中">
      <el-aside width="250px" style="padding-right: 10px;border-right: 1px solid #d7d7d7">
        <h3 class="aside-title">响应文件</h3>
        <response-file-pdf-tabs
          :supplierId="supplierId"
          v-model="menuOrder"
          @tab-click="handleChapter"
          @addContrast="addContrast"
          @removeContrast="removeContrast"
        >
        </response-file-pdf-tabs>
        <div class="mt30">
          <div class="mb10">
            <el-button
              type="primary"
              style="width: 100%;"
              @click="previewVideo">
              演示视频
            </el-button>
          </div>
        </div>
      </el-aside>
      <el-main style="padding: 0;">
        <pdf-view :src="pdfData" type="blob" v-if="pdfData&&chapterType===5"/>
        <quotation-sheet
          :heads="heads"
          :bodyMaps="bodyMaps"
          v-if="chapterType===3"
        >
        </quotation-sheet>
        <annex
          v-if="chapterType===4"
          :files="attachFiles"
        >
        </annex>
      </el-main>
      <el-aside
        width="200px"
        style="padding-left: 10px;"
        v-if="chapterType===1"
      >
        <h3 class="aside-title">附件</h3>
        <chapter-annex :files="attachFiles"></chapter-annex>
      </el-aside>
    </el-container>
    <contrast></contrast>

    <!-- 得分点定位弹框 -->
    <el-dialog
      title="导航"
      :visible.sync="isPositionDialog"
      top="5vh"
      custom-class="maxW1100"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div
        style="max-height: calc(93vh - 94px);overflow: auto;min-height: 400px;"
        v-if="isPositionDialog"
      >
        <el-container>
          <el-main style="padding: 0;">
            <iframe
              style="height: 700px;"
              class="html-iframe"
              :srcdoc="getPreviewCKEditorHtml(menuText)"
              v-if="chapterType===1"
            >
            </iframe>
            <quotation-sheet
              :heads="heads"
              :bodyMaps="bodyMaps"
              v-if="chapterType===3"
            >
            </quotation-sheet>
            <annex
              v-if="chapterType===4"
              :files="attachFiles"
            >
            </annex>
          </el-main>
          <el-aside
            width="200px"
            style="padding-left: 10px;"
            v-if="chapterType===1"
          >
            <h3 class="aside-title">附件</h3>
            <chapter-annex :files="attachFiles"></chapter-annex>
          </el-aside>
        </el-container>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import responseFilePdfTabs from './responseFilePdfTabs.vue';
import annex from './annex';
import chapterAnnex from './chapterAnnex';
import quotationSheet from './quotationSheet';
import contrast from './contrast';
import { getResponseFileMenu, getReviewResponseAtt } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import { getPreviewCKEditorHtml } from '@/utils';
import { fileDown } from '@/api/file'
import { blobValidate } from '@/utils/ruoyi'

export default {
  name: 'ResponseFilePdf',
  components: {
    responseFilePdfTabs,
    annex,
    chapterAnnex,
    quotationSheet,
    contrast
  },
  props: {
    supplierId: {
      type: [Number, String],
      default: null
    },
    supplierName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      menuList: [],
      menuOrder: null,
      menuText: null,
      heads: [], // 报价表
      bodyMaps: [], // 报价表
      attachFiles: [], // 附件
      chapterType: null, // 1 编辑器文本，2 评标办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件
      videoKey: null,
      isPositionDialog: false,
      pdfData: null
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'contrastList'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    console.log('supplierId', this.supplierId)
    // this.getMenuList();
  },
  watch: {
    supplierId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getMenuList();
        }
      }
    }
  },
  methods: {
    getPreviewCKEditorHtml,
    downFile(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    previewFile(fileKey) {
      if (!fileKey) {
        this.$modal.msgError('未查询到文件');
        return
      }
      this.$download.previewFile(fileKey)
    },
    previewVideo() {
      if (!this.videoKey) {
        this.$modal.msgError('该供应商未上传演示视频')
        return
      }
      this.$download.previewFileByAnnex(this.videoKey);
    },
    // 点击得分点定位
    positionChapter(val) {
      console.log(val)
      let orderList = val.split('.')
      let item = this.menuList.find(v => v.order === Number(orderList[0]));
      if (!item) {
        this.$modal.msgError(`未找到 ${val} 章节`);
        return
      }
      let tabItem = item.chapterList.find(v => v.order === Number(orderList[1]));
      if (!tabItem) {
        this.$modal.msgError(`未找到 ${val} 章节`);
        return
      }
      this.menuOrder = val;
      this.handleChapter(this.menuOrder, tabItem);
      this.isPositionDialog = true;
    },
    // 加入对比
    async addContrast(val) {
      console.log(val)
      let copyList = this._.cloneDeep(this.contrastList || []);
      let content = null;
      let heads = [];
      // 1 编辑器文本，2 评标办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件
      if (val.chapterType === 1 || val.chapterType === 4) {
        content = val.sectionText;
        heads = [];
      } else if (val.chapterType === 3) {
        content = this.bodyMaps;
        heads = this.heads;
      } else {
        content = null;
        heads = [];
      }

      let obj = copyList.find(v => v.sectionName === val.sectionName);
      if (!obj) {
        copyList.push({
          sectionName: val.sectionName,
          chapterType: val.chapterType,
          companyList: [{
            supplierName: this.supplierName,
            supplierId: this.supplierId,
            content,
            heads
          }]
        })
      } else {
        copyList.forEach(item => {
          if (item.sectionName === val.sectionName) {
            let cObj = item.companyList.find(item1 => item1.supplierId === this.supplierId)
            if (!cObj) {
              item.companyList.push({
                supplierName: this.supplierName,
                supplierId: this.supplierId,
                content,
                heads
              })
            }
          }
        })
      }
      await this.$store.dispatch('expertProcess/setContrastList', copyList);
    },
    // 移除对比
    removeContrast(val) {
      let copyList = this._.cloneDeep(this.contrastList || []);
      copyList.forEach((item, k, arr) => {
        if (item.sectionName === val.sectionName) {
          let index = item.companyList.findIndex(obj => obj.supplierId === this.supplierId);
          if (index >= 0) {
            item.companyList.splice(index, 1);
          }
          if (item.companyList.length === 0) {
            arr.splice(k, 1);
          }
        }
      })
      this.$store.dispatch('expertProcess/setContrastList', copyList);
    },
    // 切换章节
    handleChapter(menuOrder, data) {
      console.log(menuOrder, data)
      // 1 编辑器文本，2 评标办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件
      this.chapterType = data.chapterType;
      if (menuOrder === 's-3') {
        this.getAttList();
      } else {
        this.menuText = null;
        this.attachFiles = [];
      }
    },
    // 获取章节目录
    async getMenuList() {
      try {
        this.loading = true;
        let { data } = await getResponseFileMenu({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId
        })
        this.videoKey = data.videoUrl;
        this.bodyMaps = data.bodyMaps;
        this.heads = data.heads;
        this.menuOrder = 's-2';
        let res = await fileDown(data.pdfFile)
        const isBlob = blobValidate(res.data)
        if (isBlob) {
          this.pdfData = res.data;
        } else {
          await this.$download.printErrMsg(res.data)
        }
        this.handleChapter(this.menuOrder, { sectionName: '响应文件', chapterType: 5, menuOrder: 's-2' });
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    async getAttList() {
      try {
        let { data } = await getReviewResponseAtt({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId
        });
        this.attachFiles = data || [];
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.aside-title {
  font-size: 16px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #333333;
  margin-bottom: 15px;
}
</style>
