<template>
  <div>
    <div class="contrast-tag" :style="{'--theme': theme}" @click="lookContrast">
      <span>查</span>
      <span>看</span>
      <span>对</span>
      <span>比</span>
    </div>

    <el-dialog
      title="查看对比"
      :visible.sync="isDialog"
      custom-class="contrast-dialog"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-tabs v-model="activeName" closable @tab-remove="removeTab" type="card">
        <el-tab-pane v-for="(item,index) in contrastData" :key="index" :label="item.sectionName" :name="item.sectionName">
          <div class="contrast-card-wrap">
            <div class="contrast-card-box">
              <el-card
                class="contrast-card box-card"
                :body-style="{'padding': '10px'}"
                v-for="(item1,index1) in item.companyList"
                :key="index1"
              >
                <div slot="header" class="card-header">
                  <span style="font-weight: bold;">{{ item1.supplierName }}</span>
                  <el-button
                    size="mini"
                    icon="el-icon-close"
                    circle
                    @click="deleteContrast(index,index1,item.sectionName)">
                  </el-button>
                </div>
                <quotation-sheet
                  v-if="item.chapterType===3"
                  :heads="item1.heads"
                  :bodyMaps="item1.content"
                />
                <div class="text-box zoom-img" v-else>
                  <iframe class="html-iframe" :srcdoc="getPreviewCKEditorHtml(item1.content)"></iframe>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <el-empty v-if="contrastData.length===0" description="暂无对比数据"></el-empty>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getPreviewCKEditorHtml } from '@/utils';
import quotationSheet from '@/views/expert/process/components/reviewFiles/quotationSheet.vue';

export default {
  name: 'Contrast',
  components: { quotationSheet },
  data() {
    return {
      isDialog: false,
      activeName: null,
      contrastData: []
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'contrastList'
    ])
  },
  watch: {
    contrastList: {
      deep: true,
      immediate: true,
      handler() {
        this.contrastData = this._.cloneDeep(this.contrastList || []);
        if (this.contrastData.length > 0) {
          if (this.activeName === this.removeName || !this.activeName || !this.isDialog) {
            this.activeName = this.contrastData[0].sectionName;
          }
        } else {
          this.activeName = '';
        }
      }
    }
  },
  methods: {
    getPreviewCKEditorHtml,
    lookContrast() {
      this.isDialog = true;
    },
    deleteContrast(index, index1, sectionName) {
      this.contrastData[index].companyList.splice(index1, 1);
      if (this.contrastData[index].companyList.length === 0) {
        this.removeName = sectionName;
        this.contrastData.splice(index, 1);
      }
      this.$store.dispatch('expertProcess/setContrastList', this.contrastData);
    },
    removeTab(val) {
      this.removeName = val;
      let index = this.contrastData.findIndex(obj => {
        return obj.sectionName === val;
      });
      if (index >= 0) {
        this.contrastData.splice(index, 1);
      }
      this.$store.dispatch('expertProcess/setContrastList', this.contrastData);
    }
  }
}
</script>

<style lang="scss" scoped>
.contrast-tag {
  position: fixed;
  top: 15vh;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background: #{'var(--theme)'};
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .3);
  color: #ffffff;
  cursor: pointer;
  padding: 10px 8px;
  font-size: 14px;
  z-index: 100;
}

.contrast-card-wrap {
  width: 100%;
  overflow: auto;
  padding-bottom: 10px;

  .contrast-card-box {
    display: flex;

    .contrast-card {
      width: 15.8cm;
      margin-right: 10px;
      flex-grow: 0;
      flex-shrink: 0;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}

.text-box {
  height: 500px;
}
</style>

<style lang="scss">
.contrast-card {
  .el-card__header {
    padding: 8px 20px;
  }
}

.contrast-dialog {
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
