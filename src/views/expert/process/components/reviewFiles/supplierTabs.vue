<template>
  <ul class="clearfix tab-wrap" :style="{'--theme': theme}">
    <li
      v-for="(item,index) in supplierList"
      :key="index"
      :class="{'tab-item':true, 'tab-active':currentSupplierId===item.supplierId}"
      @click="handleTabClick(item,index)"
    >
      <div>{{ item.supplierCompanyName }}</div>
      <!--      <span v-if="item.judgesComplete===1" class="reviewed">已评</span>-->
      <svg-icon icon-class="eliminate" class-name="eliminate-icon" v-if="item.reviewScore===0"/>
    </li>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'SupplierTabs',
  props: {
    value: {},
    supplierList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentSupplierId: this.value
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  watch: {
    value(value) {
      this.setCurrentId(value);
    }
  },
  created() {
    this.setCurrentId(this.value);
  },
  methods: {
    handleTabClick(item, index) {
      // if (item.reviewScore === 0) return;
      this.setCurrentId(item.supplierId);
      this.$emit('tab-click', item.supplierId, item, index)
    },
    setCurrentId(value) {
      this.currentSupplierId = value;
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
$colorFalse: #c8c9cc;
$colorTrue: #{'var(--theme)'};
.tab-wrap {
  background-color: #FFFFFF;
  //padding-left: 10px;
  min-height: 400px;
  max-height: 600px;
  overflow: auto;
  margin-bottom: 20px;
  //max-width: 230px;

  .tab-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    min-height: 50px;
    text-align: center;
    cursor: pointer;
    padding: 5px;
    box-sizing: border-box;
    border-bottom: 1px solid #e4e4e4;
    background-color: #f4f4f4;

    .el-icon-check {
      color: $colorTrue;
      font-size: 20px;
      font-weight: bold;
      margin-left: 10px;
    }

    .reviewed {
      color: $colorTrue;
      font-size: 14px;
      font-weight: bold;
      margin-left: 10px;
      flex-shrink: 0;
    }

    .eliminate-icon {
      font-size: 50px;
      margin-left: 5px;
      color: $colorDanger;
    }

    &:last-child {
      border-bottom: none;
    }

    &.tab-disable {
      color: $colorFalse;
      cursor: not-allowed;
    }

    &.tab-active {
      color: #FFFFFF;
      background-color: $colorTrue;
      border-bottom: 1px solid $colorTrue;

      .el-icon-check {
        color: #FFFFFF;
      }

      .reviewed {
        color: #FFFFFF;
      }
    }
  }
}
</style>
