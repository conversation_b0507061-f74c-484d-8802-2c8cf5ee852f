<template>
  <ul class="clearfix tab-wrap" :style="{'--theme': theme}">
    <li v-for="(item,index) in menuList" :key="index">
      <div class="chapter-name over-ellipsis" :title="item.chapterName">
        <span v-if="item.order>=1">第{{ numToChinese(item.order) }}章</span>{{ item.chapterName }}
      </div>
      <ul>
        <li
          class="tabItem-li"
          v-for="(tabItem,tabItemIndex) in item.chapterList"
          :key="tabItemIndex"
        >
          <!--chapterType 1 编辑器文本，2 评标办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件-->
          <el-tooltip effect="dark" content="加入对比" placement="right" v-if="!isAddContrast(tabItem.sectionName)">
            <el-button
              size="mini"
              icon="el-icon-plus"
              circle
              @click="addContrast(tabItem)"
              :disabled="tabItem.chapterType===4">
            </el-button>
          </el-tooltip>
          <el-tooltip effect="dark" content="移出对比" placement="right" v-else>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-check"
              circle
              class="check-btn"
              @click="removeContrast(tabItem)">
            </el-button>
          </el-tooltip>
          <div
            :class="{'tab-item':true, 'tab-active':menuOrder === tabItem.menuOrder}"
            @click="handleTabClick(tabItem)"
            :title="tabItem.sectionName">
            <div class="over-ellipsis"><span class="mr5">{{ tabItem.menuOrder }}</span>{{ tabItem.sectionName }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </li>
      </ul>
    </li>

    <li>
      <div class="chapter-name over-ellipsis"></div>
      <ul>
        <li
          class="tabItem-li"
          v-for="(tabItem,tabItemIndex) in touSloList"
          :key="tabItemIndex"
        >
          <el-tooltip effect="dark" content="加入对比" placement="right" v-if="!isAddContrast(tabItem.sectionName)">
            <el-button
              size="mini"
              icon="el-icon-plus"
              circle
              @click="addContrast(tabItem)">
            </el-button>
          </el-tooltip>
          <el-tooltip effect="dark" content="移出对比" placement="right" v-else>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-check"
              circle
              class="check-btn"
              @click="removeContrast(tabItem)">
            </el-button>
          </el-tooltip>
          <div
            :class="{'tab-item':true, 'tab-active':menuOrder === tabItem.menuOrder}"
            @click="handleTabClick(tabItem)"
            :title="tabItem.sectionName">
            <div class="over-ellipsis">{{ tabItem.sectionName }}</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex';
import { numToChinese } from '@/utils';

export default {
  name: 'ResponseFileTabs',
  props: {
    value: {},
    menuList: {
      type: Array,
      default: () => []
    },
    supplierId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      menuOrder: this.value,
      touSloList: [
        { sectionName: '投标函附录/报价表', chapterType: 3, menuOrder: 's-1' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'contrastList'
    ])
  },
  watch: {
    value(value) {
      this.setCurrentId(value);
    }
  },
  created() {
    this.setCurrentId(this.value);
  },
  methods: {
    numToChinese,
    // 加入对比
    addContrast(item) {
      this.$emit('addContrast', item);
    },
    // 移除对比
    removeContrast(item) {
      this.$emit('removeContrast', item);
    },
    isAddContrast(sectionName) {
      let obj = this.contrastList.find(item => item.sectionName === sectionName);
      if (!obj) {
        return false;
      }
      let obj1 = obj.companyList.find(item => item.supplierId === this.supplierId);
      return !!obj1;
    },
    handleTabClick(tabItem) {
      this.setCurrentId(tabItem.menuOrder);
      this.$emit('tab-click', tabItem.menuOrder, tabItem)
    },
    setCurrentId(value) {
      this.menuOrder = value;
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
$colorFalse: #999999;
$colorTrue: #{'var(--theme)'};
.tab-wrap {
  background-color: #FFFFFF;
  min-height: 400px;
  max-height: 600px;
  overflow: auto;
  margin-bottom: 20px;

  .chapter-name {
    height: 40px;
    line-height: 40px;
    font-size: 14px;

    span {
      margin-right: 5px;
    }
  }

  .tabItem-li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 175px;
    max-width: 250px;
  }

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    padding: 0 5px 0 10px;
    box-sizing: border-box;
    margin-left: 10px;
    overflow: hidden;

    &:last-child {
      border-bottom: none;
    }

    &.tab-disable {
      color: $colorFalse;
    }

    &.tab-active {
      color: #FFFFFF;
      background-color: $colorTrue;
    }
  }
}
</style>
