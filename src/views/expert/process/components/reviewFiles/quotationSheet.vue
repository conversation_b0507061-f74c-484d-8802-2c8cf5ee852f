<template>
  <div style="padding: 0 15px;">
    <u-table
      :data="bodyMaps"
      :height="500"
      use-virtual
      :row-height="55"
      border>
      <u-table-column
        label="序号"
        align="center"
        type="index"
        width="50"
      />
      <u-table-column
        v-for="(item,k) in heads"
        :key="k"
        align="center"
        :label="item.keyName"
        :show-overflow-tooltip="true"
      >
        <template v-slot:default="{row}">
          <span v-if="item.keyType!=='file'">{{ row[item.keyVal] }}</span>
          <div v-else>
            <el-button
              v-if="row[item.keyVal]"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewFile(row[item.keyVal])"
            >查看
            </el-button>
            <span v-else>/</span>
          </div>
        </template>
      </u-table-column>
    </u-table>
  </div>
</template>

<script>
export default {
  name: 'QuotationSheet',
  props: {
    heads: {
      type: Array,
      default: () => []
    },
    bodyMaps: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    }
  }
}
</script>

<style scoped>

</style>
