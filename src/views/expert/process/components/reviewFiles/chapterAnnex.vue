<template>
  <ul class="file-list overflow" :style="{'--theme': theme}">
    <li v-for="(item,index) in files" :key="index">
      <div class="file-icon" @click="previewFile(item.fileKey)">
        <p class="file-name ellipsis-more-2" :title="item.fileName">{{ item.fileName }}</p>
      </div>
      <div class="file-mask">
        <el-button type="primary" size="mini" @click="previewFile(item.fileKey)">预览</el-button>
        <el-button type="danger" size="mini" @click="handleDown(item.fileKey)">下载</el-button>
      </div>
    </li>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'ChapterAnnex',
  props: {
    files: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  methods: {
    handleDown(fileKey) {
      this.$download.downloadFileByAnnex(fileKey)
    },
    previewFile(fileKey) {
      if (!fileKey) {
        this.$modal.msgError('未查询到文件');
        return
      }
      this.$download.previewFileByAnnex(fileKey)
    }
  }
}
</script>

<style lang="scss" scoped>
.file-list {

  li {
    position: relative;
    margin-bottom: 10px;

    &:hover {
      .file-mask {
        display: flex;
      }
    }

    .file-icon {
      width: 100%;
      min-height: 35px;
      border: 1px solid  #{'var(--theme)'};
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      padding: 5px 5px;
    }

    .file-name {
      text-align: center;
      font-size: 14px;
      color: #{'var(--theme)'};
      line-height: 1.2;
    }

    .file-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: none;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
