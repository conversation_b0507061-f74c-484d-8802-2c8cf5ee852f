<template>
  <div>
    <el-container>
      <el-aside width="210px" style="padding-right: 10px;">
        <h3 class="aside-title">供应商</h3>
        <supplier-tabs :supplierList="supplierList" v-model="currentSupplierId" @tab-click="handleSupplier"></supplier-tabs>
        <!--        <div class="mt30">-->
        <!--          <div class="mb10">-->
        <!--            <el-button type="primary" style="width: 100%;" @click="previewFile(biddingFileKey)">采购文件</el-button>-->
        <!--          </div>-->
        <!--          <div class="mb10">-->
        <!--            <el-button type="primary" style="width: 100%;" @click="previewFile(bidOpeningFileKey)">开标记录表</el-button>-->
        <!--          </div>-->
        <!--          <div class="mb10">-->
        <!--            <el-button type="primary" style="width: 100%;" @click="previewChangeFile">变更/澄清文件</el-button>-->
        <!--          </div>-->
        <!--        </div>-->
      </el-aside>
      <el-main style="padding: 0;">
        <response-file ref="responseFile" :supplierId="currentSupplierId" :supplierName="currentSupplierName" v-if="currentSupplierId"/>
      </el-main>
    </el-container>
    <contrast></contrast>

    <el-dialog
      top="0"
      width="90%"
      custom-class="pdf-dialog"
      :visible.sync="isChangeFileDialog"
      :close-on-click-modal="false"
    >
      <template v-for="(item,index) in changeFileUrl">
        <pdf-view :src="item" type="blob" height="100%" :key="index"/>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import supplierTabs from './supplierTabs';
import responseFile from './responseFile';
import contrast from './contrast';
import { mapGetters } from 'vuex';
import { fileDown } from '@/api/file';
import { getPreviewCKEditorHtml } from '@/utils';

export default {
  name: 'Index',
  components: {
    supplierTabs,
    responseFile,
    contrast
  },
  props: {
    value: {},
    supplierList: {
      type: Array,
      default: () => []
    },
    biddingFileKey: {
      type: String,
      default: null
    },
    bidOpeningFileKey: {
      type: String,
      default: null
    },
    changeFileKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentSupplierId: this.value,
      currentSupplierName: null,
      isChangeFileDialog: false,
      changeFileUrl: []
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'contrastList'
    ])
  },
  watch: {
    value(value) {
      if (this.value !== this.currentSupplierId) {
        this.setCurrentId(value);
      }
    }
  },
  created() {
    this.setCurrentId(this.value);
  },
  methods: {
    getPreviewCKEditorHtml,
    previewChangeFile() {
      if (this.changeFileKeys.length === 0) {
        this.$modal.msgError('未查询到文件');
        return
      }
      let arr = [];
      this.changeFileKeys.forEach(item => {
        arr.push(fileDown(item))
      })
      Promise.all(arr).then(res => {
        this.changeFileUrl = [];
        res.forEach(item => {
          this.changeFileUrl.push(item.data)
        })
        this.isChangeFileDialog = true;
      })
    },
    previewFile(fileKey) {
      if (!fileKey) {
        this.$modal.msgError('未查询到文件');
        return
      }
      this.$download.previewFile(fileKey)
    },
    positionChapter(val) {
      this.$refs.responseFile.positionChapter(val);
    },
    // 切换供应商
    handleSupplier(supplierId, item) {
      this.setCurrentId(supplierId);
      this.$emit('changeSupplier', supplierId, item);
    },
    // 设置当前供应商ID，以及响应文件、演示视频
    setCurrentId(value) {
      this.currentSupplierId = value;
      this.$emit('input', value);
      let obj = this.supplierList.find(item => item.supplierId === value);
      if (obj) {
        this.currentSupplierName = obj.supplierCompanyName;
      } else {
        this.currentSupplierName = null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.aside-title {
  font-size: 16px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #333333;
  margin-bottom: 15px;
}
</style>
