<template>
  <div class="text-center" :style="{'--theme': theme}">
    <el-dropdown trigger="click" @command="handleCommand">
      <el-button type="primary">
        {{ supplierName }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          :style="{'--theme': theme}"
          v-for="(item,k) in dropArray"
          :key="k"
          :command="item"
          :class="{'drop-active':item.supplierId===currentSupplierId}"
        >
          <div class="drop-item">
            <span>{{ item.supplierCompanyName }}</span>
            <!--            <i class="el-icon-check" v-if="item.judgesComplete===1"></i>-->
            <svg-icon icon-class="eliminate" class-name="eliminate-icon" v-if="item.reviewScore===0"/>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { isEmpty } from '@/utils';
import { mapGetters } from 'vuex';

export default {
  name: 'DropDown',
  props: {
    value: {},
    dropArray: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentSupplierId: this.value,
      supplierName: ''
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  watch: {
    value(value) {
      this.setCurrentId(value);
    }
  },
  created() {
    this.setCurrentId(this.value);
  },
  methods: {
    handleCommand(item) {
      // if (item.reviewScore === 0) return;
      this.setCurrentId(item.supplierId);
      this.$emit('command', item.supplierId, item)
    },
    setCurrentId(value) {
      this.currentSupplierId = value;
      if (isEmpty(value)) {
        this.supplierName = '';
      } else {
        this.supplierName = this.dropArray.find(item => item.supplierId === value).supplierCompanyName;
      }
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.drop-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-icon-check {
    color: #{'var(--theme)'};
    font-size: 20px;
    font-weight: bold;
    margin-left: 10px;
  }

  .eliminate-icon {
    font-size: 35px;
    margin-left: 5px;
    color: $colorDanger;
  }
}

.drop-active {
  background-color: #{'var(--theme)'};
  color: #ffffff;

  .drop-item {
    .el-icon-check {
      color: #FFFFFF
    }
  }
}
</style>
