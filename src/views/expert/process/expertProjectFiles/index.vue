<template>
  <div>
    <el-descriptions :column="2" border>
      <el-descriptions-item
        v-for="(item,index) in fileList"
        :key="index"
        :label="item.name"
        contentClassName="my-label"
      >
        <el-button type="text" size="mini" @click="previewFile(item.url)">预览</el-button>
        <el-button type="text" size="mini" @click="handleDown(item.url)">下载</el-button>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getProjectFileBySubCode } from '@/api/expert/reviewManage'

export default {
  name: 'ExpertProjectFiles',
  data() {
    return {
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'subpackageCode'
    ])
  },
  created() {
    this.getFiles();
  },
  methods: {
    async getFiles() {
      try {
        let { data } = await getProjectFileBySubCode({ buyItemCode: this.buyItemCode, subpackageCode: this.subpackageCode });
        this.fileList = data || [];
      } catch (e) {
        throw new Error(e)
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    }
  }
}
</script>

<style lang="scss">
.my-label {
  min-width: 165px;
}
</style>
