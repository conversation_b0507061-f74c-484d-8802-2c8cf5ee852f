<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          size="mini"
          @click="getList"
        >获取最新数据
        </el-button>
      </el-col>
    </el-row>

    <el-radio-group v-model="voteSupplierId" class="block">
      <el-table :data="tableData" border>
        <el-table-column label="序号" align="center" type="index" width="55"/>
        <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
        <el-table-column label="投票" align="center" key="operate">
          <template v-slot:default="{row}">
            <el-radio
              :label="row.supplierId"
              class="table-radio"
              :disabled="!checkPermi(['expert:process:operate']) || expertAbandon===1"
            >
            </el-radio>
          </template>
        </el-table-column>
      </el-table>
    </el-radio-group>

    <div class="text-center mt10 mb20" v-if="checkPermi(['expert:process:operate'])">
      <el-button
        type="primary"
        @click="handleVote"
        :disabled="expertAbandon===1">
        提交投票
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getSupListToRecord, reviewVote } from '@/api/expert/reviewManage'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'ExpertVoteReview',
  data() {
    return {
      loading: false,
      tableData: [],
      voteSupplierId: null
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'expertAbandon'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handleVote() {
      let obj = this.tableData.find(v => v.supplierId === this.voteSupplierId);
      if (!obj) {
        this.$modal.msgWarning('请选择供应商');
        return
      }
      this.$modal.confirm(`确认投票给 ${obj.supplierCompanyName} ？`).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await reviewVote({
            subpackageCode: this.subpackageCode,
            supplierId: this.voteSupplierId
          })
          this.$modal.msgSuccess('投票成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getSupListToRecord(this.subpackageCode);
        this.tableData = data || [];
        let obj = this.tableData.find(v => v.whetherRecord);
        this.voteSupplierId = obj ? obj.supplierId : null;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss">
</style>
