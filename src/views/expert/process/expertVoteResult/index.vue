<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="55"/>
      <el-table-column label="供应商" align="center" prop="supplierCompanyName"/>
      <el-table-column label="票数" align="center" prop="votesNum"/>
      <el-table-column label="投票人" align="center" prop="votersNames"/>
      <el-table-column label="推荐名次" align="center">
        <template v-slot:default="{row}">
          <el-select
            v-model="tableData.find(v=>v.supplierId===row.supplierId).rank"
            placeholder="请选择"
            :disabled="!isLeader || !checkPermi(['expert:process:operate'])"
            clearable>
            <el-option
              v-for="item in tableData.length"
              :key="item"
              :label="'第'+numToChinese(item)+'候选人'"
              :value="item">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <div class="report-wrap" :style="{'--theme': theme}" v-if="reportFileKey">
      <div class="mr50">
        <div class="report-file">
          <div class="mr5 report-file-icon" @click="previewFile(reportFileKey)">
            <svg-icon icon-class="pdf" class-name="icon-pdf"/>
            <p class="fontSize14">评审报告</p>
          </div>
          <el-button icon="el-icon-view" type="text" @click="previewFile(reportFileKey)">查看</el-button>
          <el-button
            icon="el-icon-s-check"
            type="text"
            @click="handleSign"
            v-if="signatureOff==='0'&&checkPermi(['expert:process:operate'])"
          >
            {{ isSign === 0 ? '签字' : '重新签字' }}
          </el-button>
        </div>
      </div>
      <ul class="report-expert" v-if="signatureOff==='0'">
        <li v-for="(item,index) in judgeSignInfo" :key="index">
          <span class="mr5">{{ item.judgeName }}</span>
          <div>
            <span :style="{'color': theme}" v-if="item.isSign===1"><i class="el-icon-success"></i>已签字</span>
            <span class="text-danger" v-else><i class="el-icon-error"></i>未签字</span>
          </div>
        </li>
      </ul>
    </div>

    <div class="text-center mt20" v-if="isLeader&&checkPermi(['expert:process:operate'])&&!reportFileKey">
      <el-button type="primary" @click="handleSubmitResults">设置供应商排名</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { expertSigne, getSupListToEnd, setResultRanking } from '@/api/expert/reviewManage'
import { checkPermi } from '@/utils/permission'
import { numToChinese } from '@/utils'

export default {
  name: 'ExpertVoteReview',
  data() {
    return {
      tableData: [],
      reportFileKey: null,
      judgeSignInfo: [],
      isSign: 0, // 0-未签字，1-已签字
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'signatureOff',
      'subpackageCode',
      'theme',
      'userId',
      'isLeader'
    ])
  },
  created() {
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList();
  },
  methods: {
    numToChinese,
    checkPermi,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleSign() {
      if (this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.$modal.loading('数据提交中，请稍候...');
            expertSigne({
              subpackageCode: this.subpackageCode,
              fileKey: this.reportFileKey,
              authCode,
              flowId
            }).then(() => {
              this.$modal.msgSuccess('签章成功');
              this.getList();
              this.$modal.closeLoading();
            }).catch((e) => {
              this.$modal.closeLoading();
              throw new Error(e);
            })
          }
        })
      } else {
        this.$modal.loading('数据提交中，请稍候...');
        expertSigne({
          subpackageCode: this.subpackageCode,
          fileKey: this.reportFileKey
        }).then(() => {
          this.$modal.msgSuccess('签章成功');
          this.getList();
          this.$modal.closeLoading();
        }).catch((e) => {
          this.$modal.closeLoading();
          throw new Error(e);
        })
      }
    },
    handleSubmitResults() {
      if (!this.tableData.some(item => item.rank === 1)) {
        this.$modal.msgError('至少选择一个第一候选人');
        return
      }
      this.$prompt('请输入评审说明', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        showInput: true,
        inputType: 'textarea',
        inputPlaceholder: '请输入评审说明',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false
          }
          const reg = '^[ ]+$'
          const re = new RegExp(reg)
          return !re.test(value) && value.length < 500
        },
        inputErrorMessage: '请输入评审说明，最多500个字符'
      }).then(async ({ value }) => {
        try {
          console.log('this.tableData', this.tableData)
          this.$modal.loading('数据提交中，请稍候...');
          await setResultRanking({
            subpackageCode: this.subpackageCode,
            rankDtoList: this.tableData.map(v => {
              return {
                rank: v.rank,
                supplierId: v.supplierId
              }
            }),
            remarksBidRejection: value
          })
          this.$modal.msgSuccess('提交成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    async getList() {
      try {
        let { data } = await getSupListToEnd(this.subpackageCode);
        this.tableData = data.supplierDetailsVoList || [];

        let judgesSign = data.judgesSign || {};
        this.reportFileKey = judgesSign.reportFileKey;
        this.judgeSignInfo = judgesSign.judgeSignInfo || [];
        let obj = this.judgeSignInfo.find(v => v.judgeId === this.userId);
        this.isSign = obj ? obj.isSign : false;
      } catch (e) {
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss">
.report-wrap {
  padding-left: 20px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .report-file {
    display: inline-flex;
    align-items: center;

    .report-file-icon {
      display: inline-block;
      text-align: center;
    }

    .icon-pdf {
      font-size: 46px;
      margin-right: 3px;
    }
  }

  .report-expert {
    li {
      display: inline-flex;
      align-items: center;
      margin-right: 15px;

      span {
        display: flex;
        align-items: center;

        i {
          font-size: 18px;
        }
      }
    }
  }
}
</style>
