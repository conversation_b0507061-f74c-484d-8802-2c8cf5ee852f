<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-form ref="formData" :rules="rules" :model="formData" size="mini">
      <el-table :data="formData.tableData" border>
        <el-table-column label="序号" type="index" width="50" align="center"/>
        <el-table-column label="审查因素" align="center" prop="reviewItem"/>
        <el-table-column label="审查因素描述" align="center" prop="reviewCriteria" min-width="190px"/>
        <el-table-column label="导航" align="center" prop="content" width="150">
          <template v-slot:default="{row}">
            <div
              v-for="(item,k) in row.scoreChapter"
              :key="k"
              @click="clickPosition(item)"
              :style="{'--theme': theme}"
              class="position">
              <el-tooltip effect="dark" :content="'章节 '+item" placement="top">
                <i class="el-icon-aim"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分值" align="center" prop="reviewScore" width="120"/>
        <el-table-column label="评审结果" align="center" prop="score" width="170" class-name="form-cell">
          <template v-slot:default="{row,$index}">
            <el-form-item :prop="'tableData.' + $index + '.score'" :rules="rules.score">
              <el-input
                v-model.trim="row.score"
                class="text-center"
                @input="inputScore"
                :disabled="!checkPermi(['expert:process:operate'])">
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
      <div class="text-right mt10 pr40 fontSize14">总分：{{ totalScore }}</div>
      <div class="text-right mt10 pr20" v-if="checkPermi(['expert:process:operate'])">
        <el-button type="primary" @click="saveReview">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { saveReviewData, getEvaluationMethod } from '@/api/expert/reviewManage';
import { isEmpty } from '@/utils';
import reg from '@/utils/reg';
import { mapGetters } from 'vuex';
import { isNumber } from '@/utils/validate';
import { checkPermi } from '@/utils/permission';
import { yPlus } from '@/utils/bignumber';

export default {
  name: 'ScoreReviewItem',
  props: {
    supplierId: {
      type: Number,
      default: null
    },
    supplierName: {
      type: String,
      default: null
    }
  },
  data() {
    let validatorScore = (rule, value, callback) => {
      if (isEmpty(value)) {
        callback(new Error('每一项评分必填'))
        return
      }
      let index = rule.field.split('.')[1];
      let obj = this.formData.tableData[index];

      let startIndex = obj.reviewCriteria.indexOf('[[');
      let endIndex = obj.reviewCriteria.indexOf(']]');

      if (startIndex >= 0 && endIndex >= 0) {
        let numStr = obj.reviewCriteria.substring(startIndex + 2, endIndex);
        let numList = numStr.split('-');
        if (numList.indexOf(value) === -1) {
          callback(new Error(`只能输入${numList.join('、')}`))
          return;
        }
        callback();
        return;
      }

      if (!(reg.money.test(value) && Number(value) <= obj.reviewScore)) {
        callback(new Error(`数据格式不对(0.00~${obj.reviewScore})`));
        return;
      }
      callback();
    };
    return {
      isType: 1, // 合格制type=0，打分制 type=1
      loading: false,
      formData: {
        tableData: []
      },
      rules: {
        score: { required: true, validator: validatorScore, trigger: 'blur' }
      }
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'userId',
      'nickName',
      'theme',
      'scoreData'
    ]),
    totalScore() {
      let num = 0;
      this.formData.tableData.forEach(item => {
        let val = Number(item.score);
        num = yPlus(num, isNumber(val) ? val : 0)
      })
      return num
    }
  },
  watch: {
    supplierId: {
      immediate: true,
      handler(val) {
        if (this.supplierId) {
          this.getList();
        }
      }
    }
  },
  created() {

  },
  methods: {
    checkPermi,
    inputScore() {
      let list = this._.cloneDeep(this.scoreData);
      let reviewList = this.formData.tableData.map(v => {
        return { uuid: v.uuid, score: v.score }
      });
      if (list.some(item => item.supplierId === this.supplierId)) {
        list.forEach(item => {
          if (item.supplierId === this.supplierId) {
            item.reviewList = reviewList
          }
        })
      } else {
        list.push({
          supplierId: this.supplierId,
          scoreList: reviewList
        })
      }
      this.$store.dispatch('expertProcess/setScoreData', list);
    },
    saveReview() {
      this.$refs['formData'].validate(async (valid) => {
        if (valid) {
          this.$confirm('<p>请确认是否提交？</p>', '提示', {
            customClass: 'max-tip',
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }).then(async () => {
            try {
              this.$modal.loading('数据提交中，请稍候...');
              await saveReviewData({
                subpackageCode: this.subpackageCode,
                supplierCompanyName: this.supplierName,
                supplierId: this.supplierId,
                isType: this.isType,
                judgeId: this.userId,
                judgeName: this.nickName,
                resultBoList: this.formData.tableData.map(item => {
                  return {
                    uuid: item.uuid,
                    score: item.score,
                    reviewItem: item.reviewItem
                    // reviewCriteria: item.reviewCriteria
                  }
                })
              });
              await this.getList();
              this.$emit('saveReview', this.supplierId);
              this.$modal.msgSuccess('提交成功');
              this.$modal.closeLoading()
            } catch (e) {
              this.$modal.closeLoading()
              throw new Error(e);
            }
          })
        }
      })
    },
    // 点击得分点定位
    clickPosition(item) {
      this.$emit('handlePosition', item)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getEvaluationMethod({
          subpackageCode: this.subpackageCode,
          type: this.isType, // 1-评分   0-符合性评审
          supplierId: this.supplierId
        })
        this.formData.tableData = data;
        let obj = this.scoreData.find(v => v.supplierId === this.supplierId);
        if (obj) {
          this.formData.tableData.forEach(item => {
            let obj1 = obj.reviewList.find(v => v.uuid === item.uuid)
            if (obj1) {
              this.$set(item, 'score', obj1.score)
            }
          })
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.position {
  display: inline-block;
  color: #{'var(--theme)'};
  font-size: 25px;
  cursor: pointer;
  vertical-align: middle;

  & + .position {
    margin-left: 15px;
  }
}
</style>
