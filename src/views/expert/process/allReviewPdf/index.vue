<template>
  <div>
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <drop-down
          :dropArray="supplierList"
          v-model="supplierId"
          @command="changeSupplier"
        >
        </drop-down>
      </el-col>
    </el-row>

    <all-review-pdf-item
      class="mb20"
      :supplierId="supplierId"
      :supplierName="supplierName"
      @handlePosition="handlePosition"
    >
    </all-review-pdf-item>

    <review-files-pdf
      ref="reviewFiles"
      :supplierList="supplierList"
      v-model="supplierId"
      @changeSupplier="changeSupplier">
    </review-files-pdf>
  </div>
</template>

<script>
import { listReviewSupplier } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import reviewFilesPdf from '../components/reviewFiles/reviewFilesPdf';
import dropDown from '../components/dropDown';
import allReviewPdfItem from '@/views/expert/process/allReviewPdf/allReviewPdfItem.vue'
import { checkPermi } from '@/utils/permission';

export default {
  name: 'AllReviewPdf',
  components: {
    reviewFilesPdf,
    allReviewPdfItem,
    dropDown
  },
  data() {
    return {
      supplierList: [],
      supplierId: null,
      supplierName: null
    }
  },
  computed: {
    ...mapGetters([
      'isLeader',
      'subpackageCode',
      'expertProcess'
    ])
  },
  created() {
    this.getSupplierList()
  },
  methods: {
    checkPermi,
    handlePosition(val) {
      this.$refs.reviewFiles.positionChapter(val);
    },
    changeSupplier(supplierId, item) {
      this.supplierName = item.supplierCompanyName;
    },
    async getSupplierList(supplierId) {
      try {
        let { data } = await listReviewSupplier(this.subpackageCode);
        this.supplierList = data;
        if (supplierId) {
          this.supplierId = supplierId;
          this.supplierName = this.supplierList.find(v => v.supplierId === supplierId).supplierCompanyName;
        } else {
          this.supplierId = this.supplierList[0].supplierId;
          this.supplierName = this.supplierList[0].supplierCompanyName;
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
