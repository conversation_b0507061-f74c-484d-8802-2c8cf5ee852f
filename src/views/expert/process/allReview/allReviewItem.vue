<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-table :data="tableData" border>
      <el-table-column label="序号" type="index" width="50" align="center"/>
      <el-table-column label="审查因素" align="center" prop="reviewItem"/>
      <el-table-column label="审查因素描述" align="center" prop="reviewCriteria" min-width="200px"/>
      <el-table-column label="导航" align="center" prop="content" width="150">
        <template v-slot:default="{row}">
          <div
            v-for="(item,k) in row.scoreChapter"
            :key="k"
            @click="clickPosition(item)"
            :style="{'--theme': theme}"
            class="position">
            <el-tooltip effect="dark" :content="'章节 '+item" placement="top">
              <i class="el-icon-aim"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getEvaluationMethodAll } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';

export default {
  name: 'AllReviewItem',
  props: {
    supplierId: {
      type: Number,
      default: null
    },
    supplierName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'theme'
    ])
  },
  watch: {
    supplierId: {
      immediate: true,
      handler(val) {
        if (this.supplierId) {
          this.getList();
        }
      }
    }
  },
  created() {

  },
  methods: {
    // 点击得分点定位
    clickPosition(item) {
      this.$emit('handlePosition', item)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getEvaluationMethodAll({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId
        })
        this.tableData = data || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.position {
  display: inline-block;
  color: #{'var(--theme)'};
  font-size: 25px;
  cursor: pointer;
  vertical-align: middle;

  & + .position {
    margin-left: 15px;
  }
}
</style>
