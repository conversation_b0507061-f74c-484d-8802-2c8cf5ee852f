<template>
  <div>
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <drop-down
          :dropArray="supplierList"
          v-model="supplierId"
          @command="changeSupplier"
        >
        </drop-down>
      </el-col>
      <el-col
        :span="1.5"
        class="fr mr20"
        v-if="checkPermi(['expert:process:operate'])"
      >
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="small"
          @click="handleExport"
        >
          下载技术与商务评审响应对照表
        </el-button>
      </el-col>
    </el-row>

    <score-review-pdf-item
      class="mb20"
      :supplierId="supplierId"
      :supplierName="supplierName"
      @handlePosition="handlePosition"
      @saveReview="saveReview"
    >
    </score-review-pdf-item>

    <review-files-pdf
      ref="reviewFiles"
      :supplierList="supplierList"
      v-model="supplierId"
      @changeSupplier="changeSupplier">
    </review-files-pdf>
  </div>
</template>

<script>
import { downReviewPdf, listReviewSupplier } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import scoreReviewPdfItem from './scoreReviewPdfItem.vue';
import dropDown from '../components/dropDown';
import { checkPermi } from '@/utils/permission';
import reviewFilesPdf from '@/views/expert/process/components/reviewFiles/reviewFilesPdf.vue'

export default {
  name: 'ScoreReview',
  components: {
    reviewFilesPdf,
    scoreReviewPdfItem,
    dropDown
  },
  data() {
    return {
      supplierList: [],
      supplierId: null,
      supplierName: null
    }
  },
  computed: {
    ...mapGetters([
      'isLeader',
      'subpackageCode'
    ])
  },
  created() {
    this.getSupplierList()
  },
  methods: {
    checkPermi,
    async handleExport() {
      try {
        this.$modal.loading('正在下载数据，请稍候...');
        let res = await downReviewPdf({
          subpackageCode: this.subpackageCode,
          supplierId: this.supplierId,
          type: 1
        });
        this.$modal.closeLoading()
        this.$download.downloadSave(res);
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e)
      }
    },
    saveReview(supplierId) {
      this.getSupplierList(supplierId);
    },
    handlePosition(val) {
      this.$refs.reviewFiles.positionChapter(val);
    },
    changeSupplier(supplierId, item) {
      this.supplierName = item.supplierCompanyName;
    },
    async getSupplierList(supplierId) {
      try {
        let { data } = await listReviewSupplier(this.subpackageCode);
        this.supplierList = data.filter(v => v.reviewScore === 1);
        if (supplierId) {
          this.supplierId = supplierId;
          this.supplierName = this.supplierList.find(v => v.supplierId === supplierId).supplierCompanyName;
        } else {
          this.supplierId = this.supplierList[0].supplierId;
          this.supplierName = this.supplierList[0].supplierCompanyName;
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
