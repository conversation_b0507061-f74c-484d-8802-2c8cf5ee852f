<template>
  <div class="app-container">
    <el-table :data="tableData" v-loading="loading" border>
      <el-table-column label="序号" type="index" width="50" align="center"/>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
        align="center"
        prop="buyItemName"
      >
      </el-table-column>
      <el-table-column label="标段(包)" align="center" prop="subpackageName"></el-table-column>
      <el-table-column
        :label="selectDictValue(dict.type.sys_dict_translate,'purchaseMethod')"
        align="center"
        prop="purchaseMethodName"
      >
      </el-table-column>
      <el-table-column label="采购（招标）文件" align="center" prop="claimsFileKey">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.claimsFileKey)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="投票选组长" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLookVote(row)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="评审方式" align="center" key="reviewMethod">
        <template v-slot:default="{row}">
          <el-radio-group
            :disabled="!(row.teamLeaderId === userId&&checkPermi(['expert:process:operate']))"
            v-model="row.reviewMethod"
            @change="handleChange(row)"
          >
            <el-radio
              v-for="(dict,index) in dict.type.review_method_type"
              :label="Number(dict.value)"
              :key="index">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="评审" align="center" v-if="checkPermi(['expert:process:operate'])">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-d-arrow-right"
            @click="toProcess(row)"
          >进入
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="投票"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="5vh"
      width="90%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >

      <el-row :gutter="10" class="mb10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="getListChoiceLeader"
          >获取最新投票信息
          </el-button>
        </el-col>
      </el-row>
      <el-table :data="votingList">
        <el-table-column label="评委姓名" align="center" prop="judgeName"/>
        <el-table-column label="科室代表" align="center" prop="delegate">
          <template v-slot:default="{row}">
            <span v-if="row.delegate===1">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column label="票数" align="center" prop="numberVotes"/>
        <el-table-column label="投票人" align="center" prop="judgeNames">
          <template v-slot:default="{row}">
            <span v-if="row.judgeNames&&row.judgeNames.length>0">{{ row.judgeNames.join('、') }}</span>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="是否投票" align="center" prop="voting">
          <template v-slot:default="{row}">
            <span v-if="row.voting === 1">已投票</span>
            <span v-else class="text-danger">未投票</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" v-if="checkPermi(['expert:process:operate'])">
          <template v-slot:default="{row}">
            <el-button
              v-if="isVote!==1"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleVote(row)"
            >投票
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="text-center">
        <el-button @click="isDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReviewManage, listChoiceLeader, addChoiceLeader, examineReview } from '@/api/expert/reviewManage';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { isEmpty } from '@/utils'

export default {
  name: 'ReviewManage',
  dicts: ['review_method_type', 'sys_dict_translate'],
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      isDialog: false,
      subpackageCode: null,
      isVote: 0, // 是否投票
      votingList: []
    }
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName'
    ])
  },
  methods: {
    checkPermi,
    async toProcess(row) {
      if (!this.$auth.hasPermi('expert:process:query')) {
        this.$modal.msgWarning('当前操作没有权限');
        return
      }
      if (isEmpty(row.reviewMethod)) {
        this.$modal.msgWarning('请设置评审方式');
        return
      }
      try {
        await this.$store.dispatch('process/setBuyItemCode', row.buyItemCode);
        await this.$store.dispatch('process/setBuyItemName', row.buyItemName);
        await this.$store.dispatch('process/setSubpackageName', row.subpackageName);
        await this.$store.dispatch('process/setSubpackageCode', row.subpackageCode);
        await this.$store.dispatch('expertProcess/setIsLeader', { subpackageCode: row.subpackageCode, userId: this.userId });
        await this.$store.dispatch('expertProcess/setContrastList', []);
        await this.$store.dispatch('expertProcess/setQualifiedData', []);
        await this.$store.dispatch('expertProcess/setScoreData', []);
        let res = await this.$store.dispatch('expertProcess/setExpertProcess', row.subpackageCode);
        await this.$router.push({ path: res[0].path });
      } catch (e) {
        throw new Error(e);
      }
    },
    handleChange(row) {
      let text = row.reviewMethod == 0 ? '调研' : row.reviewMethod == 1 ? '评审' : '投票';
      this.$modal.confirm('确认要设置评审方式为' + text + '吗？').then(() => {
        return examineReview({ subpackageCode: row.subpackageCode, reviewMethod: Number(row.reviewMethod) })
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('设置成功')
      }).catch(() => {
        this.getList();
      })
    },
    handleVote(row) {
      this.$modal.confirm(`确认投票给 ${row.judgeName} ？`).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await addChoiceLeader({
            subpackageCode: this.subpackageCode,
            judgeId: this.userId,
            judgeName: this.nickName,
            judgeLeaderId: row.judgeId,
            judgeLeaderName: row.judgeName
          })
          await this.getListChoiceLeader();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    async handleLookVote(row) {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        this.subpackageCode = row.subpackageCode;
        await this.getListChoiceLeader();
        this.$modal.closeLoading();
        this.isDialog = true;
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async getListChoiceLeader() {
      try {
        let { data } = await listChoiceLeader(this.subpackageCode)
        this.votingList = data;
        this.isVote = this.votingList.find(item => item.judgeId === this.userId).voting;
      } catch (e) {
        throw new Error(e);
      }
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listReviewManage(this.queryParams);
        this.tableData = data.records;
        this.total = data.total;
        this.loading = false
      } catch (e) {
        this.loading = false
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
