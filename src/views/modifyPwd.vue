<template>
  <div>
    <el-dialog
      title="修改密码"
      center
      width="90%"
      custom-class="maxW500"
      :visible.sync="isDialog"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="text-center text-danger mb10">
        <div>您长时间未修改密码，或当前密码强度比较低，请修改密码！</div>
        <div>密码长度8-20位，必须包含数字、字母、特殊字符$@!%*#?&</div>
      </div>
      <el-form ref="form" :model="form" label-width="110px" :rules="rules">
        <el-form-item label="原密码：" prop="oldPassword">
          <el-input v-model.trim="form.oldPassword"></el-input>
        </el-form-item>
        <el-form-item label="新密码：" prop="newPassword">
          <el-input v-model.trim="form.newPassword"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码：" prop="repeatPassword">
          <el-input v-model.trim="form.repeatPassword"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="isLoading">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateUserPwd } from '@/api/system/user';
import reg from '@/utils/reg';

export default {
  data() {
    const validateOldPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入原密码'))
      } else {
        if (this.form.newPassword !== '') {
          this.$refs.form.validateField('newPassword')
        }
        callback()
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else if (value === this.form.oldPassword) {
        callback(new Error('不能和原密码一致!'))
      } else {
        if (!reg.password.test(value)) {
          callback(new Error('8-20位，必须包含数字、字母、特殊字符$@!%*#?&'));
          return;
        }
        if (this.form.repeatPassword !== '') {
          this.$refs.form.validateField('repeatPassword')
        }
        callback()
      }
    };
    const validateRepeatPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    };
    return {
      isDialog: false,
      form: {
        oldPassword: '',
        newPassword: '',
        repeatPassword: ''
      },
      rules: {
        oldPassword: [{ required: true, validator: validateOldPassword, trigger: 'blur' }],
        newPassword: [{ required: true, validator: validatePassword, trigger: 'blur' }],
        repeatPassword: [{ required: true, validator: validateRepeatPassword, trigger: 'blur' }]
      },
      isLoading: false,
      access_token: null
    };
  },
  methods: {
    show(data) {
      this.access_token = data;
      this.isDialog = true;
      this.reset();
    },
    reset() {
      this.form = {
        oldPassword: '',
        newPassword: '',
        repeatPassword: ''
      }
      this.resetForm('form')
    },
    submit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.isLoading = true;
            await updateUserPwd(this.form.oldPassword, this.form.newPassword, this.access_token);
            this.$message.success('修改成功');
            this.isLoading = false;
            this.isDialog = false;
            this.$emit('ok');
          } catch (e) {
            this.isLoading = false;
            throw new Error(e)
          }
        } else {
          return false
        }
      });
    }
  }
};
</script>
