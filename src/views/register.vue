<template>
  <div class="register">
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">
      <h3 class="title">供应商注册</h3>
      <el-form-item prop="username">
        <el-input
          v-model.trim="registerForm.username"
type="text"
auto-complete="off"
placeholder="手机号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="nickName">
        <el-input v-model.trim="registerForm.nickName" type="text" auto-complete="off" placeholder="公司名称">
          <svg-icon slot="prefix" icon-class="tree-table" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="licNumber">
        <el-input v-model.trim="registerForm.licNumber" type="text" auto-complete="off" placeholder="统一社会信用代码">
          <svg-icon slot="prefix" icon-class="tree" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model.trim="registerForm.password"
          type="text"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input
          v-model.trim="registerForm.confirmPassword"
          type="text"
          auto-complete="off"
          placeholder="确认密码"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaOnOff">
        <el-input
          v-model.trim="registerForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
        </el-input>
        <div class="register-code">
          <img :src="codeUrl" @click="getCode" class="register-code-img"/>
        </div>
      </el-form-item>
      <el-form-item prop="mobileCode" v-if="versionType==='jxzl'||versionType==='syth'">
        <el-input
          v-model.trim="registerForm.mobileCode"
          auto-complete="off"
          placeholder="短信验证码"
          style="width: 63%"
          @keyup.enter.native="handleRegister"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
        </el-input>
        <div class="register-code">
          <el-button
            type="primary"
            @click="getSmsCode"
            :disabled="!isVerifyPhone||!isClickCode"
            class="code-btn"
          >{{ this.codeBtnValue }}
          </el-button>
        </div>
      </el-form-item>
      <p class="text-danger fontSize14 mb10" style="line-height: 1.5;">
        温馨提示：注册账号后，请登录完善企业信息，并创建印章，否则无法进行项目报名！</p>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleRegister"
        >
          <span v-if="!loading">注 册</span>
          <span v-else>注 册 中...</span>
        </el-button>
        <div style="float: left;">
          <router-link class="link-type" :to="'/home'">返回首页</router-link>
        </div>
        <div style="float: right;">
          <router-link class="link-type" :to="'/login'">使用已有账户登录</router-link>
        </div>
      </el-form-item>
    </el-form>

    <vue-particles
      class="particles-js"
      color="#409EFF"
      :particleOpacity="0.7"
      :particlesNumber="80"
      shapeType="circle"
      :particleSize="4"
      linesColor="#409EFF"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="150"
      :moveSpeed="3"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
    ></vue-particles>
  </div>
</template>

<script>
import { getCodeImg, getSmsCode, register } from '@/api/login'
import reg from '@/utils/reg'

export default {
  name: 'Register',
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      codeUrl: '',
      registerForm: {
        username: '',
        nickName: '',
        licNumber: '',
        password: '',
        confirmPassword: '',
        code: '',
        uuid: '',
        mobileCode: null
      },
      registerRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的手机号' },
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          { min: 2, max: 50, message: '公司名称长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        licNumber: [
          { required: true, trigger: 'blur', message: '请输入统一社会信用代码' },
          { pattern: reg.unifiedCreditCode, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '请输入您的密码' },
          { pattern: reg.password, message: '8-20位，必须包含数字、字母、特殊字符$@!%*#?&', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请再次输入您的密码' },
          { required: true, validator: equalToPassword, trigger: 'blur' }
        ],
        code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
        mobileCode: [{ required: true, trigger: 'change', message: '请输入短信验证码' }]
      },
      loading: false,
      captchaOnOff: true,
      codeBtnValue: '获取验证码',
      countdown: 60,
      isClickCode: true,
      timer: null
    }
  },
  computed: {
    isVerifyPhone() {
      return reg.cellphone.test(this.registerForm.username)
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getCode()
  },
  methods: {
    async getSmsCode() {
      try {
        await getSmsCode({ mobile: this.registerForm.username, smsType: 'SMS_REGISTER' });
        this.$modal.msgSuccess('验证码发送成功')
        this.countdown = 60;
        this.intervalFun();
        this.timer = setInterval(() => {
          if (this.countdown === 0) {
            this.codeBtnValue = '获取验证码';
            this.isClickCode = true;
            clearInterval(this.timer);
          } else {
            this.intervalFun();
          }
        }, 1000);
      } catch (e) {
        throw new Error(e);
      }
    },
    intervalFun() {
      this.codeBtnValue = '重新发送(' + this.countdown + ')';
      this.isClickCode = false;
      this.countdown--;
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff
        if (this.captchaOnOff) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.registerForm.uuid = res.uuid
        }
      })
    },
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          register(this.registerForm).then(res => {
            const username = this.registerForm.username
            this.$alert('<font color=\'red\'>恭喜你，您的账号 ' + username + ' 注册成功！</font>', '系统提示', {
              dangerouslyUseHTMLString: true,
              type: 'success'
            }).then(() => {
              this.$router.push('/login')
            }).catch(() => {
            })
          }).catch(() => {
            this.loading = false
            if (this.captchaOnOff) {
              this.getCode()
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.register {
  width: 100%;
  height: 100%;
  //background-image: url("../assets/images/login-background.jpg");
  background-image: linear-gradient(-180deg, #1a1454 0%, #0e81a5 100%);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.particles-js {
  width: 100%;
  height: calc(100% - 5px);
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #000000;
}

.register-form {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.75);
  width: 100%;
  max-width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.register-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.register-code-img {
  height: 38px;
}
</style>
