<template>
  <div>
    <el-dialog
      title="认证文件上传"
      center
      width="90%"
      custom-class="maxW600"
      :visible.sync="isDialog"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item label="系统验证码：" prop="sysPwd">
          <el-input
            v-model.trim="form.sysPwd"
            placeholder="请输入（必填）"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="认证文件：" prop="fileList">
          <file-upload-select
            v-model="form.fileList"
            :fileSize="1"
            :limit="1"
            :fileType="['lic']"
            @change="uploadFile"
            @remove="removeFile"
          >
          </file-upload-select>
        </el-form-item>
        <el-form-item label="系统识别码：" prop="sysCode">
          <el-input
            v-model.trim="form.sysCode"
            disabled
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="isDialog = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="submit"
          :loading="isLoading"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { upLic } from '@/api/license'

export default {
  data() {
    return {
      isDialog: false,
      form: {
        sysPwd: null,
        licFile: null,
        fileList: [],
        sysCode: null
      },
      rules: {
        sysPwd: [
          { required: true, trigger: 'blur', message: '请输入' }
        ],
        fileList: [
          { required: true, trigger: 'change', message: '请上传' }
        ],
        sysCode: [
          { required: true, trigger: 'blur', message: '请输入' }
        ]
      },
      isLoading: false,
      token: null
    };
  },
  methods: {
    show(data) {
      let { token, sysCode } = data || {};
      this.token = token;
      this.isDialog = true;
      this.reset();
      this.form.sysCode = sysCode;
    },
    reset() {
      this.form = {
        sysPwd: null,
        licFile: null,
        fileList: [],
        sysCode: null
      };
      this.resetForm('form');
    },
    removeFile(data) {
      console.log(data);
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
      this.form.licFile = fileList.length === 0 ? null : fileList[0].file;
    },
    submit() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.isLoading = true;
            await upLic({
              ...this.form,
              token: this.token
            });
            this.$message.success('提交成功');
            this.isLoading = false;
            this.isDialog = false;
            this.$emit('ok');
          } catch (e) {
            this.isLoading = false;
            throw new Error(e)
          }
        } else {
          return false
        }
      });
    }
  }
};
</script>
