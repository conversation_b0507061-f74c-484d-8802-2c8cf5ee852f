<template>
  <div>
    <el-steps :active="active" direction="vertical" class="response-steps">
      <el-step :title="responseFileName">
        <div class="description" slot="description">
          <el-tabs v-model="activeName">
            <el-tab-pane name="first">
              <span slot="label">
                <span class="text-danger mr5" v-if="!pdfFile">*</span>
                {{ responseFileName }}
              </span>
              <file-upload-single
                :disabled="releaseStatus==='1' || permit==='0' || !checkPermi(['supplier:process:operate'])"
                :showTip="true"
                :autoUpload="false"
                :file-size="300"
                accept=".pdf"
                uploadName="responseFile"
                @onSuccess="handleUpload"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini"
                  :disabled="releaseStatus==='1' || permit==='0' || !checkPermi(['supplier:process:operate'])">
                  {{
                    (releaseStatus === '0' || releaseStatus === '1') ? '重新上传' + responseFileName : '上传' + responseFileName
                  }}
                </el-button>
              </file-upload-single>
              <div class="mt20" v-if="checkFunction(['supplier_response_annex'])">
                <annex-upload
                  :disabled="releaseStatus==='1' || permit==='0' || !checkPermi(['supplier:process:operate'])"
                  @update="getResponseFileInfo"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane name="second">
               <span slot="label">
                <span class="text-danger mr5" v-if="!isAllSetScorePoint">*</span>
                评审标准响应对照
              </span>
              <set-score-point
                :disabled="releaseStatus==='1' || permit==='0' || !checkPermi(['supplier:process:operate'])"
                :reviewList="reviewList"
                :responseFileName="responseFileName"
                @update="getResponseFileInfo"
              />
            </el-tab-pane>
            <el-tab-pane name="third">
               <span slot="label">
                <span class="text-danger mr5" v-if="!isSetBargaining">*</span>
                投标函附录/报价表
              </span>
              <set-bargaining
                :disabled="releaseStatus==='1' || permit==='0' || !checkPermi(['supplier:process:operate'])"
                :heads="heads"
                :bodyMaps="bodyMaps"
                :purchaseBodyMaps="purchaseBodyMaps"
                @update="getResponseFileInfo"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-step>
      <el-step title="文件检查">
        <div class="description" slot="description">
          <el-button
            type="primary"
            size="mini"
            :disabled="releaseStatus!=='0'"
            @click="previewFile"
          >
            {{ '检查' + responseFileName }}
          </el-button>
          <div class="el-upload__tip">
            <p>1、请检查文件内容是否正确</p>
            <p>2、如果文件预览页面模糊不清晰，可以点击预览页面右上角下载按钮，将文件下载到本地阅读</p>
          </div>
        </div>
      </el-step>
      <el-step title="投递">
        <div class="description" slot="description">
          <el-button
            type="primary"
            size="mini"
            :disabled="releaseStatus!=='0' || permit==='0' || !checkPermi(['supplier:process:operate'])"
            @click="sealAndDeliver">
            投递
          </el-button>
        </div>
      </el-step>
      <el-step title="投递完成">
        <div class="description" slot="description">
          <el-button
            type="primary"
            size="mini"
            :disabled="releaseStatus!=='1'"
            @click="previewFile"
          >
            文件预览
          </el-button>
          <el-button
            type="primary"
            size="mini"
            :disabled="releaseStatus!=='1' || !checkPermi(['supplier:process:operate'])"
            @click="handleWithdraw"
          >
            撤回
          </el-button>
          <div class="el-upload__tip">
            <p>1、请检查文件内容是否正确</p>
            <p>2、如果文件预览页面模糊不清晰，可以点击预览页面右上角下载按钮，将文件下载到本地阅读</p>
          </div>
        </div>
      </el-step>
    </el-steps>

    <el-descriptions
      :column="1"
      v-hasFunction="['supplier_demo_video']"
    >
      <el-descriptions-item
        label="演示视频"
        :contentStyle="{'display':'block'}"
      >
        <file-upload-single
          :disabled=" permit==='0' || !checkPermi(['supplier:process:operate'])"
          :showUploadBtn="!videoUrl"
          :showTip="true"
          :autoUpload="false"
          :file-size="75"
          accept=".mp4"
          uploadName="demoVideo"
          :params="{
              fileTypeName: 'demoVideo',
              buyItemCode: buyItemCode,
              subpackageCode: subpackageCode,
              supplierNumber: userId,
              yearMonthSplit: createYearMonth
            }"
          @onSuccess="handleVideoUpload"
        >
          <el-button
            slot="upload-btn"
            type="primary"
            size="mini"
            :disabled="permit==='0' || !checkPermi(['supplier:process:operate'])">
            上传
          </el-button>
          <div slot="upload-right" v-if="videoUrl">
            <el-button
              type="primary"
              size="mini"
              @click="previewVideo">
              查看
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="withdrawVideo"
              :disabled="!checkPermi(['supplier:process:operate'])">
              撤回
            </el-button>
          </div>
        </file-upload-single>
        <p class="el-upload__tip" v-if="!videoUrl">
          信息化系统类项目请上传 mp4 格式的演示视频，时长不得超过10分钟
        </p>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog
      title="文件加密"
      :visible.sync="isPwdDialog"
      center
      top="5vh"
      custom-class="maxW600"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="pwd-box">
        <div class="text-center">
          <h3 class="mb20 fontSize18">请输入6位数字密码</h3>
          <pwd-group :pwdLength='6' v-model="filePassword"></pwd-group>
        </div>
        <div class="mt20">
          <p class="text-danger fontSize20" style="margin-bottom: 10px;">
            特别提醒
          </p>
          <div class="text-left fontSize20">
            <p style="margin-bottom: 5px;">1、请务必牢记{{ responseFileName }}密码，以免影响评委阅读评审。</p>
            <p>2、本人确认本文件为本人制作的本项目{{ responseFileName }}，本文件所有内容经本人确认，且完全同意！</p>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <el-button @click="isPwdDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPassword">确认</el-button>
      </template>
    </el-dialog>

    <TipProgress
      ref="progress"
      :percentage="percentage"
    />
  </div>
</template>

<script>
import { fileUploadByAnnex } from '@/api/file';
import {
  demoVideoPost,
  demoVideoWithdraw,
  getResponseFile,
  responseFileComplete, responseFilePdfUpload,
  responseFileReleaseAndStamp,
  responseFileWithdraw
} from '@/api/supplier/supplierProcess';
import { getStore, isEmpty } from '@/utils';
import { mapGetters } from 'vuex';
import { checkFunction, checkPermi } from '@/utils/permission';
// import { encryptFileToBinary } from '@/utils/jsencrypt'
import Resumable from '@/plugins/resumable'
import { getToken } from '@/utils/auth'
import TipProgress from '@/components/TipProgress/index.vue'
import AnnexUpload from '@/views/supplier/process/responseFileUpload/annexUpload.vue'
import SetScorePoint from '@/views/supplier/process/responseFilePdfUpload/setScorePoint.vue'
import SetBargaining from '@/views/supplier/process/responseFilePdfUpload/setBargaining.vue'

export default {
  name: 'ResponseFileUpload',
  components: {
    TipProgress,
    AnnexUpload,
    SetScorePoint,
    SetBargaining
  },
  data() {
    return {
      permit: '0', // 是否允许响应: 0-不允许，1-允许
      pdfFile: null,
      videoUrl: null,
      releaseStatus: null, // 0已上传 1已盖章
      isPwdDialog: false,
      filePassword: null,
      signOff: null,
      signVersion: null,
      resumable: null,
      percentage: 100,
      activeName: 'first',
      reviewList: [],
      isAllSetScorePoint: false,
      isSetBargaining: false,
      heads: [],
      purchaseBodyMaps: [],
      bodyMaps: []
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'purchaseMethodCode',
      'getPurchaseDict',
      'buyItemCode',
      'subpackageCode',
      'orgCode',
      'createYearMonth',
      'userId'
    ]),
    responseFileName() {
      return this.getPurchaseDict(this.purchaseMethodCode, 'responseFile')
    },
    active() {
      // 0 待上传 1已上传 2已盖章
      let status = null;
      if (isEmpty(this.releaseStatus)) {
        status = 0
      } else {
        switch (this.releaseStatus) {
          case '0':
            status = 2
            break;
          case '1':
            status = 4
            break;
          default:
            status = 4
        }
      }
      console.log('this.releaseStatus', this.releaseStatus)
      console.log(status)
      return status
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('sys.signature.off').then(response => {
      this.signOff = response.data;
    })
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getResponseFileInfo();
  },
  mounted() {
    this.initResumable();
  },
  methods: {
    checkPermi,
    checkFunction,
    initResumable() {
      this.resumable = new Resumable({
        target: process.env.VUE_APP_BASE_API + '/bidding/supplier/answer/up', // 替换为你的上传地址
        // 可选，额外的查询参数
        query: {
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode
        },
        headers: {
          'Authorization': 'Bearer ' + getToken(),
          'validToken': getStore('validToken')
        },
        withCredentials: true,
        // setChunkTypeFromFile: true,
        fileType: ['tepc'], // 允许上传的文件类型
        chunkSize: 10 * 1024 * 1024, // 分块大小，单位字节，这里设置为1MB
        testChunks: false, // 是否测试分块完整性，默认为false
        throttleProgressCallbacks: 1, // 节流上传进度回调的频率（毫秒）
        simultaneousUploads: 4, // 同时上传的文件数
        fileParameterName: 'file', // 用于文件块的多部分请求参数的名称
        chunkNumberParameterName: 'chunkNumber', // 当前上传 POST 参数中用于文件块的块索引
        totalChunksParameterName: 'totalChunks', // 用于文件块的块总数 POST 参数的名称
        fileNameParameterName: 'filename', // 用于文件块的原始文件名 POST 参数的名称
        currentChunkSizeParameterName: 'currentChunkSize' // 用于文件块的当前块大小 POST 参数的名称
      });
      // 绑定文件选择器，如果你的上传按钮不是input[type=file]则需要调整选择器或手动添加文件。
      // this.resumable.assignBrowse(document.querySelector('input[type=file]'));
      // 文件添加时触发的事件处理函数
      this.resumable.on('fileAdded', this.onFileAdded);
      this.resumable.on('uploadStart', this.onUploadStart);
      this.resumable.on('fileRetry', this.onFileRetry);
      // 文件上传进度时触发的事件处理函数
      this.resumable.on('fileProgress', this.onFileProgress);
      // 文件上传成功时触发的事件处理函数
      this.resumable.on('fileSuccess', this.onFileSuccess);
      // 文件上传失败时触发的事件处理函数
      this.resumable.on('fileError', this.onFileError);
    },
    async handleUpload(data) {
      // console.log('handleUpload', data.file)
      // console.log(this.resumable)
      // this.resumable.addFile(data.file);
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        await responseFilePdfUpload({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          file: data.file
        })
        await this.getResponseFileInfo();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('上传成功');
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    onFileAdded(file, event) {
      console.log('File added:', file, event);
      this.$refs.progress.show();
      this.resumable.upload();
    },
    onUploadStart() {
      // 上传开始前触发，可以用来构造 FormData 和发送数据
      console.log('onUploadStart:', this.resumable.files)
    },
    onFileRetry(file) {
      console.log('File retry:', file);
    },
    onFileProgress(file) {
      console.log('File progress:', file.progress()); // 获取上传进度信息，范围从0到100。
      this.percentage = Math.round(file.progress() * 100)
    },
    async onFileSuccess(file, message) {
      console.log('File uploaded successfully:', file, message);
      this.resumable.cancel();
      try {
        await responseFileComplete({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode
        })
        this.$modal.msgSuccess('上传成功');
        await this.getResponseFileInfo();
        this.$refs.progress.close();
      } catch (e) {
        this.$refs.progress.close();
        throw new Error(e);
      }
    },
    onFileError(file, message) {
      console.error('File upload error:', file, message);
      this.resumable.cancel();
      this.$modal.msgError(message);
      this.$refs.progress.close();
    },
    previewVideo() {
      this.$download.previewFileByAnnex(this.videoUrl);
    },
    withdrawVideo() {
      this.$alert('确定要撤回演示视频？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await demoVideoWithdraw({
            videoUrl: this.videoUrl,
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode
          })
          await this.getResponseFileInfo();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('撤回成功');
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    submitPassword() {
      const reg = '^[0-9]{6}$';
      const re = new RegExp(reg);
      if (!re.test(this.filePassword)) {
        this.$message.warning('请输入6位数字密码');
        return
      }
      this.isPwdDialog = false;
      this.submitRelease(this.filePassword);
    },
    async sealAndDeliver() {
      if (!this.pdfFile) {
        this.$modal.msgWarning(`请上传${this.responseFileName}`);
        return
      }
      if (!this.isAllSetScorePoint) {
        this.$modal.msgWarning(`请设置全部评审标准响应对照`);
        return
      }
      if (!this.isSetBargaining) {
        this.$modal.msgWarning(`请提交投标函附录/报价表`);
        return
      }

      if (!this.checkFunction(['supplier_response_file_encryption'])) {
        let tipHtml = `<p class="text-danger fontSize20" style="margin-bottom: 10px;">特别提醒</p>
                   <div class="text-left fontSize20">
                    <p>本人确认本文件为本人制作的本项目${this.getPurchaseDict(this.purchaseMethodCode, 'responseFile')}，本文件所有内容经本人确认，且完全同意！</p>
                   </div>`
        this.$confirm(tipHtml, '提示', {
          customClass: 'max-tip',
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          type: 'warning'
        }).then(() => {
          this.submitRelease();
        }).catch(() => {
        })
      } else {
        this.isPwdDialog = true;
      }
    },
    // 发布
    submitRelease(pwd = null) {
      if (this.signOff === '0' && this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.releaseAndStamp({ pwd, authCode, flowId });
          }
        })
      } else {
        this.releaseAndStamp({ pwd });
      }
    },
    // 发布并盖章
    async releaseAndStamp(args = {}) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { pwd, authCode, flowId } = args;
        await responseFileReleaseAndStamp({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          orgCode: this.orgCode,
          pdfFile: this.pdfFile,
          answerFileKey: pwd,
          authCode,
          flowId
        })
        await this.getResponseFileInfo();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('投递成功');
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleWithdraw() {
      this.$alert('确定要撤回文件？', '提示', {
        confirmButtonText: '确定'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await responseFileWithdraw({
            subpackageCode: this.subpackageCode
          })
          await this.getResponseFileInfo();
          this.$modal.closeLoading();
          this.$modal.msgSuccess('撤回成功');
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      })
    },
    previewFile() {
      this.$download.previewFile(this.pdfFile)
    },
    async handleVideoUpload(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByAnnex(params);
        await demoVideoPost({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          videoUrl: data.url
        })
        await this.getResponseFileInfo();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async getResponseFileInfo() {
      try {
        let { data } = await getResponseFile(this.subpackageCode);
        let { pdfFile, releaseStatus, videoUrl, permit, evaluationMethod, heads, purchaseBodyMaps, bodyMaps } = data;
        this.pdfFile = pdfFile;
        this.videoUrl = videoUrl;
        this.releaseStatus = releaseStatus;
        this.permit = permit;

        let obj = evaluationMethod || {};
        let conformityReview = obj.conformityReview || [];
        let scoreReview = obj.scoreReview || [];
        this.reviewList = [].concat(conformityReview).concat(scoreReview);
        this.reviewList.forEach(item => {
          this.$set(item, 'scoreChapter', item.scoreChapter || [])
        })
        this.isAllSetScorePoint = this.reviewList.every(v => v.scoreChapter.length > 0);
        this.heads = heads || [];
        this.purchaseBodyMaps = purchaseBodyMaps || [];
        this.bodyMaps = bodyMaps || [];
        this.isSetBargaining = this.bodyMaps.length > 0;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.description {
  padding: 10px 15px 25px;
}
</style>
<style lang="scss">
.response-steps {
  .el-step__main {
    width: 100% !important;
  }
}
</style>
