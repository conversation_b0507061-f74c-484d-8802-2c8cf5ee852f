<template>
  <div class="container-wrap">
    <h3 class="text-danger text-center mb10">
      请根据“审查因素描述”要求选择{{ responseFileName }}中响应的页码号，
      否则评委视同{{ responseFileName }}未按审查因素描述回答，按0分或不合格处理！
    </h3>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      :height="450"
    >
      <el-table-column
        align="center"
        label="序号"
        type="index"
        width="55"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="reviewItem"
        label="审查因素"
        width="250"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="reviewCriteria"
        label="审查因素描述"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="scoreChapter"
        :label="'对应'+responseFileName+'页码号'"
        width="200"
      >
        <template slot-scope="scope">
          <el-tag
            style="margin-right: 3px;margin-bottom: 3px;"
            v-for="(tag,index) in scope.row.scoreChapter"
            :key="index"
            :closable="!disabled"
            :disable-transitions="false"
            @close="handleClose(scope.row,tag,index)">
            {{ tag }}
          </el-tag>
          <el-button
            v-if="!disabled"
            size="mini"
            @click="showSetDialog(scope.row,scope.$index)">
            +页码
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="text-center mt20" v-if="!disabled">
      <el-button type="primary" @click="submitForm">
        保存评审办法前附表
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import reg from '@/utils/reg'
import { setEvaluationMethod } from '@/api/supplier/supplierProcess'

export default {
  name: 'SetScorePoint',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    reviewList: {
      type: Array,
      default: () => []
    },
    responseFileName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode',
      'buyItemCode'
    ])
  },
  watch: {
    reviewList: {
      handler(val) {
        this.tableData = val || [];
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  methods: {
    async submitForm() {
      try {
        this.$modal.loading('数据提交中，请稍候...')
        let list = this.tableData.map(v => {
          return {
            scoreChapter: v.scoreChapter,
            uuid: v.uuid
          }
        })
        await setEvaluationMethod({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          evaluationMethodList: list
        })
        this.$modal.msgSuccess('保存成功');
        this.$emit('update');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    handleClose(row, tag, index) {
      row.scoreChapter.splice(index, 1)
    },
    showSetDialog(row, index) {
      console.log(row)
      if (this.disabled) {
        return
      }
      this.$prompt('', '请输入页码号', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || value == '') {
            return false;
          }
          const re = reg.positiveInteger;
          return re.test(value) && value.length < 8;
        },
        inputErrorMessage: '请输入页码号，最长8个字符'
      }).then(async ({ value }) => {
        row.scoreChapter.push(value);
      }).catch(() => {
      });
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
