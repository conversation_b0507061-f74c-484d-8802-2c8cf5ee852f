<template>
  <div>
    <el-row :gutter="10" class="mb10" v-if="!disabled">
      <el-col :span="1.5" class="fr">
        <upload-excel
          class="fr mr10"
          size="mini"
          :defaultHeader="heads.map(item=>{return {label: item.keyName, key: item.keyVal}})"
          @onSuccess="handleSuccess">
        </upload-excel>
      </el-col>
    </el-row>
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      size="mini"
      style="min-height: 300px"
    >
      <u-table
        :data="form.bodyMaps"
        :height="400"
        use-virtual
        :row-height="55"
        border>
        <u-table-column
          label="序号"
          align="center"
          type="index"
          width="55"
        />
        <u-table-column
          v-for="(item,k) in heads"
          :key="k"
          class-name="form-cell"
          align="center"
          :label="item.keyName"
          :show-overflow-tooltip="true"
        >
          <template v-slot:default="{row,$index}">
            <el-form-item
              v-if="!disabled"
              :prop="'bodyMaps.'+$index+'.'+item.keyVal"
              label-width="0"
              size="mini"
              :rules="[
                    { required: item.required, message: '请输入', trigger:  ['blur', 'change'] },
                    { pattern: checkReg(item.regex)?eval2(item.regex): null, message: item.remark||'格式不正确', trigger: ['blur', 'change'] }
                  ]"
            >
              <el-input v-model.trim="row[item.keyVal]" placeholder="请输入" v-if="item.keyType!=='file'"></el-input>
              <file-upload-single
                v-else
                :fileSize="5"
                accept=".pdf"
                :uploadName="item.keyVal"
                :params="{
                    index: $index,
                    prop: 'bodyMaps.'+$index+'.'+item.keyVal,
                    fileTypeName: item.keyVal,
                    fileStatus: 1,
                    buyItemCode: buyItemCode,
                    subpackageCode: subpackageCode,
                    supplierNumber: userId,
                    yearMonthSplit: createYearMonth
                  }"
                @onSuccess="handleUpload"
              >
                <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="row[item.keyVal]"
                  @click="previewFile(row[item.keyVal])"
                >查看
                </el-button>
              </file-upload-single>
            </el-form-item>
            <div v-else>
              <el-button
                v-if="item.keyType==='file'&&row[item.keyVal]"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="previewFile(row[item.keyVal])"
              >查看
              </el-button>
              <span v-else>{{ row[item.keyVal] || '/' }}</span>
            </div>
          </template>
        </u-table-column>
        <u-table-column
          fixed="right"
          label="操作"
          align="center"
          width="70"
          v-if="checkPermi(['supplier:process:operate'])&&!disabled"
        >
          <template v-slot:default="{$index}">
            <el-button
              size="mini"
              icon="el-icon-delete"
              class="btn-text-danger"
              type="text"
              @click="removeDetail($index)"
            >删除
            </el-button>
          </template>
        </u-table-column>
      </u-table>

      <div class="text-center pt15" v-if="!disabled">
        <el-button
          plain
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-hasPermi="['supplier:process:operate']"
          @click="addDetail()"
        >
          添加一行
        </el-button>
      </div>
    </el-form>
    <div class="text-center mt20" v-if="!disabled">
      <el-button type="primary" @click="onSubmit">保存投标函附录/报价表</el-button>
    </div>
  </div>
</template>

<script>
import { setQuoteForm } from '@/api/supplier/supplierProcess';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { checkReg, eval2 } from '@/utils/validate';
import { Base64 } from 'js-base64'

export default {
  name: 'SetBargaining',
  props: {
    heads: {
      type: Array,
      default: () => []
    },
    bodyMaps: {
      type: Array,
      default: () => []
    },
    purchaseBodyMaps: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        bodyMaps: []
      },
      rules: {}
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'subpackageCode',
      'createYearMonth',
      'userId'
    ])
  },
  watch: {
    bodyMaps: {
      handler(val) {
        let list = val || [];
        this.form.bodyMaps = list.length > 0 ? list : (this.purchaseBodyMaps || []);
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  methods: {
    checkPermi,
    checkReg,
    eval2,
    handleSuccess({ data }) {
      data.forEach(item => {
        for (let itemKey in item) {
          let obj = this.heads.find(v => v.keyVal === itemKey)
          if (obj.keyType === 'file') {
            item[itemKey] = '';
          }
        }
      })
      this.form.bodyMaps = data;
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleUpload(params) {
      this.$modal.msgSuccess('上传成功');
      this.$set(this.form.bodyMaps[params.index], params.uploadName, params.url);
      this.$refs.form.clearValidate(params.prop);
    },
    defaultRow(heads) {
      let obj = {};
      heads.forEach(item => {
        obj[item.keyVal] = ''
      })
      return obj
    },
    addDetail() {
      this.form.bodyMaps.push(this.defaultRow(this.heads))
    },
    removeDetail(index) {
      this.form.bodyMaps.splice(index, 1)
    },
    onSubmit() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (!this.form.bodyMaps || this.form.bodyMaps.length === 0) {
            this.$message.error('报价数据不能为空')
            return
          }
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await setQuoteForm({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode,
              heads: this.heads.map(v => {
                let regex = Base64.toBase64(v.regex);
                return {
                  ...v,
                  regex
                }
              }),
              bodyMaps: this.form.bodyMaps
            })
            this.$modal.msgSuccess('保存成功');
            this.$emit('update');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
