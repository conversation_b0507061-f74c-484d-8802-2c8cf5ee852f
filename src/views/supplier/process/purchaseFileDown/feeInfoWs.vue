<template>
  <div class="text-danger mt30" style="display: inline-block;">
    <el-descriptions title="投标标书费收款信息" :column="1" border>
      <el-descriptions-item label="账户名称">
        {{ accountName }}
      </el-descriptions-item>
      <el-descriptions-item label="银行账户">
        {{ bankAccount }}
      </el-descriptions-item>
      <el-descriptions-item label="开户行">
        {{ openingBank }}
      </el-descriptions-item>
      <el-descriptions-item label="收费金额（元）">
        {{ bidBookAmount }}
      </el-descriptions-item>
      <el-descriptions-item label="是否收费">
        {{ bidWhetherFree === 1 ? '是' : '否' }}
      </el-descriptions-item>
      <el-descriptions-item label="付款凭证">
        <file-upload-single
          :fileSize="5"
          :showTip="true"
          :autoUpload="false"
          accept=".png, .jpg, .jpeg, .pdf"
          uploadName="paymentVoucher"
          :params="{
            fileTypeName: 'payment_voucher',
            buyItemCode: buyItemCode,
            subpackageCode: subpackageCode,
            supplierNumber: userId,
            yearMonthSplit: createYearMonth
          }"
          @onSuccess="handleUpload"
        >
          <el-button
            slot="upload-btn"
            type="primary"
            size="mini"
          >
            上传
          </el-button>
          <el-button
            slot="upload-right"
            type="success"
            size="mini"
            v-if="paymentVoucher"
            @click="previewFileByAnnex(paymentVoucher)"
          >预览
          </el-button>
        </file-upload-single>
      </el-descriptions-item>
      <!--      <el-descriptions-item label="申请发票">-->
      <!--        <el-button type="primary" size="mini" @click="lookCode(require('@/assets/invoice/'+versionType+'.png'))">申请</el-button>-->
      <!--      </el-descriptions-item>-->
    </el-descriptions>

    <div class="text-danger text-left mt20">
      <p>1、具体收费信息以及是否收费，以招标公告为准；</p>
      <p>2、上传付款凭证后请耐心等待院方审核，审核无误方可下载文件；</p>
      <p>3、申请发票时请完整填写相关信息。</p>
    </div>
  </div>
</template>

<script>
import { fileUploadByAnnex } from '@/api/file';
import { mapGetters } from 'vuex';
import { savePaymentVoucher } from '@/api/supplier/supplierProcess'
import { isEmpty } from '@/utils'

export default {
  name: 'FeeInfoWs',
  props: {
    permit: { // 是否允许投标: 0-不允许，1-允许
      type: String,
      default: '0'
    },
    paymentVoucher: {
      type: String,
      default: null
    },
    bidWhetherFree: {
      type: Number,
      default: null
    },
    bidBookAmount: {
      type: [String, Number],
      default: null
    },
    accountName: {
      type: String,
      default: null
    },
    bankAccount: {
      type: String,
      default: null
    },
    openingBank: {
      type: String,
      default: null
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'userId',
      'buyItemName',
      'buyItemCode',
      'subpackageName',
      'subpackageCode',
      'createYearMonth'
    ])
  },
  created() {
  },
  methods: {
    isEmpty,
    lookCode(url) {
      this.$imgView(url);
    },
    previewFileByAnnex(fileKey) {
      this.$download.previewFileByAnnex(fileKey)
    },
    async handleUpload(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByAnnex(params);
        await savePaymentVoucher({
          paymentVoucher: data.url,
          buyItemName: this.buyItemName,
          buyItemCode: this.buyItemCode,
          subpackageName: this.subpackageName,
          subpackageCode: this.subpackageCode
        })
        this.$modal.msgSuccess('上传成功');
        this.$emit('update')
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
