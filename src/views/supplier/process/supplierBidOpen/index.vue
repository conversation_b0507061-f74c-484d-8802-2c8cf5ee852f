<template>
  <div v-loading="loading">
    <div style="display: flex;align-items: center;flex-wrap: wrap;">
      <span class="fontSize16 mr10">剩余签到时间</span>
      <count-down
        itemWidth="20px"
        :end-time="meetingTime"
        :start-time="currentTime"
        v-if="currentTime"
        @changeCountdown="changeCountdown">
      </count-down>
    </div>

    <div class="mt20 mb20" style="max-width: 500px;display: flex;">
      <el-steps
        :active="4"
        simple
        class="simple-steps"
        :style="{'--theme': theme, 'flex':'1'}"
        v-if="checkFunction(['supplier_response_file_encryption'])">
        <el-step title="1 签到"></el-step>
        <el-step title="2 解密"></el-step>
        <el-step title="3 签字" v-if="checkFunction(['supplier_bid_sing'])"></el-step>
      </el-steps>
      <el-steps :active="4" simple class="simple-steps" :style="{'--theme': theme, 'flex':'1'}" v-else>
        <el-step title="1 签到"></el-step>
        <el-step title="2 签字" v-if="checkFunction(['supplier_bid_sing'])"></el-step>
      </el-steps>
      <el-button icon="el-icon-refresh" size="mini" type="primary" class="ml10" @click="refresh">获取最新信息</el-button>
    </div>

    <el-table :data="tableData">
      <el-table-column label="供应商" align="center" prop="supplierSignUpInfo.bidderName" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="签到" align="center" prop="bidOpeningSign">
        <template>
          <span v-if="bidOpeningSign==='1'" class="text-navy">已签到</span>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-else
            :disabled="!(isCanSingIn&&status==='-1') || !checkPermi(['supplier:process:operate'])"
            @click="handleSignIn">
            点击签到
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="解密状态" align="center" prop="bidOpeningDecrypt" v-if="checkFunction(['supplier_response_file_encryption'])">
        <template>
          <span v-if="bidOpeningDecrypt==='1'" class="text-navy">已解密</span>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-else
            :disabled="!(bidOpeningSign==='1'&&status==='0') || !checkPermi(['supplier:process:operate'])"
            @click="isPwdDialog = true;">
            点击解密
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="签章确认"
        align="center"
        prop="bidOpeningRecordForm"
        v-if="checkFunction(['supplier_bid_sing'])">
        <template v-slot:default="{row}">
          <span v-if="row.bidOpeningRecordForm==='1'" class="text-navy">已签字</span>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-else
            :disabled="sealDisabled || !checkPermi(['supplier:process:operate'])"
            @click="handleSeal">
            点击签字
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="开标记录表" align="center" v-if="checkFunction(['supplier_bid_sing'])">
        <template>
          <el-button
            :disabled="sealDisabled"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(recordFile)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否允许进入评审" align="center" prop="enterTheReview">
        <template v-slot:default="{row}">
          <div v-if="status==='2'">
            <dict-tag :options="dict.type.enter_the_review_status" :value="row.enterTheReview"/>
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="explain-box mt20">
      <h3>关于{{purchaseLabel}}人{{ bidOpenLabel }}流程的操作说明：</h3>
      <p>
        1 - {{purchaseLabel}}过程中{{purchaseLabel}}流程实施人（以下简称{{purchaseLabel}}人），在{{ bidOpenLabel }}流程中一共需要按顺序点击三个“确认”按钮：
        1- {{ bidOpenLabel }}开始；2- 唱标；3- {{ bidOpenLabel }}完成。
      </p>
      <p>
        2 - 第一步（{{ bidOpenLabel }}开始 ）：
        以{{purchaseLabel}}人在{{purchaseLabel}}公告中确定的{{ bidOpenLabel }}时间为节点，供应商在{{ bidOpenLabel }}时间前30分钟之内 “签到 ” ，
        {{purchaseLabel}}人可根据实际情况需要等待未签到的供应商，
        等待时间以{{purchaseLabel}}人点击 “{{ bidOpenLabel }}开始” 为终止节点。
        {{purchaseLabel}}人点击完成 “{{ bidOpenLabel }}开始” 按钮之后，未签到的供应商将不能参与本次{{ bidOpenLabel }}流程中的：签到、解密、签字 。
      </p>
      <p>
        3 - 第二步（唱标）：
        {{purchaseLabel}}人点击 “{{ bidOpenLabel }}开始” 按钮之后，供应商开始解密，解密时间以{{purchaseLabel}}人确认 “唱标” 为终止节点。
        {{purchaseLabel}}人点击 “唱标” 按钮之后，未解密的供应商将不能：解密、签字 。
      </p>
      <p>
        4 - 第三步（{{ bidOpenLabel }}完成）：
        {{purchaseLabel}}人点击 “唱标” 按钮之后，供应商开始签字，签字时间以{{purchaseLabel}}人确认 “{{ bidOpenLabel }}完成” 为终止节点。
        {{purchaseLabel}}人点击 “{{ bidOpenLabel }}完成” 按钮之后，未签字的供应商将不能：签字。
      </p>
      <div>
        <el-image :src="require('@/assets/images/process.png')" fit="fill"></el-image>
      </div>
    </div>

    <el-dialog
      title="标书解密"
      :visible.sync="isPwdDialog"
      center
      top="10vh"
      custom-class="maxW600"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="pwd-box">
        <div class="text-center">
          <h3 class="mb20 fontSize18">请输入6位数字密码</h3>
          <pwd-group :pwdLength='6' v-model="answerFileKey"></pwd-group>
        </div>
      </div>
      <template v-slot:footer>
        <el-button @click="isPwdDialog = false">取消</el-button>
        <el-button type="primary" @click="handleDecrypt">确认</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { bidOpeningDecrypt, bidOpeningSign, bidOpeningSignature, querySupplierBidOpening } from '@/api/supplier/supplierBidOpen';
import { checkFunction, checkPermi } from '@/utils/permission';

export default {
  name: 'SupplierBidOpen',
  dicts: ['enter_the_review_status'],
  data() {
    return {
      loading: false,
      tableData: [],
      currentTime: null,
      meetingTime: null,
      isCanSingIn: false, // 是否能签
      status: '-1', // 开标状态'-1'开标未开始，0-开标开始，1-唱标，2-开标完成
      bidOpeningSign: '0',
      bidOpeningDecrypt: '0',
      recordFile: null,
      isPwdDialog: false,
      answerFileKey: null,
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'purchaseMethodCode',
      'getPurchaseDict',
      'theme',
      'wsMsg',
      'buyItemCode',
      'subpackageCode'
    ]),
    bidOpenLabel() {
      return this.getPurchaseDict(this.purchaseMethodCode, 'bidOpen')
    },
    purchaseLabel() {
      return this.selectDictValue(this.purchaseMethodCode, 'purchase')
    },
    sealDisabled() {
      let flag = false;
      if (checkFunction(['supplier_bid_sing'])) {
        flag = this.recordFile && this.bidOpeningDecrypt === '1';
      } else {
        flag = this.recordFile && this.bidOpeningSign === '1';
      }
      return !flag
    }
  },
  watch: {
    wsMsg: {
      handler(val) {
        if (val && val.displayEnums.includes('REFRESH') && val.businessTypeEnum === 'BID_OPENING') {
          this.getList();
        }
      },
      deep: true
    }
  },
  created() {
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList()
  },
  methods: {
    checkFunction,
    checkPermi,
    refresh() {
      this.getList();
    },
    /** 签到 */
    async handleSignIn() {
      this.$modal.confirm(`确认签到？`)
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await bidOpeningSign({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode
            })
            await this.getList();
            this.$modal.closeLoading();
            this.$modal.msgSuccess('签到成功');
          } catch (e) {
            this.$modal.closeLoading();
            throw e
          }
        }).catch(() => {
        })
    },
    /** 解密 */
    async handleDecrypt() {
      const reg = '^[0-9]{6}$';
      const re = new RegExp(reg);
      if (!re.test(this.answerFileKey)) {
        this.$message.warning('请输入6位数字密码');
        return
      }
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await bidOpeningDecrypt({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          answerFileKey: this.answerFileKey
        });
        this.isPwdDialog = false;
        await this.getList();
        this.$modal.closeLoading();
        this.$modal.msgSuccess('解密成功');
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 签字盖章 */
    handleSeal() {
      if (this.signVersion === '3') {
        this.$signatureSMS({
          phoneNumber: this.phoneNumber,
          handleConfirm: (val) => {
            console.log(val)
            let { authCode, flowId } = val;
            this.$modal.loading('数据提交中，请稍候...');
            bidOpeningSignature({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode,
              recordFile: this.recordFile,
              authCode,
              flowId
            }).then(() => {
              this.$modal.msgSuccess('签字成功');
              this.getList();
              this.$modal.closeLoading();
            }).catch((e) => {
              this.$modal.closeLoading();
              throw new Error(e);
            })
          }
        })
      } else {
        this.$modal.loading('数据提交中，请稍候...');
        bidOpeningSignature({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          recordFile: this.recordFile
        }).then(() => {
          this.$modal.msgSuccess('签字成功');
          this.getList();
          this.$modal.closeLoading();
        }).catch((e) => {
          this.$modal.closeLoading();
          throw new Error(e);
        })
      }
    },
    changeCountdown(data) {
      this.isCanSingIn = data.ms <= 1000 * 60 * 30;
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await querySupplierBidOpening(this.subpackageCode);
        this.tableData = data ? [data] : [];
        this.meetingTime = data.timeVo.meetingTime;
        this.currentTime = data.timeVo.currentTime;
        this.status = data.status || '-1';
        this.bidOpeningSign = data.bidOpeningSign || '0';
        this.bidOpeningDecrypt = data.bidOpeningDecrypt || '0';
        this.recordFile = data.recordFile;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.explain-box {
  text-align: left;

  h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  p {
    text-indent: 2em;
    line-height: 1.5;
    font-size: 14px;
    margin-bottom: 5px;
  }
}
</style>

<style lang="scss">
.simple-steps {
  background: #FFFFFF;
  border: 1px solid #{'var(--theme)'};
  padding: 5px 8%;

  .el-step__title {
    font-size: 14px !important;
  }

  .el-step__icon {
    display: none !important;
  }

  .el-step__arrow::before, .el-step__arrow::after {
    background: #{'var(--theme)'} !important;
  }
}
</style>
