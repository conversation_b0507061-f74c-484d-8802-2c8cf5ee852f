<template>
  <div v-loading="loading">
    <el-table :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50px"/>
      <el-table-column label="属性" align="center" prop="attribute"/>
      <el-table-column label="描述" align="center" prop="description"/>
      <el-table-column label="响应文件" align="center">
        <template v-slot:default="{row}">
          <!--0待上传 1已上传 2被驳回-->
          <file-upload-single
            :disabled="row.status===1 || permit!==1"
            :showUploadBtn="row.status!==1&&checkPermi(['supplier:process:operate'])"
            :autoUpload="false"
            :file-size="50"
            accept=".pdf"
            uploadName="responseFile"
            :params="{
              id: row.id,
              requirementId: row.requirementId,
              bidFileKey: row.bidFileKey,
              fileTypeName: 'response_file',
              buyItemCode: buyItemCode,
              subpackageCode: subpackageCode,
              supplierNumber: userId,
              yearMonthSplit: createYearMonth
            }"
            @onSuccess="handleUpload"
          >
            <el-button
              v-if="checkPermi(['supplier:process:operate'])"
              slot="upload-btn"
              type="text"
              icon="el-icon-upload"
              size="mini"
              :disabled="row.status===1 || permit!==1"
            >
              {{ !row.bidFileKey ? '上传' : '重新上传' }}
            </el-button>
            <el-button
              slot="upload-right"
              type="text"
              icon="el-icon-view"
              size="mini"
              v-if="row.bidFileKey"
              @click="previewFile(row.bidFileKey)"
            >
              预览
            </el-button>
            <el-button
              slot="upload-right"
              type="text"
              icon="el-icon-download"
              size="mini"
              v-if="row.bidFileKey"
              @click="handleDown(row.bidFileKey)"
            >
              下载
            </el-button>
          </file-upload-single>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.response_file_no_client_status" :value="row.status"/>
          <span class="td-tip" v-if="row.status === 2">
            理由：{{ row.reason }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { fileUpload } from '@/api/file';
import { responseFileList, responseFileSave, responseFileUpdate } from '@/api/supplier/responseFileNoClient';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { getPermit } from '@/api/supplier/supplierProcess'

export default {
  name: 'ResponseFileUpload',
  dicts: ['response_file_no_client_status'],
  data() {
    return {
      loading: false,
      tableData: [],
      permit: 0 // 是否允许响应: 0-不允许，1-允许
    }
  },
  computed: {
    ...mapGetters([
      'buyItemCode',
      'subpackageCode',
      'createYearMonth',
      'userId'
    ])
  },
  created() {
    this.getPermit();
    this.getResponseFileList();
  },
  methods: {
    checkPermi,
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    async handleUpload(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUpload(params);
        if (!params.bidFileKey) {
          await responseFileSave({
            subpackageCode: this.subpackageCode,
            bidFileKeyList: [{
              bidFileKey: data.url,
              requirementId: params.requirementId
            }]
          });
        } else {
          await responseFileUpdate([{
            id: params.id,
            bidFileKey: data.url
          }])
        }
        this.$modal.msgSuccess('上传成功');
        await this.getResponseFileList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async getPermit() {
      try {
        let { data } = await getPermit(this.subpackageCode);
        this.permit = data;
      } catch (e) {
        throw new Error(e);
      }
    },
    async getResponseFileList() {
      try {
        this.loading = true;
        let { data } = await responseFileList(this.subpackageCode);
        this.tableData = data || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
