<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="0px">
      <el-table :data="heads" border>
        <el-table-column label="序号" type="index" align="center" width="60"></el-table-column>
        <el-table-column align="center" label="项目内容" prop="keyName" width="300"/>
        <el-table-column align="center" label="约定内容" class-name="form-cell">
          <template v-slot:default="{row}">
            <el-form-item
              :prop="row.keyVal"
              label-width="0"
              size="mini"
              :rules="[
                { required: row.required, message: '请输入', trigger:  ['blur', 'change'] },
                 { pattern: checkReg(row.regex)?eval2(row.regex): null, message: row.remark||'格式不正确', trigger: ['blur', 'change'] }
              ]"
            >
              <el-input
                v-if="row.keyType!=='file'"
                v-model.trim="form[row.keyVal]"
                placeholder="请输入">
              </el-input>
              <file-upload-single
                v-else
                :showUploadBtn="checkPermi(['supplier:process:operate'])"
                :fileSize="5"
                accept=".pdf"
                :uploadName="row.keyVal"
                :params="{
                  fileTypeName: row.keyVal,
                  buyItemCode: buyItemCode,
                  subpackageCode: subpackageCode,
                  supplierNumber: userId,
                  yearMonthSplit: createYearMonth
                }"
                @onSuccess="handleUpload"
              >
                <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form[row.keyVal]"
                  @click="previewFile(form[row.keyVal])"
                >预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <div class="text-center mt30" v-if="checkPermi(['supplier:process:operate'])">
      <el-button type="primary" @click="submitForm">
        保存
      </el-button>
    </div>

  </div>
</template>

<script>

import { getAdditionalInformation, saveAdditionalInformation } from '@/api/supplier/supplierProcess';
import { mapGetters } from 'vuex';
import { checkReg, eval2 } from '@/utils/validate';
import { checkPermi } from '@/utils/permission';
import { Base64 } from 'js-base64'

export default {
  name: 'SupplierAdditionalConditions',
  data() {
    return {
      heads: [],
      form: {},
      rules: {}
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'buyItemCode',
      'subpackageCode',
      'createYearMonth'
    ])
  },
  created() {
    this.getInfo();
  },
  methods: {
    checkPermi,
    eval2,
    checkReg,
    defaultRow(heads) {
      let obj = {};
      heads.forEach(item => {
        obj[item.keyVal] = ''
      })
      return obj
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleUpload(params) {
      this.$modal.msgSuccess('上传成功');
      this.$set(this.form, params.uploadName, params.url);
      this.$refs.form.clearValidate(params.uploadName);
    },
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await saveAdditionalInformation({
              buyItemCode: this.buyItemCode,
              subpackageCode: this.subpackageCode,
              additionalInformation: this.heads.map(v => {
                let regex = Base64.toBase64(v.regex);
                return {
                  ...v,
                  regex
                }
              }),
              additionalInformationMap: [this.form]
            });
            this.$modal.msgSuccess('提交成功');
            await this.getInfo();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    async getInfo() {
      try {
        let { data } = await getAdditionalInformation(this.subpackageCode);
        this.heads = data.additionalInformation;
        if (data.additionalInformationMap && data.additionalInformationMap.length > 0) {
          this.form = data.additionalInformationMap[0];
        } else {
          this.form = this.defaultRow(this.heads);
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
