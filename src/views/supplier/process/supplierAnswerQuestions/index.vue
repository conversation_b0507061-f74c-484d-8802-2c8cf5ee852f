<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="提问采购人" name="first">
      <ask-purchaser v-if="activeName==='first'"></ask-purchaser>
    </el-tab-pane>
    <el-tab-pane label="回复评委" name="second">
      <ask-expert v-if="activeName==='second'"></ask-expert>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import askPurchaser from '@/views/supplier/process/supplierAnswerQuestions/askPurchaser.vue';
import askExpert from '@/views/supplier/process/supplierAnswerQuestions/askExpert.vue';

export default {
  name: 'SupplierAnswerQuestions',
  components: { askPurchaser, askExpert },
  data() {
    return {
      activeName: 'first'
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
