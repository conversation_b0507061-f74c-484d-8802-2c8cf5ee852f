<template>
  <div v-loading="loading">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="top"
      style="max-width: 600px;"
      v-if="checkPermi(['supplier:process:operate'])"
    >
      <el-form-item label="提问内容" prop="askContent">
        <div class="flex-box">
          <el-input
            type="textarea"
            maxlength="200"
            resize="none"
            :rows="2"
            placeholder="请输入内容"
            v-model="form.askContent">
          </el-input>
          <el-button class="flex-btn" type="primary" size="small" @click="handlePut">提问</el-button>
        </div>
      </el-form-item>
    </el-form>

    <ul class="qa-list">
      <li class="overflow" v-for="(item,index) in tableData" :key="index">
        <div class="qa-item">
          <span>【供应商 - 问】{{ item.askContent }}</span>
          <span class="qa-time">提交时间：{{ item.createAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </div>
        <div class="qa-item">
          <span>【采购人 - 答】{{ item.answerContent }}</span>
          <span class="qa-time" v-if="item.updateAt">回复时间：{{ item.updateAt | formatTime('YYYY-MM-DD HH:mm') }}</span>
        </div>
      </li>
    </ul>
  </div>

</template>

<script>
import { mapGetters } from 'vuex';
import { putAnswerQuestions, listAnswerQuestions } from '@/api/answerQuestions';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'AskPurchaser',
  data() {
    return {
      loading: false,
      form: {
        askContent: null
      },
      rules: {
        askContent: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'nickName',
      'subpackageCode'
    ])
  },
  created() {
    this.getList();
  },
  methods: {
    checkPermi,
    handlePut() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await putAnswerQuestions({
              subpackageCode: this.subpackageCode,
              askUserId: this.userId,
              askUserName: this.nickName,
              askUserType: '1',
              askContent: this.form.askContent
            })
            this.$modal.msgSuccess('提问成功');
            this.form.askContent = null;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listAnswerQuestions({
          subpackageCode: this.subpackageCode,
          askUserType: '1'
        });
        this.tableData = data || [];
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;

  .flex-btn {
    align-self: flex-end;
    margin-left: 5px;
  }
}

.qa-list {
  li {
    font-size: 14px;
    line-height: 1.5;
    border-bottom: 1px solid #dfe6ec;
    padding: 10px 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .qa-item {
    margin-bottom: 5px;

    span {
      word-wrap: break-word;
      word-break: break-all;
    }

    .qa-time {
      font-size: 12px;
      color: #808080;
      margin-left: 15px;
    }
  }
}
</style>
