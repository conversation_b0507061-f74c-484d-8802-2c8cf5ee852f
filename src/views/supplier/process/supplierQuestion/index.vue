<template>
  <div>
    <el-row :gutter="10" class="mb10" v-if="checkPermi(['supplier:process:operate'])">
      <el-col :span="1.5">
        <el-button
          type="danger"
          size="mini"
          @click="handlePut"
        >质疑
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="50"/>
      <el-table-column label="提出质疑的人" align="center" prop="doubtUserName"/>
      <el-table-column label="提出质疑时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="质疑回复时间" align="center" prop="updateAt">
        <template v-slot:default="{row}">
          <span v-if="row.updateAt">{{ row.updateAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="质疑内容" align="center" width="80">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.doubtFileKey)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="质疑内容附件" align="center" prop="doubtAnnexFileKey" width="105">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            v-if="row.doubtAnnexFileKey"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.doubtAnnexFileKey)"
          >下载
          </el-button>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="质疑回复" align="center" width="80">
        <template v-slot:default="{row}">
          <el-button
            v-if="row.replyFileKey"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="previewFile(row.replyFileKey)"
          >查看
          </el-button>
          <div v-else>
            <el-button
              v-if="row.doubtUserId!==userId&&row.doubtUserType!=='1'&&checkPermi(['supplier:process:operate'])"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleReply(row)"
            >回复
            </el-button>
            <span v-else>/</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="质疑回复附件" align="center" prop="replyAnnexFileKey" width="105">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            v-if="row.replyAnnexFileKey"
            type="text"
            icon="el-icon-download"
            @click="handleDown(row.replyAnnexFileKey)"
          >下载
          </el-button>
          <span v-else>/</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="质疑"
      :visible.sync="isDialog"
      width="90%"
      top="5vh"
      custom-class="maxW1200"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item prop="fileHtml">
              <Tinymce ref="editor" v-model="form.fileHtml" :height="700" :max-height="700" v-if="isDialog"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6" style="max-width: 400px;">
            <el-form-item>
              <el-button type="success" @click="handlePreview">预览</el-button>
            </el-form-item>
            <el-form-item label="质疑类型" prop="doubtType" v-if="!isReply">
              <el-select v-model="form.doubtType" clearable placeholder="请选择" class="block">
                <el-option
                  v-for="dict in dict.type.doubt_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="附件" prop="annexKey" v-if="!isReply">
              <file-upload
                :limit="1"
                :params="{
                  fileTypeName: 'annexKey',
                  buyItemCode: buyItemCode,
                  subpackageCode: subpackageCode,
                  yearMonthSplit: createYearMonth,
                }"
                @change="getFileKey"
              >
              </file-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitForm">提交</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestion, putAndReplyQuestion } from '@/api/question';
import { mapGetters } from 'vuex';
import { templatePreview } from '@/api/system/fileTemplate';
import { checkPermi } from '@/utils/permission';
import { verifyEditor } from '@/utils/validate'

export default {
  name: 'SupplierQuestion',
  dicts: ['doubt_type'],
  data() {
    return {
      loading: false,
      tableData: [],
      isDialog: false,
      form: {
        fileHtml: '',
        doubtType: null,
        annexKey: null
      },
      rules: {
        fileHtml: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: verifyEditor, trigger: ['blur', 'change'] }
        ],
        doubtType: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      id: null,
      isReply: false,
      signOff: null,
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'buyItemCode',
      'subpackageCode',
      'createYearMonth',
      'userId',
      'nickName'
    ])
  },
  created() {
    this.getConfigKey('sys.signature.off').then(response => {
      this.signOff = response.data;
    })
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList();
  },
  methods: {
    checkPermi,
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    getFileKey(key) {
      this.form.annexKey = key
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.signOff === '0' && this.signVersion === '3') {
            this.$signatureSMS({
              phoneNumber: this.phoneNumber,
              handleConfirm: (val) => {
                console.log(val)
                let { authCode, flowId } = val;
                this.submitQuestion({ authCode, flowId });
              }
            })
          } else {
            this.submitQuestion();
          }
        }
      });
    },
    async submitQuestion(args = {}) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { authCode, flowId } = args;
        if (!this.isReply) {
          await putAndReplyQuestion({
            subpackageCode: this.subpackageCode,
            fileHtml: this.form.fileHtml,
            doubtAnnexFileKey: this.form.annexKey,
            doubtType: this.form.doubtType,
            doubtUserId: this.userId,
            doubtUserName: this.nickName,
            doubtUserType: '1', // 1投标人 2招标办 3评委
            authCode,
            flowId
          })
        } else {
          await putAndReplyQuestion({
            subpackageCode: this.subpackageCode,
            fileHtml: this.form.fileHtml,
            replyAnnexFileKey: this.form.annexKey,
            doubtType: this.form.doubtType,
            id: this.id,
            replyUserId: this.userId,
            authCode,
            flowId
          })
        }
        this.$modal.msgSuccess('提交成功');
        this.$modal.closeLoading()
        this.isDialog = false;
        await this.getList();
      } catch (e) {
        this.$modal.closeLoading()
        throw new Error(e);
      }
    },
    handleReply(row) {
      this.form.doubtType = row.doubtType;
      this.form.fileHtml = '';
      this.form.annexKey = null;
      this.id = row.id;
      this.isReply = true;
      this.isDialog = true;
    },
    handlePut() {
      this.form.fileHtml = '';
      this.form.annexKey = null;
      this.form.doubtType = null;
      this.isReply = false;
      this.isDialog = true;
    },
    handlePreview() {
      if (!this.form.fileHtml) {
        this.$modal.msgError('内容不能为空')
        return
      }
      this.$modal.loading('数据提交中，请稍候...')
      templatePreview({ filePreview: this.form.fileHtml }).then(res => {
        this.$modal.closeLoading()
        this.$pdfViewDialog({ data: res.data, type: 'blob' })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await listQuestion({
          subpackageCode: this.subpackageCode,
          doubtUserType: '1'
        });
        this.tableData = data;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw e
      }
    }
  }
}
</script>

<style scoped>

</style>
