<template>
  <div v-loading="loading" element-loading-text="数据加载中">
    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="getList"
        >获取最新信息
        </el-button>
      </el-col>
    </el-row>

    <el-table :data="tableData" border>
      <el-table-column label="报价表" align="center">
        <template v-slot:default="{row}">
          {{ row.round === 0 ? '初始报价表' : '第' + numToChinese(row.round) + '轮报价表' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <div v-if="row.launchStatus==='1'">
            <el-button
              v-if="row.bodyMaps&&row.bodyMaps.length>0"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewQuotationSheet(row)"
            >查看
            </el-button>
            <el-button
              v-if="checkPermi(['supplier:process:operate'])"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="editQuotationSheet(row)"
            >提交
            </el-button>
            <span v-else>/</span>
          </div>
          <div v-else>
            <el-button
              v-if="row.bodyMaps&&row.bodyMaps.length>0"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="previewQuotationSheet(row)"
            >查看
            </el-button>
            <span v-else class="text-danger">未报价</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="报价时限" align="center">
        <template v-slot:default="{row}">
          <div v-if="row.launchStatus==='1'">
            <count-down
              v-if="row.countdown"
              itemWidth="20px"
              :end-time="row.countdown"
              :start-time="currentDate">
            </count-down>
            <span v-else>未设置</span>
          </div>
          <span v-else>报价已结束</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center">
        <template v-slot:default="{row}">
          {{ row.serviceRequire || '/' }}
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="报价表"
      :visible.sync="isDialog"
      top="5vh"
      custom-class="maxW1200"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-row :gutter="10" class="mb10" v-if="isEdit">
        <el-col :span="1.5" class="fr">
          <upload-excel
            class="fr mr10"
            size="mini"
            :defaultHeader="heads.map(item=>{return {label: item.keyName, key: item.keyVal}})"
            @onSuccess="handleSuccess">
          </upload-excel>
        </el-col>
      </el-row>
      <el-form ref="form" :rules="rules" :model="form" size="mini" style="min-height: 300px">
<!--        <el-table-->
<!--          :data="form.bodyMaps"-->
<!--          border-->
<!--          max-height="580"-->
<!--        >-->
<!--          <el-table-column label="序号" align="center" type="index" width="50"></el-table-column>-->
<!--          <template v-for="(item,k) in heads">-->
<!--            <el-table-column-->
<!--              :label="item.keyName"-->
<!--              align="center"-->
<!--              :key="k"-->
<!--              class-name="form-cell"-->
<!--              :width="item.keyType==='file'&&isEdit?150:null"-->
<!--            >-->
<!--              <template v-slot:default="{row,$index}">-->
<!--                <el-form-item-->
<!--                  v-if="isEdit"-->
<!--                  :prop="'bodyMaps.'+$index+'.'+item.keyVal"-->
<!--                  label-width="0"-->
<!--                  size="mini"-->
<!--                  :rules="[-->
<!--                    { required: item.required, message: '请输入', trigger:  ['blur', 'change'] },-->
<!--                    { pattern: checkReg(item.regex)?eval2(item.regex): null, message: item.remark||'格式不正确', trigger: ['blur', 'change'] }-->
<!--                  ]"-->
<!--                >-->
<!--                  <el-input v-model.trim="row[item.keyVal]" placeholder="请输入" v-if="item.keyType!=='file'"></el-input>-->
<!--                  <file-upload-single-->
<!--                    v-else-->
<!--                    :fileSize="5"-->
<!--                    accept=".pdf"-->
<!--                    :uploadName="item.keyVal"-->
<!--                    :params="{-->
<!--                    index: $index,-->
<!--                    prop: 'bodyMaps.'+$index+'.'+item.keyVal,-->
<!--                    fileTypeName: item.keyVal,-->
<!--                    fileStatus: 1,-->
<!--                    buyItemCode: buyItemCode,-->
<!--                    subpackageCode: subpackageCode,-->
<!--                    supplierNumber: userId,-->
<!--                    yearMonthSplit: createYearMonth-->
<!--                  }"-->
<!--                    @onSuccess="handleUpload"-->
<!--                  >-->
<!--                    <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>-->
<!--                    <el-button-->
<!--                      slot="upload-right"-->
<!--                      type="success"-->
<!--                      size="mini"-->
<!--                      v-if="row[item.keyVal]"-->
<!--                      @click="previewFile(row[item.keyVal])"-->
<!--                    >查看-->
<!--                    </el-button>-->
<!--                  </file-upload-single>-->
<!--                </el-form-item>-->
<!--                <div v-else>-->
<!--                  <el-button-->
<!--                    v-if="item.keyType==='file'&&row[item.keyVal]"-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-view"-->
<!--                    @click="previewFile(row[item.keyVal])"-->
<!--                  >查看-->
<!--                  </el-button>-->
<!--                  <span v-else>{{ row[item.keyVal] || '/' }}</span>-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--          </template>-->
<!--          <el-table-column label="操作" align="center" width="90" v-if="isEdit">-->
<!--            <template v-slot:default="{$index}">-->
<!--              <el-button-->
<!--                size="mini"-->
<!--                icon="el-icon-delete"-->
<!--                class="btn-text-danger"-->
<!--                type="text"-->
<!--                v-hasPermi="['supplier:process:operate']"-->
<!--                @click="removeDetail($index)"-->
<!--              >删除-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->

        <u-table
          :data="form.bodyMaps"
          :height="500"
          use-virtual
          :row-height="55"
          border>
          <u-table-column
            label="序号"
            align="center"
            type="index"
            width="50"
          />
          <u-table-column
            v-for="(item,k) in heads"
            :key="k"
            align="center"
            :label="item.keyName"
            class-name="form-cell"
            :show-overflow-tooltip="true"
          >
            <template v-slot:default="{row,$index}">
              <el-form-item
                v-if="isEdit"
                :prop="'bodyMaps.'+$index+'.'+item.keyVal"
                label-width="0"
                size="mini"
                :rules="[
                    { required: item.required, message: '请输入', trigger:  ['blur', 'change'] },
                    { pattern: checkReg(item.regex)?eval2(item.regex): null, message: item.remark||'格式不正确', trigger: ['blur', 'change'] }
                  ]"
              >
                <el-input v-model.trim="row[item.keyVal]" placeholder="请输入" v-if="item.keyType!=='file'"></el-input>
                <file-upload-single
                  v-else
                  :fileSize="5"
                  accept=".pdf"
                  :uploadName="item.keyVal"
                  :params="{
                    index: $index,
                    prop: 'bodyMaps.'+$index+'.'+item.keyVal,
                    fileTypeName: item.keyVal,
                    fileStatus: 1,
                    buyItemCode: buyItemCode,
                    subpackageCode: subpackageCode,
                    supplierNumber: userId,
                    yearMonthSplit: createYearMonth
                  }"
                  @onSuccess="handleUpload"
                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="row[item.keyVal]"
                    @click="previewFile(row[item.keyVal])"
                  >查看
                  </el-button>
                </file-upload-single>
              </el-form-item>
              <div v-else>
                <el-button
                  v-if="item.keyType==='file'&&row[item.keyVal]"
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="previewFile(row[item.keyVal])"
                >查看
                </el-button>
                <span v-else>{{ row[item.keyVal] || '/' }}</span>
              </div>
            </template>
          </u-table-column>
          <u-table-column
            label="操作"
            align="center"
            width="90"
            v-if="isEdit"
          >
            <template v-slot:default="{$index}">
              <el-button
                size="mini"
                icon="el-icon-delete"
                class="btn-text-danger"
                type="text"
                v-hasPermi="['supplier:process:operate']"
                @click="removeDetail($index)"
              >删除
              </el-button>
            </template>
          </u-table-column>
        </u-table>

        <div class="text-right pr10 fontSize16" style="line-height: 50px;" v-if="!isEdit">
          总价：{{ allRowQuotationTotalPrice }} 元
        </div>

        <div class="text-center pt10" v-if="isEdit">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-plus"
            v-hasPermi="['supplier:process:operate']"
            @click="addDetail()"
          >
            添加一行
          </el-button>
        </div>
      </el-form>
      <div slot="footer" class="text-center" v-if="isEdit">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSupplierBargainList, confirmBargain } from '@/api/supplier/supplierProcess';
import { numToChinese } from '@/utils';
import { mapGetters } from 'vuex';
import { checkPermi } from '@/utils/permission';
import { checkReg, eval2 } from '@/utils/validate';
import { Base64 } from 'js-base64'

export default {
  name: 'SupplierBargaining',
  data() {
    return {
      loading: false,
      currentDate: null,
      heads: [],
      bodyMaps: [],
      tableData: [],
      isDialog: false,
      isEdit: false,
      allRowQuotationTotalPrice: null,
      round: null,
      form: {
        bodyMaps: []
      },
      rules: {},
      signOff: null,
      signVersion: null
    }
  },
  computed: {
    ...mapGetters([
      'phoneNumber',
      'buyItemCode',
      'subpackageCode',
      'createYearMonth',
      'userId'
    ])
  },
  created() {
    this.getConfigKey('sys.signature.off').then(response => {
      this.signOff = response.data;
    })
    this.getConfigKey('seal_version').then(response => {
      this.signVersion = response.data;
    })
    this.getList();
  },
  methods: {
    checkPermi,
    numToChinese,
    checkReg,
    eval2,
    handleSuccess({ data }) {
      data.forEach(item => {
        for (let itemKey in item) {
          let obj = this.heads.find(v => v.keyVal === itemKey)
          if (obj.keyType === 'file') {
            item[itemKey] = '';
          }
        }
      })
      this.form.bodyMaps = data;
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    handleUpload(params) {
      this.$modal.msgSuccess('上传成功');
      this.$set(this.form.bodyMaps[params.index], params.uploadName, params.url);
      this.$refs.form.clearValidate(params.prop);
    },
    defaultRow(heads) {
      let obj = {};
      heads.forEach(item => {
        obj[item.keyVal] = ''
      })
      return obj
    },
    addDetail() {
      this.form.bodyMaps.push(this.defaultRow(this.heads))
    },
    removeDetail(index) {
      this.form.bodyMaps.splice(index, 1)
    },
    onSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (!this.form.bodyMaps || this.form.bodyMaps.length === 0) {
            this.$message.error('报价数据不能为空')
            return
          }
          if (this.signOff === '0' && this.signVersion === '3') {
            this.$signatureSMS({
              phoneNumber: this.phoneNumber,
              handleConfirm: (val) => {
                console.log(val)
                let { authCode, flowId } = val;
                this.submitBargain({ authCode, flowId });
              }
            })
          } else {
            this.submitBargain();
          }
        }
      })
    },
    async submitBargain(args = {}) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { authCode, flowId } = args;
        await confirmBargain({
          buyItemCode: this.buyItemCode,
          subpackageCode: this.subpackageCode,
          round: this.round,
          heads: this.heads.map(v => {
            let regex = Base64.toBase64(v.regex);
            return {
              ...v,
              regex
            }
          }),
          bodyMaps: this.form.bodyMaps,
          authCode,
          flowId
        })
        this.$modal.msgSuccess('提交成功');
        this.isDialog = false;
        await this.getList();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    editQuotationSheet(row) {
      this.round = row.round;
      // if (row.round === 0) {
      //   this.form.bodyMaps = this.bodyMaps;
      // } else {
      //   let round = row.round - 1;
      //   let obj = this.tableData.find(item => item.round === round);
      //   this.form.bodyMaps = obj ? obj.bodyMaps : [];
      // }
      // if (!this.form.bodyMaps || this.form.bodyMaps.length === 0) {
      //   this.$message.error('上一轮未报价，无法报价')
      //   return
      // }
      this.form.bodyMaps = row.bodyMaps && row.bodyMaps.length > 0 ? row.bodyMaps : this.getBodyMaps(this.tableData, this.round);
      this.isEdit = true;
      this.isDialog = true;
    },
    getBodyMaps(data, round) {
      let arr = [];
      const walker = (data, round) => {
        if (round === 0) {
          arr = this.bodyMaps || [];
        } else {
          let prevRound = round - 1;
          let obj = data.find(v => v.round === prevRound);
          if (obj && obj.bodyMaps && obj.bodyMaps.length > 0) {
            arr = obj.bodyMaps;
          } else {
            walker(data, prevRound);
          }
        }
      }
      walker(data, round);
      return arr;
    },
    previewQuotationSheet(row) {
      console.log(row);
      this.form.bodyMaps = row.bodyMaps;
      this.allRowQuotationTotalPrice = row.allRowQuotationTotalPrice;
      this.isEdit = false;
      this.isDialog = true;
    },
    async getList() {
      try {
        this.loading = true;
        let { data } = await getSupplierBargainList(this.subpackageCode);
        this.currentDate = data.currentDate;
        this.heads = data.heads;
        this.bodyMaps = data.bodyMaps;
        this.tableData = this._.sortBy(data.roundList || [], 'round');
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
