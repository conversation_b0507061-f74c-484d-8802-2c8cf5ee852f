<template>
  <div>
    <div v-if="checkFunction(['supplier_result_publicity'])">
      <div v-if="resultAnnouncementReviewStatus">
        <el-result icon="error" title="您未中标" v-if="winBid==='0'"></el-result>
        <el-result icon="success" title="您已中标" v-if="winBid==='1'"></el-result>

        <div v-if="checkFunction(['supplier_result_notice'])">
          <div
            class="text-center"
            :style="{'--theme': theme}"
            v-if="resultNoticeSendingStatus"
          >
            <svg-icon icon-class="pdf" class-name="icon-pdf"/>
            <p class="fontSize14">结果通知书</p>
            <div class="mt20">
              <el-button
                size="small"
                type="success"
                @click="previewFile"
                v-if="isFee!=='1' || isPay"
              >
                预览
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="handleDown"
                v-if="isFee!=='1' || isPay"
              >
                下载
              </el-button>
              <form
                ref="form"
                name="pcForm"
                method="post"
                :action="pay_url"
                class="pay-form"
                v-if="isFee==='1'&&!isPay"
              >
                <input type="hidden" :value='mchnt_cd' name="mchnt_cd"/>
                <input type="hidden" :value='pay_type' name="pay_type"/>
                <input type="hidden" :value='order_id' name="order_id"/>
                <input type="hidden" :value='order_date' name="order_date"/>
                <input type="hidden" :value='order_amt' name="order_amt"/>
                <input type="hidden" :value='goods_name' name="goods_name"/>
                <input type="hidden" :value='rem' name="rem"/>
                <input type="hidden" :value='page_notify_url' name="page_notify_url"/>
                <input type="hidden" :value='back_notify_url' name="back_notify_url"/>
                <input type="hidden" :value='sign' name="sign"/>
                <input type="submit" value='付费后下载' class="pay-btn"/>
              </form>
            </div>
          </div>
        </div>

      </div>
      <el-empty description="项目尚未出结果，请等待..." v-else></el-empty>
    </div>
    <div v-else>
      <el-result icon="error" title="您未中标" v-if="winBid==='0'"></el-result>
      <el-result icon="success" title="您已中标" v-if="winBid==='1'"></el-result>

      <div v-if="checkFunction(['supplier_result_notice'])">
        <div
          class="text-center"
          :style="{'--theme': theme}"
          v-if="resultNoticeSendingStatus"
        >
          <svg-icon icon-class="pdf" class-name="icon-pdf"/>
          <p class="fontSize14">结果通知书</p>
          <div class="mt20">
            <el-button
              size="small"
              type="success"
              @click="previewFile"
              v-if="isFee!=='1' || isPay"
            >
              预览
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleDown"
              v-if="isFee!=='1' || isPay"
            >
              下载
            </el-button>
            <form
              ref="form"
              name="pcForm"
              method="post"
              :action="pay_url"
              class="pay-form"
              v-if="isFee==='1'&&!isPay"
            >
              <input type="hidden" :value='mchnt_cd' name="mchnt_cd"/>
              <input type="hidden" :value='pay_type' name="pay_type"/>
              <input type="hidden" :value='order_id' name="order_id"/>
              <input type="hidden" :value='order_date' name="order_date"/>
              <input type="hidden" :value='order_amt' name="order_amt"/>
              <input type="hidden" :value='goods_name' name="goods_name"/>
              <input type="hidden" :value='rem' name="rem"/>
              <input type="hidden" :value='page_notify_url' name="page_notify_url"/>
              <input type="hidden" :value='back_notify_url' name="back_notify_url"/>
              <input type="hidden" :value='sign' name="sign"/>
              <input type="submit" value='付费后下载' class="pay-btn"/>
            </form>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  getSupplierResult,
  payOrder,
  payCheck
} from '@/api/supplier/supplierProcess';
import { checkFunction } from '@/utils/permission';

export default {
  name: 'Index',
  data() {
    return {
      resultAnnouncementReviewStatus: false, // 结果公告审核通过
      resultNoticeSendingStatus: false, // 通知书是否发送
      winBid: null, //  0-未中标，1-中标
      resultNotice: null,
      isFee: null, // 是否收费
      isPay: false, // 是否支付
      orderAmt: null,
      mchnt_cd: null,
      order_date: null,
      order_id: null,
      order_amt: null,
      page_notify_url: null,
      back_notify_url: null,
      goods_name: null,
      pay_type: null,
      sign: null,
      rem: null,
      pay_url: null
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'subpackageCode',
      'subpackageName',
      'buyItemCode',
      'buyItemName'
    ])
  },
  created() {
    this.getSupplierResult();
  },
  methods: {
    checkFunction,
    async getPayInfo() {
      try {
        await this.getIsFee()
        if (this.isFee === '1') {
          await this.getIsPay();
          if (!this.isPay) {
            await this.handlePay();
          }
        }
      } catch (e) {
        throw new Error(e)
      }
    },
    async getIsFee() {
      try {
        let { data } = await this.getConfigKey('supplier.notice.download.fee.enabled');
        this.isFee = data;
        if (this.isFee === '1') {
          let orderAmtRes = await this.getConfigKey('supplier.notice.fee');
          this.orderAmt = Number(orderAmtRes.data);
        }
      } catch (e) {
        throw new Error(e)
      }
    },
    async getIsPay() {
      try {
        let { data } = await payCheck(this.subpackageCode);
        this.isPay = data;
      } catch (e) {
        throw new Error(e)
      }
    },
    async handlePay() {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        let { data } = await payOrder({
          buyItemCode: this.buyItemCode,
          buyItemName: this.buyItemName,
          subpackageCode: this.subpackageCode,
          subpackageName: this.subpackageName,
          pageNotifyUrl: window.location.href,
          orderAmt: this.orderAmt
        })
        let {
          mchnt_cd,
          order_date,
          order_id,
          order_amt,
          page_notify_url,
          back_notify_url,
          goods_name,
          pay_type,
          sign,
          rem,
          url
        } = data;
        this.mchnt_cd = mchnt_cd;
        this.order_date = order_date;
        this.order_id = order_id;
        this.order_amt = order_amt;
        this.page_notify_url = page_notify_url;
        this.back_notify_url = back_notify_url;
        this.goods_name = goods_name;
        this.pay_type = pay_type;
        this.sign = sign;
        this.rem = rem;
        this.pay_url = url;
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async handleDown() {
      this.$download.downloadFile(this.resultNotice);
    },
    previewFile() {
      this.$download.previewFile(this.resultNotice)
    },
    async getSupplierResult() {
      try {
        let { data } = await getSupplierResult(this.subpackageCode);
        this.resultNotice = data ? data.resultNotice : null;
        this.resultNoticeSendingStatus = data ? data.resultNoticeSendingStatus : false;
        this.winBid = data ? data.winBid : null;
        this.resultAnnouncementReviewStatus = data ? data.resultAnnouncementReviewStatus : false;
        if (this.winBid === '1' && this.resultNoticeSendingStatus) {
          await this.getPayInfo();
        }
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-form {
  display: inline-block;;
}

.pay-btn {
  padding: 9px 15px;
  font-size: 12px;
  border-radius: 3px;
  color: #FFFFFF;
  background-color: #1890ff;
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #1890ff;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  font-weight: 400;

  &:hover, &:focus {
    background: #46a6ff;
    border-color: #46a6ff;
    color: #FFFFFF;
  }
}
</style>
