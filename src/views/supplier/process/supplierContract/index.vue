<template>
  <div>
    <div v-if="resultNotice">
      <el-steps :active="active" direction="vertical" v-if="winBid==='1'">
        <el-step title="草拟合同">
          <div class="description" slot="description">
            <el-button size="small" type="primary" @click="handleEdit" :disabled="(contractStatus!==null&&contractStatus!==0)||winBid==='0'">
              编辑
            </el-button>
            <el-button size="small" type="primary" @click="handlePublish" :disabled="contractStatus!==0">
              发送给采购人
            </el-button>
          </div>
        </el-step>
        <el-step title="采购人确认">
          <div class="description" slot="description">
            <el-button size="small" type="primary" @click="previewFile(contractVo.contractUrl)" :disabled="contractStatus!==1">查看
            </el-button>
            <div class="el-upload__tip text-danger">
              <p>提示：请等待采购人确认！</p>
            </div>
          </div>
        </el-step>
        <el-step title="检查并确认">
          <div class="description" slot="description">
            <el-button size="small" type="primary" @click="previewFile(contractVo.contractUrl)" :disabled="contractStatus!==2">查看
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleConfirm(contractVo.contractUrl)"
              :disabled="contractStatus!==2">
              确认合同
            </el-button>
            <div class="el-upload__tip text-danger">
              <p>提示：请检查文件内容，若有问题，请联系采购人！</p>
            </div>
          </div>
        </el-step>
        <el-step title="下载草拟合同">
          <div class="description" slot="description">
            <el-button size="small" type="primary" @click="handleDown(contractVo.contractUrl)" :disabled="contractStatus!==3">
              下载
            </el-button>
          </div>
        </el-step>
        <el-step title="上传盖章合同">
          <div class="description" slot="description">
            <file-upload-single
              :showUploadBtn="processStatus!==1"
              :file-size="50"
              :disabled="contractStatus!==3||processStatus===2||processStatus===1"
              :params="{
                  fileTypeName: 'contract_file',
                  buyItemCode: buyItemCode,
                  subpackageCode: subpackageCode,
                  yearMonthSplit: createYearMonth,
                  supplierNumber: userId,
                }"
              @onSuccess="handleUpload"
            >
              <el-button slot="upload-btn" type="primary" size="small" :disabled="contractStatus!==3||processStatus===2||processStatus===1">
                上传
              </el-button>
              <el-button
                v-if="contractVo.attachment&&contractStatus===3"
                slot="upload-right"
                type="success"
                size="small"
                @click="previewFile(contractVo.attachment)"
              >
                查看
              </el-button>
              <el-button
                v-if="contractVo.attachment&&contractStatus===3"
                slot="upload-right"
                type="primary"
                size="small"
                @click="handleDown(contractVo.attachment)"
              >
                下载
              </el-button>
            </file-upload-single>
            <div class="el-upload__tip text-danger">
              <p>提示：上传盖章合同之后，请等待采购人审核！</p>
            </div>
          </div>
        </el-step>
        <el-step title="盖章合同审批">
          <div class="description" slot="description">
            <div v-if="!isEmpty(processStatus)">
              审批状态：
              <dict-tag :options="dict.type.approval_process_status" :value="processStatus"/>
              <div class="td-tip" v-if="processStatus===0">
                审批意见：{{ remark || '/' }}
              </div>
            </div>
          </div>
        </el-step>
      </el-steps>

      <el-result icon="error" title="您未中标，无法签订合同" v-if="winBid==='0'"></el-result>
    </div>
    <el-empty description="请等待结果通知..." v-else></el-empty>

    <el-dialog
      top="2vh"
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1200"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="17" :md="18" :lg="18" :xl="18">
            <el-form-item label="合同内容" prop="htmlTemplate">
              <template-select v-if="open" tmp-type="winning_contract" v-model="form.htmlTemplate"></template-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="7" :md="6" :lg="6" :xl="6">
            <el-form-item label="提示">
              <div>
                制作合同的时候，需要供应商上传：1、报价表及分项报价表；2、商务条款偏离表；3、技术偏离表；4、承诺书。以图片形式放在合同模版后面
              </div>
            </el-form-item>
            <el-form-item label=" ">
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="cancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getSupplierResult } from '@/api/supplier/supplierProcess';
import {
  addSupplierContract,
  confirmSupplierContract,
  publishSupplierContract,
  updateSupplierContract,
  upSupplierContract
} from '@/api/contractManage';
import { isEmpty } from '@/utils'

export default {
  name: 'SupplierContract',
  dicts: ['approval_process_status'],
  data() {
    return {
      winBid: null, // 0-未中标，1-中标
      resultNotice: null,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false, // 表单参数
      form: {},
      // 表单校验
      rules: {
        htmlTemplate: [
          { required: true, message: '合同内容不能为空', trigger: ['blur', 'change'] }
        ]
      },
      initEditor: true,
      contractStatus: null, // 合同状态:0-已编辑，待发送采购人,1-已提交采购人，待采购人确认,2-采购人已确认，待供应商确认,3-供应商已确认
      processStatus: null, // 0 拒绝 1 通过 2 审批中 3 撤销
      remark: null, // 审批备注
      contractVo: {
        id: null,
        contractUrl: null,
        attachment: null,
        htmlTemplate: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'createYearMonth',
      'buyItemName',
      'buyItemCode',
      'subpackageName',
      'subpackageCode',
      'userId'
    ]),
    active() {
      // contractStatus 状态:0-已编辑，待发送采购人,1-已提交采购人，待采购人确认,2-采购人已确认，待供应商确认,3-供应商已确认
      // processStatus 0 拒绝 1 通过 2 审批中 3 撤销
      let status = null;
      if (this.winBid === '0' || !this.resultNotice) {
        status = null
      } else {
        switch (this.contractStatus) {
          case 0:
            status = 0
            break;
          case 1:
            status = 1
            break;
          case 2:
            status = 2
            break;
          case 3:
            if (this.isEmpty(this.processStatus)) {
              status = 4
            } else {
              status = this.processStatus === 1 ? 6 : 5;
            }
            break;
          default:
            status = null
        }
      }
      console.log('status', status)
      return status
    }
  },
  created() {
    this.getContract();
  },
  methods: {
    isEmpty,
    async handleUpload(params) {
      try {
        this.$modal.loading('数据提交中，请稍候...');
        await upSupplierContract({
          attachment: params.url,
          id: this.contractVo.id
        });
        this.$modal.msgSuccess('上传成功');
        await this.getContract();
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        htmlTemplate: null
      };
      this.resetForm('form');
    },
    /** 编辑按钮操作 */
    handleEdit() {
      this.open = true;
      this.reset();
      for (let key in this.form) {
        this.form[key] = this.contractVo[key]
      }
    },
    handleConfirm() {
      this.$modal.confirm('是否确认合同模板内容？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await confirmSupplierContract(this.contractVo.id);
          this.$modal.msgSuccess('提交成功');
          await this.getContract();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    /** 提交采购人按钮操作 */
    handlePublish() {
      this.$modal.confirm('是否确认发送给采购人？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await publishSupplierContract(this.contractVo.id);
          this.$modal.msgSuccess('发送成功');
          await this.getContract();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          let params = {
            buyItemName: this.buyItemName,
            buyItemCode: this.buyItemCode,
            subpackageName: this.subpackageName,
            subpackageCode: this.subpackageCode,
            ...this.form
          };
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (!this.form.id) {
              await addSupplierContract(params);
            } else {
              await updateSupplierContract(params);
            }
            this.$modal.msgSuccess('提交成功');
            this.open = false;
            this.$modal.closeLoading()
            await this.getContract();
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      });
    },
    async handleDown(fileKey) {
      this.$download.downloadFile(fileKey);
    },
    previewFile(fileKey) {
      this.$download.previewFile(fileKey)
    },
    async getContract() {
      try {
        this.$modal.loading('数据查询中，请稍候...');
        let { data } = await getSupplierResult(this.subpackageCode);
        this.resultNotice = data ? data.resultNotice : null;
        this.winBid = data ? data.winBid : null;
        this.contractStatus = data && data.supplierContractVo ? data.supplierContractVo.auditStatus : null;
        this.processStatus = data && data.auditInfoVo ? data.auditInfoVo.status : null;
        this.remark = data && data.auditInfoVo ? data.auditInfoVo.remark : null;
        if (isEmpty(this.contractStatus)) {
          this.title = '添加';
          for (let key in this.contractVo) {
            this.contractVo[key] = null;
          }
        } else {
          this.title = '修改';
          let supplierContractVo = data.supplierContractVo;
          for (let key in this.contractVo) {
            this.contractVo[key] = supplierContractVo[key];
          }
        }
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.description {
  padding: 10px 15px 25px;
}
</style>
