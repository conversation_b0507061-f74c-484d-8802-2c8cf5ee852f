<template>
  <div class="text-center" v-loading="loading">
    <div>
      <el-image
        @click="downPdfFile(pdfFile)"
        v-if="pdfFile&&releaseStatus==='1'&&permit==='1'"
        style="width: 100px; height: 100px"
        :src="fileImgUrl"
        fit="fill"
        class="pointer">
      </el-image>
      <div class="file-disabled-box" v-else>
        <el-image
          style="width: 100px; height: 100px"
          :src="fileImgDisabledUrl"
          fit="fill"
        >
        </el-image>
        <p class="file-disabled-text" v-if="!(pdfFile&&releaseStatus==='1')">文件未发布</p>
        <p class="file-disabled-text" v-else>采购人未允许下载</p>
      </div>
    </div>
    <div class="mt15">
      <el-button
        type="primary"
        @click="previewFile(pdfFile)"
        :disabled="!(pdfFile && releaseStatus==='1' && permit==='1')">
        预览{{ getPurchaseDict(purchaseMethodCode, 'purchaseFile')}}
      </el-button>
      <el-button
        type="primary"
        @click="downPdfFile(pdfFile)"
        :disabled="!(pdfFile && releaseStatus==='1' && permit==='1')">
        下载{{ getPurchaseDict(purchaseMethodCode, 'purchaseFile')}}
      </el-button>
    </div>
    <div class="mt15">
      <file-list-view
        style="display: inline-block;"
        :disabled="!(pdfFile && releaseStatus==='1' && permit==='1')"
        fontSize="16px"
        :shouSlash="false"
        :fileList="claimsFileAttVoList"
        :handlePreview="previewFile"
        :handleDown="downPdfFile"
      />
    </div>

    <fee-info
      v-if="checkFunction(['supplier_bid_fee_info'])&&versionType!=='whws'"
      :permit="permit"
      :paymentVoucher="paymentVoucher"
      @update="getPurchaseFileBySupplier"
    />

    <fee-info-ws
      v-if="checkFunction(['supplier_bid_fee_info'])&&versionType==='whws'&&bidWhetherFree===1"
      :bidBookAmount="bidBookAmount"
      :bidWhetherFree="bidWhetherFree"
      :permit="permit"
      :paymentVoucher="paymentVoucher"
      :accountName="accountName"
      :bankAccount="bankAccount"
      :openingBank="openingBank"
      @update="getPurchaseFileBySupplier"
    />

  </div>
</template>

<script>
import fileImgUrl from '@/assets/images/file-zip.png';
import fileImgDisabledUrl from '@/assets/images/file-zip-disabled.png';
import { mapGetters } from 'vuex';
import { getPurchaseFileBySupplier } from '@/api/supplier/supplierProcess';
import { checkFunction, checkPermi } from '@/utils/permission';
import feeInfo from '@/views/supplier/process/purchaseFileDown/feeInfo.vue'
import feeInfoWs from '@/views/supplier/process/purchaseFileDown/feeInfoWs.vue'

export default {
  name: 'PurchaseFileDown',
  components: {
    feeInfo,
    feeInfoWs
  },
  data() {
    return {
      fileImgUrl,
      fileImgDisabledUrl,
      permit: '0', // 是否允许响应: 0-不允许，1-允许
      pdfFile: null,
      claimsFileAttVoList: [],
      releaseStatus: null, // 0已上传 1已确认
      version: null,
      paymentVoucher: null,
      bidWhetherFree: null,
      bidBookAmount: null,
      loading: false,
      accountName: null,
      bankAccount: null,
      openingBank: null
    }
  },
  computed: {
    ...mapGetters([
      'purchaseMethodCode',
      'getPurchaseDict',
      'subpackageCode'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getPurchaseFileBySupplier();
  },
  methods: {
    checkFunction,
    checkPermi,
    previewFile(fileKey) {
      if (!this.checkPermi(['supplier:process:operate'])) {
        this.$modal.msgWarning('没有操作权限');
        return
      }
      this.$download.previewFile(fileKey)
    },
    downPdfFile(fileKey) {
      if (!this.checkPermi(['supplier:process:operate'])) {
        this.$modal.msgWarning('没有操作权限');
        return
      }
      this.$download.downloadFile(fileKey);
    },
    async getPurchaseFileBySupplier() {
      try {
        this.loading = true;
        let { data } = await getPurchaseFileBySupplier(this.subpackageCode);
        let {
          pdfFile,
          claimsFileAttVoList,
          releaseStatus,
          permit,
          version,
          paymentVoucher,
          bidWhetherFree,
          bidBookAmount,
          accountName,
          bankAccount,
          openingBank
        } = data;
        this.pdfFile = pdfFile;
        let list = claimsFileAttVoList || [];
        this.claimsFileAttVoList = list.map(v => {
          return {
            name: v.fileName,
            url: v.fileKey
          }
        })
        this.releaseStatus = releaseStatus;
        this.permit = permit;
        this.version = version;
        this.paymentVoucher = paymentVoucher;
        this.bidWhetherFree = bidWhetherFree;
        this.bidBookAmount = bidBookAmount;
        this.accountName = accountName;
        this.bankAccount = bankAccount;
        this.openingBank = openingBank;
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.file-disabled-box {
  display: inline-block;
  width: 100px;
  height: 100px;
  position: relative;

  .file-disabled-text {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    color: #ffffff;
  }
}
</style>
