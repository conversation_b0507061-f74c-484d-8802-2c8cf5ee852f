<template>
  <div>
    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" type="index" width="50" align="center"/>
      <el-table-column label="公告名称" align="center" prop="bulletinName" :show-overflow-tooltip="true"/>
      <el-table-column label="公告类型" align="center" prop="bulletinTypeName" :show-overflow-tooltip="true"/>
      <el-table-column label="公告内容" align="center">
        <template v-slot:default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(row.bulletinContentKey)"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="attachmentDtoList">
        <template v-slot:default="{row}">
          <file-list-view :fileList="row.attachmentDtoList" :handlePreview="handlePreview" :handleDown="handleDown"/>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="createAt" :show-overflow-tooltip="true">
        <template v-slot:default="{row}">
          <span>{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { supplierQueryBulletin } from '@/api/supplier/supplierProcess';
import { mapGetters } from 'vuex'

export default {
  name: 'SupplierBulletinQuery',
  data() {
    return {
      tableData: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'subpackageCode'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    handleDown(fileKey) {
      this.$download.downloadFile(fileKey)
    },
    handlePreview(fileKey) {
      this.$download.previewFile(fileKey)
    },
    getList() {
      this.loading = true
      supplierQueryBulletin({
        subpackageCode: this.subpackageCode
      })
        .then(res => {
          this.tableData = res.data
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>

</style>
