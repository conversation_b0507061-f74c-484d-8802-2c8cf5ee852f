<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
      @submit.native.prevent
    >
      <el-form-item
        :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
        prop="entity.buyItemName"
      >
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <ul v-loading="loading">
      <li v-for="(item,index) in tableData" :key="index">
        <div class="project-item">
          <div
            class="project-item-left"
            :style="{borderTop:index===0?'1px solid #dfe6ec':'none'}"
          >
            {{ index + 1 }}
          </div>
          <div
            class="project-item-right"
            :style="{borderTop:index===0?'1px solid #dfe6ec':'none'}"
          >
            <h3 class="project-name over-ellipsis">
              <span>{{ item.buyItemName }}</span>
            </h3>
            <project-item-table
              :key="'project-item-'+index"
              :item="item"
              :isSignReview="isSignReview"
              @update="getList"
            />
          </div>
        </div>
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getSupplierProject } from '@/api/supplier/supplierProject';
import { mapGetters } from 'vuex';
import ProjectItemTable from '@/views/supplier/supplierProject/projectItemTable.vue'

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  components: {
    ProjectItemTable
  },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          buyItemName: null
        }
      },
      total: 0,
      tableData: [],
      isSignReview: '0'
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device',
      'getPurchaseDict'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getConfigKey('supplier_sign_up.review_status.enabled').then(response => {
      this.isSignReview = response.data || '0';
    })
    this.getList()
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        this.loading = true
        let { rows, total } = await getSupplierProject(this.queryParams)
        this.tableData = rows || [];
        this.total = total
        this.loading = false
      } catch (e) {
        this.loading = false
        throw e
      }
    }
  }
}
</script>

<style scoped lang="scss">
.project-item {
  display: flex;
  justify-content: space-between;
  align-items: stretch;

  .project-item-left {
    width: 40px;
    font-size: 14px;
    border: 1px solid #dfe6ec;
    border-right: none;
    border-top: none;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .project-item-right {
    width: calc(100% - 40px);

    .project-name {
      height: 40px;
      line-height: 40px;
      background: #f8f8f9;
      color: #515a6e;
      font-size: 14px;
      word-break: break-word;
      font-weight: 700;
      padding: 0 10px;
      border-left: 1px solid #dfe6ec;
      border-right: 1px solid #dfe6ec;
    }
  }
}
</style>
