<template>
  <div>
    <el-table :data="[0]" border :show-header="false">
      <el-table-column align="center">
        <template>
          {{ item.subpackageName }}
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop="timeNodeList"
        min-width="400px"
        class-name="step-cell"
      >
        <template v-slot:default="{row}">
          <el-steps
            :active="100"
            class="step-wrap"
            :style="{'--theme': theme}"
          >
            <el-step
              v-for="(stepItem,stepKey) in item.timeNodeList"
              :key="stepKey"
            >
              <span slot="title" class="fontSize12 text-base">
                {{ getPurchaseDict(item.purchaseMethodCode, stepItem.timeKeyName) }}
              </span>
              <span slot="description" class="fontSize12 text-base">
                {{ stepItem.time | formatTime('YYYY-MM-DD HH:mm:ss') }}
              </span>
            </el-step>
          </el-steps>
          <svg-icon icon-class="abandon-bid" class-name="project-icon" v-if="row.abandon==='1'"/>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        v-if="isSignReview==='1'"
        key="reviewStatus"
      >
        <template>
          <span class="text-info" v-if="item.reviewStatus===0">报名信息待审核</span>
          <span class="text-success" v-if="item.reviewStatus===1">报名信息审核通过</span>
          <span class="text-danger" v-if="item.reviewStatus===2">
            报名信息审核未通过，理由：{{ item.reviewReason }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" width="110px">
        <template>
          <re-sign-up
            v-if="item.reviewStatus===2&&isSignReview==='1'"
            :id="item.supplierSignUpInfo.id"
            :orgCode="item.orgCode"
            :buyItemCode="item.buyItemCode"
            :buyItemName="item.buyItemName"
            :subpackageCode="item.subpackageCode"
            :subpackageName="item.subpackageName"
            :createYearMonth="formatTime(item.buyItemCreateAt,'YYYYMM')"
            :whetherWrite="item.whetherWrite"
            :heads="item.additionalInformation"
            @success="update"
          />
          <el-button
            :disabled="item.reviewStatus!==1"
            type="primary"
            plain
            size="mini"
            @click="toProcess(item)"
          >进入流程
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import reSignUp from '@/views/supplier/supplierProject/reSignUp.vue'
import { formatTime } from '@/utils/filters'

export default {
  name: 'ProjectItemTable',
  components: {
    reSignUp
  },
  props: {
    item: {
      type: Object,
      default: () => {
      }
    },
    isSignReview: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'getPurchaseDict'
    ])
  },
  created() {

  },
  methods: {
    formatTime,
    update() {
      this.$emit('update')
    },
    async toProcess(item) {
      console.log(item)
      console.log(item.reviewStatus)
      if (!this.$auth.hasPermi('supplier:process:query')) {
        this.$modal.msgError('当前操作没有权限');
        return
      }
      if (this.isSignReview === '1' && item.reviewStatus !== 1) {
        this.$modal.msgWarning('报名信息审核未通过');
        return
      }
      await this.$store.dispatch('process/setBuyItemName', item.buyItemName)
      await this.$store.dispatch('process/setBuyItemCode', item.buyItemCode)
      await this.$store.dispatch('process/setPurchaseMethodCode', item.purchaseMethodCode)
      await this.$store.dispatch('process/setOrgCode', item.orgCode)
      await this.$store.dispatch('process/setSubpackageName', item.subpackageName)
      await this.$store.dispatch('process/setSubpackageCode', item.subpackageCode)
      await this.$store.dispatch('process/setCreateYearMonth', this.formatTime(item.buyItemCreateAt, 'YYYYMM'))
      let res = await this.$store.dispatch('process/setProcessNodes', {
        buyItemCode: item.buyItemCode,
        subpackageCode: item.subpackageCode,
        belongRole: '2'
      })
      if (res.length === 0) {
        this.$modal.msgWarning('未查询到功能配置');
        return
      }
      await this.$router.push({ path: res[0].path })
    }
  }
}
</script>

<style lang="scss">
.step-cell {
  .cell {
    line-height: normal;
  }

  .step-wrap {
    position: relative;

    .el-step__head {
      .el-step__line {
        height: 1px;

        .el-step__line-inner {
          border-width: 0 !important;
          height: 1px;
          background-color: #{'var(--theme)'};
        }
      }

      .el-step__icon {
        width: 20px;
        height: 20px;
        font-size: 13px;

        &.is-text {
          border: 1px solid;
        }

        .el-step__icon-inner {
          font-weight: normal;
        }
      }
    }

    .el-step__main {
      margin-top: 2px;

      .el-step__title {
        line-height: 1.5;
      }
    }

    .step-content {
      //font-weight: bold;
      color: #{'var(--theme)'};
    }
  }
}
</style>
