<template>
  <div class="mb10">
    <el-button
      type="success"
      plain
      size="mini"
      @click="clickSignUp"
    >重新报名
    </el-button>

    <!-- 添加或修改对话框 -->
    <el-dialog
      title="填写投标人信息"
      :visible.sync="isDialog"
      custom-class="maxW1200"
      top="1vh"
      width="90%"
      modal-append-to-body
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-card header="主体信息" shadow="never" :body-style="{'padding': '15px 10px 0px 10px'}" class="card-box">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="公司名称" prop="bidderName">
                <el-input v-model.trim="form.bidderName" placeholder="请输入" disabled/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="统一社会信用代码" prop="licNumber">
                <el-input v-model.trim="form.licNumber" placeholder="请输入" disabled/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="类别" prop="bidTypeList">
                <el-select
                  v-model="form.bidTypeList"
                  multiple
                  clearable
                  :multiple-limit="bidTypeNum"
                  placeholder="请选择"
                  class="block"
                >
                  <el-option
                    v-for="dict in dict.type.supplier_bid_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="行政区域" prop="regionCode">
                <el-cascader
                  placeholder="请选择"
                  class="block"
                  v-model="form.regionCode"
                  :options="cityList"
                  :props="{
                    value: 'code',
                    label: 'name',
                    children: 'children',
                    emitPath: false
                  }"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="详细地址" prop="contactAddress">
                <el-input v-model.trim="form.contactAddress" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model.trim="form.email" placeholder="请输入" maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType!=='whws'">
              <el-form-item label="供应商类型" prop="unitNatureList">
                <el-select
                  v-model="form.unitNatureList"
                  multiple
                  clearable
                  placeholder="请选择"
                  class="block"
                >
                  <el-option
                    v-for="dict in dict.type.supplier_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="开户银行" prop="openingBank">
                <el-input v-model.trim="form.openingBank" placeholder="请输入" maxlength="30"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="基本账户账号" prop="basicAccount">
                <el-input v-model.trim="form.basicAccount" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="注册资本" prop="registeredCapital">
                <el-input v-model.trim="form.registeredCapital" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="营业期限" prop="operatingPeriod">
                <el-input v-model.trim="form.operatingPeriod" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="成立日期" prop="dateOfEstablishment">
                <el-input v-model.trim="form.dateOfEstablishment" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="登记机关" prop="registrationAndAuthority">
                <el-input v-model.trim="form.registrationAndAuthority" placeholder="请输入" maxlength="100"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="是否中小微企业" prop="whetherMicroEnterprises">
                <el-radio-group v-model="form.whetherMicroEnterprises" disabled>
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="form.whetherMicroEnterprises===1">
              <el-form-item label="中小微企业">
                <el-button
                  type="success"
                  size="mini"
                  v-if="form.microEnterprises"
                  @click="previewFileByOther(form.microEnterprises)"
                >预览
                </el-button>
                <span v-else>/</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="营业执照或组织机构代码证件扫描件">
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".png, .jpg, .jpeg, .pdf"
                  uploadName="businessLicense"
                  :params="{
                    fileTypeName: 'businessLicense'
                  }"
                  @onSuccess="handleUploadByOther"
                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="form.businessLicense"
                    @click="previewFileByOther(form.businessLicense)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="附件（安全许可证、企业资质等）" prop="supplierAttachmentList">
                <file-upload
                  v-model="form.supplierAttachmentList"
                  :showTip="true"
                  :fileSize="50"
                  :fileType="['pdf','png','jpg','jpeg']"
                  uploadUrl="/epcfile/nonProjectFile/upload"
                  :params="{
                  fileTypeName: 'supplier_info_attachment'
                }"
                  @preview="previewFileByOther"
                  @down="downloadFileByOther"
                >
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card header="法定代表人信息" shadow="never" :body-style="{'padding': '15px 10px 0px 10px'}" class="card-box">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人姓名" prop="certificateName">
                <el-input v-model.trim="form.certificateName" placeholder="请输入" disabled/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人证件类型" prop="certificate">
                <el-select v-model="form.certificate" placeholder="请选择" class="block" disabled>
                  <el-option
                    v-for="dict in dict.type.id_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人证件号码" prop="certificateCode">
                <el-input :placeholder="form.certificateCode | formatIdCard()" maxlength="50" disabled/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item prop="contactNumber">
                <span slot="label">
                <el-tooltip content="经营负责人手机号码（法定代表人或实际控制人，不得使用其他联系人号码）" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                法定代表人手机号码
              </span>
                <el-input v-model.trim="form.contactNumber" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item label="法定代表人身份证扫描件">
                <el-button
                  type="success"
                  size="mini"
                  v-if="form.legalRepresentativeIdentityCertificate"
                  @click="previewFileByOther(form.legalRepresentativeIdentityCertificate)"
                >预览
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')" prop="infoReporterName">
                <el-input v-model.trim="form.infoReporterName" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporter')" prop="infoReporter">
                <el-select v-model="form.infoReporter" placeholder="请选择" class="block">
                  <el-option
                    v-for="dict in dict.type.id_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterCode')" prop="infoReporterCode">
                <el-input v-model.trim="form.infoReporterCode" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
                prop="infoReporterContactNumber">
                <el-input v-model.trim="form.infoReporterContactNumber" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType!=='xyzy'">
              <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'powerOfAttorney')" prop="powerOfAttorney">
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".pdf"
                  uploadName="powerOfAttorney"
                  :params="{
                      prop:'powerOfAttorney',
                      fileTypeName: 'powerOfAttorney',
                      buyItemCode: buyItemCode,
                      subpackageCode: subpackageCode,
                      supplierNumber: userId,
                      yearMonthSplit: createYearMonth
                    }"
                  @onSuccess="handleUploadByAnnex"
                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="form.powerOfAttorney"
                    @click="previewFileByAnnex(form.powerOfAttorney)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card
          header="响应附加条件"
          shadow="never"
          :body-style="{'padding': '15px 10px 0px 10px'}"
          class="card-box"
          v-if="heads.length>0">
          <el-row :gutter="20">
            <template v-for="(item,index) in heads">
              <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" :key="index">
                <el-form-item
                  :label="item.keyName"
                  :prop="'additionalInformationMap.'+item.keyVal"
                  :rules="[
                    { required: item.required, message: '请输入', trigger:  ['blur', 'change'] },
                  { pattern: checkReg(item.regex)?eval2(item.regex): null, message: item.remark||'格式不正确', trigger: ['blur', 'change'] }
                  ]"
                >
                  <el-input v-model.trim="form.additionalInformationMap[item.keyVal]" placeholder="请输入" v-if="item.keyType!=='file'"/>
                  <file-upload-single
                    v-else
                    :fileSize="10"
                    :showTip="true"
                    :autoUpload="false"
                    accept=".pdf"
                    :uploadName="item.keyVal"
                    :params="{
                      prop:'additionalInformationMap.'+item.keyVal,
                      fileTypeName: item.keyVal,
                      buyItemCode: buyItemCode,
                      subpackageCode: subpackageCode,
                      supplierNumber: userId,
                      yearMonthSplit: createYearMonth
                    }"
                    @onSuccess="handleUploadByAnnex1"
                  >
                    <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                    <el-button
                      slot="upload-right"
                      type="success"
                      size="mini"
                      v-if="form.additionalInformationMap[item.keyVal]"
                      @click="previewFileByAnnex(form.additionalInformationMap[item.keyVal])"
                    >预览
                    </el-button>
                  </file-upload-single>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-card>

        <el-card
          v-if="whetherWrite===1"
          header="关联公司及其法定代表人和股东信息"
          shadow="never"
          :body-style="{'padding': '15px 10px 0px 10px'}"
          class="card-box"
        >
          <div slot="header" class="clearfix">
            <span>关联公司及其法定代表人和股东信息</span>
            <div class="fr">
              <el-button size="mini" type="primary" plain @click="addRelatedCompany">添加关联公司</el-button>
            </div>
          </div>
          <el-card v-for="(item,index) in form.relatedList" :key="index" class="mb20">
            <div slot="header" class="clearfix">
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                icon="el-icon-delete"
                class="btn-text-danger"
                @click="removeRelatedCompany(index)"
              >
                删除
              </el-button>
            </div>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
                <el-form-item
                  label="关联公司名称"
                  :prop="'relatedList.'+index+'.bidderName'"
                  :rules="rules.bidderName"
                >
                  <el-input v-model.trim="item.bidderName" placeholder="请输入"/>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
                <el-form-item
                  label="统一社会信用代码"
                  :prop="'relatedList.'+index+'.licNumber'"
                  :rules="rules.licNumber"
                >
                  <el-input v-model.trim="item.licNumber" placeholder="请输入"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-table :data="item.shareholderList" border empty-text="请添加股东或法人信息">
              <el-table-column label="序号" align="center" type="index" width="55"></el-table-column>
              <el-table-column label="股东或法人名称" align="center" class-name="form-cell">
                <template v-slot:default="{row,$index}">
                  <el-form-item
                    :prop="'relatedList.'+index+'.shareholderList.'+$index+'.name'"
                    label-width="0"
                    size="mini"
                    :rules="rules.name"
                  >
                    <el-input v-model.trim="row.name" placeholder="请输入"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="股东或法人证件号码" align="center" class-name="form-cell">
                <template v-slot:default="{row,$index}">
                  <el-form-item
                    :prop="'relatedList.'+index+'.shareholderList.'+$index+'.number'"
                    label-width="0"
                    size="mini"
                    :rules="rules.number"
                  >
                    <el-input v-model.trim="row.number" placeholder="请输入"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="90">
                <template v-slot:default="{$index}">
                  <el-button
                    size="mini"
                    icon="el-icon-delete"
                    class="btn-text-danger"
                    type="text"
                    @click="removeRelatedShareholder(index,$index)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="text-center mt10">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addRelatedShareholder(index)"
                plain
              >
                添加股东或法人信息
              </el-button>
            </div>
          </el-card>
        </el-card>
      </el-form>
      <div slot="footer" class="text-center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { querySupplierInfo } from '@/api/supplier/supplierInfo';
import reg from '@/utils/reg';
import cityList from '@/assets/data/cityList';
import { fileUploadByAnnex, fileUploadByOther } from '@/api/file';
import { mapGetters } from 'vuex';
import { signModify } from '@/api/supplier/supplierProject'
import { checkReg, eval2 } from '@/utils/validate'
import { Base64 } from 'js-base64'

export default {
  name: 'SignUp',
  dicts: ['supplier_type', 'sys_dict_translate', 'supplier_bid_type', 'id_type'],
  props: {
    id: {
      type: Number,
      default: null
    },
    orgCode: {
      type: String,
      default: null
    },
    buyItemCode: {
      type: String,
      default: null
    },
    buyItemName: {
      type: String,
      default: null
    },
    subpackageCode: {
      type: String,
      default: null
    },
    subpackageName: {
      type: String,
      default: null
    },
    createYearMonth: {
      type: String,
      default: null
    },
    whetherWrite: {
      type: Number,
      default: 0
    },
    heads: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 是否显示弹出层
      isDialog: false,
      cityList,
      form: {
        bidderName: null,
        licNumber: null,
        contactNumber: null,
        regionCode: null,
        contactAddress: null,
        email: null,
        openingBank: null,
        basicAccount: null,
        unitNature: null,
        unitNatureList: [],
        businessLicense: null,
        legalRepresentativeIdentityCertificate: null,
        microEnterprises: null,
        certificate: null,
        certificateCode: null,
        certificateName: null,
        bidType: null,
        bidTypeList: [],
        registeredCapital: null,
        operatingPeriod: null,
        dateOfEstablishment: null,
        registrationAndAuthority: null,
        whetherMicroEnterprises: null,
        powerOfAttorney: null,
        infoReporterName: null,
        infoReporter: null,
        infoReporterCode: null,
        infoReporterContactNumber: null,
        supplierAttachmentList: [],
        relatedList: [],
        additionalInformation: [],
        additionalInformationMap: {}
      },
      rules: {
        bidderName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '公司名称长度必须介于 2 和 50 之间', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        licNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.unifiedCreditCode, message: '格式不对', trigger: 'blur' }
        ],
        contactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        regionCode: [{ required: true, message: '请选择', trigger: 'change' }],
        contactAddress: [{ required: true, message: '请输入', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        openingBank: [{ required: true, message: '请输入', trigger: 'blur' }],
        basicAccount: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.bankCardNumber, message: '格式不对', trigger: 'blur' }
        ],
        unitNatureList: [{ required: true, message: '请选择', trigger: 'change' }],
        businessLicense: [{ required: true, message: '请上传', trigger: 'change' }],
        legalRepresentativeIdentityCertificate: [{ required: true, message: '请上传', trigger: 'change' }],
        certificateCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        certificate: [{ required: true, message: '请输入', trigger: 'change' }],
        certificateName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        bidTypeList: [{ required: true, message: '请选择', trigger: 'change' }],
        registeredCapital: [{ required: true, message: '请输入', trigger: 'blur' }],
        operatingPeriod: [{ required: true, message: '请输入', trigger: 'blur' }],
        dateOfEstablishment: [{ required: true, message: '请输入', trigger: 'blur' }],
        registrationAndAuthority: [{ required: true, message: '请输入', trigger: 'blur' }],
        whetherMicroEnterprises: [{ required: true, message: '请选择', trigger: 'change' }],
        powerOfAttorney: [{ required: true, message: '请上传', trigger: 'change' }],
        infoReporterName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        infoReporterCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        infoReporter: [{ required: true, message: '请输入', trigger: 'change' }],
        infoReporterContactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 50, message: '最长50个字符', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 50, message: '最长50个字符', trigger: 'blur' }
        ]
      },
      bidTypeNum: 0
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('select.bidType.num').then(response => {
      this.bidTypeNum = Number(response.data);
    })
  },
  methods: {
    checkReg,
    eval2,
    addRelatedCompany() {
      this.form.relatedList.push({
        bidderName: null,
        licNumber: null,
        shareholderList: []
      })
    },
    removeRelatedCompany(index) {
      this.form.relatedList.splice(index, 1);
    },
    addRelatedShareholder(index) {
      this.form.relatedList[index].shareholderList.push({
        name: null,
        number: null
      });
    },
    removeRelatedShareholder(index, $index) {
      this.form.relatedList[index].shareholderList.splice($index, 1);
    },
    async handleUploadByOther(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    async handleUploadByAnnex1(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByAnnex(params);
        this.$set(this.form.additionalInformationMap, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.prop)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    async handleUploadByAnnex(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByAnnex(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.prop)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    previewFileByAnnex(fileKey) {
      this.$download.previewFileByAnnex(fileKey)
    },
    /** 提交报名 */
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            this.form.bidType = this.form.bidTypeList.join(',');
            this.form.unitNature = this.form.unitNatureList.join(',');
            await signModify({
              orgCode: this.orgCode,
              buyItemName: this.buyItemName,
              buyItemCode: this.buyItemCode,
              subpackageName: this.subpackageName,
              subpackageCode: this.subpackageCode,
              supplierSignUpInfo: {
                ...this.form,
                id: this.id
              },
              additionalInformation: this.form.additionalInformation,
              additionalInformationMap: [this.form.additionalInformationMap]
            })
            this.$modal.msgSuccess('报名成功');
            this.isDialog = false;
            this.$emit('success');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      })
    },
    cancel() {
      this.isDialog = false;
    },
    /** 点击报名 */
    async clickSignUp() {
      /** 查询供应商企业信息 */
      let { data } = await querySupplierInfo(this.userId);
      let userOrgVoList = data.userOrgVoList || [];
      let obj = userOrgVoList.find(item => item.orgCode === this.orgCode);
      if (!(obj && obj.orgAuditStatus === '2')) {
        this.$modal.msgError('企业信息审核未通过，无法报名！');
        return;
      }
      for (let key in this.form) {
        this.form[key] = data[key];
      }
      this.form.bidTypeList = this.form.bidType ? this.form.bidType.split(',') : [];
      this.form.unitNatureList = this.form.unitNature ? this.form.unitNature.split(',') : [];
      // this.form.infoReporterName = null;
      // this.form.infoReporterCode = null;
      // this.form.infoReporterContactNumber = null;
      this.form.shareholderList = [];
      this.form.relatedList = [];
      this.form.additionalInformation = this.heads.map(v => {
        let regex = Base64.toBase64(v.regex);
        return {
          ...v,
          regex
        }
      });
      this.form.additionalInformationMap = this.defaultRow(this.heads);
      this.isDialog = true;
    },
    defaultRow(heads) {
      let obj = {};
      heads.forEach(item => {
        obj[item.keyVal] = ''
      })
      return obj
    }
  }
}
</script>

<style scoped>

</style>
