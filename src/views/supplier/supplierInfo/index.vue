<template>
  <div class="app-container" v-loading="loading">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-card
        header="所属组织信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 10px 10px'}"
        class="card-box"
      >
        <el-table :data="organizeVoList" border>
          <el-table-column
            prop="orgName"
            label="组织名称"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="orgAuditStatus"
            label="审核状态"
            align="center"
            width="100"
          >
            <template v-slot:default="{row}">
              <dict-tag
                :options="dict.type.organize_audit_status"
                :value="row.orgAuditStatus"
                v-if="dict.type.organize_audit_status.some(item=>item.value===row.orgAuditStatus)"
              />
              <span v-else>/</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="reason"
            label="审核不通过理由"
            align="center"
          >
            <template v-slot:default="{row}">
              <span v-if="row.orgAuditStatus==='1'">
                {{ row.reason || '/' }}
              </span>
              <span v-else>/</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
          >
            <template v-slot:default="{row}">
              <el-button
                v-if="row.orgAuditStatus!=='0' && row.orgAuditStatus!=='2'"
                size="mini"
                type="text"
                @click="handleApply(row)"
              >
                {{ row.orgAuditStatus === '1' ? '重新申请' : '申请加入' }}
              </el-button>
              <span v-else>/</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card
        header="主体信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="公司名称" prop="bidderName">
              <el-input
                v-model.trim="form.bidderName"
                placeholder="请输入"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="统一社会信用代码" prop="licNumber">
              <el-input
                v-model.trim="form.licNumber"
                placeholder="请输入"
                :disabled="!!licNumber"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="类别" prop="bidTypeList">
              <el-select
                v-model="form.bidTypeList"
                multiple
                :multiple-limit="bidTypeNum"
                placeholder="请选择"
                class="block"
                clearable
                :disabled="isDisabled"
              >
                <el-option
                  v-for="dict in dict.type.supplier_bid_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="行政区域" prop="regionCode">
              <el-cascader
                :disabled="isDisabled"
                placeholder="请选择"
                class="block"
                v-model="form.regionCode"
                :options="cityList"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="详细地址" prop="contactAddress">
              <el-input
                v-model.trim="form.contactAddress"
                placeholder="请输入"
                maxlength="100"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="电子邮箱" prop="email">
              <el-input
                v-model.trim="form.email"
                placeholder="请输入"
                maxlength="50"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType!=='whws'">
            <el-form-item label="供应商类型" prop="unitNatureList">
              <el-select
                v-model="form.unitNatureList"
                placeholder="请选择"
                class="block"
                multiple
                clearable
                :disabled="isDisabled"
              >
                <el-option
                  v-for="dict in dict.type.supplier_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="开户银行" prop="openingBank">
              <el-input
                v-model.trim="form.openingBank"
                placeholder="请输入"
                maxlength="30"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="基本账户账号" prop="basicAccount">
              <el-input
                v-model.trim="form.basicAccount"
                placeholder="请输入"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="注册资本" prop="registeredCapital">
              <el-input
                v-model.trim="form.registeredCapital"
                placeholder="请输入"
                maxlength="100"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="营业期限" prop="operatingPeriod">
              <el-input
                v-model.trim="form.operatingPeriod"
                placeholder="请输入"
                maxlength="100"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="成立日期" prop="dateOfEstablishment">
              <el-input
                v-model.trim="form.dateOfEstablishment"
                placeholder="请输入"
                maxlength="100"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="登记机关" prop="registrationAndAuthority">
              <el-input
                v-model.trim="form.registrationAndAuthority"
                placeholder="请输入"
                maxlength="100"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="是否中小微企业" prop="whetherMicroEnterprises">
              <el-radio-group v-model="form.whetherMicroEnterprises" :disabled="isDisabled">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="form.whetherMicroEnterprises===1">
            <el-form-item label="中小微企业" prop="microEnterprises">
              <file-upload-single
                :fileSize="10"
                :showTip="true"
                :autoUpload="false"
                accept=".png, .jpg, .jpeg, .pdf"
                uploadName="microEnterprises"
                :params="{
                    fileTypeName: 'microEnterprises'
                  }"
                @onSuccess="handleUploadByOther"
                :disabled="isDisabled"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini"
                  :disabled="isDisabled"
                >
                  上传
                </el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.microEnterprises"
                  @click="previewFileByOther(form.microEnterprises)"
                >
                  预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="营业执照或组织机构代码证件扫描件" prop="businessLicense">
              <file-upload-single
                :fileSize="10"
                :showTip="true"
                :autoUpload="false"
                accept=".png, .jpg, .jpeg, .pdf"
                uploadName="businessLicense"
                :params="{
                    fileTypeName: 'businessLicense'
                  }"
                @onSuccess="handleUploadByOther"
                :disabled="isDisabled"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini"
                  :disabled="isDisabled"
                >
                  上传
                </el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.businessLicense"
                  @click="previewFileByOther(form.businessLicense)"
                >预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="附件（安全许可证、企业资质等）" prop="supplierAttachmentList">
              <file-upload
                :disabled="isDisabled"
                :showUploadBtn="!isDisabled"
                v-model="form.supplierAttachmentList"
                :showTip="true"
                :fileSize="50"
                :fileType="['pdf','png','jpg','jpeg']"
                uploadUrl="/epcfile/nonProjectFile/upload"
                :params="{
                  fileTypeName: 'supplier_info_attachment'
                }"
                @preview="previewFileByOther"
                @down="downloadFileByOther"
              >
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType==='wzlg'">
            <el-form-item label="入库申请单" prop="storageApplicationForm">
              <file-upload-single
                :fileSize="10"
                :showTip="true"
                :autoUpload="false"
                accept=".pdf"
                uploadName="storageApplicationForm"
                :params="{
                    fileTypeName: 'storageApplicationForm'
                  }"
                @onSuccess="handleUploadByOther"
                :disabled="isDisabled"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini"
                  :disabled="isDisabled"
                >
                  上传
                </el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.storageApplicationForm"
                  @click="previewFileByOther(form.storageApplicationForm)"
                >
                  预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" v-if="versionType==='wzlg'">
            <el-form-item label="龙港是否有分公司" prop="lgWhetherBranchCompany">
              <el-radio-group v-model="form.lgWhetherBranchCompany" :disabled="isDisabled">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card
        v-if="isCertificate==='1'"
        header="资格证书"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <template v-for="(item,index) in form.supplierCertificateList">
          <el-row :gutter="20" :key="index">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label="证书名称"
                :prop="'supplierCertificateList.'+index+'.certificateName'"
                :rules="rules.certificateName"
              >
                <el-input v-model.trim="item.certificateName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label="证书到期时间"
                :prop="'supplierCertificateList.'+index+'.certificateExpirationTime'"
                :rules="rules.certificateExpirationTime"
              >
                <el-date-picker
                  class="block"
                  style="width: 100%"
                  v-model="item.certificateExpirationTime"
                  type="datetime"
                  placeholder="请选择"
                  value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label="证书等级"
                :prop="'supplierCertificateList.'+index+'.certificateGrade'"
                :rules="rules.certificateGrade"
              >
                <el-input v-model.trim="item.certificateGrade" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label="证书文件"
                :prop="'supplierCertificateList.'+index+'.certificateFile'"
                :rules="rules.certificateFile"
              >
                <file-upload-single
                  :fileSize="10"
                  :showTip="true"
                  :autoUpload="false"
                  accept=".png, .jpg, .jpeg, .pdf"
                  uploadName="certificateFile"
                  :params="{
                    fileTypeName: 'certificateFile',
                    prop:'supplierCertificateList.'+index+'.certificateFile',
                    index: index
                  }"
                  @onSuccess="handleUploadCertificate"
                  :disabled="isDisabled"
                >
                  <el-button
                    slot="upload-btn"
                    type="primary"
                    size="mini"
                    :disabled="isDisabled"
                  >
                    上传
                  </el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="item.certificateFile"
                    @click="previewFileByOther(item.certificateFile)"
                  >
                    预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-form-item
                label=" "
              >
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  type="danger"
                  plain
                  @click="removeDetail(index)"
                >删除
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider :key="index"></el-divider>
        </template>
        <div class="text-center pb20">
          <el-button
            type="primary"
            plain
            size="mini"
            icon="el-icon-plus"
            :disabled="isDisabled"
            @click="handleAdd">
            添加
          </el-button>
        </div>
      </el-card>

      <el-card
        header="法定代表人信息"
        shadow="never"
        :body-style="{'padding': '15px 10px 0px 10px'}"
        class="card-box"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="法定代表人姓名" prop="certificateName">
              <el-input
                v-model.trim="form.certificateName"
                placeholder="请输入"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="法定代表人证件类型" prop="certificate">
              <el-select
                v-model="form.certificate"
                placeholder="请选择"
                class="block"
                :disabled="isDisabled"
              >
                <el-option
                  v-for="dict in dict.type.id_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="法定代表人证件号码" prop="certificateCode">
              <el-input
                :placeholder="form.certificateCode | formatIdCard(5,3,'请输入')"
                disabled
                v-if="isDisabled"
              />
              <el-input
                v-else
                v-model.trim="form.certificateCode"
                placeholder="请输入"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              label="经营负责人手机号码（法定代表人或实际控制人，不得使用其他联系人号码）"
              prop="contactNumber"
            >
              <el-input
                v-model.trim="form.contactNumber"
                placeholder="请输入"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              label="法定代表人身份证扫描件（请将身份证正反面合并为同一份PDF或图片后上传）"
              prop="legalRepresentativeIdentityCertificate"
            >
              <file-upload-single
                :fileSize="10"
                :showTip="true"
                :autoUpload="false"
                accept=".png, .jpg, .jpeg, .pdf"
                uploadName="legalRepresentativeIdentityCertificate"
                :params="{
                    fileTypeName: 'legalRepresentativeIdentityCertificate'
                  }"
                @onSuccess="handleUploadByOther"
                :disabled="isDisabled"
              >
                <el-button
                  slot="upload-btn"
                  type="primary"
                  size="mini"
                  :disabled="isDisabled"
                >
                  上传
                </el-button>
                <el-button
                  slot="upload-right"
                  type="success"
                  size="mini"
                  v-if="form.legalRepresentativeIdentityCertificate"
                  @click="previewFileByOther(form.legalRepresentativeIdentityCertificate)"
                >
                  预览
                </el-button>
              </file-upload-single>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterName')"
              prop="infoReporterName"
            >
              <el-input v-model.trim="form.infoReporterName" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'infoReporter')"
              prop="infoReporter"
            >
              <el-select
                v-model="form.infoReporter"
                placeholder="请选择"
                class="block"
                :disabled="isDisabled"
              >
                <el-option
                  v-for="dict in dict.type.id_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterCode')"
              prop="infoReporterCode"
            >
              <el-input v-model.trim="form.infoReporterCode" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item
              :label="selectDictValue(dict.type.sys_dict_translate,'infoReporterContactNumber')"
              prop="infoReporterContactNumber"
            >
              <el-input v-model.trim="form.infoReporterContactNumber" placeholder="请输入" :disabled="isDisabled"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div class="text-center mt30" v-hasPermi="['supplier:supplierInfo:edit']">
        <el-button type="primary" @click="isDisabled=false" v-if="isDisabled">修改</el-button>
        <el-button type="primary" @click="submitForm" v-if="!isDisabled">保存</el-button>
        <el-button type="primary" @click="applySuspension" v-if="versionType==='wzlg'">申请停用账号</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import cityList from '@/assets/data/cityList'
import {
  saveSupplierInfo,
  querySupplierInfo,
  updateSupplierInfo,
  supplierApplySuspension
} from '@/api/supplier/supplierInfo'
import reg from '@/utils/reg'
import { mapGetters } from 'vuex'
import { fileUploadByOther } from '@/api/file';
import {
  insertOrganize,
  listOrganize,
  updateInsertOrganize
} from '@/api/system/organize';

export default {
  name: 'SupplierInfo',
  dicts: ['supplier_type', 'organize_audit_status', 'sys_dict_translate', 'supplier_bid_type', 'id_type'],
  data() {
    return {
      cityList,
      licNumber: null,
      form: {
        id: null,
        bidderName: null,
        licNumber: null,
        contactNumber: null,
        regionCode: null,
        contactAddress: null,
        email: null,
        openingBank: null,
        basicAccount: null,
        unitNature: null,
        unitNatureList: [],
        businessLicense: null,
        storageApplicationForm: null,
        legalRepresentativeIdentityCertificate: null,
        microEnterprises: null,
        certificate: null,
        certificateCode: null,
        certificateName: null,
        bidType: null,
        bidTypeList: [],
        registeredCapital: null,
        operatingPeriod: null,
        dateOfEstablishment: null,
        registrationAndAuthority: null,
        lgWhetherBranchCompany: null,
        whetherMicroEnterprises: null,
        infoReporterName: null,
        infoReporter: null,
        infoReporterCode: null,
        infoReporterContactNumber: null,
        supplierAttachmentList: [],
        supplierCertificateList: []
      },
      rules: {
        bidderName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '公司名称长度必须介于 2 和 50 之间', trigger: 'blur' },
          { pattern: reg.character, message: '不能包含特殊字符*:?"<>|\\/', trigger: 'blur' }
        ],
        licNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.unifiedCreditCode, message: '格式不对', trigger: 'blur' }
        ],
        contactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        regionCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        contactAddress: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        openingBank: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        basicAccount: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.bankCardNumber, message: '格式不对', trigger: 'blur' }
        ],
        unitNatureList: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        businessLicense: [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        storageApplicationForm: [
          { required: false, message: '请上传', trigger: 'change' }
        ],
        legalRepresentativeIdentityCertificate: [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        certificate: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        certificateCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        certificateName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        bidTypeList: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        registeredCapital: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        operatingPeriod: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        dateOfEstablishment: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        registrationAndAuthority: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        whetherMicroEnterprises: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        lgWhetherBranchCompany: [
          { required: false, message: '请选择', trigger: 'change' }
        ],
        infoReporterName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, max: 50, message: '长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        infoReporter: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        infoReporterCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        infoReporterContactNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '格式不对', trigger: 'blur' }
        ],
        certificateExpirationTime: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        certificateGrade: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        certificateFile: [
          { required: true, message: '请上传', trigger: 'change' }
        ]
      },
      isPerfect: false, // 是否完善企业信息
      isDisabled: false,
      loading: false,
      organizeVoList: [],
      organizeList: [],
      bidTypeNum: 0,
      isCertificate: null // 1 有证书功能
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('select.bidType.num')
      .then(response => {
        this.bidTypeNum = Number(response.data);
      })
    this.getConfigKey('supplier.certificate.function')
      .then(response => {
        this.isCertificate = response.data;
      })
    this.querySupplierInfo()
  },
  methods: {
    handleAdd() {
      this.form.supplierCertificateList.push({
        certificateName: null,
        certificateExpirationTime: null,
        certificateGrade: null,
        certificateFile: null
      })
    },
    removeDetail(index) {
      this.form.supplierCertificateList.splice(index, 1);
    },
    async handleUploadCertificate(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form.supplierCertificateList[params.index], params.fileTypeName, data.url)
        this.$refs.form.clearValidate(params.prop);
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    applySuspension() {
      this.$modal.confirm('是否确认申请停用账号？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            await supplierApplySuspension()
            await this.querySupplierInfo();
            this.$modal.msgSuccess('提交成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    handleApply(row) {
      if (!this.isPerfect) {
        this.$modal.msgWarning('请先完善企业信息');
        return
      }
      this.$modal.confirm('是否确认申请加入名称为"' + row.orgName + '"的组织？')
        .then(async () => {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            if (row.orgAuditStatus === '1') {
              await updateInsertOrganize([{
                orgAuditStatus: '0',
                orgCode: row.orgCode,
                userId: this.userId
              }])
            } else {
              await insertOrganize([{
                orgAuditStatus: '0',
                orgCode: row.orgCode,
                userId: this.userId
              }])
            }
            await this.querySupplierInfo()
            this.$modal.msgSuccess('申请成功');
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }).catch(() => {
        })
    },
    async querySupplierInfo() {
      try {
        this.loading = true;
        let { data } = await querySupplierInfo(this.userId);
        this.isPerfect = data ? !!data.id : false;
        if (!data) {
          this.loading = false;
          return
        }
        let res = await listOrganize({ status: 0 });
        this.licNumber = data.licNumber;
        this.isDisabled = !!data.id;
        for (let key in this.form) {
          this.form[key] = data[key]
        }
        this.form.bidTypeList = this.form.bidType ? this.form.bidType.split(',') : [];
        this.form.unitNatureList = this.form.unitNature ? this.form.unitNature.split(',') : [];
        this.organizeVoList = res.data || [];
        let list = data.userOrgVoList || [];
        this.organizeVoList.forEach(item => {
          let obj = list.find(v => v.orgCode === item.orgCode);
          this.$set(item, 'orgAuditStatus', obj ? obj.orgAuditStatus : null);
          this.$set(item, 'reason', obj ? obj.reason : null);
        })
        console.log(this.organizeVoList)
        this.loading = false
      } catch (e) {
        this.loading = false
        throw new Error(e);
      }
    },
    previewFileByOther(fileKey) {
      this.$download.previewFileByOther(fileKey)
    },
    downloadFileByOther(fileKey) {
      this.$download.downloadFileByOther(fileKey)
    },
    async handleUploadByOther(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        let { data } = await fileUploadByOther(params);
        this.$set(this.form, params.uploadName, data.url)
        this.$refs.form.clearValidate(params.uploadName)
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    submitForm() {
      console.log(this.form);
      this.$refs['form'].validate(async valid => {
        if (valid) {
          try {
            let list = this.dict.type.supplier_type.filter(v => this.form.bidTypeList.includes(v.value));
            if (list.some(v => !!v.raw.remark)) {
              this.$modal.msgWarning(`请上传相关资质证书`);
              return
            }
            this.$modal.loading('数据提交中，请稍候...')
            this.form.bidType = this.form.bidTypeList.join(',');
            this.form.unitNature = this.form.unitNatureList.join(',');
            if (!this.form.id) {
              await saveSupplierInfo(this.form)
            } else {
              await updateSupplierInfo(this.form)
            }
            this.isDisabled = true;
            this.$modal.closeLoading()
            this.$modal.msgSuccess(`提交成功，等待审核`)
            await this.querySupplierInfo()
          } catch (e) {
            this.$modal.closeLoading()
            throw new Error(e);
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
