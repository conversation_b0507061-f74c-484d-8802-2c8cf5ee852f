<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="70px">
      <el-form-item :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" prop="entity.buyItemName">
        <el-input
          v-model="queryParams.entity.buyItemName"
          :placeholder="selectDictValue(dict.type.sys_dict_translate,'buyItemName')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告名称" prop="entity.bulletinName">
        <el-input
          v-model="queryParams.entity.bulletinName"
          placeholder="请输入公告名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border :span-method="objectSpanMethod">
      <el-table-column label="序号" prop="index" align="center" width="50"></el-table-column>
      <el-table-column :label="selectDictValue(dict.type.sys_dict_translate,'buyItemName')" align="center" prop="buyItemName"></el-table-column>
      <el-table-column label="公告名称" align="center" prop="bulletinName"></el-table-column>
      <el-table-column label="发布时间" align="center" prop="createTime">
        <template v-slot:default="{row}">
          <span>
            {{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="标段名称" align="center" prop="subpackageName"></el-table-column>
      <el-table-column label="报名是否截止" align="center" prop="whetherDeadline">
        <template v-slot:default="{row}">
          <span :style="{'color': row.whetherDeadline?theme:'#ed5565'}">
            {{ row.whetherSignEnd ? '进行中' : '已截止' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="是否报名" align="center" prop="whetherDeadline">
        <template v-slot:default="{row}">
          <span :style="{'color': row.whetherSign?theme:'#ed5565'}">
            {{ row.whetherSign ? '已报名' : '未报名' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot:default="{row}">
          <el-button type="text" size="mini" @click="toBulletinDetail(row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { inviteSupplierNotice } from '@/api/supplier/inviteBulletin';
import { mapGetters } from 'vuex';

export default {
  name: 'Index',
  dicts: ['sys_dict_translate'],
  data() {
    return {
      queryParams: {
        entity: {
          bulletinName: null,
          buyItemName: null
        },
        pageNum: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      spanArr: [],
      pos: 0
    }
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  methods: {
    toBulletinDetail(row) {
      this.$router.push({
        name: 'BulletinDetail',
        params: { bulletinId: row.bulletinId, bulletinType: row.bulletinType }
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    async getList() {
      try {
        let { rows, total } = await inviteSupplierNotice(this.queryParams);
        let list = rows || [];
        this.tableData = [];
        list.forEach((item, index) => {
          let signTimeVoList = item.signTimeVoList || [];
          signTimeVoList.forEach(v => {
            this.tableData.push({
              ...item,
              ...v,
              index: index + 1
            })
          })
        })
        this.total = total;
        this.getSpanArr(this.tableData);
      } catch (e) {
        throw new Error(e);
      }
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let columnList = [4, 5, 6];
      if (!columnList.some(item => item === columnIndex)) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return { // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      this.pos = 0;
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].bulletinId === data[i - 1].bulletinId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
