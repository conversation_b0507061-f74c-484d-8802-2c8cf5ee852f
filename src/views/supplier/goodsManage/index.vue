<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="商品名称" prop="entity.goodsName">
        <el-input
          v-model="queryParams.entity.goodsName"
          placeholder="请输入商品名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品分类" prop="entity.goodsCategory">
        <el-select
          v-model="queryParams.entity.goodsCategory"
          placeholder="请选择商品分类"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.goods_key_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态" prop="entity.goodsStatus">
        <el-select
          v-model="queryParams.entity.goodsStatus"
          placeholder="请选择商品状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.goods_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['supplier:goods:operate']"
        >上架
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border>
      <el-table-column label="序号" align="center" type="index" width="55"/>
      <el-table-column align="center" label="商品图片" prop="mainImgUrl" width="80">
        <template v-slot:default="{row}">
          <el-image
            style="width: 50px; height: 50px;vertical-align: middle;"
            :src="row.mainImgUrl"
            :preview-src-list="[row.mainImgUrl]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="goodsName"/>
      <el-table-column label="商品单价(元)" align="center" prop="goodsUnitPrice">
        <template v-slot:default="{row}">
          {{ row.goodsUnitPrice | formatMoney(2) }}
        </template>
      </el-table-column>
      <el-table-column label="商品分类" align="center" prop="goodsCategory">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.goods_key_group" :value="row.goodsCategory"/>
        </template>
      </el-table-column>
      <!--      <el-table-column label="商品详情" align="center">-->
      <!--        <template v-slot:default="{row}">-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-view"-->
      <!--            @click="handleDetail(row)"-->
      <!--          >查看-->
      <!--          </el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="创建时间" align="center" prop="createAt">
        <template v-slot:default="{row}">
          <span>{{ row.createAt | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" prop="goodsStatus">
        <template v-slot:default="{row}">
          <dict-tag :options="dict.type.goods_status" :value="row.goodsStatus"/>
          <div class="td-tip" v-if="row.goodsStatus==2">
            理由：{{ row.auditRemark || '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot:default="{row}">
          <el-button
            v-if="checkPermi(['supplier:goods:operate'])"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          >修改
          </el-button>
          <el-button
            v-if="(row.goodsStatus==2||row.goodsStatus==3)&&checkPermi(['supplier:goods:operate'])"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleGoodsUpAndDown(0,row)"
          >上架
          </el-button>
          <el-button
            v-if="row.goodsStatus==1&&checkPermi(['supplier:goods:operate'])"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleGoodsUpAndDown(3,row)"
          >下架
          </el-button>
          <el-button
            v-if="checkPermi(['supplier:goods:operate'])"
            class="btn-text-danger"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      top="5vh"
      :title="title"
      :visible.sync="open"
      width="90%"
      custom-class="maxW1100"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="商品分类" prop="goodsCategory">
              <el-select v-model="form.goodsCategory" placeholder="请选择商品分类" clearable @change="changeKeyGroup" class="block">
                <el-option
                  v-for="dict in dict.type.goods_key_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-for="(item,index) in form.goodsDetail">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" :key="index">
              <el-form-item
                :label="item.keyName"
                :prop="'goodsDetail.'+index+'.value'"
                :rules="[
                   { required: item.required, message: '请输入', trigger:  ['blur', 'change'] },
                   { pattern: eval2(item.regex), message: item.remark||'格式不正确', trigger: ['blur', 'change'] }
                  ]"
              >
                <el-input v-model="item.value" placeholder="请输入" v-if="item.keyType!=='file'"/>
                <file-upload-single
                  v-else
                  :showUploadBtn="checkPermi(['supplier:goods:operate'])"
                  :fileSize="10"
                  accept=".pdf"
                  :uploadName="'goodsDetail.'+index+'.value'"
                  :autoUpload="false"
                  :params="{
                    index: index
                  }"
                  @onSuccess="handleUpload"
                >
                  <el-button slot="upload-btn" type="primary" size="mini">上传</el-button>
                  <el-button
                    slot="upload-right"
                    type="success"
                    size="mini"
                    v-if="item.value"
                    @click="previewFileByGoods(item.value)"
                  >预览
                  </el-button>
                </file-upload-single>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="商品主图" prop="mainImgList">
              <image-upload-select
                v-model="form.mainImgList"
                @change="uploadFile"
                @preview="previewGoodsImg"
              >
              </image-upload-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="商品详情图" prop="detailImgList">
              <image-upload-select
                v-model="form.detailImgList"
                @change="uploadFile"
                @preview="previewGoodsImg"
              >
              </image-upload-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { addGoods, updateGoods, listGoods, upAndDownGoods, delGoods } from '@/api/supplier/goodsManage';
import { fileDownByGoods } from '@/api/file';
import { mapGetters } from 'vuex';
import { checkReg, eval2, isString } from '@/utils/validate'
import { allQuoteField } from '@/api/system/quoteField'
import { Base64 } from 'js-base64'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'GoodsManage',
  dicts: ['goods_status', 'goods_key_group'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entity: {
          goodsName: null,
          goodsCategory: null,
          goodsStatus: null
        }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        goodsCategory: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        mainImgList: [
          { required: true, message: '请上传', trigger: ['change', 'blur'] }
        ],
        detailImgList: [
          { required: true, message: '请上传', trigger: ['change', 'blur'] }
        ]
      },
      allHeads: []
    };
  },
  created() {
    this.reset();
    this.getAllKeys();
    this.getList();
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  methods: {
    checkPermi,
    eval2,
    checkReg,
    previewFileByGoods(data) {
      if (isString(data)) {
        this.$download.previewFileByGoods(data)
      } else {
        this.$pdfViewDialog({ data: data, type: 'blob' })
      }
    },
    async handleUpload(params) {
      try {
        this.$modal.loading('正在上传文件，请稍候...');
        this.$set(this.form.goodsDetail[params.index], 'value', params.file);
        this.$refs.form.clearValidate(params.uploadName);
        this.$modal.msgSuccess('上传成功');
        this.$modal.closeLoading();
      } catch (e) {
        this.$modal.closeLoading();
        throw new Error(e);
      }
    },
    /** 查看详情 */
    handleDetail(row) {

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await delGoods({ goodsId: row.goodsId })
          this.$modal.msgSuccess('操作成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    handleGoodsUpAndDown(goodsStatus, row) {
      this.$modal.confirm(`是否确认${goodsStatus === 3 ? '下架' : '上架'}该商品？`).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await upAndDownGoods({
            goodsStatus,
            goodsId: row.goodsId
          })
          this.$modal.msgSuccess('操作成功');
          await this.getList();
          this.$modal.closeLoading();
        } catch (e) {
          this.$modal.closeLoading();
          throw new Error(e);
        }
      }).catch(() => {
      });
    },
    async getAllKeys() {
      try {
        let { data } = await allQuoteField({
          disabled: false,
          groupType: '2'
        })
        this.allHeads = data || [];
      } catch (e) {
        throw new Error(e);
      }
    },
    changeKeyGroup(val) {
      let list = this.allHeads.filter(item => item.keyGroups.includes(val));
      this.form.goodsDetail = list.map(item => {
        return {
          ...item,
          value: null
        }
      })
    },
    uploadFile({ fileList }) {
      console.log('fileList', fileList);
    },
    previewGoodsImg(file) {
      this.$imgView(file.url)
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading('数据提交中，请稍候...');
            let goodsPicList = [];
            let mainImgList = this.form.mainImgList.map((v, index) => {
              return {
                picFile: v.file,
                picOrder: index,
                picType: 0
              }
            })
            let detailImgList = this.form.detailImgList.map((v, index) => {
              return {
                picFile: v.file,
                picOrder: index,
                picType: 1
              }
            })
            let goodsDetail = []
            this.form.goodsDetail.forEach(item => {
              goodsDetail.push({
                ...item,
                regex: Base64.toBase64(item.regex)
              })
            })
            let params = {
              goodsId: this.form.goodsId,
              goodsCategory: this.form.goodsCategory,
              goodsDetail: goodsDetail,
              goodsPicList: goodsPicList.concat(mainImgList).concat(detailImgList)
            }
            console.log(params)
            if (params.goodsId) {
              await updateGoods(params);
              this.$modal.msgSuccess('修改成功');
            } else {
              await addGoods(params);
              this.$modal.msgSuccess('新增成功');
            }
            this.open = false;
            await this.getList();
            this.$modal.closeLoading();
          } catch (e) {
            this.$modal.closeLoading();
            throw new Error(e);
          }
        }
      });
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      for (let key in this.form) {
        this.form[key] = row[key];
      }

      let goodsPicList = row.goodsPicList;
      let urlList = await Promise.all(goodsPicList.map(v => this.getImgUrl(v.picFile)));
      goodsPicList.forEach((item, index) => {
        this.$set(item, 'url', urlList[index]);
      })
      this.form.mainImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 0), 'picOrder', 'asc').map(v => {
        return {
          file: v.picFile,
          name: v.picFile,
          url: v.url
        }
      });
      this.form.detailImgList = this._.orderBy(goodsPicList.filter(v => v.picType === 1), 'picOrder', 'asc').map(v => {
        return {
          file: v.picFile,
          name: v.picFile,
          url: v.url
        }
      });
      console.log(this.form)
      this.open = true;
      this.title = '修改商品';
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加商品';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        goodsId: null,
        goodsCategory: null,
        goodsDetail: [],
        mainImgList: [],
        detailImgList: []
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 查询列表 */
    async getList() {
      try {
        this.loading = true;
        let { rows, total } = await listGoods(this.queryParams);
        this.tableData = rows || [];
        this.total = total;
        let mainList = this.tableData.map(item => {
          let mainImg = item.goodsPicList.find(v => v.picType === 0);
          return this.getImgUrl(mainImg.picFile)
        })
        let urlList = await Promise.all(mainList);
        this.tableData.forEach((item, index) => {
          this.$set(item, 'mainImgUrl', urlList[index]);
        })
        this.loading = false;
      } catch (e) {
        this.loading = false;
        throw new Error(e);
      }
    },
    // 获取图片url
    async getImgUrl(fileKey) {
      try {
        let { data } = await fileDownByGoods(fileKey)
        const blob = new Blob([data], { 'type': data.type });
        return URL.createObjectURL(blob);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
