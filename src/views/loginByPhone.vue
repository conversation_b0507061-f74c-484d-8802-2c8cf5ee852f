<template>
  <div>
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-sms">

      <el-form-item prop="username">
        <el-input
          v-model.trim="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="手机号码"
        >
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
        </el-input>
      </el-form-item>

      <el-form-item prop="mobileCode">
        <el-input
          v-model.trim="loginForm.mobileCode"
          auto-complete="off"
          placeholder="短信验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
        </el-input>
        <div class="login-code">
          <el-button
            type="primary"
            @click="getSmsCode"
            :disabled="!isVerifyPhone||!isClickCode"
            class="code-btn"
          >{{ this.codeBtnValue }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: left;">
          <router-link class="link-type" :to="'/home'">返回首页</router-link>
        </div>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import { getSmsCode } from '@/api/login';
import reg from '@/utils/reg'

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        mobileCode: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '请输入您的账号' }
        ],
        mobileCode: [{ required: true, trigger: 'change', message: '请输入短信验证码' }]
      },
      loading: false,
      // 注册开关
      register: true,
      redirect: undefined,
      loginType: 'SMS',
      codeBtnValue: '获取验证码',
      countdown: 60,
      isClickCode: true,
      timer: null
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  computed: {
    isVerifyPhone() {
      return reg.cellphone.test(this.loginForm.username)
    }
  },
  created() {
    this.reset();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    async getSmsCode() {
      try {
        await getSmsCode({ mobile: this.loginForm.username, smsType: 'SMS_LOGIN' });
        this.$modal.msgSuccess('验证码发送成功')
        this.countdown = 60;
        this.intervalFun();
        this.timer = setInterval(() => {
          if (this.countdown === 0) {
            this.codeBtnValue = '获取验证码';
            this.isClickCode = true;
            clearInterval(this.timer);
          } else {
            this.intervalFun();
          }
        }, 1000);
      } catch (e) {
        throw new Error(e);
      }
    },
    intervalFun() {
      this.codeBtnValue = '重新发送(' + this.countdown + ')';
      this.isClickCode = false;
      this.countdown--;
    },
    reset() {
      this.loginForm = {
        username: '',
        mobileCode: ''
      };
      this.resetForm('form');
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          let { username, mobileCode } = this.loginForm;
          this.$store.dispatch('Login', { username, mobileCode, loginType: this.loginType }).then(() => {
            this.loading = false;
            this.$router.push({ path: this.redirect || '/index' }).catch(() => {
            });
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login-sms {

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
</style>
