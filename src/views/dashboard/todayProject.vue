<template>
  <el-card class="mb20">
    <div slot="header" class="clearfix">
      <span><i class="el-icon-s-grid text-success mr5"></i>今日开标项目</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="toProjectList">全部项目</el-button>
    </div>
    <ul class="project-ul">
      <li v-for="(item, index) in list" :key="index">
        <div class="project-name over-ellipsis pointer hover">
          <el-tag type="info" size="small" class="mr5">{{ purchaseMethodFormat(item.purchaseMethodCode) }}</el-tag>
          <strong @click="handleDetail(item)">{{ item.buyItemName }}</strong>
        </div>
        <div class="project-info">
          <span class="mr10" v-if="item.innerCode">项目编号：{{ item.innerCode }}</span>
          <span>{{ meetingTimeLabel(item.purchaseMethodCode) }}：{{ item.meetingTime | formatTime('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
        <el-divider></el-divider>
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-card>
</template>

<script>

import { getBidOpenToday } from '@/api/workplace'
import { queryPurchaseMethod } from '@/api/purchaser/projectList'
import { mapGetters } from 'vuex'

export default {
  name: 'TodayProject',
  data() {
    return {
      purchase_method: [],
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5
      },
      total: 0
    }
  },
  created() {
    this.getPurchaseMethod();
    this.getList();
  },
  computed: {
    ...mapGetters([
      'getPurchaseDict'
    ]),
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  methods: {
    meetingTimeLabel(purchaseMethodCode) {
      return this.getPurchaseDict(purchaseMethodCode, 'meetingTime')
    },
    purchaseMethodFormat(purchaseMethodCode) {
      return this.selectDictLabel(this.purchase_method, purchaseMethodCode);
    },
    async handleDetail(item) {
      if (!this.$auth.hasPermi('project:process:query')) {
        this.$modal.msgWarning('当前操作没有权限');
        return
      }
      await this.$store.dispatch('process/setBuyItemName', item.buyItemName)
      await this.$store.dispatch('process/setBuyItemCode', item.buyItemCode)
      await this.$store.dispatch('process/setPurchaseMethodCode', item.purchaseMethodCode)
      await this.$store.dispatch('process/setCreateYearMonth', this.formatTime(item.createAt, 'YYYYMM'))
      await this.$store.dispatch('process/setOrgCode', item.orgCode);
      await this.$store.dispatch('process/setProjectDeptId', item.deptId);
      await this.$store.dispatch('process/setBidType', item.bidType);
      await this.$store.dispatch('process/setFilterStr', { versionType: this.versionType, ...item });
      await this.$store.dispatch('process/setSubpackageCode', item.subpackageCode);
      await this.$store.dispatch('process/setBulletinList', { belongRole: '1', buyItemCode: item.buyItemCode })
      let res = await this.$store.dispatch('process/setProcessNodes', {
        buyItemCode: item.buyItemCode,
        subpackageCode: item.subpackageCode,
        belongRole: '1'
      })
      if (res.length === 0) {
        this.$modal.msgSuccess('未查询到功能配置');
        return
      }
      await this.$router.push({ path: res[0].path })
    },
    toProjectList() {
      this.$router.push({ name: 'ProjectList' })
    },
    async getPurchaseMethod() {
      try {
        let { data } = await queryPurchaseMethod()
        let list = data.methodKVList || [];
        this.purchase_method = list.map(item => {
          return {
            label: item.purchaseMethodName,
            value: item.purchaseMethodCode,
            raw: item
          }
        });
      } catch (e) {
        throw new Error(e);
      }
    },
    async getList() {
      try {
        let { rows, total } = await getBidOpenToday(this.queryParams);
        this.list = rows || [];
        this.total = total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.project-ul {
  font-size: 14px;
  line-height: 1.5;
  color: #515a6e;

  .project-name {
    font-size: 14px;
    word-break: break-word;
    margin-bottom: 10px;
  }

  .project-info {
    font-size: 12px;
    color: #808695;
    display: flex;
    flex-wrap: wrap;
  }
}

</style>
