<template>
  <el-card class="mb20">
    <div slot="header" class="clearfix">
      <span><i class="el-icon-s-claim text-warning mr5"></i>待办事项</span>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="我的审批" name="first">
        <my-approval/>
      </el-tab-pane>
      <el-tab-pane label="供应商企业信息审核" name="second">
        <my-supplier-audit/>
      </el-tab-pane>
      <el-tab-pane label="供应商报名审核" name="third">
        <my-sign-up-audit/>
      </el-tab-pane>
      <el-tab-pane label="供应商评论" name="fourth">
        <my-supplier-comment/>
      </el-tab-pane>
    </el-tabs>

  </el-card>
</template>

<script>
import myApproval from '@/views/dashboard/myApproval.vue'
import mySupplierAudit from '@/views/dashboard/mySupplierAudit.vue'
import mySignUpAudit from '@/views/dashboard/mySignUpAudit.vue'
import mySupplierComment from '@/views/dashboard/mySupplierComment.vue'

export default {
  name: 'ToDoList',
  components: {
    myApproval,
    mySupplierAudit,
    mySignUpAudit,
    mySupplierComment
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
