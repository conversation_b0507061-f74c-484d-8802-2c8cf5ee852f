<template>
  <div>
    <ul class="to-do-ul">
      <li v-for="(item, index) in list" :key="index">
        <div class="to-do-content over-ellipsis">
          <span class="pointer hover" @click="handleDetail(item)">{{ item.nickName }}企业信息审核</span>
        </div>
        <div class="to-do-describe">
          <span>
            时间：{{ item.createTime | formatTime('YYYY-MM-DD HH:mm') }}
          </span>
        </div>

        <el-divider></el-divider>
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getMySupplierAudit } from '@/api/workplace'

export default {
  name: 'MySupplierAudit',
  props: {
    status: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5
      },
      total: 0,
      orgCode: null
    }
  },
  created() {
    this.orgCode = this.$cache.local.get('loginOrgCode');
    this.getList();
  },
  methods: {
    handleDetail(row) {
      this.$router.push({ name: 'SupplierInfoAudit', params: { supplierId: row.userId, orgCode: this.orgCode }})
    },
    async getList() {
      try {
        let { rows, total } = await getMySupplierAudit({ ...this.queryParams, status: this.status });
        this.list = rows || [];
        this.total = total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.to-do-ul {
  font-size: 14px;
  line-height: 1.5;
  color: #515a6e;

  .to-do-content {
    margin-bottom: 5px;
  }

  .to-do-describe {
    font-size: 12px;
    color: #808695;
    display: flex;
    justify-content: space-between;
  }
}

</style>
