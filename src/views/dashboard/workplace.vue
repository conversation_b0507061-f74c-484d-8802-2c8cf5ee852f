<template>
  <div class="app-container dashboard-workplace">

    <el-card class="mb20">
      <div slot="header" class="clearfix">
        <span><i class="el-icon-s-platform text-primary mr5"></i>工作台</span>
      </div>

      <div class="workplace-header">
        <div class="workplace-header-l">
          <h3>{{ timeText }}好，{{ nickName }}，开始您一天的工作吧！</h3>
          <p>{{ currentTime }}</p>
        </div>
        <div class="workplace-header-r">
          <el-statistic style="width: auto;">
            <template slot="title">
                <span class="fontSize16">
                  <i class="el-icon-s-grid text-success"></i>
                  项目数
                </span>
            </template>
            <template slot="formatter">
              <el-tooltip effect="dark" :content="'进行中：'+countObj.bidCount+' / 总计：'+countObj.bidTotalCount" placement="top">
                <span>{{ countObj.bidCount }} / {{ countObj.bidTotalCount }}</span>
              </el-tooltip>
            </template>
          </el-statistic>
          <el-statistic style="width: auto;" class="ml30">
            <template slot="title">
                <span class="fontSize16">
                  <i class="el-icon-message-solid text-danger"></i>
                  消息
                </span>
            </template>
            <template slot="formatter">
              <el-tooltip effect="dark" :content="'未读：'+countObj.msgCount+' / 总计：'+countObj.msgTotalCount" placement="top">
                <span>{{ countObj.msgCount }} / {{ countObj.msgTotalCount }}</span>
              </el-tooltip>
            </template>
          </el-statistic>
          <el-statistic style="width: auto;" class="ml30">
            <template slot="title">
                <span class="fontSize16">
                  <i class="el-icon-s-claim text-warning"></i>
                  待办项
                </span>
            </template>
            <template slot="formatter">
              <el-tooltip effect="dark" :content="'待办：'+countObj.todoCount+' / 总计：'+countObj.totalCount" placement="top">
                <span>{{ countObj.todoCount }} / {{ countObj.totalCount }}</span>
              </el-tooltip>
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :lg="12">
        <today-project/>
        <completed-matters/>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <to-do-list/>
        <message/>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import todayProject from '@/views/dashboard/todayProject.vue'
import message from '@/views/dashboard/message.vue'
import toDoList from '@/views/dashboard/toDoList.vue'
import completedMatters from '@/views/dashboard/completedMatters.vue'
import { mapGetters } from 'vuex'
import { getMyToDoCount } from '@/api/workplace'

export default {
  name: 'Workplace',
  components: {
    todayProject,
    message,
    toDoList,
    completedMatters
  },
  data() {
    return {
      timer: null,
      currentTime: '',
      nowTime: '',
      countObj: {}
    }
  },
  computed: {
    ...mapGetters([
      'nickName'
    ]),
    timeText() {
      const hour = this.$moment(this.nowTime).format('H');
      if (hour < 12) {
        return '上午';
      } else {
        return '下午';
      }
    }
  },
  mounted() {
    this.getNowTime();
    this.getCount();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    async getCount() {
      try {
        let { data } = await getMyToDoCount();
        this.countObj = data || {};
      } catch (e) {
        throw new Error(e)
      }
    },
    async getNowTime() {
      try {
        // const { data } = await getCurrentTime();
        this.nowTime = new Date().getTime();
        this.currentTime = this.$moment(this.nowTime).format('YYYY年MM月DD日 dddd HH:mm:ss');
        this.timer = setInterval(() => {
          this.nowTime += 1000;
          this.currentTime = this.$moment(this.nowTime).format('YYYY年MM月DD日 dddd HH:mm:ss');
        }, 1000);
      } catch (e) {
        throw new Error(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-workplace {
  //background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }

  .workplace-header {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;

    .workplace-header-l {
      flex: auto;

      h3 {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 12px;
        color: #515a6e;
      }

      p {
        color: #808695;
        font-size: 14px;
      }
    }

    .workplace-header-r {
      flex: 1;
      display: flex;
      justify-content: flex-end;
    }
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
