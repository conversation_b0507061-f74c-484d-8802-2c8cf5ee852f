<template>
  <div>
    <ul class="to-do-ul">
      <li v-for="(item, index) in list" :key="index">
        <div class="to-do-content over-ellipsis">
          <span class="pointer hover" @click="handleDetail(item)">
            {{ item.bidderName }}报名【{{ item.buyItemName }}-{{ item.subpackageName }}】审核
          </span>
        </div>
        <div class="to-do-describe">
          <span>
            报名时间：{{ item.createAt | formatTime('YYYY-MM-DD HH:mm') }}
          </span>
        </div>

        <el-divider></el-divider>
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getMySignUpAudit } from '@/api/workplace'

export default {
  name: 'MySignUpAudit',
  props: {
    status: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5
      },
      total: 0
    }
  },
  created() {
    this.getList();
  },
  computed: {
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  methods: {
    async handleDetail(item) {
      if (!this.$auth.hasPermi('project:process:query')) {
        this.$modal.msgWarning('当前操作没有权限');
        return
      }
      await this.$store.dispatch('process/setBuyItemName', item.buyItemName)
      await this.$store.dispatch('process/setBuyItemCode', item.buyItemCode)
      await this.$store.dispatch('process/setPurchaseMethodCode', item.purchaseMethodCode)
      await this.$store.dispatch('process/setCreateYearMonth', this.formatTime(item.createAt, 'YYYYMM'))
      await this.$store.dispatch('process/setOrgCode', item.orgCode);
      await this.$store.dispatch('process/setProjectDeptId', item.deptId);
      await this.$store.dispatch('process/setBidType', item.bidType);
      await this.$store.dispatch('process/setFilterStr', { versionType: this.versionType, ...item });
      await this.$store.dispatch('process/setSubpackageCode', item.subpackageCode);
      await this.$store.dispatch('process/setBulletinList', { belongRole: '1', buyItemCode: item.buyItemCode })
      let res = await this.$store.dispatch('process/setProcessNodes', {
        buyItemCode: item.buyItemCode,
        subpackageCode: item.subpackageCode,
        belongRole: '1'
      })
      if (res.length === 0) {
        this.$modal.msgSuccess('未查询到功能配置');
        return
      }
      await this.$router.push({ path: '/process/step/registrationSupplier' })
    },
    async getList() {
      try {
        let { rows, total } = await getMySignUpAudit({ ...this.queryParams, status: this.status });
        this.list = rows || [];
        this.total = total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.to-do-ul {
  font-size: 14px;
  line-height: 1.5;
  color: #515a6e;

  .to-do-content {
    margin-bottom: 5px;
  }

  .to-do-describe {
    font-size: 12px;
    color: #808695;
    display: flex;
    justify-content: space-between;
  }
}

</style>
