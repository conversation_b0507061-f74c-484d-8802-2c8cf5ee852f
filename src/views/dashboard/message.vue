<template>
  <el-card class="mb20">
    <div slot="header" class="clearfix">
      <span><i class="el-icon-message-solid text-danger mr5"></i>消息</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="oneClickRead([])" v-if="list.length>0">一键已读</el-button>
    </div>
    <ul class="to-do-ul">
      <li v-for="(item, index) in list" :key="index">
        <div class="to-do-content">
          <el-badge is-dot class="msg-badge" :hidden="item.read===1">
            <div class="over-ellipsis">
              <span class="pointer hover" @click="handleDetail(item)">{{ item.msg }}</span>
            </div>
          </el-badge>
        </div>
        <div class="to-do-describe">
           <span>
            来源：{{ item.fromUserName }}
          </span>
          <span>
            时间：{{ item.date | formatTime('YYYY-MM-DD HH:mm:ss') }}
          </span>
        </div>

        <el-divider></el-divider>
      </li>
    </ul>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-card>
</template>

<script>
import { getMyMsg, msgRead } from '@/api/workplace'

export default {
  name: 'Message',
  data() {
    return {
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5
      },
      total: 0
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleDetail(item) {
      let html = `<div>
                        <p style="display: flex;margin-bottom: 3px;">
                          <span style="flex-shrink:0;">【来源】</span>
                          <span style="flex: 1;">${item.fromUserName}</span>
                        </p>
                        <p style="display: flex;margin-bottom: 3px;">
                          <span style="flex-shrink:0;">【时间】</span>
                          <span style="flex: 1;">${this.formatTime(item.date)}</span>
                        </p>
                        <p style="display: flex;">
                          <span style="flex-shrink:0;">【内容】</span>
                          <span style="flex: 1;">${item.msg}</span>
                        </p>
                      </div>`
      this.$alert(html, '', {
        showConfirmButton: false,
        dangerouslyUseHTMLString: true
      }).catch(() => {
        this.oneClickRead([item.id]);
      })
    },
    async oneClickRead(data) {
      try {
        await msgRead(data);
        await this.getList();
      } catch (e) {
        throw new Error(e);
      }
    },
    async getList() {
      try {
        let { rows, total } = await getMyMsg(this.queryParams);
        this.list = rows;
        this.total = total;
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss">
.msg-badge {
  max-width: 95%;

  .el-badge__content.is-fixed {
    right: -3px !important;
    transform: translateY(50%) translateX(100%) !important;
  }
}
</style>
<style lang="scss" scoped>
.to-do-ul {
  font-size: 14px;
  line-height: 1.5;
  color: #515a6e;

  .to-do-content {
    margin-bottom: 5px;
  }

  .to-do-describe {
    font-size: 12px;
    color: #808695;
    display: flex;
    justify-content: space-between;
  }
}

</style>
