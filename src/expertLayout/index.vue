<template>
  <div class="app-container" style="position: relative;">
    <h3 :style="{color: theme, fontStyle:'16px'}" class="text-center over-ellipsis">{{ buyItemName }}</h3>
    <h4 :style="{color: theme}" class="text-center over-ellipsis mt10 fontSize14">{{ subpackageName }}</h4>
    <process-nav
      class="process-nav"
      :stepList="expertProcess"
      @step-click="stepClick"
    >
    </process-nav>
    <section style="position: relative;">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key"/>
      </transition>
    </section>
  </div>
</template>

<script>
import { ProcessNav } from './components'
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  components: {
    ProcessNav
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'theme',
      'expertProcess',
      'buyItemName',
      'subpackageName',
      'subpackageCode'
    ]),
    key() {
      return this.$route.path
    }
  },
  created() {
  },
  methods: {
    stepClick(item) {
      this.$router.push({ path: item.path })
    }
  }
}
</script>

<style lang="scss" scoped>
.process-nav {
  margin: 20px auto 20px;
}
</style>
