<template>
  <div :style="{height:height}">
    <iframe v-if="fileUrl" :src="fileUrl" frameborder="0" border="0" style="width: 100%; height: 100%"></iframe>
  </div>
</template>

<script>
export default {
  name: 'Index',
  props: {
    height: {
      type: String,
      default: '800px'
    },
    src: {
      type: [String, Blob],
      default: null
    },
    type: {
      type: String,
      default: 'url',
      validator: function (value) {
        // 这个值必须匹配下列字符串中的一个
        return ['url', 'blob'].includes(value)
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    fileUrl() {
      return this.getUrl()
    }
  },
  methods: {
    getUrl() {
      let fileUrl = null;
      if (!this.src) {
        fileUrl = null;
      } else {
        if (this.type === 'blob') {
          const blob = new Blob([this.src], { 'type': 'application/pdf' });
          const url = URL.createObjectURL(blob);
          fileUrl = `${process.env.VUE_APP_BASE_URL}pdfjs-legacy/web/viewer.html?file=${encodeURIComponent(url)}`;
        } else {
          fileUrl = `${process.env.VUE_APP_BASE_URL}pdfjs-legacy/web/viewer.html?file=${encodeURIComponent(this.src)}`;
        }
      }
      return fileUrl;
    }
  }
}
</script>

<style lang="scss">

</style>
