<template>
  <div class="upload-file">
    <el-upload
      :disabled="disabled"
      multiple
      action="#"
      :accept="fileType.map(item=>'.'+item).join(',')"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :file-list	="fileList"
      :on-exceed="handleExceed"
      :http-request="uploadSectionFile"
      :show-file-list="false"
      class="upload-file-uploader"
      ref="upload"
    >
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary" :disabled="disabled">选取文件</el-button>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
        <template v-if="fileType&&fileType.length>0"> 格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
        </template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="index" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-tooltip effect="dark" :content="file.name" placement="top">
          <div class="over-ellipsis mr10">
            <i class="el-icon-document"></i>
            {{ file.name }}
          </div>
        </el-tooltip>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="preview(file.url || file.file)" type="primary" v-if="preview">预览</el-link>
          <el-link :underline="false" @click="down(file.url || file.file)" type="primary" v-if="down">下载</el-link>
          <el-link :underline="false" @click="handleDelete(index,file)" type="danger" :disabled="disabled">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>

export default {
  name: 'FileUploadSelect',
  props: {
    value: {},
    // 数量限制
    limit: {
      type: Number,
      default: null
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg', 'pdf']
    fileType: {
      type: Array,
      default: () => []
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
      }
    },
    preview: {
      type: Function
    },
    down: {
      type: Function
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      fileList: this.value || []
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && ((this.fileType && this.fileType.length > 0) || this.fileSize)
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    setCurrentValue(value) {
      this.fileList = value || []
      this.$emit('input', value)
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType && this.fileType.length > 0) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$modal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传回调
    uploadSectionFile(param) {
      try {
        const file = param.file;
        this.uploadList.push({ name: file.name, url: null, file })
        if (this.uploadList.length === this.number) {
          this.fileList = this.fileList.concat(this.uploadList)
          this.uploadList = []
          this.number = 0
          this.$emit('input', this.fileList)
          this.$emit('change', { fileList: this.fileList, params: this.params })
        }
        this.$modal.closeLoading()
      } catch (e) {
        this.number--;
        this.$modal.msgError('上传失败，请重试')
        this.$modal.closeLoading()
      }
    },
    // 删除文件
    handleDelete(index, file) {
      this.fileList.splice(index, 1);
      this.$emit('input', this.fileList)
      this.$emit('change', { fileList: this.fileList, params: this.params })
      this.$emit('remove', file.url || file.file)
    }
    // handleDown(file) {
    //   this.$emit('down', file.url || file.file)
    // },
    // handlePreview(file) {
    //   this.$emit('preview', file.url || file.file)
    // }
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action {
  flex-shrink: 0;

  .el-link {
    margin-right: 10px;
  }
}
</style>
