<template>
  <div>
    <div class="message-icon" @click.stop="showChatDialog">
      <i class="el-icon-s-custom"></i>
    </div>

    <div class="message-wrap" v-el-drag v-if="isChatDialog">
      <el-card :body-style="{'padding': '0 10px 15px 10px'}">
        <div class="message-header">
          <span class="message-title el-drag">智能客服</span>
          <el-button class="ml10" type="text" @click="hideChatDialog">关闭</el-button>
        </div>
        <div class="message-box">
          <div class="chat-box">
            <div class="chat-main" ref="chatMain">
              <ul class="chat-ul overflow" v-if="chatList.length>0">
                <li class="chat-li" v-for="(item,index) in chatList" :key="index">
                  <div class="chat-time" v-if="isShowTime(item, index)">
                    {{ item.time | formatTime('YYYY年MM月DD日 HH:mm') }}
                  </div>
                  <div class="chat-left" v-if="item.from===1">
                    <div v-if="!item.isFile" class="chat-text chat-left-text">{{ item.message }}</div>
                    <el-image
                      v-else
                      style="width: 100px; height: 100px"
                      :src="item.message"
                      :preview-src-list="[item.message]"
                      fit="fill"
                    >
                    </el-image>
                  </div>
                  <div class="chat-right" v-else>
                    <div v-if="!item.isFile" class="chat-text chat-right-text">{{ item.message }}</div>
                    <el-image
                      v-else
                      style="width: 100px; height: 100px"
                      :src="item.message"
                      :preview-src-list="[item.message]"
                      fit="fill"
                    >
                    </el-image>
                  </div>
                </li>
              </ul>
              <div v-else style="text-align: center;line-height: 200px;" class="text-info">
                请输入需要咨询的问题
              </div>
            </div>

            <div class="message-input-box">
              <el-input
                @keyup.enter.native="onSend"
                ref="chatInput"
                type="textarea"
                :rows="3"
                maxlength="300"
                show-word-limit
                resize="none"
                placeholder="请在此处输入内容"
                v-model.trim="message">
              </el-input>
            </div>
            <div class="text-right">
              <file-upload-single
                :autoUpload="false"
                accept=".png, .jpg, .jpeg"
                uploadName="chatAnnex"
                :file-size="5"
                @onSuccess="handleUpload"
              >
                <i class="el-icon-picture-outline fontSize28" slot="upload-btn" style="vertical-align: middle;"></i>
              </file-upload-single>
              <el-button type="primary" size="small" @click="onSend">发送</el-button>
            </div>
          </div>
        </div>

      </el-card>
    </div>
  </div>
</template>

<script>
import elDrag from '@/directive/el-drag';
import { fileToBase64, isEmpty } from '@/utils';

export default {
  name: 'ChatMessage',
  directives: { elDrag },
  data() {
    return {
      isChatDialog: false,
      message: null,
      chatList: [],
      isChatEnd: false // 是否在最底部
    }
  },
  created() {

  },
  beforeDestroy() {
    if (this.$refs.chatMain) {
      this.$refs.chatMain.removeEventListener('scroll', this.$_scrollHandler);
    }
  },
  methods: {
    async handleUpload(params) {
      console.log(params)
      let base64 = await fileToBase64(params.file);
      if (isEmpty(base64)) {
        return
      }
      try {
        setTimeout(async () => {
          this.chatList = [
            ...this.chatList,
            {
              message: base64,
              from: 0,
              isFile: true,
              time: new Date()
            }
          ];
          this.goEnd();
        }, 150)
      } catch (e) {
        throw new Error(e);
      }
    },
    isShowTime(item, index) {
      if (index === 0) {
        return true
      } else {
        return this.$moment(item.time).minute() !== this.$moment(this.chatList[index - 1].time).minute()
      }
    },
    async $_scrollHandler() {
      let scrollHeight = this.$refs.chatMain.scrollHeight;
      let scrollTop = this.$refs.chatMain.scrollTop;
      let height = this.$refs.chatMain.offsetHeight;
      console.log(scrollTop + height - scrollHeight);
      if ((scrollTop + height) >= scrollHeight) {
        console.log('到底了');
        this.isChatEnd = true;
      } else {
        this.isChatEnd = false;
      }

      // 滚动到窗口顶部
      if (scrollTop <= 0) {
        this.$nextTick(() => {
          this.$refs.chatMain.scrollTop = this.$refs.chatMain.scrollHeight - scrollHeight;
          this.$refs.chatInput.focus();
        })
      }
    },
    // 打开聊天窗口
    async showChatDialog() {
      this.isChatDialog = !this.isChatDialog;
      if (this.isChatDialog) {
        this.$nextTick(() => {
          this.$refs.chatMain.addEventListener('scroll', this.$_scrollHandler)
        })
        // 滚动到底部
        this.goEnd();
      } else {
        this.resetData();
        this.$refs.chatMain.removeEventListener('scroll', this.$_scrollHandler);
      }
    },
    // 关闭聊天窗口
    hideChatDialog() {
      this.resetData();
      this.$refs.chatMain.removeEventListener('scroll', this.$_scrollHandler);
      this.isChatDialog = false;
    },
    // 重置数据
    resetData() {
      this.message = null;
      this.chatList = [];
      this.isChatEnd = false;
    },
    // 聊天记录滚动到底部
    goEnd() {
      this.$nextTick(() => {
        this.$refs.chatMain.scrollTop = this.$refs.chatMain.scrollHeight;
        this.$refs.chatInput.focus();
      });
    },
    getAnswer(keyword) {
      let answerAll = [
        {
          title: '忘记密码、修改密码',
          content: '您好，请联系客服重置密码，客服的电话是：052-5478512。'
        },
        {
          title: '上传文件超过上传限制',
          content: '您好，请不要把较大的图片贴如文字编辑里，做成PDF以附件形式传入客户端。'
        },
        {
          title: '注册时收不到验证码',
          content: '您好，推荐您这边注册时使用谷歌浏览器，如无效，请拨打技术支持电话：027-68850801'
        },
        {
          title: '客服电话、招标办电话、技术支持电话',
          content: '招标办电话：052-5478512。技术支持电话：027-68850801。工作时间：周一至周五，上午8:30到12:00，下午1:30至6:00。'
        },
        {
          title: '转人工、人工客服',
          content: '人工客服转接中，请等待。。。'
        }
      ]
      let obj = answerAll.find(item => item.title.includes(keyword))
      if (!obj) {
        return {
          message: '未匹配到相关问题，请留下您的联系方式，客服人员收到您的留言后会及时与您联系。',
          from: 1,
          isFile: false,
          time: new Date()
        };
      } else {
        return {
          message: obj.content,
          from: 1,
          isFile: false,
          time: new Date()
        }
      }
    },
    // 发送消息
    async onSend() {
      if (isEmpty(this.message)) {
        return
      }
      try {
        setTimeout(async () => {
          let obj = this.getAnswer(this.message);
          this.chatList = [
            ...this.chatList,
            {
              message: this.message,
              from: 0,
              isFile: false,
              time: new Date()
            },
            obj
          ];
          this.message = null;
          this.goEnd();
        }, 150)
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style lang="scss">
.message-wrap {
  position: fixed;
  z-index: 1999;
  top: 100px;
  right: 50px;
  width: 400px;

  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .message-title {
      font-weight: bold;
      flex: 1;
      padding: 10px 0;
    }
  }

  .message-box {
    display: flex;
  }

  .chat-box {
    flex: 1;
    position: relative;

    .chat-main {
      height: 300px;
      background: #F1F2F3;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .chat-ul {
      padding: 10px 10px 0;

      li.chat-li {
        line-height: 20px;
        margin-bottom: 10px;

        .chat-left, .chat-right {
          display: flex;
        }

        .chat-left {
          justify-content: flex-start;
        }

        .chat-right {
          justify-content: flex-end;
        }

        .chat-text {
          display: inline-block;
          max-width: 85%;
          padding: 5px 10px;
          border-radius: 5px;
          font-size: 13px;
          word-wrap: break-word;
          word-break: break-all;
        }

        .chat-left-text {
          background: #ffffff;
        }

        .chat-right-text {
          background: #C9E7FF;
        }

        .chat-time {
          font-size: 12px;
          text-align: center;
          line-height: 30px;
          color: #6F7173;
        }
      }
    }
  }

  .message-input-box {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.message-icon {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: $colorPrimary;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 6px 0 0 6px !important;
  position: fixed;
  right: 0;
  bottom: 120px;
  z-index: 999;

  .el-icon-s-custom {
    font-size: 26px;
    cursor: pointer;
    color: #ffffff;
  }
}

.msg-html {
  cursor: pointer;
  padding: 10px 0;

  &:hover {
    text-decoration: underline;
  }
}
</style>
