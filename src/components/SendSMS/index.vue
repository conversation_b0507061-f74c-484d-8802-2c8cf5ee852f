<template>
  <el-dialog
    :title="title"
    width="90%"
    @closed="closedDialog()"
    custom-class="maxW600 urge-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form :model="form" :rules="rules" ref="form" size="small" label-position="top">
      <el-form-item label="短信模板" prop="msgTemplate" v-if="templateList.length>0">
        <el-select v-model="form.msgTemplate" clearable placeholder="选择模板" @change="changeTemplate">
          <el-option
            v-for="dict in templateList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <el-button class="ml10" type="primary" size="mini" @click="handleInit">重置</el-button>
      </el-form-item>
      <el-form-item label="短信内容" prop="msg" v-loading="loading">
        <el-input
          v-model.trim="form.msg"
          type="textarea"
          rows="7"
          resize="none"
          maxlength="500"
          show-word-limit
          placeholder="请输入"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="短信接收人" prop="mobiles" v-if="smsRecipient.length>0">
        <el-select
          @change="changeMobiles"
          class="block"
          v-model="form.mobiles"
          multiple
          clearable
          placeholder="选择接收人"
        >
          <el-option
            v-for="(dict,index) in smsRecipient"
            :key="index"
            :label="dict.label+' - '+dict.value"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleAction('cancel')">取消</el-button>
      <el-button type="primary" size="small" @click="handleAction('confirm')">发送</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  data() {
    return {
      isDialog: false,
      title: '短信',
      inputValue: '',
      prepend: '',
      append: '',
      templateList: [],
      mobiles: [],
      smsRecipient: [],
      form: {
        msg: '',
        msgTemplate: null,
        mobiles: []
      },
      rules: {
        msg: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { max: 500, message: '最长 500 个字符', trigger: 'blur' }
        ]
      },
      loading: false
    };
  },
  created() {
    console.log('smsRecipient', this.smsRecipient)
    this.form.msg = this.inputValue;
    this.form.mobiles = this._.cloneDeep(this.mobiles);
  },
  methods: {
    changeMobiles(val) {
      console.log(val)
      console.log(this.form.mobiles)
      console.log('smsRecipient', this.smsRecipient)
    },
    handleInit() {
      this.resetForm('form')
    },
    changeTemplate(val) {
      this.form.msg = val;
    },
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleAction(action) {
      if (action === 'confirm') {
        this.$refs['form'].validate(async valid => {
          if (valid) {
            let msg = this.prepend + this.form.msg + this.append;
            this.handleConfirm({ msg, mobiles: this.form.mobiles });
          }
        })
      } else {
        this.handleCancel();
      }
    }
  }
};
</script>
<style lang="scss">
.urge-dialog {
  .el-dialog__body {
    padding: 0 20px 10px;
  }
}
</style>

<style lang="scss" scoped>
.msg-template-ul {
  display: flex;
  flex-wrap: wrap;

  li {
    cursor: pointer;
    padding: 4px 10px;
    background-color: #EAEBED;
    border-radius: 5px;
    color: #000000;
    font-size: 12px;
    margin-bottom: 10px;

    &:hover {
      background-color: #E0E2E4;
    }

    & + li {
      margin-left: 10px;
    }
  }
}
</style>
