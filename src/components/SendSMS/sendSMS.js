import Vue from 'vue';
import SendSMSVue from './index.vue';
import { isEmpty } from '@/utils'

const MsgNotifyConstructor = Vue.extend(SendSMSVue);

const SendSMS = (options = {}) => {
  const defaults = {
    title: '通知', // 默认内容
    inputValue: '', // 默认内容
    prepend: '', // 前置内容
    append: '', // 后置内容
    templateList: [],
    mobiles: [],
    smsRecipient: []
  };

  for (let key in defaults) {
    defaults[key] = isEmpty(options[key]) ? defaults[key] : options[key];
  }
  let instance = new MsgNotifyConstructor({
    data: defaults,
    methods: {
      handleConfirm(data) {
        options.handleConfirm(data) && options.handleConfirm(data).call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default SendSMS;
