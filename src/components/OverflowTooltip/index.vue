<template>
  <el-popover
    placement="top-start"
    :width="width"
    trigger="hover"
    :content="content+''"
v-cusTooltip>
    <div class="over-ellipsis" slot="reference">{{ content || '/' }}</div>
  </el-popover>
</template>

<script>
import Vue from 'vue';
import { getStyle } from 'element-ui/src/utils/dom';

export default {
  name: 'OverflowTooltip',
  props: {
    content: {
      type: [String, Number]
    },
    width: {
      type: [String, Number],
      default: 800
    }
  },
  directives: {
    cusTooltip: {
      inserted: (el, binding, vnode) => {
        el.addEventListener('mouseenter', function (e) {
          let defalutSilent = !!Vue.config.silent;
          Vue.config.silent = true;
          vnode.componentInstance.disabled = true;
          const range = document.createRange();
          range.setStart(el, 0);
          range.setEnd(el, el.childNodes.length);
          const rangeWidth = Math.round(range.getBoundingClientRect().width);
          const padding = (parseInt(getStyle(el, 'paddingLeft'), 10) || 0) + (parseInt(getStyle(el, 'paddingRight'), 10) || 0);
          if (rangeWidth + padding > el.offsetWidth || el.scrollWidth > el.offsetWidth) {
            vnode.componentInstance.disabled = false;
          }
          Vue.config.silent = defalutSilent;
        });
      }
    }
  }
}
</script>

<style scoped>

</style>
