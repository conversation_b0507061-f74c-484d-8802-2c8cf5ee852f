<template>
  <div class="upload-file">
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--picture-card" name="el-fade-in-linear" tag="ul">
      <li :key="'li-'+index" class="el-upload-list__item is-ready" v-for="(file, index) in fileList">
        <el-image
          class="el-upload-list__item-thumbnail"
          :src="file.url"
          fit="contain"></el-image>
        <div class="el-upload-list__item-actions">
          <span
            v-if="index!==0&&!disabled"
            class="el-upload-list__item-delete"
            @click="handleMoveLeft(index,file)"
          >
            <i class="el-icon-back"></i>
          </span>
          <span
            class="el-upload-list__item-preview"
            @click="handlePreview(file)"
          >
            <i class="el-icon-zoom-in"></i>
          </span>
          <!--          <span-->
          <!--            class="el-upload-list__item-delete"-->
          <!--            @click="handleDown(file)"-->
          <!--          >-->
          <!--            <i class="el-icon-download"></i>-->
          <!--          </span>-->
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleDelete(index,file)"
          >
            <i class="el-icon-delete"></i>
         </span>
          <span
            v-if="index < fileList.length-1&&!disabled"
            class="el-upload-list__item-delete"
            @click="handleMoveRight(index,file)"
          >
            <i class="el-icon-right"></i>
          </span>
        </div>
      </li>
    </transition-group>

    <el-upload
      v-if="!disabled"
      :disabled="disabled"
      multiple
      action="#"
      list-type="picture-card"
      :accept="fileType.map(item=>'.'+item).join(',')"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :http-request="uploadSectionFile"
      :show-file-list="false"
      class="upload-file-uploader"
      ref="upload"
    >
      <!-- 上传按钮 -->
      <i slot="default" class="el-icon-plus"></i>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
        <template v-if="fileType&&fileType.length>0"> 格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
        </template>
        的文件
      </div>
    </el-upload>
  </div>
</template>

<script>

export default {
  name: 'ImageUploadSelect',
  props: {
    value: {},
    // 数量限制
    limit: {
      type: Number,
      default: null
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg', 'pdf']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      fileList: this.value || []
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && ((this.fileType && this.fileType.length > 0) || this.fileSize)
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    getUrl(file) {
      const blob = new Blob([file], { 'type': file.type });
      return URL.createObjectURL(blob)
    },
    setCurrentValue(value) {
      this.fileList = value || []
      this.$emit('input', value)
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType && this.fileType.length > 0) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$modal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传回调
    uploadSectionFile(param) {
      try {
        const file = param.file;
        this.uploadList.push({ name: file.name, url: this.getUrl(file), file })
        if (this.uploadList.length === this.number) {
          this.fileList = this.fileList.concat(this.uploadList)
          this.uploadList = []
          this.number = 0
          this.$emit('input', this.fileList)
          this.$emit('change', { fileList: this.fileList, params: this.params })
        }
        this.$modal.closeLoading()
      } catch (e) {
        this.number--;
        this.$modal.msgError('上传失败，请重试')
        this.$modal.closeLoading()
      }
    },
    handleMoveLeft(index, file) {
      console.log(index, file)
      this.moveSwap(index, index - 1)
    },
    handleMoveRight(index, file) {
      console.log(index, file)
      this.moveSwap(index, index + 1)
    },
    moveSwap(x, y) {
      let tmp = this.fileList[y]
      this.$set(this.fileList, y, this.fileList[x])
      this.$set(this.fileList, x, tmp)
      console.log(this.fileList)
      this.$emit('input', this.fileList)
      this.$emit('change', { fileList: this.fileList, params: this.params })
    },
    // 删除文件
    handleDelete(index, file) {
      this.fileList.splice(index, 1);
      this.$emit('input', this.fileList)
      this.$emit('change', { fileList: this.fileList, params: this.params })
      this.$emit('remove', file)
    },
    handleDown(file) {
      this.$emit('down', file)
    },
    handlePreview(file) {
      this.$emit('preview', file)
    }
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
  display: inline-block;

  .el-upload__tip {
    width: 148px;
    line-height: 1.2;
  }
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
  vertical-align: middle;
}

</style>
