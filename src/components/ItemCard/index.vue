<template>
  <el-card
    :header="header"
    shadow="never"
    :body-style="{'padding': '15px 0 10px'}"
    class="card-box item-card"
    style="border: none;">
    <template slot="header">
      <slot name="header"></slot>
    </template>
    <slot></slot>
  </el-card>
</template>

<script>
export default {
  name: 'Index',
  props: {
    header: {
      type: String,
      default: null
    }
  }
}
</script>

<style lang="scss">
.item-card {
  padding: 0;
  position: relative;

  .el-card__header {
    font-size: 14px;
    border: none;
    font-weight: 600;
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
