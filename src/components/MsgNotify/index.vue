<template>
  <el-dialog
    :title="title"
    width="90%"
    @closed="closedDialog()"
    custom-class="maxW400 urge-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form :model="form" :rules="rules" ref="form" size="small" label-position="top">
      <el-form-item label="" prop="msg">
        <el-input
          v-model.trim="form.msg"
          type="textarea"
          rows="6"
          resize="none"
          maxlength="500"
          show-word-limit
          placeholder="请输入"
        >
        </el-input>
      </el-form-item>
    </el-form>

    <ul class="msg-template-ul">
      <li v-for="(item,index) in msgTemplate" :key="index" @click="selectMsg(item)">
        {{ item }}
      </li>
    </ul>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleAction('cancel')">{{ cancelButtonText }}</el-button>
      <el-button type="primary" size="small" @click="handleAction('confirm')">{{ confirmButtonText }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  data() {
    return {
      isDialog: false,
      title: '请输入',
      inputValue: '',
      prepend: '',
      append: '',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      msgTemplate: [],
      form: {
        msg: ''
      },
      rules: {
        msg: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 500, message: '最长 500 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.form.msg = this.inputValue;
  },
  methods: {
    selectMsg(val) {
      this.form.msg += val
    },
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleAction(action) {
      if (action === 'confirm') {
        this.$refs['form'].validate(async valid => {
          if (valid) {
            let msg = this.prepend + this.form.msg + this.append;
            this.handleConfirm(msg);
          }
        })
      } else {
        this.handleCancel();
      }
    }
  }
};
</script>
<style lang="scss">
.urge-dialog {
  .el-dialog__body {
    padding: 0 20px 10px;
  }
}
</style>

<style lang="scss" scoped>
.msg-template-ul {
  display: flex;
  flex-wrap: wrap;

  li {
    cursor: pointer;
    padding: 4px 10px;
    background-color: #EAEBED;
    border-radius: 5px;
    color: #000000;
    font-size: 12px;
    margin-bottom: 10px;

    &:hover {
      background-color: #E0E2E4;
    }

    & + li {
      margin-left: 10px;
    }
  }
}
</style>
