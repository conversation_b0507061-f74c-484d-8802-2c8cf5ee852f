import Vue from 'vue';
import MsgNotifyVue from './index.vue';

const MsgNotifyConstructor = Vue.extend(MsgNotifyVue);

const MsgNotify = (options = {}) => {
  const defaults = {
    title: '通知', // 默认内容
    inputValue: '', // 默认内容
    prepend: '', // 前置内容
    append: '', // 后置内容
    confirmButtonText: '发送',
    cancelButtonText: '取消',
    msgTemplate: []
  };

  for (let key in defaults) {
    defaults[key] = options[key] || defaults[key];
  }
  let instance = new MsgNotifyConstructor({
    data: defaults,
    methods: {
      handleConfirm(data) {
        options.handleConfirm(data) && options.handleConfirm(data).call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default MsgNotify;
