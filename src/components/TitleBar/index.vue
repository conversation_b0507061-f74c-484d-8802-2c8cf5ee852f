<template>
  <div class="top-bar">
    <div class="bar-name">
      <i class="bar-icon" :class="icon" v-if="icon"></i>
      <span>{{name}}</span>
    </div>
    <slot name="center"></slot>
    <slot name="right"></slot>
  </div>
</template>

<script>
export default {
  name: 'TopBar',
  props: {
    name: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    box-sizing: border-box;
  }

  .bar-name {
    display: flex;
    align-items: center;
  }

  .bar-icon {
    margin-right: 10px;
    font-size: 23px;
  }
</style>
