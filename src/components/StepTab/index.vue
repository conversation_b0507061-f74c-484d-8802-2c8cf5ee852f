<template>
  <ul class="clearfix tab-wrap">
    <li :style="{'--theme': theme}" class="fl tab-li" v-for="(item,index) in tabArray" :key="index">
      <div
        :class="{'tab-item':true, 'tab-active':currentTabVal===item.tabVal,'tab-disable':Number(item.step)>Number(projectStep)}"
        @click="handleTabClick(index,item)"
      >
        {{ item.label }}
      </div>
    </li>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  props: {
    value: {},
    tabArray: {
      type: Array,
      default: () => []
    },
    projectStep: {
      type: String,
      default: '1.1'
    }
  },
  data() {
    return {
      currentTabVal: this.value
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  watch: {
    value(value) {
      this.setCurrentTabVal(value)
    }
  },
  created() {
    this.setCurrentTabVal(this.value)
  },
  methods: {
    handleTabClick(index, item) {
      if (Number(item.step) > Number(this.projectStep)) return
      this.setCurrentTabVal(item.tabVal)
      this.$emit('tab-click', item, index)
    },
    setCurrentTabVal(value) {
      this.currentTabVal = value
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
$colorFalse: #999999;
.tab-wrap {
  background-color: #FFFFFF;
  margin-bottom: 20px;

  .tab-li {
    margin-right: 3%;
    position: relative;
    margin-bottom: 12px;

    &:last-child {
      margin-right: 0;
    }
  }

  .tab-item {
    font-size: 14px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
    padding: 0 15px;
    border-radius: 5px;
    box-sizing: border-box;
    color: #{'var(--theme)'};
    border: 1px solid #{'var(--theme)'};

    &.tab-disable {
      color: $colorFalse;
      border: 1px solid $colorFalse;
    }

    &.tab-active {
      color: #FFFFFF;
      background-color: #{'var(--theme)'};
      border: 1px solid #{'var(--theme)'};
      position: relative;

      &:after {
        content: "";
        width: 0;
        height: 0;
        display: block;
        border-style: solid;
        border-width: 12px 10px 0;
        border-color: #{'var(--theme)'} transparent transparent;
        position: absolute;
        top: 34px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
