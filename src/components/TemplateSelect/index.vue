<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="1.5" class="fr">
        <el-button type="primary" size="mini" @click="handleInitTemplate" v-if="!disabled">初始化模板</el-button>
        <el-button type="success" size="mini" @click="handlePreview">预览</el-button>
      </el-col>
      <el-col :span="1.5" class="fr" v-if="!disabled">
        <el-select
          size="mini"
          @change="changeTemplate"
          v-model="tmpKey"
          placeholder="选择模板"
          clearable
          class="block"
        >
          <el-option
            v-for="dict in templateList"
            :key="dict.tmpKey"
            :label="dict.tmpName"
            :value="dict.tmpKey"
          />
        </el-select>
      </el-col>
      <el-col :span="1.5" v-if="showSealTip&&!disabled">
        <p class="text-danger">
          <span>请在需要盖章的位置写上：（盖章）</span>
          <el-button type="text" size="mini" @click="$sealTipDialog()">查看示例</el-button>
        </p>
      </el-col>
    </el-row>
    <el-row>
      <Tinymce
        ref="editor"
        v-model="htmlText"
        :height="height"
        :min-height="minHeight"
        :max-height="maxHeight"
        :readonly="disabled"
        v-if="initEditor"
        @input="inputTinymce"
      />
    </el-row>
  </div>
</template>

<script>
import { listTemplateByType, templatePreview } from '@/api/system/fileTemplate';

export default {
  name: 'Index',
  props: {
    value: {},
    tmpType: {
      type: String
    },
    getTemplateFunc: {
      type: Function
    },
    height: {
      type: Number,
      default: 620
    },
    minHeight: {
      type: Number,
      default: 600
    },
    maxHeight: {
      type: Number,
      default: 800
    },
    showSealTip: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      templateList: [],
      initEditor: true,
      tmpKey: null,
      htmlText: this.value
    }
  },
  created() {

  },
  watch: {
    tmpType: {
      // deep: true,
      immediate: true,
      handler(val) {
        this.getTemplateList(val);
      }
    },
    value(value) {
      this.setCurrentValue(value)
    }
  },
  methods: {
    setCurrentValue(value) {
      this.htmlText = value;
      this.$emit('input', value);
    },
    handlePreview() {
      if (!this.htmlText) {
        this.$modal.msgError('内容不能为空')
        return
      }
      this.$modal.loading('数据提交中，请稍候...')
      templatePreview({ filePreview: this.htmlText }).then(res => {
        this.$modal.closeLoading()
        this.$pdfViewDialog({ data: res.data, type: 'blob' })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    inputTinymce() {
      this.$emit('input', this.htmlText);
    },
    handleInitTemplate() {
      this.getHtml();
    },
    changeTemplate() {
      this.$emit('changeTmp', this.tmpKey);
      this.getHtml();
    },
    async getHtml() {
      try {
        let html = '';
        if (!this.tmpKey) {
          html = '';
        } else {
          if (!this.getTemplateFunc) {
            let obj = this.templateList.find(v => v.tmpKey === this.tmpKey);
            html = obj ? obj.tmpContent : '';
          } else {
            let { data } = await this.getTemplateFunc({ tmpKey: this.tmpKey, tmpType: this.tmpType })
            html = data;
          }
        }
        this.setCurrentValue(html);
        this.initEditor = false;
        this.$nextTick(() => {
          this.initEditor = true;
        })
      } catch (e) {
        throw new Error(e);
      }
    },
    async getTemplateList(val) {
      try {
        if (val) {
          let { data } = await listTemplateByType(val);
          this.templateList = data;
        } else {
          this.templateList = [];
        }
        this.tmpKey = null;
        this.$emit('changeTmp', this.tmpKey);
      } catch (e) {
        throw new Error(e);
      }
    }
  }
}
</script>

<style scoped>

</style>
