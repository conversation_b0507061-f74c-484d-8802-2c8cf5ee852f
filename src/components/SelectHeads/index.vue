<template>
  <div class="heads-wrap">
    <el-form ref="form" size="small" label-position="left" :inline="true">
      <el-form-item label="分类" label-width="40px">
        <el-select v-model="keyGroup" placeholder="请选择分类" clearable @change="changeKeyGroup">
          <el-option
            v-for="dict in dict.type.quote_field_key_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-transfer
      :titles="['未选择', '已选择']"
      :button-texts="['移除', '添加']"
      v-model="selectValue"
      :data="columns"
      :props="{
           key: 'keyVal',
           label: 'keyName'
        }"
      @change="dataChange"
      filterable
      :filter-method="filterMethod"
      filter-placeholder="请输入名称"
    ></el-transfer>
  </div>
</template>

<script>
import { allQuoteField } from '@/api/system/quoteField'

export default {
  name: 'SelectHeads',
  dicts: ['quote_field_key_group'],
  props: {
    value: {},
    groupType: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      columns: [],
      currentValue: this.value,
      // 显隐数据
      selectValue: [],
      filterMethod(query, item) {
        return item.keyName.indexOf(query) > -1;
      },
      keyGroup: null
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    }
  },
  created() {
    this.getAllKeys();
    this.setCurrentValue(this.value)
  },
  methods: {
    async getAllKeys() {
      try {
        let { data } = await allQuoteField({
          disabled: false,
          groupType: this.groupType
        })
        this.columns = data || [];
      } catch (e) {
        throw new Error(e);
      }
    },
    changeKeyGroup(val) {
      let list = this.columns.filter(item => item.keyGroups.includes(val));
      this.setCurrentValue(list)
      this.$emit('columnChange', list)
    },
    setCurrentValue(value) {
      this.currentValue = value;
      this.selectValue = value.map(item => item.keyVal);
      this.$emit('input', value)
    },
    // 右侧列表元素变化
    dataChange(data) {
      let list = this.columns.filter(item => data.includes(item.keyVal))
      console.log(list)
      this.setCurrentValue(list)
      this.$emit('columnChange', list)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-transfer__button {
  padding: 12px;
  display: block;
  margin-left: 0px;

  .el-icon-arrow-right {
    margin-left: 5px;
  }
}

::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
</style>
