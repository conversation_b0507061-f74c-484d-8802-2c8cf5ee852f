import Vue from 'vue'
import ImgView from './index.vue';

const ImgViewConstructor = Vue.extend(ImgView); // 构造函数

const ImgViewDialog = (options = {}) => {
  if (typeof options === 'string') {
    options = {
      url: options
    }
  }

  if (options.type === 'blob') {
    const blob = new Blob([options.data], { 'type': options.data.type });
    const url = URL.createObjectURL(blob);
    options.url = url;
  }

  const instance = new ImgViewConstructor({
    data: options
  });

  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.showViewer = true;
  return instance.vm
};

export default ImgViewDialog
