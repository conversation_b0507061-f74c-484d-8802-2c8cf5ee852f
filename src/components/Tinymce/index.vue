<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" :style="{width:containerWidth}">
    <textarea :id="tinymceId" class="tinymce-textarea"/>
  </div>
</template>

<script>

import plugins from './plugins'
import toolbar from './toolbar'
import menu from './menu'
import load from './dynamicLoadScript'

const tinymceCDN = `${process.env.VUE_APP_BASE_URL}tinymce/tinymce.min.js`;
const useDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
export default {
  name: 'Tinymce',
  props: {
    id: {
      type: String,
      default: function () {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      }
    },
    value: {
      type: String,
      default: ''
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360
    },
    minHeight: {
      type: Number,
      required: false,
      default: 360
    },
    maxHeight: {
      type: Number,
      required: false
    },
    width: {
      type: [Number, String],
      required: false,
      default: 'auto'
    },
    readonly: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false
    }
  },
  computed: {
    containerWidth() {
      const width = this.width
      if (/^[\d]+(\.[\d]+)?$/.test(width)) { // matches `100`, `'100'`
        return `${width}px`
      }
      return width
    }
  },
  watch: {
    value(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() => {
          if (window.tinymce) {
            window.tinymce.get(this.tinymceId).setContent(val || '')
          }
        })
      }
    }
  },
  mounted() {
    this.init()
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce()
    }
  },
  deactivated() {
    this.destroyTinymce()
  },
  destroyed() {
    this.destroyTinymce()
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        if (err) {
          this.$message.error(err.message)
          return
        }
        this.initTinymce()
      })
    },
    initTinymce() {
      const _this = this
      window.tinymce.init({
        selector: `#${this.tinymceId}`,
        language: 'zh-Hans',
        plugins: plugins,
        toolbar: this.readonly ? false : toolbar,
        menubar: this.readonly ? false : 'edit view insert format table',
        menu: menu,
        readonly: this.readonly,
        autoresize_bottom_margin: 10,
        height: this.height,
        min_height: this.minHeight,
        max_height: this.maxHeight,
        skin: useDarkMode ? 'oxide-dark' : 'oxide', // 设置皮肤
        content_css: useDarkMode ? 'dark' : 'default',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
        font_size_formats: '10px 12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 34px 36px 48px 72px',
        font_family_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;华文楷体=华文楷体,serif;Courier New=courier new,courier,monospace;Helvetica=helvetica,arial,sans-serif;',
        contextmenu: 'image table', // 上下文（特定于内容）菜单
        toolbar_mode: 'sliding',
        quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable', // 上下文（特定于内容）工具栏
        elementpath: false, // 此选项允许您禁用编辑器底部状态栏中的元素路径
        statusbar: false, // 是否显示状态栏
        object_resizing: true,
        convert_urls: false,
        image_advtab: true, // 将“高级”选项卡添加到图像对话框,允许您向图像添加自定义样式、间距和边框。
        image_uploadtab: true,
        importcss_append: true, // 将导入的样式附加到Format菜单的末尾并替换默认格式
        advlist_bullet_styles: 'square', // 列表样式 default,circle,disc,square'
        advlist_number_styles: 'default',
        link_default_target: '_blank',
        link_title: false, // 此选项允许您禁用对话框title中的链接输入字段
        file_picker_types: 'image', // 只允许选择图片文件
        images_file_types: 'jpg,png,jpeg',
        nonbreaking_force_tab: true, // 此选项允许您在用户按下键盘键tab时强制 TinyMCE 插入三个&nbsp;
        init_instance_callback: editor => {
          if (_this.value) {
            editor.setContent(_this.value)
          }
          _this.hasInit = true
          editor.on('NodeChange Change KeyUp SetContent', this.$_changeHandler)
        },
        setup(editor) {
          editor.on('FullscreenStateChanged', (e) => {
            _this.fullscreen = e.state
          })
        },
        // 自定义图像和文件的处理方式
        images_upload_handler: (blobInfo, progress) => new Promise((resolve, reject) => {
          if (blobInfo.blob().size > 1 * 1024 * 1024) {
            reject({ message: '文件大小不能超过1MB', remove: true });
          } else {
            const img = 'data:image/jpeg;base64,' + blobInfo.base64();
            resolve(img);
          }
        })
      })
    },
    $_changeHandler() {
      this.hasChange = true;
      this.$emit('input', this.getContent())
    },
    destroyTinymce() {
      if (!window.tinymce) {
        return
      }
      const tinymce = window.tinymce.get(this.tinymceId)
      if (this.fullscreen) {
        tinymce.execCommand('mceFullScreen')
      }

      if (tinymce) {
        tinymce.off('NodeChange Change KeyUp SetContent', this.$_changeHandler)
        this.hasChange = false;
        tinymce.destroy()
      }
    },
    setContent(value) {
      window.tinymce.get(this.tinymceId).setContent(value)
    },
    getContent() {
      return window.tinymce.get(this.tinymceId).getContent()
    }
  }
}
</script>

<style lang="scss" scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}

.tinymce-container {
  ::v-deep {
    .mce-fullscreen {
      z-index: 10000;
    }
  }
}

.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}

.editor-custom-btn-container {
  position: absolute;
  right: 4px;
  top: 4px;
  /*z-index: 2005;*/
}

.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}

.editor-upload-btn {
  display: inline-block;
}
</style>
