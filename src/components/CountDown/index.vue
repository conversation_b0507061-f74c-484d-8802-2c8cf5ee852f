<template>
  <div class="count-down-box" ref="countDown" :style="{'--theme': theme, '--itemWidth': itemWidth}">
    <div class="time-item" v-if="countDown.day">{{ countDown.day }}</div>
    <span class="colon" v-if="countDown.day">{{ separator ? separator : '天' }}</span>

    <div class="time-item" v-if="countDown.hours">{{ countDown.hours }}</div>
    <span class="colon" v-if="countDown.hours">{{ separator ? separator : '时' }}</span>

    <div class="time-item" v-if="countDown.minutes">{{ countDown.minutes }}</div>
    <span class="colon" v-if="countDown.minutes">{{ separator ? separator : '分' }}</span>

    <div class="time-item" v-if="countDown.seconds">{{ countDown.seconds }}</div>
    <span class="colon" v-if="countDown.seconds&&!separator">秒</span>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'Index',
  props: {
    title: {
      type: String,
      default: '倒计时'
    },
    startTime: {
      type: [String, Number, Date],
      required: true
    },
    endTime: {
      type: [String, Number, Date],
      required: true
    },
    itemWidth: {
      type: String,
      default: '40px'
    },
    separator: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      timer: null,
      currentTime: null,
      stopTime: null
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ]),
    countDown() {
      let m1 = this.currentTime;
      let m2 = this.$moment(this.endTime).valueOf();

      let ms = m2 - m1;
      if (ms <= 0) {
        clearInterval(this.timer);
        let data = {
          isEnd: true,
          ms,
          day: null,
          hours: null,
          minutes: '00',
          seconds: '00'
        }
        this.$emit('changeCountdown', data);
        return data
      }
      const day = Math.floor(ms / (24 * 60 * 60 * 1000));
      const hours = Math.floor(ms % (24 * 60 * 60 * 1000) / (60 * 60 * 1000));
      const minutes = Math.floor(ms % (24 * 60 * 60 * 1000) % (60 * 60 * 1000) / (60 * 1000));
      const seconds = Math.floor(ms % (24 * 60 * 60 * 1000) % (60 * 60 * 1000) % (60 * 1000) / 1000);

      let tDay = day <= 0 ? null : day;
      let tHours = day <= 0 && hours <= 0 ? null : this.prefixInteger(hours, 2);
      let tMinutes = day <= 0 && hours <= 0 && minutes <= 0 ? '00' : this.prefixInteger(minutes, 2);
      let tSeconds = day <= 0 && hours <= 0 && minutes <= 0 && seconds <= 0 ? '00' : this.prefixInteger(seconds, 2);

      let data = {
        isEnd: false,
        ms,
        day: tDay,
        hours: tHours,
        minutes: tMinutes,
        seconds: tSeconds
      }
      this.$emit('changeCountdown', data);
      return data
    }
  },
  mounted() {
    this.$nextTick(() => {
      clearInterval(this.timer);
      this.currentTime = this.$moment(this.startTime).valueOf();
      this.timer = setInterval(() => {
        this.currentTime += 1000;
      }, 1000);
    });
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 数字前补 0，num传入的数字，n需要的字符长度
    prefixInteger(num, n) {
      return (Array(n).join('0') + num).slice(-n);
    }
  }
}
</script>

<style lang="scss" scoped>
.count-down-box {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: 100%;

  .time-item {
    display: inline-block;
    min-width: calc(var(--itemWidth) * 2);
    height: calc(var(--itemWidth) * 2);
    line-height: calc(var(--itemWidth) * 2);
    background: #{'var(--theme)'};
    color: #FFFFFF;
    text-align: center;
    font-size: calc(var(--itemWidth) * 1);
    position: relative;
    padding: 0 10px;
  }

  .colon {
    display: inline-block;
    width: #{'var(--itemWidth)'};
    text-align: center;
    font-size: calc(var(--itemWidth) * 0.8);
    color: #{'var(--theme)'};
  }
}
</style>
