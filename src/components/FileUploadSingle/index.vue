<template>
  <div class="upload-wrap">
    <div class="upload-box">
      <el-upload
        v-if="showUploadBtn"
        class="upload-btn"
        :disabled="disabled"
        :ref="uploadName"
        action="#"
        :accept="accept"
        :limit="1"
        :show-file-list="false"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        :http-request="uploadSectionFile"
        :on-remove="removeFile"
      >
        <slot name="upload-btn">
          <el-button type="primary" :disabled="disabled">上传</el-button>
        </slot>
      </el-upload>
      <slot name="upload-right"></slot>
    </div>
    <div v-if="showTip&&showUploadBtn" class="el-upload__tip">
      <slot name="upload-tip">
        请上传<span v-if="fileSize>0">大小不超过<b style="color: #f56c6c">{{ fileSize }}MB</b></span>
        <template v-if="accept"> 格式为 <b style="color: #f56c6c">{{ accept }}</b></template>
        的文件
      </slot>
    </div>
  </div>

</template>

<script>
import { isEmpty } from '@/utils'
import { fileUpload } from '@/api/file'

export default {
  name: 'Index',
  props: {
    params: {
      type: Object,
      default: () => {
      }
    },
    uploadName: {
      type: String,
      default: 'upload'
    },
    fileSize: { // 上传限制文件大小
      type: Number,
      default: 5
    },
    accept: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showTip: {
      type: Boolean,
      default: false
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    showUploadBtn: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleExceed() {
      this.$modal.msgWarning(`最多上传1个文件`)
    },
    beforeUpload(file) {
      if (!isEmpty(this.accept)) {
        let rules = this._.map(this.accept.split(','), this._.trim)
        let index = file.name.lastIndexOf('.')
        let format = file.name.substring(index).toLowerCase()
        if (this._.indexOf(rules, format) === -1) {
          this.$modal.msgError(`文件格式不正确, 请上传${rules.join(' ')}格式文件!`)
          return false
        }
      }

      let fileSize = file.size
      if (fileSize <= 0) {
        this.$modal.msgError('上传文件大小不能为空')
        return false
      }
      if (this.fileSize > 0 && fileSize > Number(this.fileSize) * 1024 * 1024) {
        this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
        return false
      }
    },
    removeFile() {
      this.$emit('onRemove', this.uploadName)
    },
    uploadSectionFile(param) {
      const file = param.file
      if (this.autoUpload) {
        this.$modal.loading('正在上传文件，请稍候...')
        fileUpload({
          ...this.params,
          file
        })
          .then(res => {
            this.$emit('onSuccess', {
              ...this.params,
              uploadName: this.uploadName,
              file,
              name: res.data.name,
              url: res.data.url
            })
            this.$modal.closeLoading()
          })
          .catch(() => {
            this.$modal.msgError('上传失败，请重试')
            this.$modal.closeLoading()
          })
      } else {
        this.$emit('onSuccess', { ...this.params, uploadName: this.uploadName, file })
      }
      this.$refs[this.uploadName].clearFiles()
    }
  }
}
</script>

<style lang="scss">
.upload-wrap {
  .upload-box {
  }

  .el-upload__tip {
    line-height: 1.5;
  }
}

.upload-btn {

  line-height: inherit;
  display: inline-block;

  .el-upload-list {

    .el-upload-list__item {
      margin-top: 0;
    }
  }
}

.el-button + .upload-btn {
  margin-left: 10px;
}

.upload-btn + .el-button {
  margin-left: 10px;
}
</style>
