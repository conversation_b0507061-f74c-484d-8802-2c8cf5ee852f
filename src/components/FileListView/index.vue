<template>
  <div  :style="{'--fontSize': fontSize}">
    <ul class="upload-file-list el-upload-list el-upload-list--text" v-if="fileList&&fileList.length>0">
      <li class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList" :key="index">
        <el-tooltip effect="dark" :content="file.name" placement="top">
          <div class="over-ellipsis mr10">
            {{ file.name }}
          </div>
        </el-tooltip>
        <div class="ele-upload-list__item-content-action">
          <el-link
            :underline="false"
            @click="handlePreview(file.url)"
            type="primary"
            v-if="handlePreview"
            :disabled="disabled"
          >
            预览
          </el-link>
          <el-link
            :underline="false"
            @click="handleDown(file.url)"
            type="primary"
            v-if="handleDown"
            :disabled="disabled"
          >
            下载
          </el-link>
        </div>
      </li>
    </ul>
    <span v-if="!(fileList&&fileList.length>0)&&shouSlash">/</span>
  </div>
</template>

<script>

export default {
  name: 'FileListView',
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    shouSlash: {
      type: Boolean,
      default: true
    },
    handlePreview: {
      type: Function
    },
    handleDown: {
      type: Function
    },
    disabled: {
      type: Boolean,
      default: false
    },
    fontSize: {
      type: String,
      default: '12px'
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.upload-file-list .el-upload-list__item {
  font-size: #{'var(--fontSize)'};
  //border: 1px solid #e4e7ed;
  line-height: 1.5;
  margin-bottom: 5px;
  position: relative;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action {
  flex-shrink: 0;

  .el-link {
    font-size: #{'var(--fontSize)'};
    margin-right: 5px;
  }
}
</style>
