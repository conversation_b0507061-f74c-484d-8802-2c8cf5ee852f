<template>
  <el-dialog
    top="0"
    width="90%"
    @closed="closedDialog"
    custom-class="pdf-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
  >
    <iframe :src="data" frameborder="0" border="0" style="width: 100%; height: 100%"></iframe>
  </el-dialog>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      isDialog: false,
      data: ''
    }
  },
  methods: {
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleConfirm() {

    },
    handleCancel() {

    }
  }
}
</script>

<style lang="scss">
.pdf-dialog {
  max-width: 90%;
  background: transparent;
  margin-top: 0 !important;

  .el-dialog__header {
    padding: 0;
    height: 0;
    border: none;

    .el-dialog__headerbtn {
      top: 0;
      right: -40px;
      font-size: 40px;
      line-height: 1;

      .el-dialog__close {
        color: #FFFFFF;
      }
    }
  }

  .el-dialog__body {
    padding: 0 !important;
    height: 100vh;
  }
}
</style>
