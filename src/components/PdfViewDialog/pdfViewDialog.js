import Vue from 'vue'
import PdfViewIndex from './index.vue';

const PdfViewConstructor = Vue.extend(PdfViewIndex); // 构造函数

const PdfViewDialog = (options = {}) => {
  if (typeof options === 'string') {
    options = {
      data: options
    }
  }

  if (options.type === 'blob') {
    const blob = new Blob([options.data], { 'type': 'application/pdf' });
    const url = URL.createObjectURL(blob);
    options.data = `${process.env.VUE_APP_BASE_URL}pdfjs-legacy/web/viewer.html?file=${encodeURIComponent(url)}`;
  } else {
    options.data = `${process.env.VUE_APP_BASE_URL}pdfjs-legacy/web/viewer.html?file=${encodeURIComponent(options.data)}`;
  }

  const instance = new PdfViewConstructor({
    data: options,
    methods: {
      handleConfirm() {
        options.handleConfirm && options.handleConfirm.call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default PdfViewDialog
