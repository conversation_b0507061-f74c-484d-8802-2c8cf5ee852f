<template>
  <el-dialog
    center
    title="选择印章"
    width="90%"
    @closed="closedDialog()"
    custom-class="maxW500 seal-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form :model="form" :rules="rules" ref="form" size="small" label-position="top">
      <el-form-item label="企业印章" prop="orgSealChecked" v-if="sealType===0||sealType===1">
        <div class="seal-select-box" v-if="sealData.orgSealBase64">
          <el-checkbox v-model="form.orgSealChecked">
            <img :src="sealData.orgSealBase64" alt="" style="width: 110px;">
          </el-checkbox>
        </div>
      </el-form-item>
      <el-form-item label="法定代表人印章" prop="psnSealChecked" v-if="sealType===0||sealType===2">
        <div class="seal-select-box" v-if="sealData.psnSealBase64">
          <el-checkbox v-model="form.psnSealChecked">
            <img :src="sealData.psnSealBase64" alt="" style="width: 110px;">
          </el-checkbox>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleAction('cancel')">取消</el-button>
      <el-button type="primary" size="medium" @click="handleAction('confirm')">确认提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { querySeal } from '@/api/sealManage';

export default {
  data() {
    let validateChecked = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
    return {
      isDialog: false,
      orgCode: null,
      sealType: 0, // 查询印章类型 0全部 1企业印章，2法人章
      form: {
        orgSealChecked: false,
        psnSealChecked: false
      },
      rules: {
        orgSealChecked: [{ required: true, validator: validateChecked, trigger: 'change' }],
        psnSealChecked: [{ required: true, validator: validateChecked, trigger: 'change' }]
      },
      sealData: {}
    };
  },
  created() {
    this.querySeal();
  },
  methods: {
    async querySeal() {
      try {
        let { data } = await querySeal({ orgCode: this.orgCode });
        this.sealData = data || {};
        this.sealData.orgSealBase64 = data && data.orgSealBlob ? this._images(data.orgSealBlob) : null;
        this.sealData.psnSealBase64 = data && data.psnSealBlob ? this._images(data.psnSealBlob) : null;
      } catch (e) {
        throw new Error(e);
      }
    },
    _images(item) {
      return item ? `data:image/png;base64,${item}` : null
    },
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleAction(action) {
      if (action === 'confirm') {
        this.$refs['form'].validate(async valid => {
          if (valid) {
            let data = {};
            if (this.form.orgSealChecked) {
              let orgSeal = {
                orgSealId: this.sealData.orgSealId,
                orgAccountId: this.sealData.orgAccountId,
                orgSealBlob: this.sealData.orgSealBlob,
                orgSealBase64: this.sealData.orgSealBase64
              };
              Object.assign(data, orgSeal);
            }
            if (this.form.psnSealChecked) {
              let psnSeal = {
                psnSealId: this.sealData.psnSealId,
                psnAccountId: this.sealData.psnAccountId,
                psnSealBlob: this.sealData.psnSealBlob,
                psnSealBase64: this.sealData.psnSealBase64
              };
              Object.assign(data, psnSeal);
            }
            this.handleConfirm(data);
          }
        })
      } else {
        this.handleCancel();
      }
    }
  }
};
</script>

<style lang="scss">
.seal-dialog {
  .el-dialog__body {
    padding: 0 20px;
  }

  .seal-select-box {
    .el-checkbox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row-reverse;
    }
  }
}
</style>
