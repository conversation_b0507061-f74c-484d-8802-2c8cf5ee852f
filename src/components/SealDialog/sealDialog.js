import Vue from 'vue';
import sealVue from './index.vue';

const SealDialogConstructor = Vue.extend(sealVue);

const SealDialog = (options = {}) => {
  const defaults = {
    sealType: 0, // 查询印章类型 0全部 1企业印章，2法人章
    orgCode: null
  };

  for (let key in defaults) {
    defaults[key] = options[key] || defaults[key];
  }
  let instance = new SealDialogConstructor({
    data: defaults,
    methods: {
      handleConfirm(data) {
        options.handleConfirm(data) && options.handleConfirm(data).call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default SealDialog;
