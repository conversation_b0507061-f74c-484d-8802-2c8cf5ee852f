<template>
  <div></div>
</template>

<script>
import SockJs from 'sockjs-client';
import Stomp from 'stompjs';
import { getToken } from '@/utils/auth';

export default {
  name: '<PERSON>Mq',
  props: {
    userId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      text: '',
      client: null,
      MQTT_topic: '',
      reconInv: null
    }
  },
  beforeRouteLeave(to, from, next) {
    this.onStop();
    clearTimeout(this.reconInv);
    next();
  },
  mounted() {
    this.MQTT_topic = `/queue/${this.userId}`;
    this.initClient();
  },
  beforeDestroy() {
    this.onStop();
    clearTimeout(this.reconInv);
  },
  methods: {
    initClient() {
      if (this.client == null || !this.client.connected) {
        let ws = new SockJs(`${process.env.VUE_APP_BASE_API}/websocket/stomp`);
        this.client = Stomp.over(ws);
        this.client.heartbeat.outgoing = 20000; // 发送频率
        this.client.heartbeat.incoming = 0; // 接收频率
        this.connect();
      }
    },
    connect() {
      // 连接服务器
      this.client.connect({ 'Admin-Token': getToken() }, this.onConnected, this.onError);
    },
    // 定义连接成功回调函数
    onConnected(frame) {
      console.log('服务端 Socket 连接建立');
      // console.log('onConnected: ' + frame);
      this.client.subscribe(this.MQTT_topic, this.onMessage)
    },
    // 定义连接失败回调函数
    onError(frame) {
      // 错误信息
      console.log('Socket 连接失败');
      console.log('Failed: ' + frame)
      // 10s后重新连接一次
      this.reconInv = setTimeout(() => {
        this.initClient();
      }, 10000)
    },
    onMessage(frame) {
      // 接收消息
      // console.log('接收消息frame：')
      this.$emit('triggerMsg', frame.body)
      // frame.ack();
    },
    onSend() {
      // 发送消息
      this.client.send(this.MQTT_topic, {}, this.text);
    },
    onStop() {
      // 关闭消息
      if (!this.client) {
        this.client.disconnect();
      }
      this.client = null;
      // this.socket.close();
    }
  }
}
</script>

<style scoped>

</style>
