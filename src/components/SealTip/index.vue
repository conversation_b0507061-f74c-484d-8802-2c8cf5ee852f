<template>
  <el-dialog
    center
    title="请在公告中需要盖公章的位置写上：（盖章）"
    top="5vh"
    width="90%"
    @closed="closedDialog"
    custom-class="maxW1200 seal-tip-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <img width="100%" src="../../assets/images/example_seal.png" alt="">
    <div slot="footer" class="dialog-footer">
      <el-button @click="isDialog=false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      isDialog: false,
      data: ''
    }
  },
  methods: {
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleConfirm() {

    },
    handleCancel() {

    }
  }
}
</script>

<style lang="scss">
.seal-tip-dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
