<template>
  <el-dialog
    :title="dialogTitle"
    width="600px"
    @closed="closedDialog"
    custom-class="tip-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
  >
    <div :class="['icon-box',type && !iconClass ? `icon-${ type }` : '' ]" v-if="showIcon">
      <i :class="iconClass" v-if="iconClass"></i>
      <i :class="typeClass" v-else></i>
    </div>
    <p class="tip-title" :style="{color: tipTitleColor,fontSize: tipTitleFontSize}" v-if="tipTitle" v-html="tipTitle"></p>
    <p class="tip-text" :style="{color: tipTextColor,fontSize: tipTextFontSize}" v-if="tipText" v-html="tipText"></p>
    <p class="tip-sub-text" :style="{color: tipSubColor,fontSize: tipSubFontSize}" v-if="tipSubText" v-html="tipSubText"></p>
    <div slot="footer" class="dialog-footer text-center">
      <el-button @click="isDialog = false">{{ btnText }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
const typeMap = {
  success: 'success',
  info: 'info',
  warning: 'warning',
  error: 'error'
};
export default {
  name: 'Index',
  data() {
    return {
      isDialog: false,
      type: 'info',
      iconClass: '',
      showIcon: true,
      dialogTitle: '提示',
      tipTitle: '',
      tipText: '',
      tipSubText: '',
      btnText: '关闭',
      tipTitleColor: '',
      tipTextColor: '',
      tipSubColor: '',
      tipTitleFontSize: '22px',
      tipTextFontSize: '16px',
      tipSubFontSize: '16px'
    }
  },
  computed: {
    typeClass() {
      return this.type && !this.iconClass ? `el-icon-${typeMap[this.type]}` : '';
    }
  },
  methods: {
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    }
  }
}
</script>
<style lang="scss" scoped>
.icon-info {
  color: $colorInfo;
}

.icon-success {
  color: $colorSuccess;
}

.icon-warning {
  color: $colorWarning;
}

.icon-danger {
  color: $colorDanger;
}
</style>
<style lang="scss">
.tip-dialog {
  .tip-title {
    text-align: center;
    margin-bottom: 20px;
  }

  .tip-text {
    text-align: center;
  }

  .tip-sub-text {
    text-align: center;
    margin-top: 10px;
  }

  .icon-box {
    text-align: center;
    margin-bottom: 20px;

    i {
      font-size: 60px;
    }
  }

  .el-dialog__body {
    min-height: 150px;
    padding-top: 0;
    padding-bottom: 40px;
  }
}
</style>
