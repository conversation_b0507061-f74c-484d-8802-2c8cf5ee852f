import Vue from 'vue'
import tipView from './index.vue';

const TipDialogConstructor = Vue.extend(tipView); // 构造函数

const TipDialog = (options = {}) => {
  if (typeof options === 'string') {
    options = {
      tipText: options
    }
  }

  const instance = new TipDialogConstructor({
    data: options
  });

  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default TipDialog
