<template>
  <ul class="pwd-group clearfix">
    <li class="pwd-item fl" v-for="item in pwdLength" :key="item">
      <pwd-item
        :disabled="disabled"
        :defaultVal="pwdData[item-1]"
        :index="item"
        :isFocus="item===focusIndex"
        @deletePwd="deletePwd"
        @setPwd="setPwd"
      >
      </pwd-item>
    </li>
  </ul>
</template>

<script>
import pwdItem from './pwdItem';

export default {
  name: 'Index',
  components: {
    pwdItem
  },
  model: {
    prop: 'modelVal', // 指向props的参数名
    event: 'change'// 事件名称
  },
  props: {
    modelVal: {
      type: String,
      default: ''
    },
    pwdLength: {
      type: Number,
      default: 6
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pwdData: new Array(this.pwdLength),
      focusIndex: 1
    }
  },
  watch: {
    pwdLength: {
      deep: true,
      immediate: true,
      handler() {
        for (let i = 0; i < this.pwdLength; i++) {
          this.$set(this.pwdData, i, '');
        }
      }
    },
    pwdData: {
      deep: true,
      immediate: true,
      handler() {
        const password = this.pwdData.join('');
        this.$emit('change', password);
      }
    }
  },
  methods: {
    setPwd(val, index) {
      this.$set(this.pwdData, index - 1, val);
      this.focusIndex = index === this.pwdLength ? index : index + 1;
    },
    deletePwd(index) {
      this.$set(this.pwdData, index - 1, '');
      this.focusIndex = index === 1 ? 1 : index - 1;
    }
  }
}
</script>

<style lang="scss" scoped>
  .pwd-group {
    display: inline-block;

    .pwd-item {
      & + .pwd-item {
        margin-left: 10px;
      }
    }
  }
</style>

