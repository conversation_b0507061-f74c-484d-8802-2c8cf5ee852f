<template>
  <el-input
    :style="{'--theme': theme}"
    :disabled="disabled"
    class="pwd-input"
    type="text"
    v-model="pwd"
    ref="pwdItem"
    @keyup.delete.native="deletePwd"
  >
  </el-input>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'PwdItem',
  props: {
    index: {
      type: Number,
      default: 1
    },
    isFocus: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pwd: ''
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ])
  },
  watch: {
    pwd: {
      handler() {
        this.pwd = this.pwd.replace(/[^\d]/, '');
        this.pwd = this.pwd.replace(/\d(\d)/, '$1');
        if (this.pwd) {
          this.$emit('setPwd', this.pwd, this.index);
        } else {
          this.$refs['pwdItem'].focus();
        }
      }
    },
    isFocus: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          if (this.isFocus) {
            this.$refs['pwdItem'].focus();
          }
        })
      }
    }
  },
  methods: {
    deletePwd() {
      if (!this.pwd) {
        this.$emit('deletePwd', this.index);
      }
    }
  }
}
</script>

<style lang="scss">
  $pwdWidth: 60px;
  .pwd-input {
    width: $pwdWidth;

    .el-input__inner {
      text-align: center;
      height: $pwdWidth;
      line-height: $pwdWidth;
      font-size: 30px;
      color: #{'var(--theme)'};
      border-radius: 0;
    }
  }
</style>
