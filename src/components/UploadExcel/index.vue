<template>
  <div class="upload-wrap">
    <el-button type="primary" :size="size" @click="showDialog">{{ btnText }}</el-button>
    <el-dialog
      :title="btnText"
      :visible.sync="isDialog"
      @closed="closedDialog"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-upload
        ref="excelUpload"
        :limit="1"
        accept=".xlsx, .xls"
        action="#"
        :on-remove="removeFile"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        :http-request="uploadSectionFile"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <p style="margin-bottom: 5px;">仅允许导入 .xlsx, .xls 格式文件，大小不超过 {{ fileSize }} MB。</p>
          <p style="margin-bottom: 5px;">请使用本系统提供的 Excel 模版，其它格式模版系统无法识别！</p>
          <el-link
            type="primary"
            :underline="false"
            style="font-size:14px;vertical-align: baseline;"
            @click="handleDownload"
          >下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'

export default {
  name: 'UploadExcel',
  props: {
    defaultHeader: {
      type: Array,
      default: () => []
    },
    size: {
      type: String,
      default: 'medium'
    },
    fileSize: {
      type: [String, Number],
      default: 10
    },
    btnText: {
      type: String,
      default: '导入Excel表格'
    }
    // onSuccess: Function
  },
  data() {
    return {
      loading: false,
      isDialog: false,
      rawFile: null,
      excelData: {
        header: null,
        results: null,
        data: []
      },
      downloadLoading: false
    }
  },
  methods: {
    handleDownload() {
      this.downloadLoading = true;
      import('@/utils/export2Excel').then(excel => {
        let tHeader = this.defaultHeader.map(item => item.label);
        excel.export_json_to_excel({
          header: tHeader,
          data: [],
          filename: '模板'
        })
        this.downloadLoading = false;
      }).catch((e) => {
        this.downloadLoading = false;
        throw new Error(e);
      })
    },
    showDialog() {
      this.rawFile = null;
      this.isDialog = true;
    },
    closedDialog() {
      this.$refs.excelUpload.clearFiles();
      this.rawFile = null;
      this.isDialog = false
    },
    submit() {
      if (!this.rawFile) {
        this.$message.error(`请选择文件`)
        return
      }
      this.readerData(this.rawFile)
    },
    removeFile() {
      this.rawFile = null;
    },
    handleExceed() {
      this.$message.error(`最多上传1个文件`)
    },
    beforeUpload(file) {
      if (!this.isExcel(file)) {
        this.$message.error('是能上传 .xlsx, .xls 格式的文件')
        return false
      }
      if (file.size <= 0) {
        this.$message.error('上传文件大小不能为空')
        return false
      }
    },
    uploadSectionFile(param) {
      this.rawFile = param.file;
    },
    readerData(rawFile) {
      this.loading = true
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const header = this.getHeaderRow(worksheet)
          const results = XLSX.utils.sheet_to_json(worksheet)
          this.generateData({ header, results })
          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },
    generateData({ header, results }) {
      this.excelData.header = header;
      this.excelData.results = results;
      this.excelData.data = [];
      // 匹配数据
      if (this.defaultHeader.length > 0) {
        if (!this.validateHeader(header)) {
          this.$message.error('数据格式不一致，无法导入，请检查模板是否正确')
          return
        }
        let data = [];
        results.forEach(item => {
          let obj = {};
          this.defaultHeader.forEach(v => {
            obj[v.key] = item[v.label] || null;
          })
          data.push(obj)
        })
        this.excelData.data = data;
      }
      this.isDialog = false;
      // this.onSuccess && this.onSuccess(this.excelData)
      this.$emit('onSuccess', this.excelData)
    },
    validateHeader(header) {
      let isValidate = true;
      let list = this.defaultHeader.map(item => item.label);
      if (list.length !== header.length) {
        isValidate = false;
      }
      header.forEach(item => {
        if (list.every(v => v !== item)) {
          isValidate = false;
        }
      })
      return isValidate
    },
    isExcel(file) {
      return /\.(xlsx|xls)$/.test(file.name)
    }
  }
}
</script>

<style lang="scss">
.upload-wrap {
  display: inline-block;
}

.upload-btn {

  line-height: inherit;
  display: inline-block;

  .el-upload-list {

    .el-upload-list__item {
      margin-top: 0;
    }
  }
}

.el-button + .upload-wrap {
  margin-left: 10px;
}

.upload-wrap + .el-button {
  margin-left: 10px;
}
</style>
