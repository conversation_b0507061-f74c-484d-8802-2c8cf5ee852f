<template>
  <el-dialog
    width="400px"
    custom-class="tip-progress-dialog"
    :visible.sync="isDialog"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :destroy-on-close="true"
    :fullscreen="true"
    :modal="false"
  >
    <div style="text-align: center">
      <el-progress
        type="circle"
        :percentage="percentage"
        text-color="#409eff"
      >
      </el-progress>
      <div
        class="mt10 progress-text"
        v-if="percentage<100"
      >
        文件上传中，请稍候...
      </div>
      <div
        class="mt10 progress-text"
        v-if="percentage===100"
      >
        文件上传完成，正在解析中...
      </div>
    </div>

  </el-dialog>
</template>

<script>
export default {
  name: 'TipProgress',
  props: {
    percentage: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isDialog: false
    }
  },
  methods: {
    show() {
      this.isDialog = true
    },
    close() {
      this.isDialog = false
    }
  }
}
</script>
<style lang="scss">
.tip-progress-dialog {
  background: rgba(0, 0, 0, 0.7);
  box-shadow: none;

  .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog__body {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-text {
    color: #409eff;
  }
}
</style>
