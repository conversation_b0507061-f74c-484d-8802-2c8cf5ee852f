<template>
  <ul class="tab-wrap" :class="classObj">
    <li
      v-for="(item,index) in tabArray"
      :key="index"
      :class="{
        'nav-item':true,
        'tab-active':currentValue===item[defaultOption.value]
      }"
      :style="{'--theme': theme, fontSize}"
      @click="handleTabClick(item[defaultOption.value],item)"
    >
      {{ item[defaultOption.label] }}
      <slot :itemValue="item[defaultOption.value]"></slot>
    </li>
  </ul>
</template>

<script>
import { isEmpty } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  props: {
    value: {},
    option: {
      type: Object,
      default: () => {
      }
    },
    tabArray: {
      type: Array,
      default: () => []
    },
    fontSize: {
      type: String,
      default: '14px'
    }
  },
  data() {
    return {
      isEmpty,
      currentValue: this.value,
      defaultOption: {
        value: 'value',
        label: 'name'
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  watch: {
    value(value) {
      this.setCurrentValue(value)
    },
    option: {
      deep: true,
      immediate: true,
      handler() {
        this._.assign(this.defaultOption, this.option)
      }
    }
  },
  created() {
    this.setCurrentValue(this.value)
  },
  methods: {
    handleTabClick(value, item) {
      this.setCurrentValue(value)
      this.$emit('tab-click', value, item)
    },
    setCurrentValue(value) {
      this.currentValue = value
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-wrap {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  box-sizing: border-box;
  list-style: none;

  .nav-item {
    line-height: 2;
    padding: 0 20px;
    cursor: pointer;
    box-sizing: border-box;
    text-align: center;
    position: relative;

    &.tab-active {
      background-repeat: no-repeat;
      background-size: cover;
      position: relative;
      color: #{'var(--theme)'};
    }
  }
}

.mobile.tab-wrap {
  .nav-item {
    font-size: 14px !important;
    padding: 0 10px;
  }
}
</style>
