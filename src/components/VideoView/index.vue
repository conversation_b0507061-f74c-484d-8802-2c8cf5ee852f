<template>
  <el-dialog
    title="视频"
    width="90%"
    @closed="closedDialog"
    custom-class="maxW1100 video-dialog"
    :visible.sync="isDialog"
    top="2vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <video-player
      :video-url="videoUrl"
    ></video-player>
  </el-dialog>
</template>

<script>

export default {
  name: 'Index',
  data() {
    return {
      isDialog: false,
      videoUrl: null
    }
  },
  methods: {
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleConfirm() {

    },
    handleCancel() {

    }
  }
}
</script>

<style lang="scss">
.video-dialog {
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>
