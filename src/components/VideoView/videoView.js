import Vue from 'vue'
import VideoView from './index.vue';

const VideoViewConstructor = Vue.extend(VideoView); // 构造函数

const VideoViewDialog = (options = {}) => {
  let videoUrl = null;
  if (typeof options === 'string') {
    videoUrl = options
  }

  if (options.type === 'blob') {
    const blob = new Blob([options.data], { 'type': 'application/mp4' });
    videoUrl = URL.createObjectURL(blob);
  } else {
    videoUrl = options.data;
  }

  const instance = new VideoViewConstructor({
    data: {
      videoUrl
    },
    methods: {
      handleConfirm() {
        options.handleConfirm && options.handleConfirm.call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default VideoViewDialog
