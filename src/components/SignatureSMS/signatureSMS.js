import Vue from 'vue';
import SignatureSMSVue from './index.vue';

const SignatureSMSConstructor = Vue.extend(SignatureSMSVue);

const SignatureSMS = (options = {}) => {
  const defaults = {
    title: '签章短信验证', // 默认内容
    phoneNumber: '', // 手机号
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  };

  for (let key in defaults) {
    defaults[key] = options[key] || defaults[key];
  }
  let instance = new SignatureSMSConstructor({
    data: defaults,
    methods: {
      handleConfirm(data) {
        options.handleConfirm(data) && options.handleConfirm(data).call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default SignatureSMS;
