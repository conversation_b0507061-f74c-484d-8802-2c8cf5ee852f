<template>
  <el-dialog
    :title="title"
    width="90%"
    @closed="closedDialog()"
    custom-class="maxW400 sms-dialog"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form :model="form" :rules="rules" ref="form" label-position="top" class="sms-main">

      <el-form-item prop="phoneNumber">
        <el-input
          v-model.trim="form.phoneNumber"
          type="text"
          auto-complete="off"
          placeholder="手机号码"
        >
          <svg-icon slot="prefix" icon-class="phone"/>
        </el-input>
      </el-form-item>

      <el-form-item prop="authCode">
        <el-input
          v-model.trim="form.authCode"
          auto-complete="off"
          placeholder="短信验证码"
          style="width: 63%"
        >
          <svg-icon slot="prefix" icon-class="validCode"/>
        </el-input>
        <div class="sms-code">
          <el-button
            type="primary"
            @click="getSmsCode"
            :disabled="!isVerifyPhone||!isClickCode"
            style="width: 100%;"
          >{{ codeBtnValue }}
          </el-button>
        </div>
      </el-form-item>

    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleAction('cancel')">{{ cancelButtonText }}</el-button>
      <el-button type="primary" size="medium" @click="handleAction('confirm')">{{ confirmButtonText }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

import reg from '@/utils/reg'
import { getSignatureCode } from '@/api/sealManage'

export default {
  data() {
    return {
      isDialog: false,
      title: '请输入',
      phoneNumber: '',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      form: {
        phoneNumber: '',
        authCode: ''
      },
      rules: {
        phoneNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
          { pattern: reg.cellphone, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        authCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      },
      codeBtnValue: '获取验证码',
      countdown: 60,
      isClickCode: true,
      timer: null,
      flowId: null
    };
  },
  created() {
    this.form.phoneNumber = this.phoneNumber;
  },
  computed: {
    isVerifyPhone() {
      return reg.cellphone.test(this.form.phoneNumber)
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    async getSmsCode() {
      try {
        let { data } = await getSignatureCode({ phone: this.form.phoneNumber });
        this.flowId = data;
        this.$modal.msgSuccess('验证码发送成功')
        this.countdown = 60;
        this.intervalFun();
        this.timer = setInterval(() => {
          if (this.countdown === 0) {
            this.codeBtnValue = '获取验证码';
            this.isClickCode = true;
            clearInterval(this.timer);
          } else {
            this.intervalFun();
          }
        }, 1000);
      } catch (e) {
        throw new Error(e);
      }
    },
    intervalFun() {
      this.codeBtnValue = '重新发送(' + this.countdown + ')';
      this.isClickCode = false;
      this.countdown--;
    },
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleAction(action) {
      if (action === 'confirm') {
        this.$refs['form'].validate(async valid => {
          if (valid) {
            this.handleConfirm({ ...this.form, flowId: this.flowId });
          }
        })
      } else {
        this.handleCancel();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.sms-main {
  .sms-code {
    width: 33%;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
</style>
