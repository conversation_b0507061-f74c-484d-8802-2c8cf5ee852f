import Vue from 'vue'
import CanvasSign from './index.vue';

const CanvasSignConstructor = Vue.extend(CanvasSign); // 构造函数

const CanvasSignDialog = (options = {}) => {
  let defaultData = {
    title: '手写签名',
    confirmBtnText: '确定'
  };

  for (let key in defaultData) {
    if (key === 'showConfirmBtn' || key === 'showCancelBtn') {
      defaultData[key] = options[key]
    } else {
      defaultData[key] = options[key] || defaultData[key];
    }
  }

  const instance = new CanvasSignConstructor({
    data: defaultData,
    methods: {
      handleConfirm(data) {
        options.handleConfirm(data) && options.handleConfirm(data).call(this);
        instance.vm.isDialog = false;
      },
      handleCancel() {
        options.handleCancel && options.handleCancel.call(this);
        instance.vm.isDialog = false;
      }
    }
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.isDialog = true;
  return instance.vm
};

export default CanvasSignDialog
