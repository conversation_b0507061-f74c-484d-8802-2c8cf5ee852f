<template>
  <el-dialog
    :title="title"
    @closed="closedDialog"
    custom-class="pdf-canvas-dialog"
    :fullscreen="true"
    :visible.sync="isDialog"
    :close-on-click-modal="false"
  >
    <div class="canvas-wrap">
      <vue-esign
        ref="esign"
        :width="800"
        :height="400"
        :isCrop="isCrop"
        :lineWidth="lineWidth"
        :lineColor="lineColor"
        :bgColor.sync="bgColor"/>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleReset">重置</el-button>
      <el-button type="primary" size="medium" @click="handleGenerate">{{ confirmBtnText }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import vueEsign from 'vue-esign'
export default {
  name: 'Index',
  components: { vueEsign },
  data() {
    return {
      isDialog: false,
      title: '手写签名',
      confirmBtnText: '确定',
      lineWidth: 4,
      lineColor: 'red',
      bgColor: '',
      isCrop: true
    }
  },
  methods: {
    closedDialog() {
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el)
    },
    handleConfirm(data) {

    },
    handleCancel() {

    },
    handleReset() {
      this.$refs.esign.reset()
    },
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        this.handleConfirm(res)
      }).catch(() => {
        // 画布没有签字时会执行这里 'Not Signned'
        this.$message.warning('请签名')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.canvas-wrap {
  background-color: #FFFFFF
}
</style>
<style lang="scss">
.pdf-canvas-dialog {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    flex: 1;
    background-color: #f6f6f6;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
