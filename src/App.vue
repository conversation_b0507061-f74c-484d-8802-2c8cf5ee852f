<template>
  <div id="app">
    <router-view/>

    <chat-message v-if="catShow==='1'"></chat-message>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import watermark from 'watermark-dom';
import ChatMessage from '@/components/ChatMessage/ChatMessage';

export default {
  name: 'App',
  components: {
    ChatMessage
  },
  data() {
    return {
      catShow: null
    }
  },
  created() {
    console.log(process.env)
    // 在页面加载时读取sessionStorage里的状态信息
    if (sessionStorage.getItem('state')) {
      this.$store.replaceState(Object.assign({}, this.$store.state,
        JSON.parse(sessionStorage.getItem('state'))))
    }
    // 页面刷新时将state数据存储到sessionStorage中
    window.addEventListener('beforeunload', () => {
      sessionStorage.setItem('state', JSON.stringify(this.$store.state))
    })

    window.addEventListener('storage', (e) => {
      if (e.key === 'loginOrgCode' || e.key === 'loginOrgName') {
        location.href = '/index';
      }
    })

    this.getConfigKey('sys.chatMsg.show').then(response => {
      this.catShow = response.data;
    })

    this.getConfigKey('sys.signature.off').then(response => {
      this.$store.dispatch('setSignatureOff', response.data)
    });
  },
  computed: {
    ...mapGetters([
      'wsMsg',
      'nickName'
    ])
  },
  watch: {
    wsMsg: {
      handler(val) {
        console.log(val)
        // 刷新 REFRESH, 通知 NOTIFY
        if (val && val.displayEnums.includes('NOTIFY')) {
          let html = `<div>
                        <p style="display: flex;margin-bottom: 3px;">
                          <span style="flex-shrink:0;">【来源】</span>
                          <span style="flex: 1;">${val.fromUserName}</span>
                        </p>
                        <p style="display: flex;margin-bottom: 3px;">
                          <span style="flex-shrink:0;">【时间】</span>
                          <span style="flex: 1;">${this.formatTime(val.date)}</span>
                        </p>
                        <p style="display: flex;">
                          <span style="flex-shrink:0;">【内容】</span>
                          <span style="flex: 1;">${val.msg}</span>
                        </p>
                      </div>`
          this.$notify({
            title: '重要通知',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            message: html,
            position: 'bottom-right',
            customClass: 'msg-notify',
            duration: 10000
          });
        }
      },
      deep: true
    },
    nickName: {
      handler(val) {
        this.$nextTick(() => {
          if (this.nickName) {
            watermark.load({
              watermark_txt: this.nickName + ' ' + new Date().toLocaleString(),
              watermark_width: 300,
              watermark_alpha: 0.08,
              watermark_fontsize: '16px',
              watermark_height: 160
            });
          } else {
            let watermark_element = document.getElementById('wm_div_id');
            if (watermark_element) {
              watermark.remove();
            }
          }
        })
      },
      deep: true
    }
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
}
</script>
