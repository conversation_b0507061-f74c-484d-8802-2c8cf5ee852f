<template>
  <ul class="clearfix tab-wrap">
    <li :style="{'--theme': theme}" class="tab-li" v-for="(item,index) in stepList" :key="index">
      <div
        :class="{'tab-item':true, 'tab-active':hasActive(item.path),'tab-disable':item.disabled}"
        @click="handleTabClick(index,item)"
      >
        <span v-if="showIndex" class="tab-num">{{ index + 1 }}</span>
        {{
          getPurchaseDict(purchaseMethodCode, item.dictKey) === item.dictKey ? item.label : getPurchaseDict(purchaseMethodCode, item.dictKey)
        }}
      </div>
    </li>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  props: {
    stepList: {
      type: Array,
      default: () => []
    },
    showIndex: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'purchaseMethodCode',
      'getPurchaseDict'
    ])
  },
  methods: {
    hasActive(currentRoute) {
      const route = this.$route
      const { path } = route
      return currentRoute === path
    },
    handleTabClick(index, item) {
      if (item.disabled) return
      this.$emit('step-click', item)
    }
  }
}
</script>

<style lang="scss" scoped>
$colorFalse: #999999;
.tab-wrap {
  background-color: #FFFFFF;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .tab-li {
    margin-right: 2%;
    position: relative;
    margin-bottom: 12px;

    &:last-child {
      margin-right: 0;
    }
  }

  .tab-item {
    font-size: 14px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
    padding: 0 15px;
    border-radius: 5px;
    box-sizing: border-box;
    color: #{'var(--theme)'};
    border: 1px solid #{'var(--theme)'};

    &.tab-disable {
      color: $colorFalse;
      border: 1px solid $colorFalse;

      .tab-num {
        border: 1px solid $colorFalse;
      }
    }

    &.tab-active {
      color: #FFFFFF;
      background-color: #{'var(--theme)'};
      border: 1px solid #{'var(--theme)'};
      position: relative;

      &:after {
        content: "";
        width: 0;
        height: 0;
        display: block;
        border-style: solid;
        border-width: 12px 10px 0;
        border-color: #{'var(--theme)'} transparent transparent;
        position: absolute;
        top: 34px;
        left: 50%;
        transform: translateX(-50%);
      }

      .tab-num {
        border: 1px solid #FFFFFF;
      }
    }
  }

  .tab-num {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    border: 1px solid #{'var(--theme)'};
    border-radius: 50%;
    font-size: 12px;
    text-align: center;
  }
}
</style>
