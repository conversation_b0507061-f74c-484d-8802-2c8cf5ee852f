<template>
  <div class="app-container" style="position: relative;">
    <div class="change-view" v-el-drag>
      <el-tooltip class="item" effect="dark" content="切换视图" placement="bottom-end">
        <el-button circle type="success" icon="el-icon-set-up" class="el-drag" @click="changeView"></el-button>
      </el-tooltip>
    </div>
    <h3 :style="{color: theme, fontStyle:'16px'}" class="text-center over-ellipsis">{{ buyItemName }}</h3>

    <div class="text-right pr35" v-if="checkPermi(['purchaser:project:complete'])">
      <el-button
        type="warning"
        size="small"
        @click="clickComplete"
      >
        项目完成
      </el-button>
    </div>

    <process-nav
      class="process-nav"
      :stepList="processNode"
      :showIndex="true"
      @step-click="stepClick"
    >
    </process-nav>

    <process-nav
      class="process-nav"
      style="justify-content: flex-end;"
      :stepList="processNotNode"
      @step-click="stepClick"
    >
    </process-nav>

    <section style="position: relative;">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key"/>
      </transition>
    </section>
  </div>
</template>

<script>
import { ProcessNav } from './components'
import { mapGetters } from 'vuex'
import elDrag from '@/directive/el-drag';
import { checkPermi } from '@/utils/permission'
import { clickComplete } from '@/api/purchaser/projectList'

export default {
  name: 'Index',
  directives: { elDrag },
  components: {
    ProcessNav
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'theme',
      'processPage',
      'buyItemName',
      'buyItemCode',
      'subpackageCode',
      'wsMsg',
      'showView'
    ]),
    key() {
      return this.$route.path
    },
    processNode() {
      let list = this.processPage.filter(item => !!item.ranking);
      return this._.orderBy(list, ['ranking', 'sort'], ['asc', 'asc']);
    },
    processNotNode() {
      let list = this.processPage.filter(item => !item.ranking);
      return this._.orderBy(list, 'sort', 'asc');
    }
  },
  watch: {
    wsMsg: {
      handler(val) {
        console.log('***********', val)
        if (val && val.displayEnums.includes('REFRESH') && val.businessTypeEnum === 'SUPPLIER_PROCESS_UPDATE') {
          console.log('刷新process/setProcessNodes')
          this.$store.dispatch('process/setProcessNodes', {
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode,
            belongRole: '1'
          })
        }
      },
      deep: true
    }
  },
  created() {
    this.$store.dispatch('process/setProcessNodes', {
      buyItemCode: this.buyItemCode,
      subpackageCode: this.subpackageCode,
      belongRole: '1'
    });
  },
  methods: {
    checkPermi,
    clickComplete() {
      this.$confirm('<p>是否确定完成该项目？</p>', '提示', {
        customClass: 'max-tip',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(async () => {
        try {
          this.$modal.loading('数据提交中，请稍候...');
          await clickComplete(this.buyItemCode);
          this.$modal.msgSuccess('操作成功');
          this.$modal.closeLoading()
        } catch (e) {
          this.$modal.closeLoading()
          throw new Error(e);
        }
      })
    },
    stepClick(item) {
      this.$router.push({ path: item.path })
    },
    changeView() {
      this.$store.dispatch('process/setShowView', !this.showView)
    }
  }
}
</script>

<style lang="scss" scoped>
.process-nav {
  margin: 20px auto 20px;
}

.change-view {
  box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.4);
  position: fixed;
  z-index: 9999999;
  top: 60px;
  right: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
