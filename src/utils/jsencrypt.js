import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'
import CryptoJS from 'crypto-js';
import CryptoU8array from '@/utils/crypto-en'
import { getUUID } from '@/utils/index';

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\n' +
  'nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

const privateKey = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\n' +
  '7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\n' +
  'PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\n' +
  'kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\n' +
  'cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\n' +
  'DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\n' +
  'YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\n' +
  'UP8iWi1Qw0Y='

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

// 接口请求数据加密公钥
const apiPublicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC3Lb0O4zgEakDfJ4XJO5zadXep9bQeWyJ6pa0e328PYQYZgLNP7eVrAP7mVZgG+8D4MicIcStTQnBxF8AEyJKrh/M/3WSSK2zDvrZn1paWf4SA8zFIn5cuYlcUH+WuxghQn3kKRUW2qtBY9eaGF5qntascctNgQTHmW3eqQzDBQIDAQAB';

// 接口响应数据解密私钥
const apiPrivateKey = 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALROqKeWuu+G6z6V7lesaAIC8FWWJ8qYRRy4HbbakJBH+OEWfD+0/MmMnZ28aMiV3qDy34SLfddDxvWJo/SR8iL8bjeqOEQxenu8Ogec+290w4F8IW6Ips/kZ5pnkg/TUn1GATOSV+RbB90okuykbBEbGKaNqGczJ/lI7RpfNvCpAgMBAAECgYA9RzJYaoizmRXgGlJ7Z3Odo2QMolB5sRBj90rZ9yQEdQFndh3aBOeYk/qJPhwad5zG9GP0hvfIrhczIYkgOG2i1ZvBAFBP7IZiGJz5PxS9QOFPg926sI6Mv3nBIS0+U88IyzPL/fQWNvhc3b9Y95kYp4p0Wk4zzNe9HNNUMQHdUQJBAOwA6EoVSlxlpNivoAGrMynLlnHmZ7fEpXXQINUbhpX8+I3fazoWcRaYpfLmVKa82DJXHUe8URFX3oir3kAocVUCQQDDlahWFmYmtNYqLitJdIdltTcmQtAgHlfshdYnq6Gg8jSjwh40sXF8MgZfG03+sfdmKbSG3e+7Ihb/X5P/odIFAkEAlz3Rn0BbojDlXpPWN5uOMzesFxwv1Z3o50JU+B0mt9IhO1I1dklRecijeLFRCHW3GzOmqQUu8q1cCDwUNwtz7QJBAJ3BT8coR/q+b+QT20xjVnaeBT6yM2dEskyP4x2aXUMROY5Am9aKrWuseeEqh+2ApHld+EO0LZJ2O7B96kUNw/UCQHhXTTBHc2HkyU84U2+OAB2hJtJBmj+eGl0iqNfOq3JyiIemC/bV74sASLa+NN9CJRotBh9jzmzNpwEi24Y8KHE=';

// 接口请求数据加密
export function encryptApi(txt) {
  let key = getUUID(16, 16);

  // 将秘钥转换为utf8格式
  let keyHex = CryptoJS.enc.Utf8.parse(key);
  let srcs = CryptoJS.enc.Utf8.parse(txt);
  // 加密模式为ECB，补码方式为PKCS5Padding（也就是PKCS7）
  let encrypted = CryptoJS.AES.encrypt(srcs, keyHex, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  let content = encrypted.toString();

  // 加密key
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(apiPublicKey)
  let aesKey = encryptor.encrypt(key)

  return {
    'content': content,
    'aesKey': aesKey
  }
}

// 接口响应数据解密
export function decryptApi(content, aesKey) {
  // 解密key
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(apiPrivateKey)
  let key = encryptor.decrypt(aesKey)

  // 解密content
  // 将秘钥转换为utf8格式
  let keyHex = CryptoJS.enc.Utf8.parse(key);
  let decrypt = CryptoJS.AES.decrypt(content, keyHex, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  return JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt).toString())
}

// 使用 AES 进行文件加密
export function encryptFile(word) {
  let key = getUUID(16, 16);
  const keyHex = CryptoJS.enc.Utf8.parse(key); // 十六位十六进制数作为秘钥
  const messageWordArray = CryptoU8array.u8array.parse(word);
  let encrypted = CryptoJS.AES.encrypt(messageWordArray, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  let encryptedBytes = encrypted.ciphertext
  let content = CryptoU8array.u8array.stringify(encryptedBytes)

  // 加密key
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(apiPublicKey)
  let aesKey = encryptor.encrypt(key)

  return {
    'content': content,
    'aesKey': aesKey
  }
}

// 使用 AES 进行文件解密
export function decryptFile(word, key) {
  const messageWordArray = CryptoU8array.u8array.parse(word);
  let dcBase64String = messageWordArray.toString(CryptoJS.enc.Base64);
  let decrypt = CryptoJS.AES.decrypt(dcBase64String, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoU8array.u8array.stringify(decrypt)
}

export function encryptFileToBinary(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = () => {
      try {
        let fileContent = new Uint8Array(reader.result);
        let { content, aesKey } = encryptFile(fileContent);
        console.log('加密后串', content);
        let blobType = file.type
        let fileName = file.name
        let blob = new Blob([content], { type: blobType })
        let blobToFile = new File([blob], fileName, {
          type: blobType,
          lastModified: Date.now()
        })
        console.log('加密后文件', blobToFile);
        resolve({ file: blobToFile, aesKey })
      } catch (e) {
        reject(e)
      }
    }
    reader.onerror = () => {
      console.log('文件读取出错');
      reject('文件读取出错')
    }
    reader.onabort = () => {
      console.log('文件读取中断');
      reject('文件读取中断')
    }
  })
}

export function decryptToBinary(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    // 读取文件 转 ArrayBuffer
    reader.readAsArrayBuffer(file);
    reader.onload = () => {
      try {
        // 转 Uint8Array
        let fileContent = new Uint8Array(reader.result);
        // 解密
        let decryptContent = decryptFile(fileContent);
        console.log('解密后串', decryptContent);
        let blobType = file.type
        let fileName = file.name
        let blob = new Blob([decryptContent], { type: blobType })
        // let blobToFile = new File([blob], fileName, {
        //   type: blobType,
        //   lastModified: Date.now()
        // })
        // console.log('解密后文件', blobToFile);
        // resolve(blobToFile)
        resolve({ blob, fileName })
      } catch (e) {
        reject(e)
      }
    }
    reader.onerror = () => {
      console.log('文件读取出错');
      reject('文件读取出错')
    }
    reader.onabort = () => {
      console.log('文件读取中断');
      reject('文件读取中断')
    }
  })
}
