import { formatTime } from '@/utils/filters';
import { Base64 } from 'js-base64'
import pako from 'pako'
import { isArray } from '@/utils/validate'
import _ from 'lodash'

/**
 * 存储sessionStorage
 */
export const setStore = (name, content) => {
  if (!name) return
  if (typeof content !== 'string') {
    content = JSON.stringify(content)
  }
  sessionStorage.setItem(name, content)
}

/**
 * 获取sessionStorage
 */
export const getStore = name => {
  if (!name) return
  return sessionStorage.getItem(name)
}

/**
 * 删除sessionStorage
 */
export const removeStore = name => {
  if (!name) return
  sessionStorage.removeItem(name)
}

/**
 * 清空sessionStorage
 */
export const clearStore = () => {
  sessionStorage.clear()
}

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == '') return ''
  var date = new Date(cellValue)
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function parseTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return formatTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) {
      s++
    } else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * 生成uuid
 * @param {Number} len 长度
 * @param {Number} radix 基数(进制) 2 10 16
 * @returns {String}
 */
export function getUUID(len, radix) {
  let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  let uuid = [];
  let i;
  radix = radix || chars.length;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
  } else {
    // rfc4122, version 4 form
    let r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16;
        uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
      }
    }
  }

  return uuid.join('');
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase
    ? val => map[val.toLowerCase()]
    : val => map[val]
}

export const exportDefault = 'export default '

export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}

/**
 * 判断是否为空(包括空字符串、空格、null、undefined、{})
 * @param {string,number,Object} value
 * @return {Boolean}
 */
export function isEmpty(value) {
  // 先判断是否为null，返回true ，判断是否为空字符串，返回true
  if (value == null || typeof (value) == 'undefined' || value === undefined) {
    return true
  }
  // 空对象
  if (value instanceof Object) {
    if (JSON.stringify(value) == '{}') {
      return true
    }
  }
  // 判断空字符串、空格
  if ((value + '').replace(/(^\s*)|(\s*$)/g, '').length === 0) {
    return true
  }
  // 不为空返回false
  return false
}

/**
 * 阿拉伯数字转中文数字
 * @param {Number} num
 * @return {String}
 */
export function numToChinese(num) {
  if (!/^\d*(\.\d*)?$/.test(num)) {
    alert('Number is wrong!')
    return 'Number is wrong!'
  }
  let AA = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  let BB = ['', '十', '百', '千', '万', '亿', '点', '']
  let a = ('' + num).replace(/(^0*)/g, '').split('.')
  let k = 0
  let re = ''
  for (let i = a[0].length - 1; i >= 0; i--) {
    switch (k) {
      case 0:
        re = BB[7] + re
        break
      case 4:
        if (!new RegExp('0{4}\\d{' + (a[0].length - i - 1) + '}$').test(a[0])) {
          re = BB[4] + re
        }
        break
      case 8:
        re = BB[5] + re
        BB[7] = BB[5]
        k = 0
        break
    }
    if (k % 4 === 2 && a[0].charAt(i + 2) !== 0 && a[0].charAt(i + 1) === 0) re = AA[0] + re
    if (a[0].charAt(i) != 0) re = AA[a[0].charAt(i)] + BB[k % 4] + re
    k++
  }
  // 加上小数部分(如果有小数部分)
  if (a.length > 1) {
    re += BB[6]
    for (let i = 0; i < a[1].length; i++) re += AA[a[1].charAt(i)]
  }
  return re
}

/**
 * 压缩文件模板
 * @param {String} str
 * @return {String}
 */
export function toZip(str) {
  let zip = pako.gzip(str, { to: 'string' })
  return Base64.encode(zip)
}

/**
 * 将文件转为base64
 * @param {Blob} file 这个file参数 也就是文件信息
 * @return {Object}
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    let fileResult = ''
    reader.readAsDataURL(file)
    // 开始转
    reader.onload = function () {
      fileResult = reader.result
    }
    // 转 失败
    reader.onerror = function (error) {
      reject(error)
    }
    // 转 结束  咱就 resolve 出去
    reader.onloadend = function () {
      resolve(fileResult)
    }
  })
}

/**
 * 将文件转为Blob
 * @param {Blob} file 这个file参数 也就是文件信息
 * @return
 */
export const fileToBlob = async (file) => {
  let dataUrl = await fileToBase64(file)
  let arr = dataUrl.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], {
    type: mime
  })
}

/**
 * 根据最里层子元素的某个属性获取其父级某个属性的集合
 * @param {Array} array
 * @param {String} childAttr 最里层子元素的属性值
 * @param {String} attrName 匹配的属性名
 * @param {String} parentAttrName 需返回的父级属性名
 * @param {String} child child的名
 * @return {Array} 父级集合
 */
export const getParents = (array, childAttr, attrName, parentAttrName, child = 'children') => {
  const stack = []
  let going = true
  const walker = (array, childAttr) => {
    array.forEach(item => {
      if (!going) return
      stack.push(item[parentAttrName])
      if (item[attrName] == childAttr) {
        going = false
      } else if (item[child]) {
        walker(item[child], childAttr)
      } else {
        stack.pop()
      }
    })
    if (going) stack.pop()
  }
  walker(array, childAttr)
  return stack
}

/**
 * 根据某个属性获取其它属性
 * @param {Array} data
 * @param {String} attrVal
 * @param {String} attrName
 * @param {String} needAttr 需要的属性的名
 * @param {String} child child的名
 * @return {String} 返回需要的属性值
 */
export const getCurrentData = (data, attrVal, attrName, needAttr, child = 'children') => {
  let name = ''
  const walker = (data, attrVal) => {
    data.forEach(item => {
      if (item[attrName] == attrVal) {
        name = item[needAttr]
      } else if (item[child]) {
        walker(item[child], attrVal)
      }
    })
  }
  walker(data, attrVal)
  return name
}

/**
 * 获取编辑器预览html
 * @param {String} editorHtml
 * @return {String}
 */
export const getPreviewEditorHtml = (editorHtml) => {
  let headHtml = '';
  headHtml += `<link type="text/css" rel="stylesheet" href="${process.env.VUE_APP_BASE_URL}tinymce/skins/ui/oxide/content.min.css">`;
  headHtml += `<link type="text/css" rel="stylesheet" href="${process.env.VUE_APP_BASE_URL}tinymce/skins/content/default/content.min.css">`;
  headHtml += `<style type="text/css">body { font-family:Helvetica,Arial,sans-serif; font-size:16px }</style>`;

  let previewHtml = '<!DOCTYPE html>' + '<html>' + '<head>' + headHtml + '</head>' + '<body class="mce-content-body">' + editorHtml + '</body>' + '</html>';
  return previewHtml;
}

/**
 * 获取编辑器预览html
 * @param {String} editorHtml
 * @return {String}
 */
export const getPreviewCKEditorHtml = (editorHtml) => {
  let headHtml = '';
  headHtml += `<style type="text/css">html{cursor:text;*cursor:auto} img,input,textarea{cursor:default}</style>`;
  headHtml += `<link type="text/css" rel="stylesheet" href="${process.env.VUE_APP_BASE_URL}ckeditor/style.css">`;
  let previewHtml = '<!DOCTYPE html>' + '<html>' + '<head>' + headHtml + '</head>' + '<body class="document-editor cke_editable cke_editable_themed cke_contents_ltr cke_show_borders">' + editorHtml + '</body>' + '</html>';
  return previewHtml;
}

/**
 * 修改文件名称
 * @param {File} file 文件对象
 * @param {String} prefix 前缀
 * @returns {file}
 */
export function modifyFileName(file, prefix) {
  let fileName = prefix + '_' + file.name;
  fileName = fileName.replace(/[*:?"<>|\/\\]/g, '');
  return new File([file], fileName, { type: file.type });
}

/**
 * 添加动画类并自动删除
 * @param {Object} node 元素
 * @param {String} animation 动画css名称
 * @param {String} prefix 前缀
 */
export function animateCSS(node, animation, prefix = 'animate__') {
  return new Promise((resolve, reject) => {
    const animationName = `${prefix}${animation}`;
    // const node = document.querySelector(element);

    node.classList.add(`${prefix}animated`, animationName);

    // When the animation ends, we clean the classes and resolve the Promise
    function handleAnimationEnd(event) {
      event.stopPropagation();
      node.classList.remove(`${prefix}animated`, animationName);
      resolve('Animation ended');
    }

    node.addEventListener('animationend', handleAnimationEnd, { once: true });
  });
}

/**
 * 添加动画类
 * @param {Object} node 元素
 * @param {String} animation 动画css名称
 * @param {String} prefix 前缀
 */
export function addAnimateCSS(node, animation, prefix = 'animate__') {
  const animationName = `${prefix}${animation}`;
  // const node = document.querySelector(element);
  node.classList.add(`${prefix}animated`, animationName);
}

/**
 * 删除动画类
 * @param {Object} node 元素
 * @param {String} animation 动画css名称
 * @param {String} prefix 前缀
 */
export function removeAnimateCSS(node, animation, prefix = 'animate__') {
  const animationName = `${prefix}${animation}`;
  // const node = document.querySelector(element);
  node.classList.remove(`${prefix}animated`, animationName);
}

/**
 * 格式化FormData参数
 * @param {Object} data
 * @return {FormData} formData
 */
export const tansParamsFormData = (data) => {
  let formData = new FormData();
  const walker = (obj, props) => {
    for (const key in obj) {
      if (isArray(obj[key])) {
        obj[key].forEach((item, index) => {
          if (_.isPlainObject(item)) {
            walker(item, `${props}${key}[${index}].`);
          } else if (item instanceof File || item instanceof Blob) {
            formData.append(`${props}${key}`, item)
          } else {
            if (!isEmpty(item)) {
              formData.append(`${props}${key}`, item)
            }
          }
        })
      } else if (_.isPlainObject(obj[key])) {
        walker(obj[key], `${props}${key}.`);
      } else if (obj[key] instanceof File || obj[key] instanceof Blob) {
        formData.append(`${props}${key}`, obj[key])
      } else {
        if (!isEmpty(obj[key])) {
          formData.append(`${props}${key}`, obj[key])
        }
      }
    }
  }
  console.log(data)
  walker(data, '');
  return formData
}
