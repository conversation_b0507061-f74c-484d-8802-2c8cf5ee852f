import Vue from 'vue'
import VueParticles from 'vue-particles'

// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 字典标签组件
import DictTag from '@/components/DictTag';
// 分页组件
import Pagination from '@/components/Pagination';
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar';
// 富文本组件
import Editor from '@/components/Editor';
import Tinymce from '@/components/Tinymce';
// 文件上传组件
import FileUpload from '@/components/FileUpload';
import FileUploadSingle from '@/components/FileUploadSingle';
import FileUploadSelect from '@/components/FileUploadSelect';
// 图片上传组件
import ImageUpload from '@/components/ImageUpload';
// 图片预览组件
import ImagePreview from '@/components/ImagePreview';
import NavTab from '@/components/NavTab';
import TitleBar from '@/components/TitleBar';
import StepTab from '@/components/StepTab';
import ItemCard from '@/components/ItemCard';
import CountDown from '@/components/CountDown';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import VideoPlayer from '@/components/VideoPlayer';
import PwdGroup from '@/components/PwdGroup';
import OverflowTooltip from '@/components/OverflowTooltip';
import UploadExcel from '@/components/UploadExcel';
import FileListView from '@/components/FileListView';
import TemplateSelect from '@/components/TemplateSelect';
import PdfView from '@/components/PdfView';
import ImgView from '@/components/ImgView/imgView';
import PdfViewDialog from '@/components/PdfViewDialog/pdfViewDialog';
import VideoView from '@/components/VideoView/videoView';
import SealDialog from '@/components/SealDialog/sealDialog'
import SealTipDialog from '@/components/SealTip/sealTip'
import TipDialog from '@/components/TipDialog/tipDialog';
import MsgNotify from '@/components/MsgNotify/msgNotify';
import CanvasSign from '@/components/canvasSign/canvasSign';
import SignatureSMS from '@/components/SignatureSMS/signatureSMS';
import SendSMS from '@/components/SendSMS/sendSMS';
import ImageUploadSelect from '@/components/ImageUploadSelect/index.vue'

// 全局组件挂载
Vue.use(VueMeta)
Vue.use(VueParticles)
DictData.install()
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('Tinymce', Tinymce)
Vue.component('FileUpload', FileUpload)
Vue.component('FileUploadSingle', FileUploadSingle)
Vue.component('FileUploadSelect', FileUploadSelect)
Vue.component('ImageUploadSelect', ImageUploadSelect)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('NavTab', NavTab)
Vue.component('TitleBar', TitleBar)
Vue.component('StepTab', StepTab)
Vue.component('ItemCard', ItemCard)
Vue.component('CountDown', CountDown)
Vue.component('ElImageViewer', ElImageViewer);
Vue.component('VideoPlayer', VideoPlayer);
Vue.component('PwdGroup', PwdGroup);
Vue.component('OverflowTooltip', OverflowTooltip);
Vue.component('UploadExcel', UploadExcel);
Vue.component('FileListView', FileListView);
Vue.component('TemplateSelect', TemplateSelect);
Vue.component('PdfView', PdfView);
Vue.prototype.$imgView = ImgView;
Vue.prototype.$pdfViewDialog = PdfViewDialog;
Vue.prototype.$videoView = VideoView;
Vue.prototype.$sealDialog = SealDialog;
Vue.prototype.$sealTipDialog = SealTipDialog;
Vue.prototype.$tipDialog = TipDialog;
Vue.prototype.$msgNotify = MsgNotify;
Vue.prototype.$canvasSign = CanvasSign;
Vue.prototype.$signatureSMS = SignatureSMS;
Vue.prototype.$sendSMS = SendSMS;
