import BigNumber from 'bignumber.js'

export const bigNum = (num) => {
  return new BigNumber(num)
}

// 加法 (多个参数的情况)
export const yPlus = (...argArray) => {
  let num = bigNum(0)
  argArray.forEach(item => {
    num = num.plus(bigNum(item))
  })
  return num.toNumber()
}

// 减法 (多个参数的情况) (注意顺序);
export const yMinus = (...argArray) => {
  let num = bigNum(argArray[0])
  const array = argArray.slice(1)
  array.forEach(item => {
    num = num.minus(bigNum(item))
  })
  return num.toNumber()
}

// 乘法 (多个参数的情况)
export const yMultipliedBy = (...argArray) => {
  let num = bigNum(1)
  argArray.forEach((item) => {
    num = num.times(bigNum(item))
  })
  return num.toNumber()
}

// 除法 (多个参数的情况) (注意顺序)
export const yDividedBy = (...argArray) => {
  let num = bigNum(argArray[0])
  const array = argArray.slice(1)
  array.forEach(item => {
    num = num.div(bigNum(item))
  })
  return num.toNumber()
}

/**
 * 格式化数字
 * @param {Number} num 123455.32323
 * @param {Number} n 2
 * @param {String} separator // , -
 * @return {String} 123,455.32
 */
export const yToFixed = (num, n, separator = null) => {
  const m = bigNum(num)
  if (m.isNaN()) {
    return num
  }
  if (!separator) {
    return m.toFixed(n)
  }
  const fmt = {
    prefix: '',
    decimalSeparator: '.',
    groupSeparator: separator,
    groupSize: 3,
    secondaryGroupSize: 0,
    fractionGroupSeparator: ' ',
    fractionGroupSize: 0,
    suffix: ''
  }
  return m.toFormat(n, fmt)
}

