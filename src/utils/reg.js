export default {
  cellphone: /^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/, // 手机号
  idNumber: /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/,
  unifiedCreditCode: /^[0-9A-Z]{18}$/, // 统一社会信用代码
  email: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  bankCardNumber: /^[0-9]{1,30}$/, // 银行卡号
  character: /^((?!([*:?"<>|\/\\])).)*$/, // 不能包含*:?"<>|\/
  password: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@!%*#?&]).{8,20}$/, // 8-20位，必须包含数字、字母、特殊字符$@!%*#?&
  money: /^([1-9]\d{0,11}|0)(\.\d{1,2})?$/, // 验证金额0.00~***********.99
  zipCode: /^\d{6}(?!\d)$/, // 邮政编码
  number: /^\d+$/, // 大于等于0的整数
  positiveInteger: /^[1-9]\d*$/, // 正整数git
  chinese: /[\u4e00-\u9fa5]/gm // 中文
}
