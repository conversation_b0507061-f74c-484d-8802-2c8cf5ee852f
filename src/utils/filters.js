import moment from 'moment';
import { yToFixed } from '@/utils/bignumber'
import { selectDictLabel } from '@/utils/ruoyi';

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1E18, symbol: 'E' },
    { value: 1E15, symbol: 'P' },
    { value: 1E12, symbol: 'T' },
    { value: 1E9, symbol: 'G' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * 首字母转大写
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * 10000 => "10,000.00"
 * @param {number} money
 * @param {number} n 保留n位小数
 * @param {string} separator ','
 */
export function formatMoney(money, n, separator) {
  return yToFixed(money, n, separator)
}

/**
 * @param {Number,String,Object} time
 * @param {String} format 'YYYY/MM/DD HH:mm:ss'
 * @param {String} placeholder
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss', placeholder) {
  if (!time) {
    return placeholder || null
  }
  return moment(time).format(format)
}

/**
 * @param {Number,String} value
 * @param {Array} dict
 */
export function dictFormat(value, dict) {
  if (!value) {
    return null
  }
  return selectDictLabel(dict, value);
}

/**
 * ****************** => "4211**19980911**20"
 * @param {String} idCard
 * @param {number} frontShow 前面显示多少位
 * @param {number} afterShow 后面显示多少位
 * @param {String} nullTip 后面显示多少位
 * @return {string}
 */
export function formatIdCard(idCard, frontShow = 4, afterShow = 3, nullTip = null) {
  if (!idCard) {
    return nullTip || null;
  }
  let reg = new RegExp('(\\d{' + frontShow + '})(\\d+)(\\d{' + afterShow + '})')
  return idCard.replace(reg, function (x, y, z, p) {
    let i = '';
    while (i.length < z.length) {
      i += '*'
    }
    return y + i + p
  })
}
