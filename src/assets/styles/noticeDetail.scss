// 公告详情页样式
.notice-detail-wrap {
  background-color: #ffffff;
  padding: 20px 50px 0;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.notice-title {
  font-size: 22px;
  font-weight: normal;
  line-height: 1.5;
  text-align: center;
  margin: 0 auto 10px;
  word-break: break-all;
}

.notice-time {
  text-align: center;
  font-size: 12px;
  margin-bottom: 30px;
}

.text-group {
  margin-bottom: 20px;
}

.text-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
}

.text-content {
  padding: 0 40px;
  box-sizing: border-box;

  .notice-text:last-child {
    margin-bottom: 0;
  }
}

.notice-text {
  font-size: 14px;
  text-align: left;
  line-height: 1.5;
  margin-bottom: 10px;
  display: flex;
  word-break: break-all;

  .text-index {
    margin-right: 20px;
  }

  .text-span {
    flex: 1;
    word-break: break-all;
  }
}

.notice-detail-wrap.mobile {
  padding: 20px 15px 0;

  .notice-title {
    font-size: 18px;
  }

  .text-title {
    font-size: 14px;
  }

  .text-content {
    padding: 0 10px;
  }
}
