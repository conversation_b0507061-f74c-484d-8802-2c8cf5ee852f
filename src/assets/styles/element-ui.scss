// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.el-dialog__body {
  padding: 10px 20px;
}

.el-dialog--center .el-dialog__body {
  padding: 10px 20px;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-pagination {
  //text-align: right;
  //margin-top: 20px;
}

.el-radio {
  margin-right: 15px;
}

.el-radio__label {
  padding-left: 5px;
}

.el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.el-textarea .el-input__count {
  line-height: normal;
}

.el-message-box {
  max-width: 90%;
}

.max-tip {
  width: 600px;
  border-radius: 0;

  .el-message-box__content {
    text-align: center;
    min-height: 150px;
    padding-top: 10px;
    padding-bottom: 40px;
  }

  .el-message-box__status {
    position: static;
    transform: none;
    font-size: 60px !important;
    margin-bottom: 20px;
  }

  .el-message-box__message {
    font-size: 16px;
    padding: 0;
  }

  .el-textarea__inner {
    resize: none;
    min-height: 75px !important;
  }

  .el-message-box__btns {
    text-align: center;
    padding-top: 10px;
    padding-bottom: 10px;

    .el-button {
      padding: 10px 20px;
      font-size: 14px;
      border-radius: 4px;
    }
  }
}

.form-cell {
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  .cell {
    padding: 0 12px;
  }

  .el-form-item {
    margin-top: 13px;
    margin-bottom: 13px;
  }
}

.el-drawer__header {
  margin-bottom: 20px;
}

.vue-treeselect__control {
  box-sizing: border-box !important;

  .vue-treeselect__input-container {
    line-height: 34px;
  }
}

.el-divider--horizontal{
  margin: 20px 0;
}

/* 隐藏具有 aria-hidden 属性的输入元素 */
input[aria-hidden="true"],
.el-radio__original {
  display: none !important;
}

.table-radio {
  .el-radio__label {
    display: none;
  }
}

.el-radio__inner {
  border: 1px solid #333333;
}

.el-radio__input.is-disabled.is-checked {
  .el-radio__inner {
    border-color: $colorPrimary;
    background: $colorPrimary;

    &::after {
      background-color: #fff;
    }
  }
}

.el-radio__input.is-disabled.is-checked + span.el-radio__label {
  color: $colorPrimary;
}
