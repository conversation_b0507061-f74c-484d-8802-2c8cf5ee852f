@import './sidebar-variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  color: #333333;
}

body, div, h1, h2, h3, h4, h5, h6, p, dl, dd, ul, ol, pre, form, input, textarea, th, td, select, footer {
  margin: 0;
  padding: 0;
}

ul, li {
  list-style: none
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.block {
  display: block !important;
}

.pointer {
  cursor: pointer;
}

.hover {
  &:hover {
    color: $colorPrimary;
  }
}

.inlineBlock {
  display: inline-block !important;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.overflow {
  &:before, &:after {
    content: "";
    display: table;
    clear: both;
  }
}

.aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center !important;

  .el-input__inner {
    text-align: center;
  }
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.img-w {
  width: 100%;
}

.img-h {
  height: 100%;
}

.over-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin ellipsis-more {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@for $i from 1 through 4 {
  .ellipsis-more-#{$i} {
    @include ellipsis-more;
    -webkit-line-clamp: $i;
  }
}

.tox-statusbar__branding {
  display: none;
}

.html-iframe {
  background-color: #fff;
  border: 0;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
}

.project-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80px;
  opacity: 0.8;
  z-index: 99;
}

.icon-pdf {
  font-size: 80px;
  color: $colorPrimary;
  cursor: pointer;
}

.tox-tinymce-aux {
  z-index: 4000 !important;
}

.tinymce-view {
  .tox-tinymce {
    border: none;
  }

  .tox-editor-header {
    padding: 0 !important;
    box-shadow: none !important;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
}

.process-collapse {
  border-bottom: 0;

  .el-collapse-item {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  }

  .el-collapse-item__header {
    padding: 0 10px;
  }

  .el-collapse-item__wrap {
    margin-bottom: 10px;
    padding: 0 10px;
  }
}

.module-title {
  color: #000000;
  text-align: center;
  font-size: 30px;
  padding-bottom: 15px;
  position: relative;
  margin-bottom: 30px;

  &:after {
    content: '';
    background: $colorPrimary;
    width: 34px;
    height: 2px;
    bottom: 0px;
    position: absolute;
    left: 50%;
    margin-left: -17px;
  }
}

.home-info-text {
  margin-bottom: 15px;

  p {
    font-size: 16px;
    line-height: 28px;
    margin-bottom: 8px;
    text-align: justify;
    text-indent: 2rem;
  }
}

.home-info-img {
  width: 90%;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .container {
    padding: 0 10px;
  }

  .module-title {
    font-size: 26px;
    margin-bottom: 25px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .container {
    padding: 0 10px;
  }

  .module-title {
    font-size: 26px;
    margin-bottom: 25px;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .container {
    padding: 0 10px;
  }

  .module-title {
    font-size: 26px;
    margin-bottom: 25px;
  }
}

@media (min-width: 1200px) {

}
