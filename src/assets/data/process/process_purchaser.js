/**
 * 采购人流程步骤以及tabs
 */
const processList = [
  {
    purchaseFunctionName: '采购公告',
    purchaseFunctionKey: 'purchaser_procurement_bulletin',
    path: '/process/step/procurementBulletin',
    dictKey: 'purchaser_procurement_bulletin',
  },
  {
    purchaseFunctionName: '公示公告',
    purchaseFunctionKey: 'purchaser_bulletin',
    path: '/process/step/bulletin',
    dictKey: 'purchaser_bulletin',
  },
  {
    purchaseFunctionName: '采购文件',
    purchaseFunctionKey: 'purchaser_purchase_file',
    path: '/process/step/purchaseFile',
    dictKey: 'purchaser_purchase_file',
  },
  {
    purchaseFunctionName: '采购文件',
    purchaseFunctionKey: 'purchaser_purchase_file_pdf',
    path: '/process/step/purchaseFilePdf',
    dictKey: 'purchaser_purchase_file_pdf',
  },
  {
    purchaseFunctionName: '采购文件',
    purchaseFunctionKey: 'purchaser_purchase_file_no_client',
    path: '/process/step/purchaseFileNoClient',
    dictKey: 'purchaser_purchase_file_no_client',
  },
  {
    purchaseFunctionName: '报名单位',
    purchaseFunctionKey: 'purchaser_registration_supplier',
    path: '/process/step/registrationSupplier',
    dictKey: 'purchaser_registration_supplier',
  },
  {
    purchaseFunctionName: '开标',
    purchaseFunctionKey: 'purchaser_bid_open',
    path: '/process/step/bidOpen',
    dictKey: 'purchaser_bid_open',
  },
  {
    purchaseFunctionName: '评审组',
    purchaseFunctionKey: 'purchaser_review_expert',
    path: '/process/step/reviewExpert',
    dictKey: 'purchaser_review_expert',
  },
  {
    purchaseFunctionName: '评审',
    purchaseFunctionKey: 'purchaser_start_review',
    path: '/process/step/startReview',
    dictKey: 'purchaser_start_review',
  },
  {
    purchaseFunctionName: '成交结果',
    purchaseFunctionKey: 'purchaser_result',
    path: '/process/step/result',
    dictKey: 'purchaser_result',
  },
  {
    purchaseFunctionName: '调研报告',
    purchaseFunctionKey: 'purchaser_research_report',
    path: '/process/step/researchReport',
    dictKey: 'purchaser_research_report',
  },
  {
    purchaseFunctionName: '议价',
    purchaseFunctionKey: 'purchaser_bargaining',
    path: '/process/step/bargaining',
    dictKey: 'purchaser_bargaining',
  },
  {
    purchaseFunctionName: '项目文件',
    purchaseFunctionKey: 'purchaser_project_files',
    path: '/process/step/projectFiles',
    dictKey: 'purchaser_project_files',
  },
  {
    purchaseFunctionName: '答疑',
    purchaseFunctionKey: 'purchaser_answer_questions',
    path: '/process/step/answerQuestions',
    dictKey: 'purchaser_answer_questions',
  },
  {
    purchaseFunctionName: '质疑',
    purchaseFunctionKey: 'purchaser_question',
    path: '/process/step/question',
    dictKey: 'purchaser_question',
  },
  {
    purchaseFunctionName: '合同',
    purchaseFunctionKey: 'purchaser_contract',
    path: '/process/step/contract',
    dictKey: 'purchaser_contract',
  },
  // {
  //   purchaseFunctionName: '技术参数要求',
  //   purchaseFunctionKey: 'purchaser_technical_params',
  //   path: '/process/step/technicalParams',
  //   dictKey: 'purchaser_technical_params',
  // },
  {
    purchaseFunctionName: '监标人',
    purchaseFunctionKey: 'purchaser_monitor_bid_person',
    path: '/process/step/monitorBidPerson',
    dictKey: 'purchaser_monitor_bid_person',
  },
  {
    purchaseFunctionName: '项目审批',
    purchaseFunctionKey: 'purchaser_project_approval',
    path: '/process/step/projectApproval',
    dictKey: 'purchaser_project_approval',
  },
  {
    purchaseFunctionName: '评审报告',
    purchaseFunctionKey: 'purchaser_review_report',
    path: '/process/step/reviewReport',
    dictKey: 'purchaser_review_report',
  },
]

export default processList
