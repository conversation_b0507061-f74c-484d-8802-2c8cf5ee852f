/**
 * 获取流程步骤列表
 * @param {string} belongRole  所属角色[1-采购人，2-供应商，3-评委]
 * @returns {Array}
 */
export const getProgressData = (belongRole) => {
  let stepType = [];
  switch (belongRole) {
    case '1':
      stepType = 'purchaser';
      break;
    case '2':
      stepType = 'supplier';
      break;
    default:
      stepType = 'expert';
  }
  let data = require('@/assets/data/process/process_' + stepType + '.js')
  return data.default
}
