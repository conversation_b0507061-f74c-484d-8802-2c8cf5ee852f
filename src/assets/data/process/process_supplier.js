/**
 * 供应商流程步骤以及tabs
 */
const processList = [
  {
    purchaseFunctionName: '项目公告',
    purchaseFunctionKey: 'supplier_bulletin_query',
    path: '/supplierProcess/step/supplierBulletinQuery',
    dictKey: 'supplier_bulletin_query',
  },
  {
    purchaseFunctionName: '响应附加条件',
    purchaseFunctionKey: 'supplier_additional_conditions',
    path: '/supplierProcess/step/supplierAdditionalConditions',
    dictKey: 'supplier_additional_conditions',
  },
  {
    purchaseFunctionName: '采购文件下载',
    purchaseFunctionKey: 'supplier_purchase_file',
    path: '/supplierProcess/step/purchaseFileDown',
    dictKey: 'supplier_purchase_file',
  },
  {
    purchaseFunctionName: '采购文件下载',
    purchaseFunctionKey: 'supplier_purchase_file_pdf',
    path: '/supplierProcess/step/purchaseFilePdfDown',
    dictKey: 'supplier_purchase_file_pdf',
  },
  {
    purchaseFunctionName: '响应文件上传',
    purchaseFunctionKey: 'supplier_response_file',
    path: '/supplierProcess/step/responseFileUpload',
    dictKey: 'supplier_response_file',
  },
  {
    purchaseFunctionName: '响应文件上传',
    purchaseFunctionKey: 'supplier_response_file_pdf',
    path: '/supplierProcess/step/responseFilePdfUpload',
    dictKey: 'supplier_response_file_pdf',
  },
  {
    purchaseFunctionName: '响应文件上传',
    purchaseFunctionKey: 'supplier_response_file_upload_no_client',
    path: '/supplierProcess/step/responseFileUploadNoClient',
    dictKey: 'supplier_response_file_upload_no_client',
  },
  {
    purchaseFunctionName: '开标',
    purchaseFunctionKey: 'supplier_bid_open',
    path: '/supplierProcess/step/supplierBidOpen',
    dictKey: 'supplier_bid_open',
  },
  {
    purchaseFunctionName: '议价',
    purchaseFunctionKey: 'supplier_bargaining',
    path: '/supplierProcess/step/supplierBargaining',
    dictKey: 'supplier_bargaining',
  },
  {
    purchaseFunctionName: '成交结果',
    purchaseFunctionKey: 'supplier_result',
    path: '/supplierProcess/step/supplierResult',
    dictKey: 'supplier_result',
  },
  {
    purchaseFunctionName: '合同',
    purchaseFunctionKey: 'supplier_contract',
    path: '/supplierProcess/step/supplierContract',
    dictKey: 'supplier_contract',
  },
  {
    purchaseFunctionName: '答疑',
    purchaseFunctionKey: 'supplier_answer_questions',
    path: '/supplierProcess/step/supplierAnswerQuestions',
    dictKey: 'supplier_answer_questions',
  },
  {
    purchaseFunctionName: '质疑',
    purchaseFunctionKey: 'supplier_question',
    path: '/supplierProcess/step/supplierQuestion',
    dictKey: 'supplier_question',
  },
]

export default processList
