import hasRole from './permission/hasRole'
import hasPermi from './permission/hasPermi'
import hasPermiAnd from './permission/hasPermiAnd'
import dialogDrag from './dialog/drag'
import dialogDragWidth from './dialog/dragWidth'
import dialogDragHeight from './dialog/dragHeight'
import clipboard from './module/clipboard'
import hasFunction from './function/hasFunction'

const install = function (Vue) {
  Vue.directive('hasRole', hasRole)
  Vue.directive('hasPermi', hasPermi)
  Vue.directive('hasPermiAnd', hasPermiAnd)
  Vue.directive('clipboard', clipboard)
  Vue.directive('dialogDrag', dialogDrag)
  Vue.directive('dialogDragWidth', dialogDragWidth)
  Vue.directive('dialogDragHeight', dialogDragHeight)
  Vue.directive('hasFunction', hasFunction)
}

if (window.Vue) {
  window['hasRole'] = hasRole
  window['hasPermi'] = hasPermi
  window['hasPermiAnd'] = hasPermiAnd
  window['hasFunction'] = hasFunction
  Vue.use(install); // eslint-disable-line
}

export default install
