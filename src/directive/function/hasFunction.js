/**
 * v-hasFunction 功能处理
 */

import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    console.log(el, binding)
    const { value } = binding
    const processFunction = store.getters && store.getters.processFunction

    if (value && value instanceof Array && value.length > 0) {
      const functionFlag = value

      const hasFunction = processFunction.some(val => {
        return functionFlag.includes(val)
      })

      if (!hasFunction) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置功能标签值`)
    }
  }
}
