/**
 * v-hasPermi 操作权限处理
 * Copyright (c) 2019 ruoyi
 */

import auth from '@/plugins/auth';

export default {
  inserted(el, binding, vnode) {
    const { value } = binding

    if (value && value instanceof Array && value.length > 0) {
      const hasPermissions = auth.hasPermiAnd(value)

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
