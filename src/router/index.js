import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import HomeLayout from '@/homeLayout'
import ProcessLayout from '@/processLayout'
import SupplierLayout from '@/supplierLayout'
import ExpertLayout from '@/expertLayout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 noLogin: false                  // 如果设置为true，则该路由不需要登录就可以进入
 }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/modifyPassword',
    component: () => import('@/views/modifyPassword'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/',
    component: HomeLayout,
    redirect: '/home',
    children: [
      // {
      //   path: 'webHome',
      //   name: 'WebHome',
      //   component: () => import('@/views/wwwViews/webHome/index'),
      //   hidden: true,
      //   meta: { title: '官网首页', noLogin: true, noCache: false }
      // },
      {
        path: 'product',
        name: 'Product',
        component: () => import('@/views/wwwViews/product/index'),
        hidden: true,
        meta: { title: '产品介绍', noLogin: true, noCache: false }
      },
      {
        path: 'partners',
        name: 'Partners',
        component: () => import('@/views/wwwViews/partners/index'),
        hidden: true,
        meta: { title: '合作伙伴', noLogin: true, noCache: false }
      },
      {
        path: 'aboutUs',
        name: 'AboutUs',
        component: () => import('@/views/wwwViews/aboutUs/index'),
        hidden: true,
        meta: { title: '关于我们', noLogin: true, noCache: false }
      },
      {
        path: 'home',
        name: 'Home',
        component: () => process.env.VUE_APP_VERSION_TYPE === 'smart' ? import('@/views/wwwViews/webHome/index') : import('@/views/wwwViews/home/<USER>'),
        meta: { title: '官网首页', icon: 'guide', noLogin: true, noCache: true }
      },
      {
        path: 'homeBulletin',
        name: 'HomeBulletin',
        component: () => import('@/views/wwwViews/homeBulletin/index'),
        hidden: true,
        meta: { title: '公告公示', noLogin: true, noCache: false }
      },
      {
        path: 'bulletinDetail/:bulletinId/:bulletinType',
        name: 'BulletinDetail',
        component: () => import('@/views/wwwViews/bulletinDetail/index'),
        hidden: true,
        meta: { title: '公告详情', noLogin: true, activeMenu: '/homeBulletin', noCache: true }
      },
      {
        path: 'homeHelpCenter',
        name: 'HomeHelpCenter',
        component: () => import('@/views/wwwViews/homeHelpCenter/index'),
        hidden: true,
        meta: { title: '帮助中心', noLogin: true, noCache: true }
      },
      {
        path: 'homeHelpDetail/:noticeId',
        name: 'HomeHelpDetail',
        component: () => import('@/views/wwwViews/homeHelpCenter/homeHelpDetail'),
        hidden: true,
        meta: { title: '帮助详情', noLogin: true, activeMenu: '/homeHelpCenter', noCache: true }
      },
      {
        path: 'noticeAnnouncement',
        name: 'NoticeAnnouncement',
        component: () => import('@/views/wwwViews/noticeAnnouncement/index'),
        hidden: true,
        meta: { title: '信息公示', noLogin: true, noCache: true }
      },
      {
        path: 'noticeAnnouncementDetail/:noticeId',
        name: 'NoticeAnnouncementDetail',
        component: () => import('@/views/wwwViews/noticeAnnouncement/noticeAnnouncementDetail'),
        hidden: true,
        meta: { title: '信息公示详情', noLogin: true, activeMenu: '/noticeAnnouncement', noCache: true }
      },
      {
        path: 'homeEntrustBulletin',
        name: 'HomeEntrustBulletin',
        component: () => import('@/views/wwwViews/homeEntrustBulletin/index'),
        hidden: true,
        meta: { title: '委托公告', noLogin: true, noCache: false }
      },
      {
        path: 'homeEntrustBulletinDetail/:id',
        name: 'HomeEntrustBulletinDetail',
        component: () => import('@/views/wwwViews/homeEntrustBulletin/detail'),
        hidden: true,
        meta: { title: '委托公告详情', noLogin: true, activeMenu: '/homeEntrustBulletin', noCache: true }
      },
      {
        path: 'externalBulletin',
        name: 'ExternalBulletin',
        component: () => import('@/views/wwwViews/externalBulletin/index'),
        hidden: true,
        meta: { title: '院外招标', noLogin: true, noCache: false }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '个人中心', icon: 'dashboard', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user', noCache: true }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user', noCache: true }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role', noCache: true }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict', noCache: true }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job', noCache: true }
      }
    ]
  },
  {
    path: '/organize/organizeInfo',
    component: Layout,
    hidden: true,
    permissions: ['system:organize:query'],
    children: [
      {
        path: 'index/:orgCode/:orgName',
        component: () => import('@/views/system/organize/organizeInfo'),
        name: 'OrganizeInfo',
        meta: { title: '组织信息', activeMenu: '/system/organize', noCache: true }
      }
    ]
  },
  {
    path: '/supplierAudit/supplierInfoAudit',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:supplier:query'],
    children: [
      {
        path: 'index/:supplierId(\\d+)/:orgCode',
        component: () => import('@/views/purchaser/supplierAudit/supplierInfoAudit'),
        name: 'SupplierInfoAudit',
        meta: { title: '供应商详情审核', activeMenu: '/systemSettings/supplierAudit', noCache: true }
      }
    ]
  },
  {
    path: '/projectArchive/projectArchiveDetail',
    component: Layout,
    hidden: true,
    permissions: ['project:archive:query'],
    children: [
      {
        path: 'index/:buyItemCode',
        component: () => import('@/views/purchaser/projectArchive/detail.vue'),
        name: 'ProjectArchiveDetail',
        meta: { title: '归档项目详情', activeMenu: '/projectArchive', noCache: true }
      }
    ]
  },
  {
    path: '/aiReview/aiReviewDetailSingle',
    component: Layout,
    hidden: true,
    permissions: ['ai:review:query'],
    children: [
      {
        path: 'index/:type',
        component: () => import('@/views/purchaser/aiReview/detailSingle.vue'),
        name: 'AiReviewDetailSingle',
        meta: { title: 'AI审查', activeMenu: '/aiReview', noCache: true }
      }
    ]
  },
  {
    path: '/aiReview/aiReviewDetailMultiple',
    component: Layout,
    hidden: true,
    permissions: ['ai:review:query'],
    children: [
      {
        path: 'index/:type',
        component: () => import('@/views/purchaser/aiReview/detailMultiple.vue'),
        name: 'AiReviewDetailMultiple',
        meta: { title: 'AI审查', activeMenu: '/aiReview', noCache: true }
      }
    ]
  },
  // 审批中心
  {
    path: '/initiateApproval',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:approval:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/purchaser/approvalCenter/initiateApproval.vue'),
        name: 'InitiateApproval',
        meta: { title: '发起审批', activeMenu: '/approvalCenter', noCache: true }
      }
    ]
  },
  {
    path: '/alreadyApprovalDetail',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:approval:query'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/approvalCenter/alreadyApprovalDetail.vue'),
        name: 'AlreadyApprovalDetail',
        meta: { title: '审批详情', activeMenu: '/approvalCenter' }
      }
    ]
  },
  {
    path: '/pendingApprovalDetail',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:approval:query'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/approvalCenter/pendingApprovalDetail.vue'),
        name: 'PendingApprovalDetail',
        meta: { title: '审批详情', activeMenu: '/approvalCenter' }
      }
    ]
  },
  {
    path: '/copyApprovalDetail',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:approval:query'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/approvalCenter/copyApprovalDetail.vue'),
        name: 'CopyApprovalDetail',
        meta: { title: '审批详情', activeMenu: '/approvalCenter' }
      }
    ]
  },
  // 创建项目
  {
    path: '/projectList/createProject',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:project:add'],
    redirect: '/projectList/createProject/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/purchaser/projectList/createProject'),
        name: 'CreateProject',
        meta: { title: '创建项目', activeMenu: '/projectList', noCache: true }
      }
    ]
  },
  {
    path: '/projectList/copyProject',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:project:reAdd'],
    children: [
      {
        path: 'index/:buyItemCode',
        component: () => import('@/views/purchaser/projectList/copyProject'),
        name: 'CopyProject',
        meta: { title: '再次采购', activeMenu: '/projectList', noCache: true }
      }
    ]
  },
  {
    path: '/projectList/editProject',
    component: Layout,
    hidden: true,
    permissions: ['purchaser:project:edit', 'purchaser:project:query'],
    children: [
      {
        path: 'index/:buyItemCode',
        component: () => import('@/views/purchaser/projectList/editProject'),
        name: 'EditProject',
        meta: { title: '修改项目', activeMenu: '/projectList', noCache: true }
      }
    ]
  },
  // 商城项目
  {
    path: '/projectList/shopProjectCreate',
    component: Layout,
    hidden: true,
    permissions: ['shopping:mall:buy'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/purchaser/shoppingCart/shopProjectCreate.vue'),
        name: 'ShopProjectCreate',
        meta: { title: '创建项目', activeMenu: '/shoppingCart', noCache: true }
      }
    ]
  },
  // 申购项目
  {
    path: '/applyProject/applyProjectCreate',
    component: Layout,
    hidden: true,
    permissions: ['project:apply:purchase'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/applyProject/applyProjectCreate'),
        name: 'ApplyProjectCreate',
        meta: { title: '创建项目', activeMenu: '/applyProject', noCache: true }
      }
    ]
  },
  {
    path: '/applyProject/applyProjectDetail',
    component: Layout,
    hidden: true,
    permissions: ['project:apply:query'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/applyProject/applyProjectDetail'),
        name: 'ApplyProjectDetail',
        meta: { title: '项目详情', activeMenu: '/applyProject', noCache: true }
      }
    ]
  },
  // 分派项目
  {
    path: '/assignProject/assignProjectCreate',
    component: Layout,
    hidden: true,
    permissions: ['project:assign:purchase'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/assignProject/assignProjectCreate'),
        name: 'AssignProjectCreate',
        meta: { title: '创建项目', activeMenu: '/assignProject', noCache: true }
      }
    ]
  },
  {
    path: '/assignProject/assignProjectDetail',
    component: Layout,
    hidden: true,
    permissions: ['project:assign:query'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/assignProject/assignProjectDetail'),
        name: 'AssignProjectDetail',
        meta: { title: '项目详情', activeMenu: '/assignProject', noCache: true }
      }
    ]
  },
  // 委托项目管理
  {
    path: '/entrustProject/createEntrustProject',
    component: Layout,
    hidden: true,
    permissions: ['entrust:project:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/purchaser/entrustProject/createEntrustProject'),
        name: 'CreateEntrustProject',
        meta: { title: '创建项目', activeMenu: '/entrustProject', noCache: true }
      }
    ]
  },
  {
    path: '/entrustProject/editEntrustProject',
    component: Layout,
    hidden: true,
    permissions: ['entrust:project:query', 'entrust:project:edit'],
    children: [
      {
        path: 'index/:id(\\d+)',
        component: () => import('@/views/purchaser/entrustProject/editEntrustProject'),
        name: 'EditEntrustProject',
        meta: { title: '修改项目', activeMenu: '/entrustProject', noCache: true }
      }
    ]
  },
  {
    path: '/entrustProject/entrustProjectProcess',
    component: Layout,
    hidden: true,
    permissions: ['entrust:process:query'],
    children: [
      {
        path: 'index/:id(\\d+)/:projectCode/:yearMonthSplit/:projectName',
        component: () => import('@/views/purchaser/entrustProject/entrustProjectProcess'),
        name: 'EntrustProjectProcess',
        meta: { title: '项目流程', activeMenu: '/entrustProject', noCache: true }
      }
    ]
  },
  {
    path: '/shoppingMall/goodsDetail',
    component: Layout,
    hidden: true,
    permissions: ['shopping:mall:query'],
    children: [
      {
        path: 'index/:goodsId(\\d+)',
        component: () => import('@/views/purchaser/shoppingMall/detail.vue'),
        name: 'GoodsDetail',
        meta: { title: '商品详情', activeMenu: '/shoppingMall', noCache: true }
      }
    ]
  },
  // 流程
  {
    path: '/process',
    component: Layout,
    redirect: '/projectList',
    hidden: true,
    permissions: ['project:process:query'],
    children: [
      {
        path: 'step',
        component: ProcessLayout,
        hidden: true,
        redirect: '/projectList',
        permissions: ['project:process:query'],
        children: [
          {
            path: 'procurementBulletin',
            component: () => import('@/views/purchaser/process/procurementBulletin/index'),
            name: 'ProcurementBulletin',
            meta: { title: '采购公告', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'bulletin',
            component: () => import('@/views/purchaser/process/bulletin/index'),
            name: 'Bulletin',
            meta: { title: '公示公告', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'purchaseFile',
            component: () => import('@/views/purchaser/process/purchaseFile/index'),
            name: 'PurchaseFile',
            meta: { title: '采购文件', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'purchaseFilePdf',
            component: () => import('@/views/purchaser/process/purchaseFilePdf/index'),
            name: 'PurchaseFilePdf',
            meta: { title: '采购文件', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'purchaseFileNoClient',
            component: () => import('@/views/purchaser/process/purchaseFileNoClient/index'),
            name: 'PurchaseFileNoClient',
            meta: { title: '采购文件', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'registrationSupplier',
            component: () => import('@/views/purchaser/process/registrationSupplier/index'),
            name: 'RegistrationSupplier',
            meta: { title: '报名单位', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'reviewExpert',
            component: () => import('@/views/purchaser/process/reviewExpert/index'),
            name: 'ReviewExpert',
            meta: { title: '评审组', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'answerQuestions',
            component: () => import('@/views/purchaser/process/answerQuestions/index'),
            name: 'AnswerQuestions',
            meta: { title: '答疑', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'question',
            component: () => import('@/views/purchaser/process/question/index'),
            name: 'Question',
            meta: { title: '质疑', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'bidOpen',
            component: () => import('@/views/purchaser/process/bidOpen/index'),
            name: 'BidOpen',
            meta: { title: '开标', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'startReview',
            component: () => import('@/views/purchaser/process/startReview/index'),
            name: 'StartReview',
            meta: { title: '评审', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'result',
            component: () => import('@/views/purchaser/process/result/index'),
            name: 'Result',
            meta: { title: '成交结果', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'researchReport',
            component: () => import('@/views/purchaser/process/researchReport/index'),
            name: 'ResearchReport',
            meta: { title: '调研报告', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'bargaining',
            component: () => import('@/views/purchaser/process/bargaining/index'),
            name: 'Bargaining',
            meta: { title: '议价', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'projectFiles',
            component: () => import('@/views/purchaser/process/projectFiles/index'),
            name: 'ProjectFiles',
            meta: { title: '项目文件', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'contract',
            component: () => import('@/views/purchaser/process/contract/index'),
            name: 'Contract',
            meta: { title: '合同', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'monitorBidPerson',
            component: () => import('@/views/purchaser/process/monitorBidPerson/index'),
            name: 'MonitorBidPerson',
            meta: { title: '监标人', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'projectApproval',
            component: () => import('@/views/purchaser/process/projectApproval/index'),
            name: 'ProjectApproval',
            meta: { title: '项目审批', activeMenu: '/projectList', noCache: true }
          },
          {
            path: 'reviewReport',
            component: () => import('@/views/purchaser/process/reviewReport/index'),
            name: 'ReviewReport',
            meta: { title: '评审报告', activeMenu: '/projectList', noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/supplierProcess',
    component: Layout,
    redirect: '/supplierProject',
    hidden: true,
    permissions: ['supplier:process:query'],
    children: [
      {
        path: 'step',
        component: SupplierLayout,
        hidden: true,
        redirect: '/supplierProject',
        permissions: ['supplier:process:query'],
        children: [
          {
            path: 'supplierBulletinQuery',
            component: () => import('@/views/supplier/process/supplierBulletinQuery/index'),
            name: 'SupplierBulletinQuery',
            meta: { title: '项目公告', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierAdditionalConditions',
            component: () => import('@/views/supplier/process/supplierAdditionalConditions/index'),
            name: 'SupplierAdditionalConditions',
            meta: { title: '响应附加条件', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'purchaseFileDown',
            component: () => import('@/views/supplier/process/purchaseFileDown/index'),
            name: 'PurchaseFileDown',
            meta: { title: '采购文件下载', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'purchaseFilePdfDown',
            component: () => import('@/views/supplier/process/purchaseFilePdfDown/index'),
            name: 'PurchaseFilePdfDown',
            meta: { title: '采购文件下载', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'responseFileUpload',
            component: () => import('@/views/supplier/process/responseFileUpload/index'),
            name: 'ResponseFileUpload',
            meta: { title: '响应文件上传', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'responseFilePdfUpload',
            component: () => import('@/views/supplier/process/responseFilePdfUpload/index'),
            name: 'ResponseFilePdfUpload',
            meta: { title: '响应文件上传', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'responseFileUploadNoClient',
            component: () => import('@/views/supplier/process/responseFileUploadNoClient/index'),
            name: 'ResponseFileUploadNoClient',
            meta: { title: '响应文件上传', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierBidOpen',
            component: () => import('@/views/supplier/process/supplierBidOpen/index'),
            name: 'SupplierBidOpen',
            meta: { title: '开标', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierBargaining',
            component: () => import('@/views/supplier/process/supplierBargaining/index'),
            name: 'SupplierBargaining',
            meta: { title: '议价', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierResult',
            component: () => import('@/views/supplier/process/supplierResult/index'),
            name: 'SupplierResult',
            meta: { title: '成交结果', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierContract',
            component: () => import('@/views/supplier/process/supplierContract/index'),
            name: 'SupplierContract',
            meta: { title: '合同', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierAnswerQuestions',
            component: () => import('@/views/supplier/process/supplierAnswerQuestions/index'),
            name: 'SupplierAnswerQuestions',
            meta: { title: '答疑', activeMenu: '/supplierProject', noCache: true }
          },
          {
            path: 'supplierQuestion',
            component: () => import('@/views/supplier/process/supplierQuestion/index'),
            name: 'supplierQuestion',
            meta: { title: '质疑', activeMenu: '/supplierProject', noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/expertProcess',
    component: Layout,
    redirect: '/reviewManage',
    hidden: true,
    permissions: ['expert:process:query'],
    children: [
      {
        path: 'step',
        component: ExpertLayout,
        hidden: true,
        redirect: '/reviewManage',
        permissions: ['expert:process:query'],
        children: [
          {
            path: 'conformityReview',
            component: () => import('@/views/expert/process/conformityReview/index'),
            name: 'ConformityReview',
            meta: { title: '初步评审', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'scoreReview',
            component: () => import('@/views/expert/process/scoreReview/index'),
            name: 'ScoreReview',
            meta: { title: '详细评审', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'conformityReviewPdf',
            component: () => import('@/views/expert/process/conformityReviewPdf/index'),
            name: 'ConformityReviewPdf',
            meta: { title: '初步评审', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'scoreReviewPdf',
            component: () => import('@/views/expert/process/scoreReviewPdf/index'),
            name: 'ScoreReviewPdf',
            meta: { title: '详细评审', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertBargaining',
            component: () => import('@/views/expert/process/expertBargaining/index'),
            name: 'ExpertBargaining',
            meta: { title: '议价', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertResult',
            component: () => import('@/views/expert/process/expertResult/index'),
            name: 'ExpertResult',
            meta: { title: '评审结果', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'allReview',
            component: () => import('@/views/expert/process/allReview/index'),
            name: 'AllReview',
            meta: { title: '调研', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'allReviewPdf',
            component: () => import('@/views/expert/process/allReviewPdf/index'),
            name: 'AllReviewPdf',
            meta: { title: '调研', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertResearchReport',
            component: () => import('@/views/expert/process/expertResearchReport/index'),
            name: 'ExpertResearchReport',
            meta: { title: '调研报告', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertAnswerQuestions',
            component: () => import('@/views/expert/process/expertAnswerQuestions/index'),
            name: 'ExpertAnswerQuestions',
            meta: { title: '答疑', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertQuestion',
            component: () => import('@/views/expert/process/expertQuestion/index'),
            name: 'ExpertQuestion',
            meta: { title: '质疑', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertProjectFiles',
            component: () => import('@/views/expert/process/expertProjectFiles/index'),
            name: 'ExpertProjectFiles',
            meta: { title: '项目文件', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertVoteReview',
            component: () => import('@/views/expert/process/expertVoteReview/index'),
            name: 'ExpertVoteReview',
            meta: { title: '投票评审', activeMenu: '/reviewManage', noCache: true }
          },
          {
            path: 'expertVoteResult',
            component: () => import('@/views/expert/process/expertVoteResult/index'),
            name: 'ExpertVoteResult',
            meta: { title: '投票评审结果', activeMenu: '/reviewManage', noCache: true }
          }
        ]
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
