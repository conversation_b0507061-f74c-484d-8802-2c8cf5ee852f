<template>
  <div class="app-container" style="position: relative;">
    <h3 :style="{color: theme, fontStyle:'16px'}" class="text-center over-ellipsis">{{ buyItemName }}</h3>
    <h4 :style="{color: theme}" class="text-center over-ellipsis mt10 fontSize14">{{ subpackageName }}</h4>
    <process-nav
      class="process-nav"
      :stepList="processNode"
      :showIndex="true"
      @step-click="stepClick"
    >
    </process-nav>

    <process-nav
      class="process-nav"
      style="justify-content: flex-end;"
      :stepList="processNotNode"
      @step-click="stepClick"
    >
    </process-nav>
    <section style="position: relative;">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key"/>
      </transition>
    </section>
  </div>
</template>

<script>
import { ProcessNav } from './components'
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  components: {
    ProcessNav
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'theme',
      'wsMsg',
      'processPage',
      'buyItemName',
      'subpackageName',
      'subpackageCode'
    ]),
    key() {
      return this.$route.path
    },
    processNode() {
      let list = this.processPage.filter(item => !!item.ranking);
      return this._.orderBy(list, ['ranking', 'sort'], ['asc', 'asc']);
    },
    processNotNode() {
      let list = this.processPage.filter(item => !item.ranking);
      return this._.orderBy(list, 'sort', 'asc');
    }
  },
  watch: {
    wsMsg: {
      handler(val) {
        console.log('***********', val)
        if (val && val.displayEnums.includes('REFRESH') && val.businessTypeEnum === 'SUPPLIER_PROCESS_UPDATE') {
          console.log('刷新process/setProcessNodes')
          this.$store.dispatch('process/setProcessNodes', {
            buyItemCode: this.buyItemCode,
            subpackageCode: this.subpackageCode,
            belongRole: '2'
          })
        }
      },
      deep: true
    }
  },
  created() {
    this.$store.dispatch('process/setProcessNodes', {
      buyItemCode: this.buyItemCode,
      subpackageCode: this.subpackageCode,
      belongRole: '2'
    });
  },
  methods: {
    stepClick(item) {
      this.$router.push({ path: item.path })
    }
  }
}
</script>

<style lang="scss" scoped>
.process-nav {
  margin: 20px auto 20px;
}
</style>
