<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    count: {
      type: Number,
      default: 0
    }
  },
  render(h, context) {
    const { icon, title, count } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(<svg-icon icon-class={icon}/>)
    }

    if (title) {
      /** 小红点开始 */
      if (title === '协同工作流' || title === '分派项目') {
        if (title.length > 5) {
          vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)
        } else {
          console.log('count', count)
          vnodes.push(<span slot='title'>{(title)}
            <el-badge value={(count)} class='badge-item' hidden={(count == 0)}/></span>)
        }
      } else {
        if (title.length > 5) {
          vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)
        } else {
          vnodes.push(<span slot='title'>{(title)}</span>)
        }
      }
      /** 小红点结束 */
      // if (title.length > 5) {
      //   vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)
      // } else {
      //   vnodes.push(<span slot='title'>{(title)}</span>)
      // }
    }
    return vnodes
  }
}
</script>
<style scoped>
.badge-item {
  margin-top: -20px;
  margin-left: 5px;
}
</style>
