import request from '@/utils/request'
import { Message } from 'element-ui'
import { saveAs } from 'file-saver'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { blobValidate } from '@/utils/ruoyi'
import modal from '@/plugins/modal'
import PdfViewDialog from '@/components/PdfViewDialog/pdfViewDialog'
import ImgView from '@/components/ImgView/imgView'
import VideoView from '@/components/VideoView/videoView';

const baseURL = process.env.VUE_APP_BASE_API

export default {
  // 通用文件下载
  downloadFileByGeneral(fileKey) {
    this.download('/epcfile/dingTalkFile/download', fileKey);
  },
  // 通用文件预览
  previewFileByGeneral(fileKey) {
    this.preview('/epcfile/dingTalkFile/download', fileKey);
  },
  // 商品图片下载
  downloadFileByGoods(fileKey) {
    this.download('/epcfile/productFile/download', fileKey);
  },
  // 商品图片预览
  previewFileByGoods(fileKey) {
    this.preview('/epcfile/productFile/download', fileKey);
  },
  // 项目内归档文件下载
  downloadFile(fileKey) {
    this.download('/epcfile/projectFile/download', fileKey);
  },
  // 项目内归档文件预览
  previewFile(fileKey) {
    this.preview('/epcfile/projectFile/download', fileKey);
  },
  // 项目内非归档文件下载
  downloadFileByAnnex(fileKey) {
    this.download('/epcfile/projectFile/downloadEnclosure', fileKey);
  },
  // 项目内非归档文件预览
  previewFileByAnnex(fileKey) {
    this.preview('/epcfile/projectFile/downloadEnclosure', fileKey);
  },
  // 非项目文件下载
  downloadFileByOther(fileKey) {
    this.download('/epcfile/nonProjectFile/download', fileKey);
  },
  // 非项目文件预览
  previewFileByOther(fileKey) {
    this.preview('/epcfile/nonProjectFile/download', fileKey);
  },
  // 公开文件下载
  downloadFileByPublic(fileKey) {
    this.download('/epcfile/nonProjectFile/public/downloadChart', fileKey);
  },
  // 公开文件预览
  previewFileByPublic(fileKey) {
    this.preview('/epcfile/nonProjectFile/public/downloadChart', fileKey);
  },
  // 委托项目文件下载
  downloadFileByEntrust(fileKey) {
    this.download('/epcfile/agencyProject/agencyDownload', fileKey);
  },
  // 委托项目文件预览
  previewFileByEntrust(fileKey) {
    this.preview('/epcfile/agencyProject/agencyDownload', fileKey);
  },
  download(url, fileKey) {
    modal.loading('正在下载数据，请稍候')
    request({
      url: url,
      method: 'get',
      responseType: 'blob',
      params: { fileKey }
    }).then((res) => {
      modal.closeLoading()
      this.downloadSave(res)
    }).catch((r) => {
      modal.closeLoading()
      Message.error('下载文件出现错误！')
    })
  },
  preview(url, fileKey) {
    modal.loading('正在下载数据，请稍候')
    request({
      url: url,
      method: 'get',
      responseType: 'blob',
      params: { fileKey }
    }).then(async (res) => {
      modal.closeLoading()
      const isBlob = blobValidate(res.data)
      if (isBlob) {
        let fileName = this.getFileName(res)
        let format = fileName.substring(fileName.lastIndexOf('.')).toLowerCase()
        let imgFormats = ['.png', '.jpg', '.jpeg']
        if (format === '.mp4') {
          VideoView({ data: res.data, type: 'blob' })
        } else if (format === '.pdf') {
          PdfViewDialog({ data: res.data, type: 'blob' })
        } else if (imgFormats.some(item => item === format)) {
          ImgView({ data: res.data, type: 'blob' })
        } else {
          Message.error('该文件不支持预览！')
        }
      } else {
        await this.printErrMsg(res.data)
      }
    }).catch((r) => {
      modal.closeLoading()
      Message.error('下载文件出现错误！')
    })
  },
  zip(url, name) {
    modal.loading('正在下载数据，请稍候')
    url = baseURL + url
    request({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      modal.closeLoading()
      const isBlob = blobValidate(res.data)
      if (isBlob) {
        const blob = new Blob([res.data], { type: 'application/zip' })
        this.saveAs(blob, name)
      } else {
        await this.printErrMsg(res.data)
      }
    }).catch((r) => {
      modal.closeLoading()
      Message.error('下载文件出现错误！')
    })
  },
  downloadSave(res) {
    const isBlob = blobValidate(res.data)
    if (isBlob) {
      const blob = new Blob([res.data])
      saveAs(blob, this.getFileName(res))
    } else {
      this.printErrMsg(res.data)
    }
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts)
  },
  getFileName(res) {
    const str = res.headers['content-disposition']
    const index = str.indexOf('filename')
    const str1 = str.substr(index)
    const fileName = str1.substring(str1.indexOf('"') + 1, str1.lastIndexOf('"'))
    return decodeURIComponent(fileName)
  },
  async printErrMsg(data) {
    const resText = await data.text()
    const rspObj = JSON.parse(resText)
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
    Message.error(errMsg)
  }
}

