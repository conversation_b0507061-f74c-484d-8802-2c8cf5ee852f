import Vue from 'vue'

import Cookies from 'js-cookie'
import _ from 'lodash'
import moment from 'moment';

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css';// 引入样式

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import 'animate.css'
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import * as filters from './utils/filters'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from '@/api/system/dict/data';
import { getConfigKey } from '@/api/system/config';
import {
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
  selectDictValue,
  initDict
} from '@/utils/ruoyi'
import { formatTime } from './utils/filters';

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.initDict = initDict
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.formatTime = formatTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictValue = selectDictValue
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype._ = _;
Vue.prototype.$moment = moment;
moment.locale('zh-cn');

// 全局组件
import '@/utils/components';

Vue.use(directive)
Vue.use(plugins)

Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.use(UmyUi);

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
