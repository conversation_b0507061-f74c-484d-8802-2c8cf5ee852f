import request from '@/utils/request'

// 查询印章
export function querySeal(params) {
  return request({
    url: '/system/seal/getByUserId',
    method: 'get',
    params
  })
}

// 添加印章
export function addSeal(data) {
  return request({
    url: '/system/seal/add',
    method: 'post',
    data
  })
}

// 删除印章
export function removeSeal(data) {
  return request({
    url: '/system/seal/remove',
    method: 'post',
    data
  })
}

// 删除组织印章
export function removeOrgRemove(params) {
  return request({
    url: '/system/seal/orgRemove',
    method: 'get',
    params
  })
}

// 签章验证码
export function getSignatureCode(params) {
  return request({
    url: '/seal/signSealBack/getMessageCode',
    method: 'get',
    params
  })
}

// 企业印章授权
export function orgGrantAuthorization(params) {
  return request({
    url: '/seal/signSealBack/orgGrantAuthorization',
    method: 'get',
    params
  })
}

// 企业印章授权记录
export function getSealAuthenticationList(data) {
  return request({
    url: '/seal/signSealBack/getSealAuthenticationList',
    method: 'post',
    data
  })
}

// 企业印章授权结果
export function getAuthorizationResult(params) {
  return request({
    url: '/seal/signSealBack/getAuthorizationResult',
    method: 'get',
    params
  })
}
