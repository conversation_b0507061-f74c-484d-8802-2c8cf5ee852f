import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 委托项目文件上传
export function fileUploadByEntrust(data) {
  return request({
    url: '/epcfile/agencyProject/agencyFilesUp',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 委托项目文件下载
export function fileDownByEntrust(fileKey) {
  return request({
    url: '/epcfile/agencyProject/agencyDownload',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 委托项目文件删除
export function fileDeleteByEntrust(fileKey) {
  return request({
    url: '/epcfile/agencyProject/deleteByFileKey',
    method: 'get',
    params: { fileKey }
  })
}

// 委托项目文件打包
export function entrustPackZip(params) {
  return request({
    url: '/epcfile/agencyProject/agencyProjectFileToZip',
    method: 'get',
    params
  })
}

// 委托项目打包文件下载
export function fileDownByEntrustZip(buyItemCode) {
  return request({
    url: '/epcfile/agencyProject/zipFileDownload',
    method: 'get',
    responseType: 'blob',
    params: { buyItemCode }
  })
}

// 创建项目
export function saveEntrustProject(data) {
  return request({
    url: '/agent/agent.project/createAgentCreate',
    method: 'post',
    data
  })
}

// 修改项目
export function upEntrustProject(data) {
  return request({
    url: '/agent/agent.project/modifyAgentInfo',
    method: 'post',
    data
  })
}

// 删除项目
export function delEntrustProject(params) {
  return request({
    url: '/agent/agent.project/del',
    method: 'get',
    params
  })
}

// 查询项目详情
export function queryEntrustProjectDetail(id) {
  return request({
    url: '/agent/agent.project/queryAgentProject',
    method: 'get',
    params: { id }
  })
}

// 查询项目列表
export function queryEntrustProjectList(data) {
  return request({
    url: '/agent/agent.project/projectList',
    method: 'post',
    data
  })
}

// 审核项目
export function auditEntrustProject(data) {
  return request({
    url: '/agent/agent.project/audit',
    method: 'post',
    data
  })
}

// 保存招标准备信息
export function saveEntrustPrepareInfo(data) {
  return request({
    url: '/agent/agent.project/savePrepareInfo',
    method: 'post',
    data
  })
}

// 保存中标结果信息
export function saveEntrustWinBid(data) {
  return request({
    url: '/agent/agent.project/chooseWinBid',
    method: 'post',
    data
  })
}

// 查询中标人页面
export function queryEntrustProjectWinPage(projectCode) {
  return request({
    url: '/agent/agent.project/queryWinPage',
    method: 'get',
    params: { projectCode }
  })
}

// 查询文件
export function queryEntrustFileList(projectCode) {
  return request({
    url: '/agent/agent.project/showFile',
    method: 'get',
    params: { projectCode }
  })
}

// 项目报表导出
export function exportExcelOfAgent(data) {
  return request({
    url: '/agent/agent.project/exportExcelOfAgent',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 项目完成
export function entrustProjectComplete(params) {
  return request({
    url: '/agent/agent.project/complete',
    method: 'get',
    params
  })
}

// 查询项目是否完成
export function queryEntrustProjectIsComplete(params) {
  return request({
    url: '/agent/agent.project/isComplete',
    method: 'get',
    params
  })
}
