import request from '@/utils/request'

// 查询配置
export function getHomePurchaseConfig(data) {
  return request({
    url: '/bidding/home/<USER>/homeConfig',
    method: 'post',
    data
  })
}

// 官网首页公告列表
export function listHomeBulletin(data) {
  return request({
    url: '/bidding/home/<USER>/homePage',
    method: 'post',
    data
  })
}

// 官网首页公告详情
export function getHomeBulletinInfo(bulletinId) {
  return request({
    url: '/bidding/home/<USER>/homePageInfo',
    method: 'get',
    params: { bulletinId }
  })
}

// 供应商报名
export function supplierSign(data) {
  return request({
    url: '/bidding/supplier/sign/up',
    method: 'post',
    data
  })
}

// 供应商查询招标项目下标段是否报名
export function supplierSignWhether(params) {
  return request({
    url: '/bidding/home/<USER>',
    method: 'get',
    params
  })
}

// 江西公共资源搜索采购公告
export function searchJX(params) {
  return request({
    url: '/bidding/public/searchJX',
    method: 'get',
    params
  })
}
