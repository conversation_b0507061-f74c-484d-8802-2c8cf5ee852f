import request from '@/utils/request'

// 查询公告列表
export function listEntrustBulletin(query) {
  return request({
    url: '/bidding/entrust/bulletin/page',
    method: 'get',
    params: query
  })
}

// 审核人查询公告列表
export function listEntrustBulletinByReviewer(query) {
  return request({
    url: '/bidding/entrust/bulletin/pageByReviewer',
    method: 'get',
    params: query
  })
}

// 查询首页公告列表
export function listEntrustBulletinByHome(query) {
  return request({
    url: '/bidding/entrust/bulletin/public/homepage',
    method: 'get',
    params: query
  })
}

// 新增公告
export function addEntrustBulletin(data) {
  return request({
    url: '/bidding/entrust/bulletin/publish',
    method: 'post',
    data
  })
}

// 修改公告
export function updateEntrustBulletin(data) {
  return request({
    url: '/bidding/entrust/bulletin/up',
    method: 'post',
    data
  })
}

// 删除公告
export function delEntrustBulletin(data) {
  return request({
    url: '/bidding/entrust/bulletin/del',
    method: 'post',
    data
  })
}

// 审核人删除公告
export function delEntrustBulletinByReviewer(data) {
  return request({
    url: '/bidding/entrust/bulletin/delByReviewer',
    method: 'post',
    data
  })
}

// 公告是否展示
export function changeEntrustBulletinShow(data) {
  return request({
    url: '/bidding/entrust/bulletin/show',
    method: 'post',
    data
  })
}

// 审核公告
export function reviewEntrustBulletin(data) {
  return request({
    url: '/bidding/entrust/bulletin/audit',
    method: 'post',
    data
  })
}
