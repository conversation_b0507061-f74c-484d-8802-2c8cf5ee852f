import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 查询供应商流程
export function getProcessNodes(subpackageCode, processRole) {
  return request({
    url: '/bidding/supplier/nodes',
    method: 'get',
    params: { subpackageCode, processRole }
  })
}

// 查询公告列表
export function supplierQueryBulletin(data) {
  return request({
    url: '/bidding/supplier/queryBulletin',
    method: 'post',
    data
  })
}

// 查询响应附加条件
export function getAdditionalInformation(subpackageCode) {
  return request({
    url: '/bidding/supplier/additionalInformation',
    method: 'get',
    params: { subpackageCode }
  })
}

// 保存响应附加条件
export function saveAdditionalInformation(data) {
  return request({
    url: '/bidding/supplier/additionalInformation',
    method: 'post',
    data
  })
}

// 查询采购文件
export function getPurchaseFileBySupplier(subpackageCode) {
  return request({
    url: '/bidding/supplier/claims',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询响应文件
export function getResponseFile(subpackageCode) {
  return request({
    url: '/bidding/supplier/answer',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询是否允许投标
export function getPermit(subpackageCode) {
  return request({
    url: '/bidding/bid.file/requirement/permit',
    method: 'get',
    params: { subpackageCode }
  })
}

// 响应文件上传
export function responseFileUpload(data) {
  return request({
    url: '/bidding/supplier/answer/up',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 响应文件解析
export function responseFileComplete(data) {
  return request({
    url: '/bidding/supplier/answer/complete',
    method: 'post',
    data: data
  })
}

// 响应文件上传
export function responseFilePdfUpload(data) {
  return request({
    url: '/bidding/supplier/answer/up/pdf',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 设置响应对照
export function setEvaluationMethod(data) {
  return request({
    url: '/bidding/supplier/answer/evaluationMethod',
    method: 'post',
    data: data
  })
}

// 设置报价表
export function setQuoteForm(data) {
  return request({
    url: '/bidding/supplier/answer/quoteForm',
    method: 'post',
    data: data
  })
}

// 采购文件下载
export function purchaseFileDown(subpackageCode) {
  return request({
    url: '/bidding/supplier/claims/load',
    method: 'get',
    responseType: 'blob',
    params: { subpackageCode }
  })
}

// 响应文件盖章并投递
export function responseFileReleaseAndStamp(data) {
  return request({
    url: '/bidding/supplier/answer/releaseAndStamp',
    method: 'post',
    data
  })
}

// 响应文件撤回
export function responseFileWithdraw(data) {
  return request({
    url: '/bidding/supplier/answer/revocation',
    method: 'post',
    data
  })
}

// 发布视频
export function demoVideoPost(data) {
  return request({
    url: '/bidding/supplier/answer/postDemoVideo',
    method: 'post',
    data
  })
}

// 撤回视频
export function demoVideoWithdraw(data) {
  return request({
    url: '/bidding/supplier/answer/recallDemoVideo',
    method: 'post',
    data
  })
}

// 供应商查询结果
export function getSupplierResult(subpackageCode) {
  return request({
    url: '/bidding/supplier/bidWinningResult',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询议价列表
export function getSupplierBargainList(subpackageCode) {
  return request({
    url: '/bidding/supplier/bargain/query',
    method: 'get',
    params: { subpackageCode }
  })
}

// 提交报价
export function confirmBargain(data) {
  return request({
    url: '/bidding/supplier/bargain/create',
    method: 'post',
    data
  })
}

// 保存付款凭证
export function savePaymentVoucher(data) {
  return request({
    url: '/bidding/supplier/paymentVoucher',
    method: 'post',
    data
  })
}

// 付款下单
export function payOrder(data) {
  return request({
    url: '/bidding/supplier/win/notice/pay',
    method: 'post',
    data
  })
}

// 付款下单
export function payCheck(subpackageCode) {
  return request({
    url: '/bidding/supplier/check-permission',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询响应附件
export function getResponseAtt(subpackageCode) {
  return request({
    url: '/bidding/supplier/answer/att',
    method: 'get',
    params: { subpackageCode }
  })
}

// 删除响应附件
export function delResponseAtt(data) {
  return request({
    url: '/bidding/supplier/answer/att/del',
    method: 'post',
    data
  })
}
