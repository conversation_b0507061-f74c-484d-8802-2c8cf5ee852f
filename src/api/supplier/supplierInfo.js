import request from '@/utils/request'

// 查询投标人企业信息
export function querySupplierInfo(userId) {
  return request({
    url: '/system/supplier/company/queryByUserId',
    method: 'get',
    params: { userId }
  })
}

// 保存投标人企业信息
export function saveSupplierInfo(data) {
  return request({
    url: '/system/supplier/company/create',
    method: 'post',
    data
  })
}

// 修改投标人企业信息
export function updateSupplierInfo(data) {
  return request({
    url: '/system/supplier/company/update',
    method: 'post',
    data
  })
}

// 申请停用账号
export function supplierApplySuspension() {
  return request({
    url: '/system/supplier/disable/request',
    method: 'post'
  })
}

// 查询资格证书
export function getCertificateList() {
  return request({
    url: '/system/supplier/company/getCertificateList',
    method: 'get'
  })
}

