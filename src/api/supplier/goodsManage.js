import request from '@/utils/request'
import { isEmpty } from '@/utils'

// 查询列表
export function listGoods(data) {
  return request({
    url: '/bidding/supplier/goods/page',
    method: 'post',
    data
  })
}

// 新增
export function addGoods(data) {
  return request({
    url: '/bidding/supplier/goods/save',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return transformRequest(data)
    }]
  })
}

// 修改
export function updateGoods(data) {
  return request({
    url: '/bidding/supplier/goods/update',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return transformRequest(data)
    }]
  })
}

function transformRequest(data) {
  let formData = new FormData()
  for (const key in data) {
    if (key === 'goodsDetail' || key === 'goodsPicList') {
      data[key].forEach((item, index) => {
        for (let itemKey in item) {
          if (!isEmpty(item[itemKey])) {
            formData.append(`${key}[${index}].${itemKey}`, item[itemKey])
          }
        }
      })
    } else {
      if (!isEmpty(data[key])) {
        formData.append(key, data[key])
      }
    }
  }
  return formData
}

// 上架下架
export function upAndDownGoods(data) {
  return request({
    url: '/bidding/supplier/goods/upAndDown',
    method: 'post',
    data
  })
}

// 删除
export function delGoods(data) {
  return request({
    url: '/bidding/supplier/goods/del',
    method: 'post',
    data
  })
}
