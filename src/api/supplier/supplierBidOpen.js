import request from '@/utils/request'

// 标段开标列表
export function querySupplierBidOpening(subpackageCode) {
  return request({
    url: '/bidding/supplier/sign/query/subpackageCode',
    method: 'get',
    params: { subpackageCode }
  })
}

// 签到
export function bidOpeningSign(data) {
  return request({
    url: '/bidding/supplier/answer/bidOpeningSign',
    method: 'post',
    data
  })
}

// 解密
export function bidOpeningDecrypt(data) {
  return request({
    url: '/bidding/supplier/answer/bidOpeningDecrypt',
    method: 'post',
    data
  })
}

// 开标记录表签字
export function bidOpeningSignature(data) {
  return request({
    url: '/bidding/supplier/answer/bidOpeningSignature',
    method: 'post',
    data
  })
}
