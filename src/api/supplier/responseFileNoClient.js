import request from '@/utils/request';

// 响应文件列表
export function responseFileList(subpackageCode) {
  return request({
    url: '/bidding/bid.file/requirement/query',
    method: 'get',
    params: { subpackageCode }
  })
}

// 响应文件保存
export function responseFileSave(data) {
  return request({
    url: '/bidding/bid.file/requirement/saves',
    method: 'post',
    data
  })
}

// 响应文件修改
export function responseFileUpdate(data) {
  return request({
    url: '/bidding/bid.file/requirement/updates',
    method: 'post',
    data
  })
}
