import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 应招未招检查
export function aiReviewYZWZ(data) {
  return request({
    url: '/bidding/ai/purchase/yzwz',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 资格条件检查
export function aiReviewZGTJ(data) {
  return request({
    url: '/bidding/ai/purchase/zgtj',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 评分办法检查
export function aiReviewPFBF(data) {
  return request({
    url: '/bidding/ai/purchase/pfbb',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 肢解项⽬检查
export function aiReviewZJXM(data) {
  return request({
    url: '/bidding/ai/purchase/zjxm',
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}
