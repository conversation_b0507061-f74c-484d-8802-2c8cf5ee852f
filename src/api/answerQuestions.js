import request from '@/utils/request'

// 查询答疑列表
export function listAnswerQuestions(params) {
  return request({
    url: '/bidding/purchase/process/answerList',
    method: 'get',
    params: params
  })
}

// 提问
export function putAnswerQuestions(data) {
  return request({
    url: '/bidding/purchase/process/submitAsk',
    method: 'post',
    data
  })
}

// 回复
export function replyAnswerQuestions(data) {
  return request({
    url: '/bidding/purchase/process/replyAsk',
    method: 'post',
    data
  })
}
