import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 生成授权文件
export function generateLic(data) {
  return request({
    url: '/auth/lic/generate',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function closeLicTip(data) {
  let token = data.token;
  return request({
    url: '/auth/lic/tip/close',
    method: 'post',
    headers: {
      'Authorization': token ? 'Bearer ' + token : null
    },
    data
  })
}

export function upLic(data) {
  let token = data.token;
  return request({
    url: '/auth/lic/up',
    method: 'post',
    data: data,
    dataType: 'formData',
    headers: {
      'Authorization': token ? 'Bearer ' + token : null
    },
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}
