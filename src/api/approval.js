import request from '@/utils/request';

// 查询已发起
export function getAlreadyApproval(data) {
  return request({
    url: '/bidding/audit/page',
    method: 'post',
    data
  })
}

// 查询我的审批
export function getPendingApproval(data) {
  return request({
    url: '/bidding/audit/queryByAuditor',
    method: 'post',
    data
  })
}

// 查询抄送给我
export function getCopyApproval(data) {
  return request({
    url: '/bidding/audit/queryByCarbonCopy',
    method: 'post',
    data
  })
}

// 查询审批详情
export function getApprovalDetail(id) {
  return request({
    url: '/bidding/audit/query',
    method: 'get',
    params: { id }
  })
}

// 发起审批
export function initiateApproval(data) {
  return request({
    url: '/bidding/audit/initiate',
    method: 'post',
    data
  })
}

// 审批
export function auditApproval(data) {
  return request({
    url: '/bidding/audit/audit',
    method: 'post',
    data
  })
}

// 保存审批附件
export function saveFileApproval(data) {
  return request({
    url: '/bidding/audit/upAtts',
    method: 'post',
    data
  })
}

// 评论
export function commentApproval(data) {
  return request({
    url: '/bidding/audit/comment',
    method: 'post',
    data
  })
}

// 撤回
export function cancelApprovalById(data) {
  return request({
    url: '/bidding/audit/cancel',
    method: 'post',
    data
  })
}

// 撤回
export function cancelApprovalByCode(auditCode) {
  return request({
    url: '/bidding/audit/cancel/auditCode',
    method: 'post',
    data: { auditCode }
  })
}

// 下载审批单
export function exportApprovalForm(auditCode) {
  return request({
    url: '/bidding/pdf/export/audit',
    method: 'get',
    responseType: 'blob',
    params: { auditCode }
  })
}

// 查询审批详情
export function queryNeedAuditCount() {
  return request({
    url: '/bidding/audit/queryNeedAudit',
    method: 'get'
  })
}
