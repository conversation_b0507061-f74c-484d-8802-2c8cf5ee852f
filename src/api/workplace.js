import request from '@/utils/request'

// 今日开标
export function getBidOpenToday(params) {
  return request({
    url: '/bidding/workbench/bidOPenItemPage',
    method: 'get',
    params
  })
}

// 我的审批-待办/已办
export function getMyApproval(params) {
  return request({
    url: '/bidding/workbench/audit/my',
    method: 'get',
    params
  })
}

// 供应商企业信息审核-待办/已办
export function getMySupplierAudit(params) {
  return request({
    url: '/bidding/workbench/supplier/company/audit',
    method: 'get',
    params
  })
}

// 供应商报名审核-待办/已办
export function getMySignUpAudit(params) {
  return request({
    url: '/bidding/workbench/supplier/sign/audit',
    method: 'get',
    params
  })
}

// 供应商评论-待办/已办
export function getMySupplierComment(params) {
  return request({
    url: '/bidding/workbench/supplier/commentPage',
    method: 'get',
    params
  })
}

// 消息列表
export function getMyMsg(params) {
  return request({
    url: '/system/user/msg/list',
    method: 'get',
    params
  })
}

// 消息一键已读
export function msgRead(data) {
  return request({
    url: '/system/user/msg/read',
    method: 'post',
    data
  })
}

// 待办/已办数量
export function getMyToDoCount() {
  return request({
    url: '/bidding/workbench/todo/count',
    method: 'get'
  })
}
