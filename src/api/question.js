import request from '@/utils/request'

// 查询质疑时间
export function getQuestionTime(subpackageCode) {
  return request({
    url: '/bidding/purchase/process/queryDoubtTime',
    method: 'get',
    params: { subpackageCode }
  })
}

// 保存质疑时间
export function saveQuestionTime(data) {
  return request({
    url: '/bidding/purchase/process/saveTime',
    method: 'post',
    data
  })
}

// 质疑列表
export function listQuestion(data) {
  return request({
    url: '/bidding/purchase/process/doubtList',
    method: 'post',
    data
  })
}

// 提出/回复质疑
export function putAndReplyQuestion(data) {
  return request({
    url: '/bidding/purchase/process/askDoubt',
    method: 'post',
    data
  })
}
