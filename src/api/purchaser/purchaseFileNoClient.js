import request from '@/utils/request';

// 采购文件列表
export function requirementList(data) {
  return request({
    url: '/bidding/tender.file/requirement/list',
    method: 'post',
    data
  })
}

// 采购文件增加
export function requirementAdd(data) {
  return request({
    url: '/bidding/tender.file/requirement/add',
    method: 'post',
    data
  })
}

// 采购文件修改
export function requirementUpdate(data) {
  return request({
    url: '/bidding/tender.file/requirement/updates',
    method: 'post',
    data
  })
}

// 采购文件删除
export function requirementDel(data) {
  return request({
    url: '/bidding/tender.file/requirement/dels',
    method: 'post',
    data
  })
}

// 查询供应商采购文件
export function requirementListBySupplier(data) {
  return request({
    url: '/bidding/tender.file/requirement/queryReject',
    method: 'post',
    data
  })
}

// 驳回
export function requirementRejection(data) {
  return request({
    url: '/bidding/tender.file/requirement/rejection',
    method: 'post',
    data
  })
}
