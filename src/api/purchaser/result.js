import request from '@/utils/request';

// 采购人查看中标结果页面
export function queryWinResultPage(buyItemCode) {
  return request({
    url: '/bidding/purchase/win/winPage',
    method: 'get',
    params: { buyItemCode }
  })
}

// 确认评审结果
export function confirmReviewResult(subpackageCode) {
  return request({
    url: '/bidding/purchase/win/confirmReviewResult',
    method: 'get',
    params: { subpackageCode }
  })
}

// 采购人发送通知书
export function sendNotice(data) {
  return request({
    url: '/bidding/purchase/win/sendNotice',
    method: 'post',
    data
  })
}

// 采购人设定中标人
export function saveWinResult(data) {
  return request({
    url: '/bidding/purchase/win/selectWin',
    method: 'post',
    data
  })
}
