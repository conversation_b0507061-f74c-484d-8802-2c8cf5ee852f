import request from '@/utils/request'

// 查询会议室列表
export function queryRoomList(data) {
  return request({
    url: '/bidding/meetingRoom/page',
    method: 'post',
    data
  })
}

// 新增会议室
export function addRoom(data) {
  return request({
    url: '/bidding/meetingRoom/add',
    method: 'post',
    data
  })
}

// 修改会议室
export function editRoom(data) {
  return request({
    url: '/bidding/meetingRoom/update',
    method: 'post',
    data
  })
}

// 删除会议室
export function removeRoom(data) {
  return request({
    url: '/bidding/meetingRoom/delete',
    method: 'post',
    data
  })
}

// 会议室状态
export function enabledRoom(data) {
  return request({
    url: '/bidding/meetingRoom/enabled',
    method: 'post',
    data
  })
}

// 查询会议室预约列表
export function queryRoomReservation(params) {
  return request({
    url: '/bidding/meetingRoom/schedule/page',
    method: 'get',
    params
  })
}

// 审核预约
export function auditReservation(data) {
  return request({
    url: '/bidding/meetingRoom/schedule/audit',
    method: 'post',
    data
  })
}

// 添加预约
export function addReservation(data) {
  return request({
    url: '/bidding/meetingRoom/schedule/add',
    method: 'post',
    data
  })
}

// 查询我的预约
export function queryMyReservation() {
  return request({
    url: '/bidding/meetingRoom/schedule/myList',
    method: 'post'
  })
}

// 删除预约
export function removeReservation(roomScheduleIds) {
  return request({
    url: '/bidding/meetingRoom/cancel',
    method: 'get',
    params: { roomScheduleIds }
  })
}

