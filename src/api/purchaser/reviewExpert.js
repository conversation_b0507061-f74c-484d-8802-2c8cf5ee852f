import request from '@/utils/request'

// 查询评委组列表
export function queryJudgeList(buyItemCode) {
  return request({
    url: '/bidding/extract/judge/queryJudgeList',
    method: 'get',
    params: { buyItemCode }
  })
}

// 查询抽取记录的评委
export function queryLogJudge(data) {
  return request({
    url: '/bidding/extract/judge/queryLogJudge',
    method: 'post',
    data
  })
}

// 根据评委昵称模糊匹配评委信息
export function getJudgeInfoByName(data) {
  return request({
    url: '/system/user/getJudgeInfoByName',
    method: 'post',
    data
  })
}

// 保存评委
export function saveExtractJudgeInfo(data) {
  return request({
    url: '/bidding/extract/judge/saveExtractJudgeInfo',
    method: 'post',
    data
  })
}

// 评委名单审批
export function auditJudgeInfo(data) {
  return request({
    url: '/bidding/extract/judge/auditJudgeInfo',
    method: 'post',
    data
  })
}
