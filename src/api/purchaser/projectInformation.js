import request from '@/utils/request';

// 项目资料列表
export function getProjectInformationList(data) {
  return request({
    url: `/bidding/project.before/page`,
    method: 'post',
    data
  })
}

// 项目资料删除
export function delProjectInformation(ids) {
  return request({
    url: `/bidding/project.before/del`,
    method: 'get',
    params: { ids }
  })
}

// 分派
export function addProjectAssign(data) {
  return request({
    url: `/bidding/project.before.allocate/allocate`,
    method: 'post',
    data
  })
}

// 分派信息查询
export function getProjectAssignInfo(externalProjectDataId) {
  return request({
    url: `/bidding/project.before.allocate/pageByUser`,
    method: 'get',
    params: { externalProjectDataId }
  })
}

// 分派信息删除
export function delProjectAssign(ids) {
  return request({
    url: `/bidding/project.before.allocate/del`,
    method: 'get',
    params: { ids }
  })
}

// 分派列表
export function getProjectAssignList(data) {
  return request({
    url: `/bidding/project.before.allocate/page`,
    method: 'post',
    data
  })
}

// 去采购查详情
export function getProjectAssignDetail(id) {
  return request({
    url: `/bidding/project.before.allocate/info`,
    method: 'get',
    params: { id }
  })
}

// 去采购查详情
export function queryNotPurchasedCount() {
  return request({
    url: `/bidding/project.before.allocate/cnt`,
    method: 'post'
  })
}

