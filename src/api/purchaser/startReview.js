import request from '@/utils/request';

// 查询确认评审页面列表
export function confirmReviewPage(buyItemCode) {
  return request({
    url: '/bidding/purchase/win/confirmReviewPage',
    method: 'get',
    params: { buyItemCode }
  })
}

// 确认会签
export function submitConfirmCounterSign(data) {
  return request({
    url: '/bidding/purchase/win/confirmCounterSign',
    method: 'post',
    data
  })
}

// 评审确认
export function submitConfirmReview(data) {
  return request({
    url: '/bidding/purchase/win/confirmReview',
    method: 'post',
    data
  })
}

// 保存投标报价基准分
export function saveSource(data) {
  return request({
    url: '/bidding/purchase/win/saveSource',
    method: 'post',
    data
  })
}

// 重新投票
export function againVote(subpackageCode) {
  return request({
    url: '/bidding/purchase/win/againVote',
    method: 'get',
    params: { subpackageCode }
  })
}

// 重新评审
export function againReview(subpackageCode) {
  return request({
    url: '/bidding/purchase/win/againConfirmReview',
    method: 'get',
    params: { subpackageCode }
  })
}
