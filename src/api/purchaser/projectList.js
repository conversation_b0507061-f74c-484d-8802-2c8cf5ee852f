import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 项目列表
export function queryProjectList(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/buyItemPage`,
    method: 'post',
    data
  })
}

// 查询项目详细信息
export function queryProjectInfo(buyItemCode) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/queryBuyItemInfo`,
    method: 'get',
    params: { buyItemCode }
  })
}

// 创建项目
export function createProjectInfo(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/createBuyItem`,
    method: 'post',
    data
  })
}

// 修改项目
export function modifyProjectInfo(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/updateBuyItemInfo`,
    method: 'post',
    data
  })
}

export function createProjectInfoByFile(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/createBuyItem`,
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

export function modifyProjectInfoByFile(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/updateBuyItemInfo`,
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 查询项目下标段列表
export function getSubpackageList(buyItemCode) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/subInfo`,
    method: 'get',
    params: { buyItemCode }
  })
}

// 删除项目
export function delBuyItemInfo(buyItemCode) {
  return request({
    url: `/bidding/itemSubpackage/delItem`,
    method: 'get',
    params: { buyItemCode }
  })
}

// 查询采购方式
export function queryPurchaseMethod(purchaseMethodCode) {
  return request({
    url: '/bidding/itemSubpackage/queryPurchaseMethod',
    method: 'get',
    params: { purchaseMethodCode }
  })
}

// 查询项目功能点
export function queryItemFunctionInfo(params) {
  return request({
    url: '/bidding/itemSubpackage/queryItemFunctionInfo',
    method: 'get',
    params
  })
}

// 查询当前登录人所拥有的组织信息
export function currentOrgInfo() {
  return request({
    url: '/bidding/itemSubpackage/currentOrgInfo',
    method: 'get'
  })
}

// 查询当前组织下的所有人员
export function queryOrgPerson(orgCode) {
  return request({
    url: '/bidding/itemSubpackage/queryOrgPerson',
    method: 'get',
    params: { orgCode }
  })
}

// 添加标签
export function addLabel(data) {
  return request({
    url: `/bidding/itemSubpackage/addLabel`,
    method: 'post',
    data
  })
}

// 删除标签
export function delLabel(data) {
  return request({
    url: `/bidding/itemSubpackage/delLabel`,
    method: 'post',
    data
  })
}

// 项目报表生成
export function generateProjectReport(data) {
  return request({
    url: `/bidding/project/export`,
    method: 'post',
    data
  })
}

// 项目报表下载
export function downloadProjectReport() {
  return request({
    url: '/bidding/project/download',
    method: 'get',
    responseType: 'blob'
  })
}

// 申购项目列表
export function queryApplyProjectListByDingTalk(params) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/dingtalk/projectRequestPage`,
    method: 'get',
    params
  })
}

// 申购项目详情
export function queryApplyProjectInfoByDingTalk(dingTalkId) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/dingtalk/dingtalkProjectInfo`,
    method: 'get',
    params: { dingTalkId }
  })
}

// 申购项目信息确认
export function confirmApplyProjectInfoByDingTalk(dingTalkId) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/dingtalk/dingtalkEnterInfo`,
    method: 'get',
    params: { dingTalkId }
  })
}

// 申购项目修改
export function editApplyProjectInfoByDingTalk(data) {
  return request({
    url: `/bidding/${process.env.VUE_APP_VERSION_TYPE}.buyItem/dingtalk/dingtalkEditInfo`,
    method: 'post',
    data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 项目完成
export function clickComplete(buyItemCode) {
  return request({
    url: '/bidding/purchase/win/completeItem',
    method: 'get',
    params: { buyItemCode }
  })
}
