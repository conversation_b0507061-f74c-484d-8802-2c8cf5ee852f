import request from '@/utils/request';

// 调研报告
export function queryReport(buyItemCode) {
  return request({
    url: '/bidding/purchase.report/reportInfo',
    method: 'get',
    params: { buyItemCode }
  })
}

export function editReport(data) {
  return request({
    url: '/bidding/purchase.report/editReport',
    method: 'post',
    data
  })
}

// 调研报告
export function generateReportPDF(data) {
  return request({
    url: '/bidding/purchase.report/initReport',
    method: 'post',
    data
  })
}

export function removeReport(subpackageCode) {
  return request({
    url: '/bidding/purchase.report/clear',
    method: 'get',
    params: { subpackageCode }
  })
}

export function signReport(params) {
  return request({
    url: '/bidding/purchase.report/purchaseSign',
    method: 'get',
    params
  })
}

export function queryReportByExpert(subpackageCode) {
  return request({
    url: '/review/review/researchInformation',
    method: 'get',
    params: { subpackageCode }
  })
}

