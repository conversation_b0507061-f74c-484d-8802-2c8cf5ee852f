import request from '@/utils/request'

// 查询企业信息
export function querySupplierInfoByAudit(userId) {
  return request({
    url: '/system/supplier/company/permit/queryByUserId',
    method: 'get',
    params: { userId }
  })
}

// 审核页面查询供应商列表
export function querySupplierList(data) {
  return request({
    url: '/system/user/supplier/page',
    method: 'post',
    data
  })
}

// 设置供应商等级
export function editSupplierGrade(params) {
  return request({
    url: '/system/user/editSupplierGrade',
    method: 'get',
    params
  })
}

// 添加供应商信息
export function addSupplier(data) {
  return request({
    url: '/system/supplier/company/addSupplier',
    method: 'post',
    data
  })
}

// 设置入库状态
export function updateStorageType(params) {
  return request({
    url: '/system/supplier/company/updateStorageType',
    method: 'get',
    params
  })
}
