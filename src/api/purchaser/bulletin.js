import request from '@/utils/request'

// 查询模板内容
export function getTemplateContent(params) {
  return request({
    url: '/bidding/bulletin/getNoticeTemplate',
    method: 'get',
    params
  })
}

// 查询填充后的模板内容
export function fillTemplate(data) {
  return request({
    url: '/bidding/bulletin/fillTemplate',
    method: 'post',
    data
  })
}

// 发布采购公告
export function publishProcurementBulletin(data) {
  return request({
    url: '/bidding/bulletin/publishTenderBulletin',
    method: 'post',
    data
  })
}

// 发布公告
export function publishBulletin(data) {
  return request({
    url: '/bidding/bulletin/publishBulletin',
    method: 'post',
    data
  })
}

// 修改公告
export function updateBulletin(data) {
  return request({
    url: '/bidding/bulletin/updateBulletin',
    method: 'post',
    data
  })
}

// 查询公告列表
export function listBulletinInner(buyItemCode) {
  return request({
    url: '/bidding/bulletin/innerBulletinPage',
    method: 'get',
    params: { buyItemCode }
  })
}

// 查询采购公告列表
export function listProcurementBulletin(buyItemCode) {
  return request({
    url: '/bidding/bulletin/innerTenderBulletinPage',
    method: 'get',
    params: { buyItemCode }
  })
}

// 公告删除
export function delBulletin(bulletinId) {
  return request({
    url: '/bidding/bulletin/delBulletin',
    method: 'get',
    params: { bulletinId }
  })
}

// 撤回审核
export function rollBackAudit(data) {
  return request({
    url: '/bidding/bulletin/backAudit',
    method: 'post',
    data
  })
}

// 查询自己组织中审核通过的供应商列表
export function findByCompanyAndOrg(params) {
  return request({
    url: '/bidding/bulletin/purchase/supplier/findByCompanyAndOrg',
    method: 'get',
    params
  })
}

// 查询公告审核列表
export function listBulletin(data) {
  return request({
    url: '/bidding/bulletin/bulletinPage',
    method: 'post',
    data
  })
}

// 审核公告
export function auditBulletin(data) {
  return request({
    url: '/bidding/bulletin/auditBulletin',
    method: 'post',
    data
  })
}

// 公告是否展示
export function showBulletin(params) {
  return request({
    url: '/bidding/bulletin/showBulletin',
    method: 'get',
    params
  })
}

// 公告详情
export function getBulletinInfo(bulletinId) {
  return request({
    url: '/bidding/bulletin/bulletinInfo',
    method: 'get',
    params: { bulletinId }
  })
}

// 查询项目时间
export function getTimeBySubpackageCode(subpackageCode) {
  return request({
    url: '/bidding/bulletin/bulletinItemTimeInfo',
    method: 'get',
    params: { subpackageCode }
  })
}
