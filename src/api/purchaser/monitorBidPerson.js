import request from '@/utils/request'

export function getMonitorInfo(buyItemCode) {
  return request({
    url: '/bidding/monitor/monitorInfo',
    method: 'get',
    params: { buyItemCode }
  })
}

export function addMonitor(data) {
  return request({
    url: '/bidding/monitor/addMonitor',
    method: 'post',
    data
  })
}

export function monitorInfoList(data) {
  return request({
    url: '/bidding/monitor/monitorPage',
    method: 'post',
    data
  })
}

export function signMonitor(params) {
  return request({
    url: '/bidding/monitor/signMonitor',
    method: 'get',
    params
  })
}
