import request from '@/utils/request'

// 查询开标列表
export function listBidOpening(subpackageCode) {
  return request({
    url: '/bidding/purchase/bidOpening/bidder/list',
    method: 'get',
    params: { subpackageCode }
  })
}

// 开标开始
export function startBidOpening(data) {
  return request({
    url: '/bidding/purchase/bidOpening/start',
    method: 'post',
    data
  })
}

// 开标完成
export function endBidOpening(data) {
  return request({
    url: '/bidding/purchase/bidOpening/complete',
    method: 'post',
    data
  })
}

// 唱标
export function singBidOpening(data) {
  return request({
    url: '/bidding/purchase/bidOpening/sing',
    method: 'post',
    data
  })
}

// 重新开标
export function restartBidOpening(data) {
  return request({
    url: '/bidding/purchase/bidOpening/restart',
    method: 'post',
    data
  })
}

// 查询开标状态
export function statusBidOpening(subpackageCode) {
  return request({
    url: '/bidding/purchase/bidOpening/query',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询投标人报价表
export function queryBidderQuote(params) {
  return request({
    url: '/bidding/purchase/process/quoteFormInfo',
    method: 'get',
    params
  })
}
