import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 采购文件上传
export function uploadPurchaseFile(data) {
  return request({
    url: '/bidding/purchase/claims/up',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 采购文件上传PDF
export function uploadPurchaseFilePdf(data) {
  return request({
    url: '/bidding/purchase/claims/uploadPDF',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 采购人上传评审项
export function uploadEvaluation(data) {
  return request({
    url: '/bidding/purchase/claims/uploadEvaluation',
    method: 'post',
    data
  })
}

// 采购人上传附件
export function uploadPurchaseAtt(data) {
  return request({
    url: '/bidding/purchase/claims/answer/up/att',
    method: 'post',
    data
  })
}

// 查询采购附件
export function getPurchaseAtt(subpackageCode) {
  return request({
    url: '/bidding/purchase/claims/answer/att',
    method: 'get',
    params: { subpackageCode }
  })
}

// 删除采购附件
export function delPurchaseAtt(ids) {
  return request({
    url: '/bidding/purchase/claims/answer/att/del',
    method: 'get',
    params: { ids }
  })
}

// 确认发布采购文件
export function releaseAndStampPurchaseFile(data) {
  return request({
    url: '/bidding/purchase/claims/releaseAndStamp',
    method: 'post',
    data
  })
}

// 查询采购文件
export function getPurchaseFile(buyItemCode) {
  return request({
    url: '/bidding/purchase/claims',
    method: 'get',
    params: { buyItemCode }
  })
}

// 查询其他标段采购文件
export function getPurchaseFileList(data) {
  return request({
    url: '/bidding/purchase/claims/page',
    method: 'post',
    data
  })
}

// 引入其它包的采购文件
export function importOtherFile(data) {
  return request({
    url: '/bidding/purchase/claims/importOtherFile',
    method: 'post',
    data
  })
}

// 将采购文件应用到其他包
export function applyToOtherPackages(data) {
  return request({
    url: '/bidding/purchase/claims/applyToOtherPackages',
    method: 'post',
    data
  })
}

// 采购文件解析
export function purchaseFileComplete(data) {
  return request({
    url: '/bidding/purchase/claims/complete',
    method: 'post',
    data
  })
}
