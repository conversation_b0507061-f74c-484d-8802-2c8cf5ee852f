import request from '@/utils/request'

// 查询议价列表
export function listBargaining(buyItemCode) {
  return request({
    url: '/bidding/purchase/bargain/list',
    method: 'get',
    params: { buyItemCode }
  })
}

// 发起、结束议价
export function startOrEndBargain(data) {
  return request({
    url: '/bidding/purchase/bargain/startOrEndBargain',
    method: 'post',
    data
  })
}

// 导出议价结果表
export function exportExcelBargaining(buyItemCode) {
  return request({
    url: '/bidding/excel/exportBargainExcel',
    method: 'post',
    responseType: 'blob',
    data: { buyItemCode }
  })
}
