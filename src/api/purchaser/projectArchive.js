import request from '@/utils/request';

// 项目归档列表
export function queryProjectArchiveList(data) {
  return request({
    url: `/bidding/purchase/process/buyItemFilePage`,
    method: 'post',
    data
  })
}

// 打包
export function projectFilesByZip(params) {
  return request({
    url: `/epcfile/projectFile/projectFilesByzip`,
    method: 'get',
    params
  })
}

// 批量下载
export function downloadFileList(params) {
  return request({
    url: `/epcfile/projectFile/downloadFileList`,
    method: 'get',
    responseType: 'blob',
    params: params
  })
}

