import request from '@/utils/request';

// 根据报名列表
export function supplierSignList(buyItemCode) {
  return request({
    url: '/bidding/purchase/process/supplierSignList',
    method: 'get',
    params: { buyItemCode }
  })
}

// 是否允许响应
export function signPermit(data) {
  return request({
    url: '/bidding/purchase/process/allow',
    method: 'post',
    data
  })
}

// 是否合格供应商
export function signQualify(data) {
  return request({
    url: '/bidding/purchase/process/qualify',
    method: 'post',
    data
  })
}

// 查看响应附件条件
export function getSupplierCondition(params) {
  return request({
    url: '/bidding/purchase/process/supplierCondition',
    method: 'get',
    params
  })
}

// 报表导出
export function exportExcelOfRegisteredBidders(subpackageCode) {
  return request({
    url: '/bidding/purchase/process/exportExcelOfRegisteredBidders',
    method: 'get',
    responseType: 'blob',
    params: { subpackageCode }
  })
}

// 查看供应商对比
export function supplierSignContrast(subpackageCode) {
  return request({
    url: '/bidding/purchase/process/supplierSignContrast',
    method: 'get',
    params: { subpackageCode }
  })
}

// 报名信息审核
export function auditSignInfo(data) {
  return request({
    url: '/bidding/purchase/process/auditSignInfo',
    method: 'post',
    data
  })
}
