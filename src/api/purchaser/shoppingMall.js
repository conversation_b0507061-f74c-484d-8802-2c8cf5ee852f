import request from '@/utils/request'

// 查详情
export function getGoodsInfo(goodsId) {
  return request({
    url: '/bidding/purchaser/goods/goodsInfo',
    method: 'get',
    params: { goodsId }
  })
}

// 加购物车
export function addCart(data) {
  return request({
    url: '/bidding/purchaser/goods/addCart',
    method: 'post',
    data
  })
}

// 购物车
export function cartList(data) {
  return request({
    url: '/bidding/purchaser/goods/cartList',
    method: 'post',
    data
  })
}

// 购物车删除
export function delCart(data) {
  return request({
    url: '/bidding/purchaser/goods/delCart',
    method: 'post',
    data
  })
}

// 购物车
export function cartInfo(cartIds) {
  return request({
    url: '/bidding/purchaser/goods/cartInfo',
    method: 'get',
    params: { cartIds }
  })
}
