import request from '@/utils/request';

// 抽取评委
export function expertExtract(data) {
  return request({
    url: '/bidding/extract/judge/extract',
    method: 'post',
    data
  })
}

// 保存抽取的评委
export function saveExpertExtract(data) {
  return request({
    url: '/bidding/extract/judge/saveJudge',
    method: 'post',
    data
  })
}

// 查询抽取评委记录列表
export function getExtractRecordList(data) {
  return request({
    url: '/bidding/extract/judge/extractJudgeLog',
    method: 'post',
    data
  })
}

// 查询抽取评委记录详情
export function queryExpertInfo(data) {
  return request({
    url: '/bidding/extract/judge/queryJudgeInfo',
    method: 'post',
    data
  })
}

// 查询项目
export function queryProjectName(data) {
  return request({
    url: '/bidding/extract/judge/queryBuyItemName',
    method: 'post',
    data
  })
}

// 重新抽取
export function againExtract(data) {
  return request({
    url: '/bidding/extract/judge/againExtract',
    method: 'post',
    data
  })
}

// 查询可保存评委的项目
export function queryExtractBuyItemInfo(data) {
  return request({
    url: '/bidding/extract/judge/queryExtractBuyItemInfo',
    method: 'post',
    data
  })
}

// 保存评委到项目
export function batchSaveExtractJudgeInfo(data) {
  return request({
    url: '/bidding/extract/judge/batchSaveExtractJudgeInfo',
    method: 'post',
    data
  })
}

// 标记
export function markJudge(data) {
  return request({
    url: '/bidding/extract/judge/markJudge',
    method: 'post',
    data
  })
}

// 删除记录
export function delJudgeLog(data) {
  return request({
    url: '/bidding/extract/judge/delJudgeLog',
    method: 'post',
    data
  })
}
