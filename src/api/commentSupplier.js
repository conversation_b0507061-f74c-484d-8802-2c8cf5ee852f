import request from '@/utils/request';

// 添加供应商评论
export function addSupplierComment(data) {
  return request({
    url: '/bidding/comment/commentSupplier',
    method: 'post',
    data
  })
}

// 删除供应商评论
export function deleteSupplierComment(id) {
  return request({
    url: '/bidding/comment/deleteSupplierComment',
    method: 'get',
    params: { id }
  })
}

// 当前项目中供应商评论
export function getSupplierCommentByProject(params) {
  return request({
    url: '/bidding/comment/supplierCommentInfo',
    method: 'get',
    params
  })
}

// 查询供应商的所有评论
export function getSupplierCommentAll(supplierId) {
  return request({
    url: '/system/user/getSupplierCommentInfo',
    method: 'get',
    params: { supplierId }
  })
}
