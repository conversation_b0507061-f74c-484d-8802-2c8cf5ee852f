import request from '@/utils/request'
import { tansParamsFormData } from '@/utils'

// 文件上传
export function fileUploadByUrl(url = '/epcfile/projectFile/upload', data) {
  return request({
    url: url,
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

/** 项目内归档文件 */

// 项目内归档文件上传
export function fileUpload(data) {
  return request({
    url: '/epcfile/projectFile/upload',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 项目内归档文件下载
export function fileDown(fileKey) {
  return request({
    url: '/epcfile/projectFile/download',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 项目内归档文件删除
export function fileDelete(fileKey) {
  return request({
    url: '/epcfile/projectFile/deleteFileKey',
    method: 'get',
    params: { fileKey }
  })
}

/** 项目内非归档文件 */

// 项目内非归档文件上传
export function fileUploadByAnnex(data) {
  return request({
    url: '/epcfile/projectFile/uploadEnclosure',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 项目内非归档文件下载
export function fileDownByAnnex(fileKey) {
  return request({
    url: '/epcfile/projectFile/downloadEnclosure',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 项目内非归档文件删除
export function fileDeleteByAnnex(fileKey) {
  return request({
    url: '/epcfile/projectFile/deleteEnclosureFileKey',
    method: 'get',
    params: { fileKey }
  })
}

/** 非项目文件 */

// 非项目文件上传
export function fileUploadByOther(data) {
  return request({
    url: '/epcfile/nonProjectFile/upload',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}

// 非项目文件下载
export function fileDownByOther(fileKey) {
  return request({
    url: '/epcfile/nonProjectFile/download',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 非项目文件删除
export function fileDeleteByOther(fileKey) {
  return request({
    url: '/epcfile/nonProjectFile/delete',
    method: 'get',
    params: { fileKey }
  })
}

/** 公开文件 */

// 公开文件列表
export function getFileListByPublic(params) {
  return request({
    url: '/epcfile/nonProjectFile/public/chartList',
    method: 'get',
    params
  })
}

// 公开文件下载
export function fileDownByPublic(fileKey) {
  return request({
    url: '/epcfile/nonProjectFile/public/downloadChart',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 商品图片下载
export function fileDownByGoods(fileKey) {
  return request({
    url: '/epcfile/productFile/download',
    method: 'get',
    responseType: 'blob',
    params: { fileKey }
  })
}

// 下载本地模版
export function downTemplate(fileName) {
  return request({
    baseURL: process.env.VUE_APP_BASE_URL, // url = base url + request url
    url: `/${fileName}`,
    method: 'get',
    responseType: 'blob' // 重要
  })
}

// 轮播图上传
export function fileUploadByCarousel(data) {
  return request({
    url: '/epcfile/nonProjectFile/imageUpload',
    method: 'post',
    data: data,
    dataType: 'formData',
    transformRequest: [function (data) {
      return tansParamsFormData(data)
    }]
  })
}
