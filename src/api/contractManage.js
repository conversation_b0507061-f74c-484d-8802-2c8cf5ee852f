import request from '@/utils/request'

// 供应商编辑合同
export function addSupplierContract(data) {
  return request({
    url: '/bidding/contract/add',
    method: 'post',
    data
  })
}

// 合同修改
export function updateSupplierContract(data) {
  return request({
    url: '/bidding/contract/update',
    method: 'post',
    data
  })
}

// 合同提交采购人
export function publishSupplierContract(id) {
  return request({
    url: '/bidding/contract/publish',
    method: 'get',
    params: { id }
  })
}

// 合同确认
export function confirmSupplierContract(id) {
  return request({
    url: '/bidding/contract/confirm',
    method: 'post',
    data: { id }
  })
}

// 合同上传
export function upSupplierContract(data) {
  return request({
    url: '/bidding/contract/upAttachment',
    method: 'post',
    data: data
  })
}

// 合同列表
export function listContract(buyItemCode) {
  return request({
    url: '/bidding/purchase.contract/list',
    method: 'get',
    params: { buyItemCode }
  })
}

// 合同审批
export function auditContract(data) {
  return request({
    url: '/bidding/purchase.contract/auditContract',
    method: 'post',
    data
  })
}

// 合同修改
export function editContractTemplate(data) {
  return request({
    url: '/bidding/purchase.contract/editContractTemplate',
    method: 'post',
    data
  })
}

// 合同退回
export function backContractTemplate(data) {
  return request({
    url: '/bidding/purchase.contract/backContractTemplate',
    method: 'post',
    data
  })
}
