import request from '@/utils/request';

// 评委评审列表
export function listReviewManage(params) {
  return request({
    url: '/review/review/subpackageList',
    method: 'get',
    params
  })
}

// 评委投票列表
export function listChoiceLeader(subpackageCode) {
  return request({
    url: '/review/choiceLeader/list',
    method: 'get',
    params: { subpackageCode }
  })
}

// 评委投票
export function addChoiceLeader(params) {
  return request({
    url: '/review/choiceLeader/add',
    method: 'get',
    params
  })
}

// 获取评审功能列表的顺序以及是否显示
export function processNodesList(subpackageCode) {
  return request({
    url: '/review/review/processNodesList',
    method: 'get',
    params: { subpackageCode }
  })
}

// 获取组长
export function getLeader(subpackageCode) {
  return request({
    url: '/review/review/leader',
    method: 'get',
    params: { subpackageCode }
  })
}

// 进入评审的供应商列表
export function listReviewSupplier(subpackageCode) {
  return request({
    url: '/review/review/supplierList',
    method: 'get',
    params: { subpackageCode }
  })
}

// 查询评审办法
export function getEvaluationMethod(params) {
  return request({
    url: '/review/review/query',
    method: 'get',
    params
  })
}

// 保存评审数据
export function saveReviewData(data) {
  return request({
    url: '/review/review/submit',
    method: 'post',
    data
  })
}

// 点击下一步
export function clickNextStep(subpackageCode) {
  return request({
    url: '/review/review/nextStep',
    method: 'get',
    params: { subpackageCode }
  })
}

// 组长同意合格制评审结论
export function groupLeaderOk(params) {
  return request({
    url: '/review/review/groupLeaderOk',
    method: 'get',
    params
  })
}

// 查询目录,响应文件url,演示视频url
export function getResponseFileMenu(params) {
  return request({
    url: '/review/review/respFile',
    method: 'get',
    params
  })
}

// 查询附件
export function getReviewResponseAtt(params) {
  return request({
    url: '/review/review/answer/att',
    method: 'get',
    params
  })
}

// 获取评审结果
export function queryReviewResults(subpackageCode) {
  return request({
    url: '/review/review/reviewSummary',
    method: 'get',
    params: { subpackageCode }
  })
}

// 评委提交评审结果
export function setResultRanking(data) {
  return request({
    url: '/review/review/setRank',
    method: 'post',
    data
  })
}

// 评委签章
export function expertSigne(params) {
  return request({
    url: '/review/review/sign',
    method: 'get',
    params
  })
}

// 设置评审方式
export function examineReview(params) {
  return request({
    url: '/review/review/examineReview',
    method: 'get',
    params
  })
}

// 查询所有评审办法
export function getEvaluationMethodAll(params) {
  return request({
    url: '/review/review/querySurvey',
    method: 'get',
    params
  })
}

// 查询项目文件
export function getProjectFileBySubCode(params) {
  return request({
    url: '/review/review/getProjectFileBySubCode',
    method: 'get',
    params
  })
}

// 获取投票列表
export function getSupListToRecord(subpackageCode) {
  return request({
    url: '/review/review/getSupListToRecord',
    method: 'get',
    params: { subpackageCode }
  })
}

// 评审投票
export function reviewVote(params) {
  return request({
    url: '/review/review/vote',
    method: 'get',
    params
  })
}

// 投票结果列表
export function getSupListToEnd(subpackageCode) {
  return request({
    url: '/review/review/getSupListToEnd',
    method: 'get',
    params: { subpackageCode }
  })
}

export function downReviewPdf(params) {
  return request({
    url: '/review/review.pdf/down.compare',
    method: 'get',
    responseType: 'blob',
    params: params
  })
}
