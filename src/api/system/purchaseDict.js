import request from '@/utils/request'

// 查询采购字典列表
export function listDictionary(query) {
  return request({
    url: '/system/dictionary/public/list',
    method: 'get',
    params: query
  })
}

// 新增采购字典
export function addDictionary(data) {
  return request({
    url: '/system/dictionary/addDictionary',
    method: 'post',
    data: data
  })
}

// 修改采购字典
export function updateDictionary(data) {
  return request({
    url: '/system/dictionary/updateDictionaryById',
    method: 'post',
    data: data
  })
}

// 删除采购字典
export function delDictionary(purchasePersonalizedDictionaryId) {
  return request({
    url: '/system/dictionary/removeById',
    method: 'get',
    params: { purchasePersonalizedDictionaryId }
  })
}

// 查询采购字典详细
export function getDictionary(purchasePersonalizedDictionaryId) {
  return request({
    url: '/system/dictionary/getDictionaryInfo',
    method: 'get',
    params: { purchasePersonalizedDictionaryId }
  })
}
