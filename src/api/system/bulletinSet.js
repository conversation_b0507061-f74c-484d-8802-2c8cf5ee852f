import request from '@/utils/request'

// 查询
export function listBulletinSet(query) {
  return request({
    url: '/system/method/announcement/findByPage',
    method: 'get',
    params: query
  })
}

// 新增
export function addBulletinSet(data) {
  return request({
    url: '/system/method/announcement/create',
    method: 'post',
    data: data
  })
}

// 修改
export function updateBulletinSet(data) {
  return request({
    url: '/system/method/announcement/upByAnnouncementId',
    method: 'post',
    data: data
  })
}

// 删除
export function delBulletinSet(data) {
  return request({
    url: '/system/method/announcement/delByAnnouncementId',
    method: 'post',
    data
  })
}
