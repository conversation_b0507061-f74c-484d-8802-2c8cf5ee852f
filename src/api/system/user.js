import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user',
    method: 'get',
    params: { userId }
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 新增评委用户
export function addUserByExpert(data) {
  return request({
    url: '/system/user/expert/add',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user/editup',
    method: 'post',
    data: data
  })
}

// 查询专家列表
export function listExpert(query) {
  return request({
    url: '/system/user/expert/page',
    method: 'get',
    params: query
  })
}

// 修改专家
export function updateSupplierOrExpert(data) {
  return request({
    url: '/system/user/supplierOrExpert/edit',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delUser(userIds, orgCode) {
  return request({
    url: '/system/user/remove',
    method: 'get',
    params: { userIds, orgCode }
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'post',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'post',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile/updateProfile',
    method: 'post',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword, token) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'post',
    headers: {
      'Authorization': token ? 'Bearer ' + token : null
    },
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole',
    method: 'get',
    params: { userId }
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'get',
    params: data
  })
}

// 黑名单
export function updateBlacklist(params) {
  return request({
    url: '/system/user/supplier/updateBlacklist',
    method: 'get',
    params
  })
}

// 查询采购人用户列表
export function listPurchaserUser(query) {
  return request({
    url: '/system/user/purchaseList',
    method: 'get',
    params: query
  })
}
