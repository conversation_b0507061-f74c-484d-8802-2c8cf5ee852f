import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: '/system/notice/public',
    method: 'get',
    params: { noticeId }
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: '/system/notice/editup',
    method: 'post',
    data: data
  })
}

// 删除公告
export function delNotice(noticeIds) {
  return request({
    url: '/system/notice/remove',
    method: 'get',
    params: { noticeIds }
  })
}
