import request from '@/utils/request'

// 查询模板列表
export function listTemplate(query) {
  return request({
    url: '/system/tmp/findByPage',
    method: 'get',
    params: query
  })
}

// 新增模板
export function addTemplate(data) {
  return request({
    url: '/system/tmp/addTmp',
    method: 'post',
    data: data
  })
}

// 修改模板
export function updateTemplate(data) {
  return request({
    url: '/system/tmp/updateTmp',
    method: 'post',
    data: data
  })
}

// 删除模板
export function delTemplate(tmpId) {
  return request({
    url: '/system/tmp/delTmp',
    method: 'get',
    params: { tmpId }
  })
}

// 查询模板详细
export function getTemplate(tmpId) {
  return request({
    url: '/system/tmp/getContent',
    method: 'get',
    params: { tmpId }
  })
}

// 根据分组查询模板列表
export function listTemplateByType(tmpType) {
  return request({
    url: '/system/tmp/findByType',
    method: 'get',
    params: { tmpType }
  })
}

// 预览模板内容PDF
export function templatePreview(data) {
  return request({
    url: '/bidding/bulletin/filePreview',
    method: 'post',
    responseType: 'blob',
    data
  })
}
