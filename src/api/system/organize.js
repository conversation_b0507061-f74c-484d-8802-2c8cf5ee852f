import request from '@/utils/request'

// 查询列表
export function listOrganize(data) {
  return request({
    url: '/system/organize/orgList',
    method: 'post',
    data
  })
}

// 新增
export function addOrganize(data) {
  return request({
    url: '/system/organize/addOrg',
    method: 'post',
    data: data
  })
}

// 修改
export function updateOrganize(data) {
  return request({
    url: '/system/organize/editOrg',
    method: 'post',
    data: data
  })
}

// 删除
export function delOrganize(organizeId) {
  return request({
    url: '/system/organize/delOrg',
    method: 'get',
    params: { organizeId }
  })
}

// 绑定组织
export function insertOrganize(data) {
  return request({
    url: '/system/organize/batchInsert',
    method: 'post',
    data: data
  })
}

// 修改绑定组织
export function updateInsertOrganize(data) {
  return request({
    url: '/system/organize/batchUpdate',
    method: 'post',
    data: data
  })
}

// 审核
export function auditOrganize(data) {
  return request({
    url: '/system/organize/audit',
    method: 'post',
    data: data
  })
}

// 查询组织信息
export function queryPurchaserInfo(orgCode) {
  return request({
    url: '/system/tender.info/queryTender',
    method: 'get',
    params: { orgCode }
  })
}

// 保存组织信息
export function savePurchaserInfo(data) {
  return request({
    url: '/system/tender.info/addTender',
    method: 'post',
    data
  })
}

// 修改组织信息
export function updatePurchaserInfo(data) {
  return request({
    url: '/system/tender.info/updateTender',
    method: 'post',
    data
  })
}
