import request from '@/utils/request'

// 查询功能列表
export function listFunction(query) {
  return request({
    url: '/system/function/list',
    method: 'get',
    params: query
  })
}

// 新增功能
export function addFunction(data) {
  return request({
    url: '/system/function/addFunction',
    method: 'post',
    data: data
  })
}

// 修改功能
export function updateFunction(data) {
  return request({
    url: '/system/function/updateFunctionInfoById',
    method: 'post',
    data: data
  })
}

// 删除功能
export function delFunction(purchaseFunctionId) {
  return request({
    url: '/system/function/removeById',
    method: 'get',
    params: { purchaseFunctionId }
  })
}

// 查询功能详细
export function getFunction(purchaseFunctionId) {
  return request({
    url: '/system/function/getFunctionInfoById',
    method: 'get',
    params: { purchaseFunctionId }
  })
}

// 查询功能下拉树结构
export function treeFunction(query) {
  return request({
    url: '/system/function/showFunctionPoint',
    method: 'get',
    params: query
  })
}
