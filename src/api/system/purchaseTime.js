import request from '@/utils/request'

// 设置采购时间
export function addPurchaseTime(data) {
  return request({
    url: '/system/method/addOrUpdateMethodAndNoticeTime',
    method: 'post',
    data: data
  })
}

// 查询采购时间
export function getPurchaseTime(purchaseMethodCode) {
  return request({
    url: '/system/method/getNoticeTimeByMethodCode',
    method: 'get',
    params: { purchaseMethodCode }
  })
}
