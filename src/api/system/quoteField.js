import request from '@/utils/request'

// 查询标价表字段列表
export function listQuoteField(data) {
  return request({
    url: '/bidding/attribute/page',
    method: 'post',
    data
  })
}

// 新增标价表字段
export function addQuoteField(data) {
  return request({
    url: '/bidding/attribute/create',
    method: 'post',
    data: data
  })
}

// 修改标价表字段
export function updateQuoteField(data) {
  return request({
    url: '/bidding/attribute/update',
    method: 'post',
    data: data
  })
}

// 删除标价表字段
export function delQuoteField(ids) {
  return request({
    url: '/bidding/attribute/del',
    method: 'post',
    data: ids
  })
}

// 查询所有字段列表
export function allQuoteField(params) {
  return request({
    url: '/bidding/attribute/list',
    method: 'get',
    params
  })
}
