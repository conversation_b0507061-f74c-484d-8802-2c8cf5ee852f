import request from '@/utils/request'

// 查询采购方式列表
export function listMethod(query) {
  return request({
    url: '/system/method/list',
    method: 'get',
    params: query
  })
}

// 新增采购方式
export function addMethod(data) {
  return request({
    url: '/system/method/addMethod',
    method: 'post',
    data: data
  })
}

// 修改采购方式
export function updateMethod(data) {
  return request({
    url: '/system/method/updateMethodById',
    method: 'post',
    data: data
  })
}

// 删除采购方式
export function delMethod(purchaseMethodId) {
  return request({
    url: '/system/method/removeById',
    method: 'get',
    params: { purchaseMethodId }
  })
}

// 查询采购方式详细
export function getMethod(purchaseMethodId) {
  return request({
    url: '/system/method/getMethodInfoById',
    method: 'get',
    params: { purchaseMethodId }
  })
}
