import request from '@/utils/request'

// 查询模板列表
export function listApprovalTemplate(data) {
  return request({
    url: '/system/audit.tmp/auditTmpList',
    method: 'post',
    data
  })
}

// 新增模板
export function addApprovalTemplate(data) {
  return request({
    url: '/system/audit.tmp/addAuditTmp',
    method: 'post',
    data: data
  })
}

// 修改模板
export function updateApprovalTemplate(data) {
  return request({
    url: '/system/audit.tmp/editAuditTmp',
    method: 'post',
    data: data
  })
}

// 删除模板
export function delApprovalTemplate(id) {
  return request({
    url: '/system/audit.tmp/delAuditTmp',
    method: 'get',
    params: { id }
  })
}

// 批量删除模板
export function delApprovalTemplateBatch(ids) {
  return request({
    url: '/system/audit.tmp/delAuditTmpBatch',
    method: 'get',
    params: { ids }
  })
}

// 查询模板详细
export function getApprovalTemplateById(id) {
  return request({
    url: '/system/audit.tmp/auditTmpInfo',
    method: 'get',
    params: { id }
  })
}

// 查询模板详细
export function getApprovalTemplateByKey(params) {
  return request({
    url: '/system/audit.tmp/auditTmpKey',
    method: 'get',
    params
  })
}
