const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  bannerHeight: state => state.app.bannerHeight,
  theme: state => state.settings.theme,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  dict: state => state.dict.dict,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  userId: state => state.user.userId,
  name: state => state.user.name,
  nickName: state => state.user.nickName,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  deptName: state => state.user.deptName,
  deptId: state => state.user.deptId,
  phoneNumber: state => state.user.phoneNumber,
  blacklistStatus: state => state.user.blacklistStatus,
  blacklistLiftEndTime: state => state.user.blacklistLiftEndTime,
  leader: state => state.user.leader,
  signatureOff: state => state.user.signatureOff,
  approvalCount: state => state.user.approvalCount,
  notPurchasedCount: state => state.user.notPurchasedCount,
  sysCode: state => state.user.sysCode,
  customerExpireDate: state => state.user.customerExpireDate,
  permission_routes: state => state.permission.routes,
  topbarRouters: state => state.permission.topbarRouters,
  defaultRoutes: state => state.permission.defaultRoutes,
  sidebarRouters: state => state.permission.sidebarRouters,
  wsMsg: state => state.websocket.wsMsg,
  // 采购字段
  purchaseDictList: state => state.purchaseDict.purchaseDictList,
  getPurchaseDict: state => (purchaseMethodCode, keyName) => {
    let obj = state.purchaseDict.purchaseDictList.find(item => item.purchaseMethodCode === purchaseMethodCode && item.keyName === keyName);
    return obj ? obj.valueName : keyName;
  },
  // 流程通用字段
  showView: state => state.process.showView,
  buyItemName: state => state.process.buyItemName,
  buyItemCode: state => state.process.buyItemCode,
  subpackageName: state => state.process.subpackageName,
  subpackageCode: state => state.process.subpackageCode,
  createYearMonth: state => state.process.createYearMonth,
  purchaseMethodCode: state => state.process.purchaseMethodCode,
  orgCode: state => state.process.orgCode,
  processFunction: state => state.process.processFunction,
  processPage: state => state.process.processPage,
  bulletinList: state => state.process.bulletinList,
  projectDeptId: state => state.process.projectDeptId,
  bidType: state => state.process.bidType,
  filterStr: state => state.process.filterStr,
  // 评委流程字段
  expertProcess: state => state.expertProcess.expertProcess,
  isLeader: state => state.expertProcess.isLeader,
  contrastList: state => state.expertProcess.contrastList,
  qualifiedData: state => state.expertProcess.qualifiedData,
  scoreData: state => state.expertProcess.scoreData,
  expertAbandon: state => state.expertProcess.expertAbandon
}
export default getters
