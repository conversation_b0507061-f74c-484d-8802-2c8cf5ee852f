import { queryItemFunctionInfo } from '@/api/purchaser/projectList';
import { getProgressData } from '@/assets/data/process/process';
import { getProcessNodes } from '@/api/supplier/supplierProcess';
import { isEmpty } from '@/utils'
import _ from 'lodash';

const getDefaultState = () => {
  return {
    showView: true,
    buyItemName: null,
    buyItemCode: null,
    subpackageName: null,
    subpackageCode: null,
    createYearMonth: null,
    purchaseMethodCode: null,
    orgCode: null,
    projectDeptId: null,
    processFunction: [],
    processPage: [],
    bulletinList: [],
    bidType: null,
    filterStr: null
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_SHOW_VIEW: (state, data) => {
    state.showView = data
  },
  SET_BUY_ITEM_NAME: (state, data) => {
    state.buyItemName = data
  },
  SET_BUY_ITEM_CODE: (state, data) => {
    state.buyItemCode = data
  },
  SET_SUBPACKAGE_NAME: (state, data) => {
    state.subpackageName = data
  },
  SET_SUBPACKAGE_CODE: (state, data) => {
    state.subpackageCode = data
  },
  SET_CREATE_YEAR_MONTH: (state, data) => {
    state.createYearMonth = data
  },
  SET_PURCHASE_METHOD_CODE: (state, data) => {
    state.purchaseMethodCode = data
  },
  SET_ORG_CODE: (state, data) => {
    state.orgCode = data
  },
  SET_PROCESS_FUNCTION: (state, data) => {
    state.processFunction = data
  },
  SET_PROCESS_PAGE: (state, data) => {
    state.processPage = data
  },
  SET_BULLETIN_LIST: (state, data) => {
    state.bulletinList = data
  },
  SET_PROJECT_DEPT_ID: (state, data) => {
    state.projectDeptId = data
  },
  SET_BID_TYPE: (state, data) => {
    state.bidType = data
  },
  SET_FILTER_STR: (state, data) => {
    state.filterStr = data;
  }
}

const actions = {
  setShowView({ commit }, data) {
    commit('SET_SHOW_VIEW', data)
  },
  setBuyItemName({ commit }, data) {
    commit('SET_BUY_ITEM_NAME', data)
  },
  setBuyItemCode({ commit }, data) {
    commit('SET_BUY_ITEM_CODE', data)
  },
  setSubpackageName({ commit }, data) {
    commit('SET_SUBPACKAGE_NAME', data)
  },
  setSubpackageCode({ commit }, data) {
    commit('SET_SUBPACKAGE_CODE', data)
  },
  setCreateYearMonth({ commit }, data) {
    commit('SET_CREATE_YEAR_MONTH', data)
  },
  setPurchaseMethodCode({ commit }, data) {
    commit('SET_PURCHASE_METHOD_CODE', data)
  },
  setOrgCode({ commit }, data) {
    commit('SET_ORG_CODE', data)
  },
  setProjectDeptId({ commit }, data) {
    commit('SET_PROJECT_DEPT_ID', data)
  },
  setBidType({ commit }, data) {
    commit('SET_BID_TYPE', data)
  },
  setFilterStr({ commit }, data) {
    let { versionType, buyBodyTotalAmount, buyClass, bidType } = data;
    let filterStr = null;
    if (versionType === 'wzlg') {
      // buyClass bidType buyBodyTotalAmount
      let price = Number(buyBodyTotalAmount);
      // buyClass 经营工程类E000000；工程类A000000；服务类C000000
      // bidType 劳务1 货物2 机械租赁3 第三方4 单一工程类5 物资6
      let str = '';
      if (buyClass === 'E000000') {
        switch (bidType) {
          case '1':
            str = '经营工程类-劳务-' + (price < 100000 ? '分管' : price <= 300000 ? '总经理' : '董事长');
            break;
          case '2':
            str = '经营工程类-货物-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          case '3':
            str = '经营工程类-机械租赁-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          case '4':
            str = '经营工程类-第三方-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          default:
            str = null;
        }
      } else if (buyClass === 'A000000') {
        switch (bidType) {
          case '5':
            str = '工程类-单一工程类-' + (price < 100000 ? '分管' : price <= 300000 ? '总经理' : '董事长');
            break;
          default:
            str = null;
        }
      } else if (buyClass === 'C000000') {
        switch (bidType) {
          case '3':
            str = '服务类-机械租赁-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          case '4':
            str = '服务类-第三方-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          case '6':
            str = '服务类-物资-' + (price <= 30000 ? '分管' : price <= 100000 ? '总经理' : '董事长');
            break;
          default:
            str = null;
        }
      }
      filterStr = str
    } else {
      filterStr = null;
    }
    commit('SET_FILTER_STR', filterStr)
  },
  setProcessFunction({ commit }, data) {
    commit('SET_PROCESS_FUNCTION', data)
  },
  setProcessPage({ commit }, data) {
    commit('SET_PROCESS_PAGE', data)
  },
  setBulletinList({ commit, dispatch }, { belongRole, buyItemCode }) {
    return new Promise((resolve, reject) => {
      queryItemFunctionInfo({ belongRole, buyItemCode }).then(response => {
        let { data } = response;
        let bulletinKVList = data.bulletinKVList || [];
        commit('SET_BULLETIN_LIST', bulletinKVList.map(item => {
          return {
            label: item.anName,
            value: item.anType,
            tags: item.tag ? item.tag.split(',') : []
          }
        }))
        resolve();
      }).catch(error => {
        reject(error)
      })
    })
  },
  setProcessNodes({ commit, dispatch }, { buyItemCode, subpackageCode, belongRole }) {
    return new Promise((resolve, reject) => {
      // belongRole 1-采购人，2-供应商，3-专家
      getProcessNodes(subpackageCode, belongRole).then(async response => {
        let { data } = response;
        let allFunc = [];
        let pageList = [];
        if (belongRole === '1' && !(data && data.length > 0)) {
          let response1 = await queryItemFunctionInfo({ belongRole, buyItemCode })
          allFunc = _.orderBy(response1.data.functionKVList || [], ['ranking', 'sort'], ['asc', 'asc']);
          allFunc.forEach(item => {
            // 兼容之前的项目，之前的项目没有配置流程节点的功能，所以从功能配置中取
            item.ranking = item.whetherNode === '1' ? 1 : null;
          })
        } else {
          allFunc = _.orderBy(data || [], ['ranking', 'sort'], ['asc', 'asc']);
        }
        let list = getProgressData(belongRole);
        pageList = allFunc.filter(item => item.belongLevel === '1');
        pageList.forEach(item => {
          let obj = list.find(v => v.purchaseFunctionKey === item.purchaseFunctionKey);
          item.path = obj ? obj.path : null;
          item.dictKey = obj ? obj.dictKey : null;
          item.label = item.purchaseFunctionName;
          item.disabled = isEmpty(item.isShow) ? false : item.isShow !== '1';
        })

        commit('SET_PROCESS_FUNCTION', allFunc.map(item => item.purchaseFunctionKey));
        commit('SET_PROCESS_PAGE', pageList);
        resolve(pageList);
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
