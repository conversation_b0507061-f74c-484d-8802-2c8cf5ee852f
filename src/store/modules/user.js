import { login, logout, getInfo, refreshToken } from '@/api/login'
import { getToken, setToken, setExpiresIn, removeToken } from '@/utils/auth'
import { removeStore, setStore } from '@/utils';
import cache from '@/plugins/cache'
import { queryNeedAuditCount } from '@/api/approval'
import { queryNotPurchasedCount } from '@/api/purchaser/projectInformation'
import { formatTime } from '@/utils/filters'

const getDefaultState = () => {
  return {
    token: getToken(),
    userId: null,
    name: '', // 登录账号
    nickName: '', // 用户呢称
    avatar: '',
    roles: [],
    permissions: [],
    deptName: null,
    deptId: null,
    phoneNumber: null,
    leader: null,
    signatureOff: null, // 1关闭 ，0开启
    // organize: [],
    approvalCount: 0,
    notPurchasedCount: 0, // 分派项目未采购数量
    blacklistStatus: 0, // 是否拉黑1是0否
    blacklistLiftEndTime: 0, // 拉黑截止时间
    sysCode: null,
    customerExpireDate: null
  }
}

const user = {
  state: getDefaultState(),

  mutations: {
    RESET_STATE: (state) => {
      Object.assign(state, getDefaultState())
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time
    },
    SET_NAME_ID: (state, userId) => {
      state.userId = userId
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NICK_NAME: (state, nickName) => {
      state.nickName = nickName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_DEPT_NAME: (state, deptName) => {
      state.deptName = deptName
    },
    SET_DEPT_ID: (state, deptId) => {
      state.deptId = deptId
    },
    SET_PHONE_NUMBER: (state, phoneNumber) => {
      state.phoneNumber = phoneNumber
    },
    SET_LEADER: (state, leader) => {
      state.leader = leader
    },
    SET_SIGNATURE_OFF: (state, data) => {
      state.signatureOff = data
    },
    // SET_ORGANIZE: (state, organize) => {
    //   state.organize = organize
    // },
    SET_APPROVAL_COUNT: (state, data) => {
      state.approvalCount = data
    },
    SET_NOT_PURCHASED_COUNT: (state, data) => {
      state.notPurchasedCount = data
    },
    SET_BLACK_LIST_STATUS: (state, data) => {
      state.blacklistStatus = data
    },
    SET_BLACK_LIST_LIFT_END_TIME: (state, data) => {
      state.blacklistLiftEndTime = data
    },
    SET_SYS_CODE: (state, data) => {
      state.sysCode = data
    },
    SET_CUSTOMER_EXPIRE_DATE: (state, data) => {
      state.customerExpireDate = data
    }
  },

  actions: {
    setApprovalCount({ commit }) {
      return new Promise((resolve, reject) => {
        queryNeedAuditCount().then(res => {
          let data = res.data;
          commit('SET_APPROVAL_COUNT', data || 0);
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },

    setNotPurchasedCount({ commit }) {
      return new Promise((resolve, reject) => {
        queryNotPurchasedCount().then(res => {
          let data = res.data;
          commit('SET_NOT_PURCHASED_COUNT', data || 0);
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },

    setSignatureOff({ commit }, data) {
      commit('SET_SIGNATURE_OFF', data)
    },

    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(res => {
          let data = res.data;
          if (!data.flag && !data.needUpLic) {
            setToken(data.access_token)
            commit('SET_TOKEN', data.access_token)
            setExpiresIn(data.expires_in)
            commit('SET_EXPIRES_IN', data.expires_in)
            let licInfo = data.licInfo || {};
            commit('SET_SYS_CODE', licInfo.sysCode);
            commit('SET_CUSTOMER_EXPIRE_DATE', licInfo.customerExpireDate);
            let org = data.org || [];
            let organize = org.filter(v => v.orgAuditStatus === '2').map(v => {
              return {
                orgName: v.orgName,
                orgCode: v.orgCode
              }
            })
            cache.local.set('organize', JSON.stringify(organize));
            cache.local.set('loginOrgName', org.length > 0 ? org[0].orgName : null);
            cache.local.set('loginOrgCode', org.length > 0 ? org[0].orgCode : null);
          }
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state, dispatch }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const org = res.organize
          if (!user) {
            return
          }
          setStore('validToken', res.validToken);
          commit('SET_TOKEN', getToken())
          const avatar = (user.avatar == '' || user.avatar == null) ? require('@/assets/images/profile.jpg') : user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME_ID', user.userId)
          commit('SET_NAME', user.userName)
          commit('SET_NICK_NAME', user.nickName)
          commit('SET_AVATAR', avatar)
          commit('SET_DEPT_NAME', user.dept ? user.dept.deptName : null)
          commit('SET_DEPT_ID', user.dept ? user.dept.deptId : null)
          commit('SET_PHONE_NUMBER', user.phonenumber)
          commit('SET_BLACK_LIST_STATUS', user.blacklistStatus)
          commit('SET_BLACK_LIST_LIFT_END_TIME', formatTime(user.blacklistLiftEndTime, 'YYYY-MM-DD HH:mm:ss', '/'))
          commit('SET_LEADER', user.dept ? user.dept.leader : null)
          let organize = org.filter(v => v.orgAuditStatus === '2').map(v => {
            return {
              orgName: v.orgName,
              orgCode: v.orgCode
            }
          })
          cache.local.set('organize', JSON.stringify(organize));
          // commit('SET_ORGANIZE', organize.filter(item => item.orgAuditStatus === '2'))
          dispatch('websocket/websocketInit', `/queue/${user.userId}`)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 刷新token
    RefreshToken({ commit, state }) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token).then(res => {
          setExpiresIn(res.data)
          commit('SET_EXPIRES_IN', res.data)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ dispatch, commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(async () => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          // removeToken()
          dispatch('websocket/websocketStop');
          removeStore('validToken');
          await dispatch('resetData');
          cache.local.remove('organize');
          cache.local.remove('loginOrgName');
          cache.local.remove('loginOrgCode');
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },

    resetData({ commit }) {
      return new Promise(resolve => {
        removeToken(); // must remove  token  first
        commit('RESET_STATE');
        commit('permission/RESET_STATE', {}, { root: true });
        commit('expertProcess/RESET_STATE', {}, { root: true });
        commit('process/RESET_STATE', {}, { root: true });
        commit('entrustProcess/RESET_STATE', {}, { root: true });
        commit('tagsView/RESET_STATE', {}, { root: true });
        commit('purchaseDict/RESET_STATE', {}, { root: true });
        resolve()
      })
    }
  }
}

export default user
