import SockJs from 'sockjs-client';
import Stomp from 'stompjs';
import { getToken } from '@/utils/auth';

const getDefaultState = () => {
  return {
    url: '', // 连接地址
    checkInterval: null, // 断线重连时 检测连接是否建立成功
    ws: null,
    stompClient: null,
    wsMsg: null // 接收到的信息
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  WEBSOCKET_INIT(state, topic) {
    this.commit('websocket/RESET_STATE');
    if (state.stompClient == null || !state.stompClient.connected) {
      state.url = `${process.env.VUE_APP_BASE_API}/system/stomp`;
      console.log('state.ws：', state.ws)
      if (state.stompClient != null && state.ws && state.ws.readyState === SockJs.OPEN) {
        console.log('SockJs.OPEN：' + state.ws.readyState)
        state.stompClient.disconnect(() => {
          this.commit('websocket/WEBSOCKET_CONNECT', topic)
        })
      } else if (state.stompClient != null && state.ws && state.ws.readyState === SockJs.CONNECTING) {
        console.log('连接正在建立：' + state.ws.readyState)
        return;
      } else {
        this.commit('websocket/WEBSOCKET_CONNECT', topic)
      }
      if (!state.checkInterval) {
        const interval = setInterval(() => {
          console.log('检测连接：' + state.ws.readyState)
          if (state.stompClient != null && state.stompClient.connected) {
            clearInterval(state.checkInterval)
            state.checkInterval = null
            console.log('重连成功')
          } else if (state.stompClient != null && state.ws.readyState != SockJs.CONNECTING) {
            // 经常会遇到websocket的状态为open 但是stompClient的状态却是未连接状态，故此处需要把连接断掉 然后重连
            state.stompClient.disconnect(() => {
              this.commit('websocket/WEBSOCKET_CONNECT', topic)
            })
          }
        }, 3000)
        state.checkInterval = interval
      }
    } else {
      console.log('连接已建立成功，不再执行')
    }
  },
  WEBSOCKET_CONNECT(state, topic) {
    console.log('开始建立连接...')
    const _this = this
    const ws = new SockJs(state.url);
    state.ws = ws
    // 获取STOMP子协议的客户端对象
    const stompClient = Stomp.over(ws);
    // stompClient.debug = null //关闭控制台打印
    stompClient.heartbeat.outgoing = 20000;
    stompClient.heartbeat.incoming = 0;// 客户端不从服务端接收心跳包
    // 向服务器发起websocket连接
    stompClient.connect({ 'Admin-Token': getToken() },
      frame => {
        // 连接成功回调函数
        console.log('Socket 连接成功，topic：' + topic)
        stompClient.subscribe(topic, res => {
          console.log('接收消息frame：', JSON.parse(res.body))
          state.wsMsg = JSON.parse(res.body);
        });
      },
      err => {
        // 连接失败回调函数
        console.log('Socket 连接失败: ' + err)
        // 第一次连接失败和连接后断开连接都会调用这个函数 此处调用重连
        setTimeout(() => {
          console.log('连接失败重连: ' + state.url)
          _this.commit('websocket/WEBSOCKET_INIT', state.url)
        }, 10000)
      }
    );
    state.stompClient = stompClient
  },
  WEBSOCKET_SEND(state, data) {
    state.stompClient.send(data.topic, {}, JSON.stringify(data.data));
  },
  WEBSOCKET_UNSUBSCRIBE(state, data) {
    state.stompClient.unsubscribe(data)
  },
  WEBSOCKET_STOP(state) {
    console.log('断开连接：', state.stompClient)
    if (state.stompClient) {
      state.stompClient.disconnect();
    }
    this.commit('websocket/RESET_STATE');
  }
}

const actions = {
  websocketInit({ commit }, data) {
    commit('WEBSOCKET_INIT', data)
  },
  websocketSend({ commit }, data) {
    commit('WEBSOCKET_SEND', data)
  },
  websocketUnsubscribe({ commit }, data) {
    commit('WEBSOCKET_UNSUBSCRIBE', data)
  },
  websocketStop({ commit }) {
    commit('WEBSOCKET_STOP')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
