import { getLeader, processNodesList } from '@/api/expert/reviewManage';
import { getProgressData } from '@/assets/data/process/process';

const getDefaultState = () => {
  return {
    expertProcess: [], // 评委流程tabs
    isLeader: false, // 是否组长
    contrastList: [], // 加入对比的数据
    qualifiedData: [],
    scoreData: [],
    expertAbandon: null // 废标状态 1废标
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_EXPERT_PROGRESS: (state, data) => {
    state.expertProcess = data
  },
  SET_IS_LEADER: (state, data) => {
    state.isLeader = data
  },
  SET_CONTRAST_LIST: (state, data) => {
    state.contrastList = data
  },
  SET_QUALIFIED_DATA: (state, data) => {
    state.qualifiedData = data
  },
  SET_SCORE_DATA: (state, data) => {
    state.scoreData = data
  }
}

const actions = {
  setExpertProcess({ commit, dispatch }, subpackageCode) {
    return new Promise((resolve, reject) => {
      processNodesList(subpackageCode).then(async response => {
        let { data } = response;
        let pageList = data || [];
        let list = getProgressData('3');
        pageList.forEach(item => {
          let obj = list.find(v => v.purchaseFunctionKey === item.nodesType);
          item.path = obj ? obj.path : null;
          item.dictKey = obj ? obj.dictKey : null;
          item.label = item.nodesName;
          item.disabled = item.isShow !== 1;
        })
        commit('SET_EXPERT_PROGRESS', pageList)
        resolve(pageList);
      }).catch(error => {
        reject(error)
      })
    })
  },
  setIsLeader({ commit }, { subpackageCode, userId }) {
    return new Promise((resolve, reject) => {
      getLeader(subpackageCode).then(async response => {
        let { data } = response;
        let judgeId = data ? data.judgeId : null;
        commit('SET_IS_LEADER', judgeId === userId)
        resolve();
      }).catch(error => {
        reject(error)
      })
    })
  },
  setContrastList({ commit }, data) {
    commit('SET_CONTRAST_LIST', data)
  },
  setQualifiedData({ commit }, data) {
    commit('SET_QUALIFIED_DATA', data)
  },
  setScoreData({ commit }, data) {
    commit('SET_SCORE_DATA', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
