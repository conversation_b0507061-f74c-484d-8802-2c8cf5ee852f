import { listDictionary } from '@/api/system/purchaseDict';

const getDefaultState = () => {
  return {
    purchaseDictList: []
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_PURCHASE_DICT_LIST: (state, data) => {
    state.purchaseDictList = data
  }
}

const actions = {
  setPurchaseDictList({ commit }) {
    return new Promise((resolve, reject) => {
      listDictionary().then(response => {
        let { rows } = response;
        commit('SET_PURCHASE_DICT_LIST', rows || [])
        resolve();
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
