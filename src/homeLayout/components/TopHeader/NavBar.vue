<template>
  <div class="nav-wrap">
    <ul class="nav-ul clearfix hidden-xs-only" :class="classObj">
      <li
        class="fl nav-li"
        :style="{'--theme': theme}"
        :class="{'active' : isActive(dict.value)}"
        v-for="(dict,index) in navList"
        :key="index"
      >
        <router-link class="a-link" :to="dict.value">{{ dict.label }}</router-link>
      </li>
    </ul>

    <hamburger
      :is-active="opened"
      class="hamburger-container hidden-sm-and-up"
      @toggleClick="toggleSideBar"
    />

    <el-drawer
      title="导航栏"
      ref="drawer"
      :visible.sync="drawer"
      custom-class="drawer-wrap"
      direction="rtl"
      :size="200"
      :with-header="false"
      :append-to-body="true"
      :modal-append-to-body="true"
      :show-close="false"
      :before-close="handleClose">
      <div class="drawer-sidebar-container">
        <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
          <el-menu
            :default-active="activeMenu"
            :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
            :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
            :active-text-color="settings.theme"
            :collapse-transition="false"
            mode="vertical"
          >
            <template v-for="(dict,key) in navList">
              <div class="menu-link" :key="key" @click="toLink(dict)">
                <el-menu-item :index="dict.value">
                  <span slot="title">{{ dict.label }}</span>
                </el-menu-item>
              </div>
            </template>
          </el-menu>
        </el-scrollbar>
      </div>
    </el-drawer>
  </div>
</template>

<script>

import { mapGetters, mapState } from 'vuex'
import Hamburger from '@/components/Hamburger'
import variables from '@/assets/styles/sidebar-variables.scss';
import { getToken } from '@/utils/auth'

export default {
  dicts: ['home_nav'],
  components: {
    Hamburger
  },
  data() {
    return {
      opened: false,
      drawer: false
    }
  },
  computed: {
    ...mapState(['settings']),
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    hasToken() {
      return getToken();
    },
    navList() {
      if (this.hasToken) {
        return this.dict.type.home_nav
      } else {
        return this.dict.type.home_nav.filter(item => item.raw.remark !== 'hasToken');
      }
    }
  },
  methods: {
    toLink(item) {
      this.$router.push({ path: item.value });
      this.$refs.drawer.closeDrawer();
      this.opened = false;
    },
    toggleSideBar() {
      this.opened = !this.opened;
      this.drawer = !this.drawer;
    },
    handleClose(done) {
      done();
    },
    isActive(currentRoute) {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return currentRoute === meta.activeMenu
      }
      return currentRoute === path
    }
  }
}
</script>

<style lang="scss">
.drawer-wrap .el-scrollbar {
  height: 100%;
}

.drawer-wrap .scrollbar-wrapper {
  overflow-x: hidden !important;

  .el-menu {
    border-right: none;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/assets/styles/sidebar-variables.scss";

.nav-wrap {
  display: inline-block;
  height: $navHeight;
  background-color: #ffffff;
}

.nav-ul {
  list-style: none;
  display: inline-block;
}

.nav-li {
  height: $navHeight;
  line-height: $navHeight;
  text-align: center;
  position: relative;
  font-size: 16px;
  padding: 0 20px;

  .a-link {
    display: inline-block;
  }

  &.active {

    .a-link {
      color: #{'var(--theme)'};
      position: relative;

      &:after {
        content: '';
        width: 100%;
        height: 2px;
        background: #{'var(--theme)'};
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}

.hamburger-container {
  line-height: $navHeight;
  height: 100%;
  float: right;
  cursor: pointer;
  transition: background .3s;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background: rgba(0, 0, 0, .025)
  }
}

.drawer-sidebar-container {
  background-color: $base-menu-background;
  height: 100%;
  overflow: hidden;
  -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.menu-link {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

@media (max-width: 767px) {
  .nav-li {
    font-size: 14px;
    padding: 0 10px !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .nav-li {
    font-size: 15px;
    padding: 0 10px !important;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .nav-li {
    padding: 0 15px !important;
  }
}

@media (min-width: 1200px) {

}
</style>
