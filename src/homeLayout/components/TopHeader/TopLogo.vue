<template>
  <div class="logo-wrap " @click="toHome" v-if="versionType!=='wzlg'">
    <el-image class="logo" :src="require('@/assets/logo/'+versionType+'.png')" fit="contain"></el-image>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TopLogo',
  data() {
    return {
      logoImg: ''
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  methods: {
    toHome() {
      this.$router.push({ path: '/home' })
    }
  }
}
</script>

<style lang="scss" scoped>
.logo-wrap {
  flex-shrink: 0;
  height: $headerHeight;
  display: inline-block;
  margin-right: 10px;
  cursor: pointer;
  width: 300px;
  padding: 10px 0;

  .logo {
    max-width: 100%;
    height: 100%;
    vertical-align: middle;
  }
}
</style>
