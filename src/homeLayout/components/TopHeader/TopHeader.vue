<template>
  <div class="fixed-header-home">
    <top></top>
    <div class="container header">
      <top-logo/>
      <nav-bar style="flex: 1;"></nav-bar>
    </div>
  </div>
</template>

<script>
import Top from './Top'
import NavBar from './NavBar'
import TopLogo from './TopLogo'

export default {
  name: 'TopHeader',
  components: {
    Top,
    NavBar,
    TopLogo
  },
  props: {
    isFixedTop: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-header-home {
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 999;

  .header{
    height: $headerHeight;
    display: flex;
    align-items: center;
  }
}
</style>
