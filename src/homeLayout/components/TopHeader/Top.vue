<template>
  <div class="app-top">
    <div class="container top-wrap" :class="classObj">
      <div class="top-l">{{ getTitle() }}</div>
      <div class="top-r" v-if="hasToken">
        <span class="hidden-xs-only">{{ nickName || name }}</span>
        <span class="pointer" :style="{'color':theme}" @click="toPersonalCenter">个人中心</span>
        <span class="pointer" :style="{'color':theme}" @click="logout">退出</span>
      </div>
      <div class="top-r" v-else>
        <span class="pointer" @click="toLogin">登录</span>
        <span class="pointer" @click="toRegister">供应商注册</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'

export default {
  name: 'Top',
  data() {
    return {
      hasToken: '',
      timer: null,
      currentTime: '',
      nowTime: ''
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'nickName',
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.hasToken = getToken()
  },
  methods: {
    getTitle() {
      return process.env.VUE_APP_TITLE
    },
    toLogin() {
      this.$router.push({ path: '/login' })
    },
    toRegister() {
      this.$router.push({ path: '/register' })
    },
    toPersonalCenter() {
      this.$router.push({ path: '/index' })
    },
    async logout() {
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$modal.loading('退出系统中，请稍候...')
        this.$store.dispatch('LogOut').then(() => {
          this.$modal.closeLoading()
          location.href = '/index'
        }).catch(() => {
          this.$modal.closeLoading()
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-top {
  background-color: #000000;
}

.top-wrap {
  height: $topHeight;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #FFFFFF;
}

.top-l {
  font-size: 12px;
}

.top-r {
  font-size: 12px;

  span {
    margin-left: 20px;
  }
}

.mobile.top-wrap {
  padding: 0 10px;

  .top-l {
    font-size: 12px;
  }

  .top-r {
    font-size: 12px;

    span {
      margin-left: 10px;
    }
  }
}
</style>
