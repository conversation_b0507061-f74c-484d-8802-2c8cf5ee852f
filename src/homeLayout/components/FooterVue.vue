<template>
  <footer :class="classObj">
    <div class="footer-top">
      <h3>友情链接</h3>
      <div class="footer-content">
        <ul>
          <li v-for="dict in dict.type.sys_friendship_link" :key="dict.value">
            <a :href="dict.value" target="_blank">{{ dict.label }}</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <div>
          <p v-html="footerInfo"></p>
          <p>
            <span class="mr10">{{ getTitle() }}版权所有</span><a href="https://beian.miit.gov.cn/" target="_blank">{{ websiteICP }}</a>
            <span class="ml15">技术支持：</span>
            <a href="#" v-if="versionType==='wzlg'">万达信息股份有限公司</a>
            <a href="http://www.epc1688.com/" target="_blank" v-else>易建采科技（武汉）有限公司</a>
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>

import { mapGetters } from 'vuex'

export default {
  name: 'FooterVue',
  dicts: ['sys_friendship_link'],
  data() {
    return {
      websiteICP: null,
      footerInfo: null
    }
  },
  computed: {
    ...mapGetters([
      'theme',
      'device'
    ]),
    classObj() {
      return {
        mobile: this.device === 'mobile'
      }
    },
    versionType() {
      return process.env.VUE_APP_VERSION_TYPE
    }
  },
  created() {
    this.getConfigKey('sys.website.ICP').then(response => {
      this.websiteICP = response.data;
    })
    this.getConfigKey('sys.website.footer').then(response => {
      this.footerInfo = response.data;
    })
  },
  methods: {
    getTitle() {
      return process.env.VUE_APP_TITLE
    }
  }
}
</script>

<style lang="scss">
footer {
  position: relative;
  box-sizing: border-box;
  height: $footerHeight;
}

.footer-top {
  background-color: #555555;
  padding: 20px 10px 0;
  height: $footerTopHeight;
  color: #AAAAAA;

  h3 {
    font-size: 18px;
    text-align: center;
    margin-bottom: 15px;
  }

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;

    ul {
      list-style: none;
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      li {
        line-height: 30px;
        text-align: center;
        font-size: 14px;
        padding: 0 25px;
      }
    }
  }
}

.footer-bottom {
  width: 100%;
  background: #000000;

  .footer-bottom-content {
    max-width: 1200px;
    margin: 0 auto;
    height: $footerBottomHeight;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }

  p {
    color: #FFFFFF;
    font-size: 12px;
    line-height: 20px;
  }
}

.mobile {
  .footer-content {
    li {
      font-size: 12px !important;
    }
  }
}

@media (max-width: 767px) {
  .footer-bottom {
    padding: 0 10px !important;
  }
}
</style>
