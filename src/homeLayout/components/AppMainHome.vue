<template>
  <section class="app-main-home">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key"/>
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMainHome',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main-home {
  min-height: calc(100vh - #{$topHeight} - #{$headerHeight} - #{$footerHeight});
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.fixed-header-home + .app-main-home {
  margin-top: calc(#{$topHeight} + #{$headerHeight});
}
</style>
