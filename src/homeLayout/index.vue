<template>
  <div class="app-home-wrapper">
    <el-scrollbar
      ref="scrollbar"
      style="height:100%"
      wrap-class="home-scrollbar-wrapper"
      :native="false"
    >
      <top-header ref="topHeader" :isFixedTop="isFixedTop"></top-header>
      <app-main-home ref="appMainHome"/>
      <footer-vue ref="footerVue"></footer-vue>
      <el-backtop target=".el-scrollbar__wrap"></el-backtop>
    </el-scrollbar>
  </div>
</template>

<script>
import { AppMainHome, TopHeader, FooterVue } from './components'
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'Layout',
  components: {
    AppMainHome,
    TopHeader,
    FooterVue
  },
  mixins: [ResizeMixin],
  data() {
    return {
      isDialog: false,
      isFixedTop: false
    }
  },
  watch: {
    $route(newValue, oldValue) {
      this.addCachedView();
      let el = document.querySelector('.el-scrollbar__wrap')
      el.scrollTop = 0
    }
  },
  mounted() {
    this.addCachedView();
    this.handleScroll()
  },
  methods: {
    addCachedView() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addCachedView', this.$route)
      }
      return false
    },
    handleScroll() {
      const scrollbarEl = this.$refs.scrollbar.wrap
      scrollbarEl.onscroll = () => {
        if (scrollbarEl.scrollTop > 50) {
          this.isFixedTop = true
        } else {
          this.isFixedTop = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-home-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  //background: #f6f6f6;
}
</style>
<style lang="scss">
.home-scrollbar-wrapper {
  overflow-x: hidden !important;

  .el-scrollbar__bar.is-vertical {
    right: 0px;
  }

  .el-backtop {
    //border: 1px solid $colorPrimary;
  }
}
</style>
